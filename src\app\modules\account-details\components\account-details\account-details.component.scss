@import 'styles/functions/rem-calc';
@import 'styles/mixins/rtl';
@import 'styles/variables';

.section-title {
  font-size: rem-calc(20px);
  color: #323232;
}

.subtitle {
  font-weight: 300;
  font-size: rem-calc(14px);
}

.line {
  height: 1px;
  margin: 0;
  background: #c3e5ed;
  border: none;
}

.successful-verification-message {
  position: absolute;
  top: rem-calc(65px);
  color: $color-5-a;
  padding-inline-start: rem-calc(10px);
}
:host ::ng-deep {
  .tar-dialog .p-dialog {
    position: fixed !important;
    max-width: rem-calc(600px);
    width: 100% !important;
    text-align: center;

    &-titlebar {
      text-align: center !important;
    }

    &-content {
      .btn-wrap {
        justify-content: center;
        flex-direction: row-reverse;
      }
    }
  }
}
