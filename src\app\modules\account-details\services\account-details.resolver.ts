import { Resolve } from '@angular/router';
import { Injectable } from '@angular/core';
import { AccountDetailsService } from './account-details.service';

@Injectable({
  providedIn: 'root',
})
export class AccountDetailsResolver implements Resolve<any> {
  constructor(private accountDetailsService: AccountDetailsService) {}

  resolve() {
    return this.accountDetailsService.getAccountData();
  }
}
