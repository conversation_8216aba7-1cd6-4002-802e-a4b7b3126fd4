import { Component, Input, OnInit } from '@angular/core';
import { RepaymentBox, RepaymentsPerYear } from '../loan-repayments.component';

@Component({
  selector: 'app-box-repayments',
  templateUrl: './box-repayments.component.html',
  styleUrls: ['./box-repayments.component.scss'],
})
export class BoxRepaymentsComponent implements OnInit {
  @Input('repaymentsData')
  public repaymentsData: RepaymentsPerYear;

  @Input('repaymentBoxItem')
  public repaymentBoxItem: RepaymentBox;

  tooltipText: any;

  constructor() {}

  ngOnInit() {
    this.tooltipText = this.tooltipTemplate();
  }

  tooltipTemplate() {
    const dateRow = ' תאריך תשלום ' + this.repaymentBoxItem.repaymentDate;
    const principalRow =
      '  סכום הקרן  ' +
      this.repaymentBoxItem.principalPayment.toLocaleString() +
      ' ₪ ';
    const interestRow =
      '  סכום ריבית  ' +
      this.repaymentBoxItem.interestPayment.toLocaleString() +
      ' ₪ ';
    const tooltipInnerHtml =
      '<span>' +
      dateRow +
      '</span>' +
      '<span>' +
      principalRow +
      '</span>' +
      '<span>' +
      interestRow +
      '</span>';

    return tooltipInnerHtml;
  }
}
