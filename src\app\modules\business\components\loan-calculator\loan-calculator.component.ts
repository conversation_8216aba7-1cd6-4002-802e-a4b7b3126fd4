import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { RegistrationService } from '../../service/registration.service';
import { ActivatedRoute } from '@angular/router';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { AppValidators } from '../../../shared/validators/validators';
import { Subscription } from 'rxjs';
import { LoanRequestService } from '../../service/loan-request.service';
import { InitialRegistrationParams } from '../../layouts/register/initial-registration-params';
import { Dropdown } from 'primeng/dropdown';
import { SelectItem } from 'primeng/api';

@Component({
  selector: 'loan-calculator',
  templateUrl: './loan-calculator.component.html',
  styleUrls: ['./loan-calculator.component.scss'],
})
export class LoanCalculatorComponent implements OnInit {
  @ViewChild('periodDropdown')
  periodDropdown: Dropdown;

  @Input('loanDurationOptions')
  loanDurationOptions: SelectItem[];

  loanCalculatorForm: UntypedFormGroup;
  monthlyPayment: number;
  currentSliderValue: number;
  monthPeriod: any = {};

  initialRegistrationParams: InitialRegistrationParams;
  minLoanDuration: number;

  private loanDurationOptionsSubscription: Subscription;
  private monthsTranslation: any;

  constructor(
    private registrationService: RegistrationService,
    private loanRequestService: LoanRequestService,
    private route: ActivatedRoute,
    private fb: UntypedFormBuilder
  ) {}

  ngOnInit() {
    this.initialRegistrationParams =
      this.registrationService.getInitialRegistrationParams();

    this.getDefaultMinLoan();
    this.monthlyPayment = this.loanRequestService.calculateMonthlyPayment(
      this.registrationService.loanRequest.amount,
      this.initialRegistrationParams.interest,
      this.registrationService.loanRequest.period
    );

    this.createForm();
    this.loanDurationOptionsSubscription = this.route.data.subscribe(
      (result) => {
        this.monthsTranslation = result.monthsTranslation;
      }
    );
    this.monthPeriod = this.registrationService.loanRequest.period;
    this.loanCalculatorForm.get('period').setValue(this.minLoanDuration);
    this.loanCalculatorForm.valueChanges.subscribe((formValue) => {
      this.registrationService.loanRequest.period = +formValue.period;
      this.registrationService.loanRequest.amount = formValue.amount;
      this.registrationService.monthlyPayment = formValue.monthlyPayment;
    });

    this.setMonthlyPayment();
  }

  monthlyPaymentSliderChange(event: any) {
    const value = event.value;
    this.currentSliderValue = value;
    this.loanCalculatorForm
      .get('period')
      .setValue(this.initialRegistrationParams.maxPeriod - value);
    this.monthlyPayment = this.loanRequestService.calculateMonthlyPayment(
      this.registrationService.loanRequest.amount,
      this.initialRegistrationParams.interest,
      this.registrationService.loanRequest.period
    );
    this.loanCalculatorForm.get('monthlyPayment').setValue(this.monthlyPayment);
  }

  setMonthlyPayment() {
    this.monthlyPayment = this.loanRequestService.calculateMonthlyPayment(
      this.registrationService.loanRequest.amount,
      this.initialRegistrationParams.interest,
      this.registrationService.loanRequest.period
    );
    this.loanCalculatorForm.get('monthlyPayment').setValue(this.monthlyPayment);
    this.loanCalculatorForm
      .get('monthlyPaymentSlider')
      .setValue(
        this.initialRegistrationParams.maxPeriod -
          this.loanCalculatorForm.get('period').value
      );
  }

  incrementMonthPayment() {
    let period = this.loanCalculatorForm.get('period').value;
    if (period > this.minLoanDuration) {
      period -= this.initialRegistrationParams.stepPeriod;
      this.loanCalculatorForm.get('period').setValue(period);
      this.setMonthlyPayment();
    }
  }

  decrementMonthPayment() {
    let period = this.loanCalculatorForm.get('period').value;
    if (period < this.initialRegistrationParams.maxPeriod) {
      period += this.initialRegistrationParams.stepPeriod;
      this.loanCalculatorForm.get('period').setValue(period);
      this.setMonthlyPayment();
    }
  }

  setMinLoanAmount() {
    if (
      this.loanCalculatorForm.get('amount').value <
      this.initialRegistrationParams.minLoanAmount
    ) {
      this.loanCalculatorForm
        .get('amount')
        .setValue(this.initialRegistrationParams.minLoanAmount);
      this.setMonthlyPayment();
    }
  }

  setMinPeriod() {
    if (this.loanCalculatorForm.get('period').value < this.minLoanDuration) {
      this.loanCalculatorForm.get('period').setValue(this.minLoanDuration);
      this.setMonthlyPayment();
    }
  }

  createForm() {
    this.loanCalculatorForm = this.fb.group({
      amount: [
        this.registrationService.loanRequest.amount,
        [AppValidators.getValidator('smallAmount')],
      ],
      monthlyPayment: [this.monthlyPayment],
      monthlyPaymentSlider: [this.monthlyPayment],
      period: [null /*this.getSelectedItem()*/, [Validators.required]],
    });
  }

  getSelectedItem() {
    for (const selectItem of this.loanDurationOptions) {
      if (
        selectItem.value &&
        selectItem.value.period === this.registrationService.loanRequest.period
      )
        return selectItem;
    }
  }

  onSubmit($event: any) {
    this.registrationService.loanRequest.period =
      this.loanCalculatorForm.get('period').value;
    this.registrationService.loanRequest.amount =
      this.loanCalculatorForm.get('amount').value;
  }

  getDefaultMinLoan() {
    this.registrationService.getMinLoanDuration('').subscribe((data) => {
      this.minLoanDuration = data.minLoanDuration;
      this.loanCalculatorForm.get('period').setValue(data.minLoanDuration);
      this.setMonthlyPayment();
    });
  }
}
