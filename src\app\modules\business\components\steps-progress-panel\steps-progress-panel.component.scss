@import 'styles/vendors/bootstrap/share';
@import 'styles/variables';

.steps-panel-wrap {
  background: #fff;
  position: relative;
  padding: 25px 15px;
  margin-bottom: 30px;
  @include media-breakpoint-up(md) {
    padding: 25px 25px;
    margin-bottom: 65px;
  }
}

:host ::ng-deep .steps-panel-heading {
  color: #000;
  font-size: 20px;
  margin-bottom: 20px;
  width: 220px;

  @include media-breakpoint-up(lg) {
    margin: 0;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size: 22px;

    .rtl & {
      left: auto;
      right: 0;
    }
  }
}

:host ::ng-deep .step-holder {
  max-width: 1225px;
  margin: 0 auto;
  position: relative;
  @include media-breakpoint-up(lg) {
    padding-left: 180px;
    padding-right: 190px;
    min-height: 50px;
  }
}

.steps-section {
  @include media-breakpoint-up(lg) {
    padding: 0 30px;
    max-width: 745px;
    margin: 0 auto;
  }
}

:host ::ng-deep .flags {
  position: absolute;
  left: 5px;
  top: 5px;
  cursor: pointer;

  .rtl & {
    left: auto;
    right: 5px;
  }
}

:host ::ng-deep .close-btn {
  height: 20px;
  width: 20px;
  display: block;
  z-index: 5;
  position: absolute;
  top: 15px;
  right: 5px;
  transform: translateY(-50%);
  @include media-breakpoint-up(lg) {
    right: 50px;
    top: 50%;
  }

  .rtl & {
    right: auto;
    left: 5px;
    @include media-breakpoint-up(lg) {
      right: auto;
      left: 50px;
    }
  }

  &:after,
  &:before {
    background: #000;
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    height: 1px;
    width: 17px;
    transform: translate(-50%, -50%) rotate(-45deg);
  }

  &:after {
    transform: translate(-50%, -50%) rotate(45deg);
  }
}

:host ::ng-deep .p-steps {
  &.steps-custom {
    position: relative;

    &:after {
      content: '';
      background: #979797;
      height: 1px;
      display: none;
      left: 23px;
      bottom: -26px;
      position: absolute;
      @include media-breakpoint-up(sm) {
        display: block;
        right: 20.5%;
      }
      @include media-breakpoint-up(lg) {
        right: 20%;
      }

      .rtl & {
        left: auto;
        right: 23px;
        @include media-breakpoint-up(sm) {
          display: block;
          right: 23px;
          left: 20.5%;
        }
        @include media-breakpoint-up(lg) {
          left: 20%;
          right: 23px;
        }
      }
    }

    .p-menuitem-link {
      color: inherit;
      display: inline-block;
      vertical-align: top;
      padding: 5px;

      @include media-breakpoint-up(sm) {
        white-space: nowrap;
      }
    }

    .p-steps-number {
      display: block;
      margin: 0 auto 5px;
      height: 36px;
      width: 36px;
      border-radius: 50%;
      border: 1px solid #00bec7;
      background: #00bec7;
      color: #fff;
      font-size: 14px;
      line-height: 34px;
      position: relative;
      z-index: 3;
      white-space: normal;
      @include media-breakpoint-up(sm) {
        display: inline-block;
        vertical-align: middle;
        margin: 0;
      }
      @include media-breakpoint-up(md) {
        height: 45px;
        width: 45px;
        line-height: 43px;
      }

      &:after {
        content: '';
        position: absolute;
        height: 8px;
        width: 8px;
        border-radius: 50%;
        left: 50%;
        display: none;
        transform: translateX(-50%);
        background: #00bec7;
        bottom: -35px;
        @include media-breakpoint-up(sm) {
          display: block;
        }
      }
    }

    .p-steps-title {
      padding: 0 4px;
      color: inherit;
      white-space: normal;
      display: inline-block;
      vertical-align: middle;
    }
  }

  ul {
    display: flex;
    justify-content: center;
    margin: 0 -5px;
  }

  .p-steps-item {
    width: 25%;
    border: none;
    background: transparent;
    float: none;
    color: #00bec7;

    &.p-state-disabled,
    &.p-widget:disabled {
      opacity: 1;
    }

    &.p-state-highlight ~ .p-steps-item {
      color: #575757;

      .p-steps-number {
        color: #acacac;
        background: transparent;
        border: 1px solid #979797;

        &:after {
          background: #b2b2b2;
        }
      }

      &:not(.p-state-disabled):hover {
        color: #acacac;

        a {
          color: inherit;
        }
      }
    }

    &:not(.p-state-highlight):not(.p-state-disabled):hover {
      background: transparent;
      color: #00bec7;

      a {
        color: inherit;
      }
    }

    &.p-state-highlight {
      color: #00bec7;

      .p-steps-number {
        background: #00bec7;
        color: #fff;
        border-color: #00bec7;

        &:after {
          background: #00bec7;
        }
      }
    }
  }
}
