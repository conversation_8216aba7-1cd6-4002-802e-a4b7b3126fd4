import {
  ActivatedRouteSnapshot,
  Resolve,
  RouterStateSnapshot,
} from '@angular/router';
import { Injectable } from '@angular/core';
import { TypeEnum } from '../../model/type-enum';
import { Observable } from 'rxjs';
import { EnumService } from '../../service/enum.service';

@Injectable()
export class CollateralOptionsResolver implements Resolve<TypeEnum[]> {
  constructor(private enumService: EnumService) {}

  resolve(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<TypeEnum[]> | Promise<TypeEnum[]> | TypeEnum[] {
    return this.enumService.getCategoryOptions('collateral');
  }
}
