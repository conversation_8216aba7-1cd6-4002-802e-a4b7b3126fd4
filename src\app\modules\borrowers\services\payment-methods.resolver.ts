import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { Observable } from 'rxjs';
import { PaymentMethods } from '../models/payment-methods';
import { BorrowersService } from '../services/borrowers.service';

@Injectable({ providedIn: 'root' })
export class PaymentMethodsResolver implements Resolve<PaymentMethods> {
  constructor(private borrowersService: BorrowersService) {}

  resolve(): Observable<PaymentMethods> {
    return this.borrowersService.getPaymentMethods();
  }
}
