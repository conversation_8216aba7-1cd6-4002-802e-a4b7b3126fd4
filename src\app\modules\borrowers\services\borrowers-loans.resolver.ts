import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  Resolve,
  RouterStateSnapshot,
} from '@angular/router';
import { Observable } from 'rxjs';
import { ActiveLoansServiceResponse } from '../models/active-loans-service-response';
import { BorrowersService } from '../services/borrowers.service';

@Injectable({
  providedIn: 'root',
})
export class BorrowersLoansResolver
  implements Resolve<ActiveLoansServiceResponse> {
  constructor(private borrowersService: BorrowersService) {}

  resolve(
    route: ActivatedRouteSnapshot
  ): Observable<ActiveLoansServiceResponse> {
    return this.borrowersService.getActiveLoansInfo();
  }
}
