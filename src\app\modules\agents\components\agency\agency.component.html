<app-table-generator
  [config]="config"
  [tableData]="managerData.agentSummaryList"
  [dataKey]="'agentId'"
  [expansionTemplate]="expansionTemplate"
  [bodyTemplate]="bodyTemplate"
  [headerTemplate]="headerTemplate"
  (selectedRowData)="selectedRowData($event)"
  [rowExpand]="'single'"
  [loading]="loading"
></app-table-generator>
<ng-template #headerTemplate>
  <div class="card">
    <div class="card-body">
      <app-agent [detail]="managerData"></app-agent>
    </div>
  </div>
</ng-template>

<ng-template #expansionTemplate let-columns="columns">
  <app-deposit-withdrawals
    *ngIf="agentDetail"
    [agentDetail]="agentDetail"
  ></app-deposit-withdrawals>
</ng-template>

<ng-template #bodyTemplate let-columns="columns" let-rowData="rowData">
  <td>
    <span class="td-hold">
      {{ rowData.agentId }}
    </span>
  </td>
  <td>
    <span class="td-hold td-big"
      >{{ rowData?.agentFirstName }} {{ rowData?.agentLastName }}</span
    >
  </td>
  <td>
    <span class="td-hold td-socialid">{{ rowData.socialId }}</span>
  </td>
  <td>
    <span class="td-hold"
      >{{ rowData.portfolioAmount | number: '1.0-2' }}
      {{ 'currency' | translate }}</span
    >
  </td>
</ng-template>
