<div class="white-box-wrap">
  <div class="business-owners-wrap">
    <div class="loan-info-title">
      <h2>רישום שותפים</h2>
    </div>
    <div class="search-wrap">
      <div class="search-box d-none">
        <div class="search-inner">
          <div class="input-wrap">
            <button type="button" class="search-btn">
              <i class="icon-search" aria-hidden="true"></i>
            </button>
            <input
              type="search"
              class="form-control search-control"
              placeholder="חיפוש רשומים"
            />
          </div>
        </div>
      </div>
      <div class="btn-box">
        <button
          type="button"
          class="btn btn-primary"
          (click)="openBzModal(BusinessOwnerstemplate)"
        >
          הוסף
        </button>
      </div>
    </div>
    <div class="list-wrap">
      <div>
        <business-owner-box
          class="owner-box"
          *ngFor="let owner of ownersAsArray"
          [owner]="owner"
          (toggleDeleteData)="deleteBusinessOwner($event)"
          (toggleEditData)="toggleUpdateBusinessOwner($event)"
        ></business-owner-box>
      </div>
      <div class="empty-list" [hidden]="ownersAsArray.length">
        <span>לא נמצאו שותפים</span>
      </div>
    </div>
    <div class="btn-holder">
      <button type="button" class="btn btn-primary" (click)="submit()">
        המשך
      </button>
    </div>
  </div>
  <ng-template #BusinessOwnerstemplate>
    <div class="modal-body">
      <span
        class="close-btn"
        (click)="toggleCancelUpdateBusinessOwner()"
      ></span>
      <shareholders-modal-form
        [businessOwnersForm]="businessOwnersForm"
        (toggleSaveData)="saveOrCreateBusinessOwner($event)"
        (toggleCancelData)="toggleCancelUpdateBusinessOwner($event)"
        [backEndValidationErrors]="apiErrorResponse"
      >
      </shareholders-modal-form>
    </div>
  </ng-template>
</div>
