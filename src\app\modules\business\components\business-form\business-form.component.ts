import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import {
  AbstractControl,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { Business } from '../../model/business';
import { AutoCompleteService } from '../../../shared/services/autocomplete-service';
import { TypeEnum } from '../../model/type-enum';
import { EnumService } from '../../service/enum.service';
import { HelperService } from '../../service/helper.service';
import { ApiErrorsResponse } from '../../../shared/models/api-errors-response';
import { ErrorHandling } from '../../../shared/models/error-handling';
import { AppConstants } from '../../../shared/utils/app-constans';
import { FileHolder } from '../../../shared/models/file-holder';
import { Base64EncodedFile } from '../../model/base-64-encoded-file';
import { AppValidators } from '../../../shared/validators/validators';
import { BusinessService } from '../../service/business.service';
import { Address } from '../../model/address';
import { PRadioButton } from '../../model/p-radio-button';
import { PAutoComplete } from '../../model/p-auto-complete';
import { UtilityService } from '../../service/utility.service';
import { DropdownOption } from '~tarya/modules/shared/models/option';

@Component({
  selector: 'business-form',
  templateUrl: './business-form.component.html',
  styleUrls: ['./business-form.component.scss'],
})
export class BusinessFormComponent implements OnInit {
  disabledBtn: boolean;

  @Input('business')
  business: Business;

  @Input('currentLang')
  currentLang: string;

  @Input('businessTypes')
  businessTypes: TypeEnum[];

  @Input('businessFields')
  businessFields: TypeEnum[];

  @Input('uploadBusinessLogoResource')
  uploadBusinessLogoResource: string;

  @Input('uploadedImages')
  uploadedImages: string[];

  @Input('backEndValidationErrors')
  set backEndValidationErrors(backEndValidationErrors: ApiErrorsResponse) {
    ErrorHandling.formSetErrors(
      backEndValidationErrors,
      this.businessModelForm
    );
  }

  @Output('businessEmitter')
  businessEmitter: EventEmitter<Business> = new EventEmitter();

  businessModelForm: UntypedFormGroup;

  businessTypesPRadiobutton: PRadioButton[];
  businessFieldsPAutocomplete: PAutoComplete[];

  filteredStreetsSingle: DropdownOption[];
  filteredCitiesSingle: DropdownOption[];
  filteredSics: PAutoComplete[] = [];
  filteredBusinessFields: PAutoComplete[] = [];

  currentDate = new Date();
  currentYear;
  minEstablishmentYear = 1948;

  businessDescriptionMaxLength = 250;
  leftCharacters: number = this.businessDescriptionMaxLength;

  yearMask = AppConstants.YEAR_MASK;
  registrationNumberMask = AppConstants.TZ_AND_REGISTRATION_NUMBER_MASK;

  private businessFieldSics: PAutoComplete[] = [];

  constructor(
    private autoCompleteService: AutoCompleteService,
    private fb: UntypedFormBuilder,
    private enumService: EnumService,
    private helperService: HelperService,
    private businessService: BusinessService,
    private utilityService: UtilityService
  ) {}

  ngOnInit() {
    this.currentYear = this.currentDate.getFullYear();
    this.businessTypesPRadiobutton =
      this.helperService.fromTypeEnumToPRadioButton(this.businessTypes);
    this.businessFieldsPAutocomplete =
      this.helperService.fromTypeEnumToPAutocomplete(this.businessFields);

    this.createForm();

    // fix for the p-autocomplete bug - to set initial values
    this.businessModelForm.get('city').setValue({
      name: this.business.address.city,
    });
    this.businessModelForm.get('street').setValue({
      name: this.business.address.street,
    });

    if (this.business.businessField.id) {
      this.businessModelForm
        .get('businessField')
        .setValue(
          this.businessFieldsPAutocomplete.find(
            (value) => value.code.id == this.business.businessField.id
          )
        );
      this.populateSics({ query: '' }).then(() => {
        if (this.business.businessSic) {
          this.businessModelForm
            .get('businessSic')
            .setValue(
              this.businessFieldSics.find(
                (value) => value.code.id == this.business.businessSic.id
              )
            );
        }
      });
    }
  }

  updateLeftChars(control: AbstractControl) {
    this.leftCharacters =
      this.businessDescriptionMaxLength -
      (control.value === null ? 0 : control.value.length);
  }

  ngAfterViewInit() {
    setTimeout(() => {
      for (const key in this.businessModelForm.controls) {
        if (this.businessModelForm.controls[key]) {
          this.businessModelForm.controls[key].markAsPristine();
        }
      }
    }, 50);
  }

  createForm() {
    this.businessModelForm = this.fb.group({
      type: [this.business.type.id, [Validators.required]],
      name: [this.business.name, [Validators.required]],
      registrationNumber: [
        this.business.registrationNumber,
        [Validators.required, AppValidators.registrationNumberValidator],
      ],
      businessField: [this.business.businessField.id, [Validators.required]],
      businessSic: [this.business.businessSic.id, [Validators.required]],
      establishmentYear: [
        this.business.establishmentYear,
        [Validators.required, Validators.minLength(4)],
      ],
      city: [this.business.address.city, [Validators.required]],
      street: [this.business.address.street, [Validators.required]],
      streetNumber: [this.business.address.streetNumber, [Validators.required]],
      zipcode: [
        this.business.address.zipcode,
        [AppValidators.getValidator('zip-code')],
      ],
      description: [this.business.description, [Validators.required]],
      logo: null,
    });

    if (!this.businessModelForm.get('registrationNumber').value) {
      this.businessModelForm
        .get('registrationNumber')
        .setAsyncValidators(
          AppValidators.registrationNumberIsTakenValidator(this.businessService)
        );
    } else {
      this.businessModelForm.get('registrationNumber').disable();
    }

    this.updateLeftChars(this.businessModelForm.get('description'));
  }

  async filterCitiesSingle(event) {
    this.filteredCitiesSingle = await this.utilityService.filterCities(
      event.query
    );
  }

  async filterStreetSingle(event) {
    const city =
      this.businessModelForm.get('city').value.name ||
      this.businessModelForm.get('city').value;
    this.filteredStreetsSingle = await this.utilityService.filterStreets(
      city,
      event.query
    );
  }

  populateSics(event): Promise<any> {
    return new Promise((resolve) => {
      const businessField = this.businessModelForm.get('businessField').value;
      if (!businessField) {
        return;
      }
      this.enumService
        .getCategoryOptions(
          'bf' + this.capitalizeFirstLetter(businessField.code.name)
        )
        .subscribe((options: TypeEnum[]) => {
          resolve(
            (this.businessFieldSics =
              this.helperService.fromTypeEnumToPAutocompleteBusinessSic(
                options
              ))
          );
        });
    });
  }

  suggestSics(event) {
    this.filteredSics = this.businessFieldSics.filter((domain) =>
      domain.name.toLowerCase().includes(event.query.toLowerCase())
    );
  }

  suggestBusinessFields(event) {
    this.filteredBusinessFields = this.businessFieldsPAutocomplete.filter(
      (bs) => bs.name.toLowerCase().includes(event.query.toLowerCase())
    );
  }

  onBusinessFieldSelect(event: Event) {
    this.populateSics({ query: '' });
    this.businessModelForm.get('businessSic').patchValue(null);
  }

  private capitalizeFirstLetter(str: string) {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  submit(event) {
    const businessToEmit = {} as Business;
    this.disabledBtn = true;
    businessToEmit.id = this.business.id;
    businessToEmit.type = {} as TypeEnum;
    businessToEmit.type.id = this.businessModelForm.get('type').value;
    businessToEmit.name = this.businessModelForm.get('name').value;
    businessToEmit.registrationNumber =
      this.businessModelForm.get('registrationNumber').value;
    businessToEmit.businessField = {} as TypeEnum;
    businessToEmit.businessField.id =
      this.businessModelForm.get('businessField').value.code.id;
    businessToEmit.businessSic = {} as TypeEnum;
    businessToEmit.businessSic.id =
      this.businessModelForm.get('businessSic').value.code.id;
    businessToEmit.establishmentYear =
      this.businessModelForm.get('establishmentYear').value;
    businessToEmit.address = {} as Address;
    businessToEmit.address.city =
      this.businessModelForm.get('city').value.name ||
      this.businessModelForm.get('city').value;
    businessToEmit.address.street =
      this.businessModelForm.get('street').value.name ||
      this.businessModelForm.get('street').value;
    businessToEmit.address.streetNumber =
      this.businessModelForm.get('streetNumber').value;
    businessToEmit.address.zipcode =
      this.businessModelForm.get('zipcode').value;
    businessToEmit.description =
      this.businessModelForm.get('description').value;
    businessToEmit.logo = this.businessModelForm.get('logo').value;

    this.businessEmitter.emit(businessToEmit);
  }

  onImageSelected($event: FileHolder) {
    const reader = new FileReader();
    reader.readAsDataURL($event.file);
    reader.onload = () => {
      const logoFile = {} as Base64EncodedFile;
      logoFile.filename = $event.name;
      logoFile.filetype = $event.file.type;
      if (typeof reader.result === 'string') {
        logoFile.value = reader.result.split(',')[1];
      }
      this.businessModelForm.get('logo').setValue(logoFile);
    };
  }

  checkCurrentYear(control: AbstractControl) {
    if (control.value > this.currentYear) {
      control.setValue(this.currentYear);
    }

    if (control.value < this.minEstablishmentYear) {
      control.setValue(this.minEstablishmentYear);
    }
  }
}
