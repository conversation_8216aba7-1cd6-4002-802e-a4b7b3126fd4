@import 'variables';

@font-face {
  font-family: '#{$icomoon-font-family}';
  src: url('#{$icomoon-font-path}/#{$icomoon-font-family}.eot?nw4wm6');
  src: url('#{$icomoon-font-path}/#{$icomoon-font-family}.eot?nw4wm6#iefix')
      format('embedded-opentype'),
    url('#{$icomoon-font-path}/#{$icomoon-font-family}.ttf?nw4wm6')
      format('truetype'),
    url('#{$icomoon-font-path}/#{$icomoon-font-family}.woff?nw4wm6')
      format('woff'),
    url('#{$icomoon-font-path}/#{$icomoon-font-family}.svg?nw4wm6##{$icomoon-font-family}')
      format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^='icon-'],
[class*=' icon-'] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: '#{$icomoon-font-family}' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-pr-deposit {
  &:before {
    content: $icon-pr-deposit;
  }
}
.icon-bank {
  &:before {
    content: $icon-bank;
  }
}
.icon-preferences {
  &:before {
    content: $icon-preferences;
  }
}
.icon-deposit {
  &:before {
    content: $icon-deposit;
  }
}
.icon-financial {
  &:before {
    content: $icon-financial;
  }
}
.icon-type {
  &:before {
    content: $icon-type;
  }
}
.icon-personal {
  &:before {
    content: $icon-personal;
  }
}
