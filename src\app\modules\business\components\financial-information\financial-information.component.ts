import { Component, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import {
  CURRENT_YEAR,
  FinancialInformationItem,
  FORECAST,
  PAST_YEAR,
} from './financial-information-constants';
import { ActivatedRoute, Router } from '@angular/router';
import { Business } from '../../model/business';
import { FinancialData } from '../../model/financial-data';
import { FutureFinancialData } from '../../model/future-financial-data';
import { BusinessService } from '../../service/business.service';
import { LoanRequestService } from '../../service/loan-request.service';
import { LOAN_PAGES } from '../../layouts/loan/loan-pages';

export class FormItem {
  year: number;
  formGroup: UntypedFormGroup;
  formTemplate: FinancialInformationItem[];
  additionalTitle?: string;
}

@Component({
  selector: 'financial-information',
  templateUrl: './financial-information.html',
  styleUrls: ['./financial-information.scss'],
})
export class FinancialInformationComponent implements OnInit {
  pastYearsForms: FormItem[] = [];
  futureYearsForms: FormItem[] = [];

  private business: Business;

  private pastFinancialDataMap: Map<number, FinancialData> = new Map<
    number,
    FinancialData
  >();
  private futureFinancialDataMap: Map<number, FutureFinancialData> = new Map<
    number,
    FutureFinancialData
  >();

  constructor(
    private route: ActivatedRoute,
    private businessService: BusinessService,
    private router: Router,
    private loanRequest: LoanRequestService
  ) {}

  ngOnInit() {
    this.business = this.loanRequest.getCurrentBusiness();
    if (this.business.financialData) {
      this.business.financialData.forEach((data: FinancialData) => {
        this.pastFinancialDataMap.set(new Date(data.date).getFullYear(), data);
      });
    }
    if (this.business.futureFinancialData) {
      this.business.futureFinancialData.forEach((data: FutureFinancialData) => {
        this.futureFinancialDataMap.set(
          new Date(data.date).getFullYear(),
          data
        );
      });
    }
    this.createForm();
  }

  createForm() {
    const currentYear = new Date().getFullYear();

    for (let year = currentYear - 3; year < currentYear; year++) {
      this.pushNewFormGroup(PAST_YEAR, year);
    }
    this.pushNewFormGroup(CURRENT_YEAR, currentYear, 'מאזן בוחן');

    for (let year = currentYear; year <= currentYear + 1; year++) {
      this.pushNewFormGroup(FORECAST, year);
    }
  }

  createFormControlsObject(
    financialInformationItems: FinancialInformationItem[],
    year: number,
    financialDataMap: Map<number, any>
  ) {
    const formControls = {};
    for (const financialItem of financialInformationItems) {
      const financialDataPerYear = financialDataMap.get(year);
      let formState = financialDataPerYear
        ? financialDataMap.get(year)[financialItem.label]
        : null;
      if (financialItem.label === 'date') {
        formState = this.convertYearToStringYYYYMMDD(year);
      }
      formControls[financialItem.label] = new UntypedFormControl(
        formState,
        financialItem.validator
      );
    }
    return formControls;
  }

  submitFinancialData(event) {
    const businessToSave = {} as Business;
    businessToSave.id = this.business.id;
    businessToSave.financialData = [];
    this.pastYearsForms.forEach((formItem: FormItem) => {
      this.replace(formItem);

      const yearData: FinancialData = formItem.formGroup.value;
      businessToSave.financialData.push(yearData);
    });

    businessToSave.futureFinancialData = [];
    this.futureYearsForms.forEach((formItem: FormItem) => {
      this.replace(formItem);

      const yearData: FutureFinancialData = formItem.formGroup.value;
      businessToSave.futureFinancialData.push(yearData);
    });

    this.business.financialData = businessToSave.financialData;
    this.business.futureFinancialData = businessToSave.futureFinancialData;

    this.businessService.updateBusiness(businessToSave).subscribe(() =>
      this.router.navigate([LOAN_PAGES[12]], {
        relativeTo: this.route.parent.parent,
      })
    );
  }

  convertYearToStringYYYYMMDD(year) {
    return year.toString() + '-01-01';
  }

  private pushNewFormGroup(
    formTemplate: FinancialInformationItem[],
    year: number,
    additionalTitle?: string
  ): void {
    this.pastYearsForms.push({
      year,
      formGroup: new UntypedFormGroup(
        this.createFormControlsObject(
          formTemplate,
          year,
          this.pastFinancialDataMap
        )
      ),
      formTemplate,
      additionalTitle,
    });
  }

  private replace(formItem: FormItem) {
    for (const i in formItem.formGroup.value) {
      if (!formItem.formGroup.value[i] === null && i !== 'date') {
        formItem.formGroup.value[i] = Number(
          formItem.formGroup.value[i].toString().replace(/,/g, '')
        );
      }
    }
  }
}
