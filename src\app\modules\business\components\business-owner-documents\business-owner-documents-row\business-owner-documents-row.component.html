<p-accordionTab>
    <p-header>
        <div class="header-content">
            <span class="heading-content">
                <strong>{{ businessOwnerData.name }}</strong>
                <span>{{businessOwnerData.businessEmailAddress}}</span>
            </span>
            <span>{{ numberOfDocuments - businessOwnerData.numberOfUploadedDocuments }} מתוך {{ numberOfDocuments}} מסמכים נותרו להעלאה</span>
        </div>
    </p-header>
    <div class="accordion-inner">
        <document-uploader *ngFor="let uploadFileItem of uploadFilesInfo" (toggleUploadData)="updateDocumentsCounter($event)" [uploadFileInfo]="uploadFileItem">
        </document-uploader>
    </div>
</p-accordionTab>