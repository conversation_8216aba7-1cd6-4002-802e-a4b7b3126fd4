import { Component, OnInit } from '@angular/core';
import { Business } from '../../model/business';
import { BusinessService } from '../../service/business.service';
import { BusinessPages } from '../../layouts/business-layout-component/business-pages';
import {
  AbstractControl,
  UntypedFormBuilder,
  UntypedFormGroup,
} from '@angular/forms';

export class BusinessInfoData {
  businessLogoResource?: string;
  businessName?: string;
  date: string;
  pendingLoans: number;
  approvedLoans: number;
}

@Component({
  selector: 'app-business-gallery',
  templateUrl: './business-gallery.component.html',
  styleUrls: ['./business-gallery.component.scss'],
})
export class BusinessGalleryComponent implements OnInit {
  businessInfoData: BusinessInfoData[] = [];
  private businesses: Business[];
  newBusinessPath: string =
    BusinessPages.BUSINESS_ROUTE + '/' + BusinessPages.CREATE_NEW_BUSINESS;

  searchText: string;
  BusinessOptionsForm: UntypedFormGroup;

  constructor(
    private businessService: BusinessService,
    private fb: UntypedFormBuilder
  ) {}

  ngOnInit() {
    this.createForm();
    this.businesses = this.businessService.getAllBusinesses();

    this.businesses.forEach((business: Business) => {
      this.businessInfoData.push({
        businessLogoResource: business.logoExists
          ? this.businessService.getBusinessLogoResource(business.id)
          : null,
        businessName: business.name,
        pendingLoans: 5,
        approvedLoans: 2,
        date: '2018-01-01',
      });
    });
  }

  createForm() {
    this.BusinessOptionsForm = this.fb.group({
      filterSearch: [null],
    });
  }

  searchFilter(control: AbstractControl) {
    this.searchText = control.value;
  }
}
