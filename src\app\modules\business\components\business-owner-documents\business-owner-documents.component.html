<div class="white-box-wrap">
    <div class="business-owner-documents">
        <form action="#">
            <div class="heading-secondary">
                <h3>מסמכים</h3>
                <h5>{{'loan-request.documents.' + documentsTypeTranslationKey | translate}}</h5>
            </div>
            <div class="documents-holder">
                <p-accordion class="bz-accordion" [multiple]="true">
                    <business-owner-documents-row *ngFor="let businessOwnerData of businessOwnersData"
                                                  [businessOwnerData]="businessOwnerData"
                                                  [fileUploadResource]="businessOwnerData.fileUploadResource" >
                    </business-owner-documents-row>
                </p-accordion>
            </div>
        </form>
        <div class="btn-holder">
            <button type="button" class="btn btn-primary" (click)="submit()">המשך</button>
        </div>
    </div>
</div>