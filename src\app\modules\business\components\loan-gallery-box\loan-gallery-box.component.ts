import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { LoanRequestStatus } from '../../enums/loan-request-status.enum';
import { LoanInfoData } from '../loans-gallery/loans-gallery.component';

@Component({
  selector: 'loan-gallery-box',
  templateUrl: './loan-gallery-box.component.html',
  styleUrls: ['./loan-gallery-box.component.scss'],
})
export class LoanGalleryBoxComponent implements OnInit {
  @Input('loanItem')
  loanItem: LoanInfoData;

  @Output('loanClicked')
  loanClicked: EventEmitter<LoanInfoData> = new EventEmitter();

  progressValue = 40;

  loanCancelled = false;

  constructor() {}

  ngOnInit() {
    this.loanCancelled =
      this.loanItem.loanStatus === LoanRequestStatus.CANCELED;
  }

  fireLoanClickedEvent($event: any) {
    this.loanClicked.emit(this.loanItem);
  }
}
