import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { AccountDetailsConfirmMobileDialogComponent } from './account-details-confirm-mobile-dialog.component';

describe('AccountDetailsConfirmMobileDialogComponent', () => {
  let component: AccountDetailsConfirmMobileDialogComponent;
  let fixture: ComponentFixture<AccountDetailsConfirmMobileDialogComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [AccountDetailsConfirmMobileDialogComponent],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(
      AccountDetailsConfirmMobileDialogComponent
    );
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
