import { AfterViewInit, Component, OnInit } from '@angular/core';
import { GeneralLoanInfo } from '../../model/general-loan-info.class';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { RegistrationService } from '../../service/registration.service';
import { REGISTRATION_STEPS } from '../../layouts/register/registration-steps';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import { EnumService } from '../../service/enum.service';
import { HelperService } from '../../service/helper.service';
import { TypeEnum } from '../../model/type-enum';
import { LoanRequestService } from '../../service/loan-request.service';
import { IdResponse } from '../../model/id-response';
import { ErrorHandling } from '../../../shared/models/error-handling';
import * as HttpStatus from 'http-status-codes';
import { InitialRegistrationParams } from '../../layouts/register/initial-registration-params';
import { LoanRequest } from '../../model/loan-request';
import { Business } from '../../model/business';
import { Observable } from 'rxjs';
import { flatMap } from 'rxjs/operators';
import { SelectItem } from 'primeng/api';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-general-loan-info',
  templateUrl: './general-loan-info.component.html',
  styleUrls: ['./general-loan-info.component.scss'],
})
export class GeneralLoanInfoComponent implements OnInit, AfterViewInit {
  loanDurationOptions: SelectItem[];
  loanPurposes: SelectItem[];
  generalLoanInfo: GeneralLoanInfo;
  generalLoanInfoForm: UntypedFormGroup;
  registrationMode: boolean;
  monthPeriod: number;
  disabledBtn: boolean;

  initialRegistrationParams: InitialRegistrationParams;
  minPeriod$: Observable<{ minLoanDuration: number }>;

  constructor(
    private registrationService: RegistrationService,
    private loanRequestService: LoanRequestService,
    private route: ActivatedRoute,
    private fb: UntypedFormBuilder,
    private router: Router,
    private translateService: TranslateService,
    private enumService: EnumService,
    private helperService: HelperService
  ) {}

  ngOnInit() {
    this.initialRegistrationParams =
      this.registrationService.getInitialRegistrationParams();
    this.registrationMode =
      this.route.snapshot.parent.url[0].path === 'register';
    this.setGeneralLoanInfo();
    this.setLoanDurationsOptions();
    this.setLoanPurposes();
    this.createForm();

    this.translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.setLoanDurationsOptions();
    });

    this.generalLoanInfoForm
      .get('amount')
      .setValue(this.generalLoanInfo.amount);
    this.monthPeriod = this.generalLoanInfo.period;

    this.generalLoanInfoForm.get('period').setValue(this.monthPeriod);
    this.minPeriod$ = this.registrationService.getMinLoanDuration('');
  }

  ngAfterViewInit() {
    setTimeout(() => {
      for (const key in this.generalLoanInfoForm.controls) {
        if (this.generalLoanInfoForm.controls[key]) {
          this.generalLoanInfoForm.controls[key].markAsPristine();
        }
      }
    }, 50);
  }

  setGeneralLoanInfo() {
    if (this.registrationMode) {
      this.generalLoanInfo = this.registrationService.getGeneralLoanInfo();
    } else {
      this.generalLoanInfo = this.loanRequestService.getGeneralLoanInfo();
    }
  }

  setMinLoanAmount() {
    if (
      this.generalLoanInfoForm.get('amount').value <
      this.initialRegistrationParams.minLoanAmount
    ) {
      this.generalLoanInfoForm
        .get('amount')
        .setValue(this.initialRegistrationParams.minLoanAmount);
    }
  }

  createForm() {
    this.generalLoanInfoForm = this.fb.group(
      {
        name: [this.generalLoanInfo.name, [Validators.required]],
        amount: [this.generalLoanInfo.amount, [Validators.required]],
        period: [this.generalLoanInfo.period, [Validators.required]],
        purpose: [this.generalLoanInfo.purpose, [Validators.required]],
        purposeFreeText: [
          this.generalLoanInfo.purposeFreeText,
          [Validators.required],
        ],
        orgCodeSwitch: [this.generalLoanInfo.orgCodeSwitch],
        orgCode: [this.generalLoanInfo.orgCode],
      }
      // {validator: this.checkIfOrgCodeIsSet('orgCodeSwitch','orgCode')}
    );
  }

  clearOrgCode() {
    this.generalLoanInfoForm.get('orgCode').patchValue('');
  }

  checkIfOrgCodeIsSet(orgCodeSwitchKey: string, orgCodeKey: string) {
    return (group: UntypedFormGroup): ValidationErrors => {
      const orgCodeSwitch = group.controls[orgCodeSwitchKey];
      const orgCode = group.controls[orgCodeKey];
      if (orgCodeSwitch) {
        if (!orgCodeSwitch.value || (orgCodeSwitch.value && orgCode.value)) {
          return null;
        }
        group.controls[orgCodeKey].setErrors({ required: true });
        return { required: true };
      }
      return null;
    };
  }

  submit($event: any) {
    $event.preventDefault();

    const success = () => {
      if (this.registrationMode) {
        this.disabledBtn = true;
        this.generalLoanInfo = this.generalLoanInfoForm.value;
        this.registrationService.saveGeneralLoanInfo(this.generalLoanInfo);
        this.router.navigate(['/register/' + REGISTRATION_STEPS[1]]);
        window.scrollTo(0, 0);
      } else {
        const loanRequest = this.loanRequestService.getCurrentLoanRequest();
        const loanRequestToSave = new LoanRequest();
        loanRequestToSave.name = this.generalLoanInfoForm.get('name').value;
        loanRequestToSave.amount = this.generalLoanInfoForm.get('amount').value;
        loanRequestToSave.period = this.generalLoanInfoForm.get('period').value;
        loanRequestToSave.purpose =
          this.generalLoanInfoForm.get('purpose').value;
        loanRequestToSave.purposeFreeText =
          this.generalLoanInfoForm.get('purposeFreeText').value;
        loanRequestToSave.orgCode =
          this.generalLoanInfoForm.get('orgCode').value;
        loanRequestToSave.business = {} as Business;
        loanRequestToSave.business.id = loanRequest.business.id;
        this.loanRequestService
          .createLoanRequest(loanRequestToSave)
          .pipe(
            flatMap((res: IdResponse) => {
              loanRequest.id = res.id;
              return this.loanRequestService.submitDraftLoanRequest(res.id);
            })
          )
          .subscribe(() =>
            this.router.navigate(['/loan-request/' + loanRequest.id])
          );
      }
    };

    const fail = (res: HttpErrorResponse) => {
      if (res.status === HttpStatus.UNPROCESSABLE_ENTITY) {
        ErrorHandling.formSetErrors(
          JSON.parse(res.error),
          this.generalLoanInfoForm
        );
      } else {
        throw new Error(res.error);
      }
    };

    if (this.generalLoanInfoForm.value.orgCodeSwitch) {
      this.registrationService
        .checkOrgCode(this.generalLoanInfoForm.value.orgCode)
        .subscribe(success, fail);
    } else {
      success();
    }
  }

  private setLoanDurationsOptions() {
    this.loanDurationOptions = [{ label: 'תקופת הלוואה', value: null }];
    this.translateService.get('global.calendar.months').subscribe((result) => {
      for (let i = 6; i <= 60; i = i + 6) {
        this.loanDurationOptions.push({
          label: i + ' ' + result,
          value: { period: i },
        });
      }
    });
  }

  private setLoanPurposes() {
    this.enumService
      .getCategoryOptions('loanPurpose')
      .subscribe((loanPurposesEnums: TypeEnum[]) => {
        this.loanPurposes = [{ label: 'בחירת מטרה', value: null }];
        this.loanPurposes.push(
          ...this.helperService.fromTypeEnumToPSelect(loanPurposesEnums)
        );
      });
  }
}
