import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ProLenderDashboardResolver } from '../lender/components/pro-lender-dashboard/pro-lender-dashboard-resolver';
import { AccountDetailsComponent } from './components/account-details/account-details.component';
import { PasswordUpdateComponent } from './components/password-update/password-update.component';
import { AccountDetailsResolver } from './services/account-details.resolver';
import { AccountDetailsLayoutComponent } from './components/account-details-layout/account-details-layout.component';
import { CanDeactivateGuard } from './services/can-deactivate-guard.service';

const routes: Routes = [
  {
    path: '',
    component: AccountDetailsLayoutComponent,
    children: [
      {
        path: '',
        redirectTo: 'details',
        pathMatch: 'full',
      },
      {
        path: 'details',
        component: AccountDetailsComponent,
        resolve: {
          lender: ProLenderDashboardResolver,
          accountDetails: AccountDetailsResolver,
        },
        data: {
          isActive: true,
          pageTitle: 'account',
        },
        canDeactivate: [CanDeactivateGuard],
      },
      {
        path: 'update-password',
        component: PasswordUpdateComponent,
        data: {
          isActive: true,
          pageTitle: 'password-update',
        },
        canDeactivate: [CanDeactivateGuard],
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: [CanDeactivateGuard],
})
export class AccountDetailsRoutingModule {}
