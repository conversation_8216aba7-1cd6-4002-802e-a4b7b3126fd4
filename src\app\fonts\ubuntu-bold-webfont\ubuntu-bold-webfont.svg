<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="ubuntubold" horiz-adv-x="1163" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="491" />
<glyph unicode="&#xfb01;" horiz-adv-x="1449" d="M143 0v1135q0 209 118 331.5t349 122.5q86 0 142.5 -14t95.5 -31l-53 -239q-33 14 -73 20t-81 6q-55 0 -93 -15.5t-59.5 -42t-30.5 -63t-9 -80.5v-53h376v-254h-376v-823h-306zM971 1389q0 84 54 132t128 48t128 -48t54 -132t-54 -132.5t-128 -48.5t-128 48.5t-54 132.5z M1001 0v1077h306v-1077h-306z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1507" d="M143 0v1135q0 209 118 331.5t349 122.5q86 0 142.5 -14t95.5 -31l-53 -239q-33 14 -73 20t-81 6q-55 0 -93 -15.5t-59.5 -42t-30.5 -63t-9 -80.5v-53h376v-254h-376v-823h-306zM1004 354v1186l305 49v-1173q0 -41 6 -74t23.5 -55.5t50 -36.5t88.5 -19l-43 -251 q-133 2 -216 28.5t-131.5 74.5t-65.5 116.5t-17 154.5z" />
<glyph unicode="&#xfb03;" horiz-adv-x="2308" d="M143 0v1135q0 209 118 331.5t349 122.5q86 0 158 -17.5t111 -33.5l-60 -244q-41 18 -89 27.5t-89 9.5q-55 0 -93 -15.5t-59.5 -42t-30.5 -63t-9 -80.5v-53h376v-254h-376v-823h-306zM1001 0v1135q0 209 118 331.5t349 122.5q86 0 142.5 -14t95.5 -31l-53 -239 q-33 14 -73 20t-81 6q-55 0 -93 -15.5t-59.5 -42t-30.5 -63t-9 -80.5v-53h376v-254h-376v-823h-306zM1829 1389q0 84 54 132t128 48t128 -48t54 -132t-54 -132.5t-128 -48.5t-128 48.5t-54 132.5zM1860 0v1077h305v-1077h-305z" />
<glyph unicode="&#xfb04;" horiz-adv-x="2365" d="M143 0v1135q0 209 118 331.5t349 122.5q86 0 158 -17.5t111 -33.5l-60 -244q-41 18 -89 27.5t-89 9.5q-55 0 -93 -15.5t-59.5 -42t-30.5 -63t-9 -80.5v-53h376v-254h-376v-823h-306zM1001 0v1135q0 209 118 331.5t349 122.5q86 0 142.5 -14t95.5 -31l-53 -239 q-33 14 -73 20t-81 6q-55 0 -93 -15.5t-59.5 -42t-30.5 -63t-9 -80.5v-53h376v-254h-376v-823h-306zM1862 354v1186l305 49v-1173q0 -41 6 -74t23.5 -55.5t50 -36.5t88.5 -19l-43 -251q-133 2 -216 28.5t-131.5 74.5t-65.5 116.5t-17 154.5z" />
<glyph unicode="&#xd;" horiz-adv-x="491" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode=" "  horiz-adv-x="491" />
<glyph unicode="&#x09;" horiz-adv-x="491" />
<glyph unicode="&#xa0;" horiz-adv-x="491" />
<glyph unicode="!" horiz-adv-x="585" d="M102 162q0 90 58.5 139t132.5 49t132 -49t58 -139t-58 -139.5t-132 -49.5t-132.5 49.5t-58.5 139.5zM131 1053v366h326v-366q0 -166 -12.5 -309.5t-32.5 -290.5h-234q-20 147 -33.5 290.5t-13.5 309.5z" />
<glyph unicode="&#x22;" horiz-adv-x="952" d="M102 1407v149h301v-151q0 -98 -15 -215t-34 -236h-202q-23 119 -36.5 236t-13.5 217zM548 1407v149h301v-151q0 -98 -15 -215t-34 -236h-202q-23 119 -36.5 236t-13.5 217z" />
<glyph unicode="#" horiz-adv-x="1431" d="M113 348v236h192l49 252h-241v235h286l68 348h266l-67 -348h237l68 348h266l-68 -348h150v-235h-195l-49 -252h244v-236h-289l-65 -348h-267l66 348h-238l-65 -348h-266l65 348h-147zM571 584h238l49 252h-237z" />
<glyph unicode="$" d="M72 123l88 235q72 -35 171 -63.5t210 -28.5q125 0 171 34t46 95q0 39 -18.5 66.5t-53.5 50.5t-88 44.5t-123 45.5q-68 25 -132 54.5t-116.5 72.5t-84 105.5t-31.5 150.5q0 68 19.5 130.5t60 113.5t106 87t156.5 52v221h258v-213q106 -10 183 -30.5t122 -40.5l-64 -246 q-68 29 -156.5 49t-187.5 20q-98 0 -141 -32.5t-43 -85.5q0 -33 13.5 -56.5t42 -42t70.5 -36t101 -37.5q90 -33 167 -72t133.5 -89t88 -117.5t31.5 -158.5q0 -61 -18.5 -121.5t-61.5 -111.5t-111.5 -88t-168.5 -53v-250h-258v243q-150 12 -243 46t-138 57z" />
<glyph unicode="%" horiz-adv-x="1880" d="M72 1055q0 193 98 295t264 102t264.5 -102t98.5 -295t-98.5 -295.5t-264.5 -102.5t-264 102.5t-98 295.5zM303 1055q0 -104 36 -152.5t95 -48.5q57 0 94 48.5t37 152.5t-36.5 151t-94.5 47q-59 0 -95 -47t-36 -151zM395 0l801 1419h285l-801 -1419h-285zM1083 365 q0 193 98.5 295t264.5 102t264 -102t98 -295t-98 -295.5t-264 -102.5t-264.5 102.5t-98.5 295.5zM1315 365q0 -104 35.5 -152.5t95.5 -48.5q57 0 94 48.5t37 152.5t-37 151t-94 47q-59 0 -95 -47t-36 -151z" />
<glyph unicode="&#x26;" horiz-adv-x="1443" d="M72 377q0 78 22.5 142.5t60.5 116.5t88 92t105 71q-49 59 -88 127.5t-39 150.5q0 180 108.5 277.5t299.5 97.5q96 0 167.5 -25.5t120 -69.5t73 -102.5t24.5 -126.5q0 -125 -71 -222t-179 -163l229 -229q18 41 36 103.5t22 109.5l254 -33q-4 -39 -14.5 -85t-26 -93 t-35 -94t-41.5 -86q66 -74 128 -161t107 -175h-313q-16 27 -45 66.5t-57 68.5q-82 -66 -190.5 -109.5t-244.5 -43.5q-143 0 -239 35.5t-154.5 92t-83 127t-24.5 140.5zM356 408q0 -31 12.5 -64t40 -60.5t70.5 -45t105 -17.5q86 0 150.5 22.5t107.5 59.5l-328 330 q-29 -14 -56.5 -34.5t-50 -48.5t-37 -62.5t-14.5 -79.5zM483 1092q0 -53 31 -103.5t80 -95.5q66 37 113 88t47 133q0 47 -31 84t-102 37q-66 0 -102 -40t-36 -103z" />
<glyph unicode="'" horiz-adv-x="505" d="M102 1407v149h301v-151q0 -98 -15 -215t-34 -236h-202q-23 119 -36.5 236t-13.5 217z" />
<glyph unicode="(" horiz-adv-x="729" d="M143 612q0 285 94.5 537t268.5 455l213 -138q-123 -174 -199 -393t-76 -461t76 -461t199 -393l-213 -137q-174 203 -268.5 455t-94.5 536z" />
<glyph unicode=")" horiz-adv-x="729" d="M10 -242q123 174 199 393t76 461t-76 461t-199 393l213 138q174 -203 268.5 -455t94.5 -537t-94.5 -536.5t-268.5 -454.5z" />
<glyph unicode="*" horiz-adv-x="1028" d="M82 1008l82 254l49 -17q25 -8 57.5 -25.5t64.5 -39t61.5 -43t52.5 -39.5q-10 27 -21.5 62.5t-22 72.5t-17.5 72.5t-7 62.5v51h266v-51q0 -27 -7 -62.5t-17.5 -72.5t-21.5 -72.5t-21 -62.5q20 18 51 39.5t62.5 43t64.5 39t57 25.5l49 17l82 -254l-49 -17q-27 -8 -61.5 -12 t-73.5 -5t-76 -1h-65q25 -16 54.5 -38t59 -45.5t56 -48t43.5 -46.5l32 -43l-217 -154l-31 41q-16 20 -31.5 53t-29.5 69t-24.5 70.5t-18.5 63.5q-8 -29 -18.5 -63.5t-24.5 -70.5t-29.5 -69t-31.5 -53l-31 -41l-217 154l33 43q16 23 42.5 47t56.5 47.5t59.5 45t54.5 38.5h-66 t-76 1t-73.5 5t-61.5 12z" />
<glyph unicode="+" d="M100 483v256h342v379h279v-379h342v-256h-342v-379h-279v379h-342z" />
<glyph unicode="," horiz-adv-x="503" d="M35 -274q47 135 65.5 253.5t18.5 196.5q0 12 -1 31.5t-2 40t-2 39t-1 24.5h319q4 -49 5 -69.5t1 -34.5q0 -61 -11 -130t-31.5 -138.5t-51.5 -139t-70 -133.5z" />
<glyph unicode="-" horiz-adv-x="696" d="M51 473v277h594v-277h-594z" />
<glyph unicode="." horiz-adv-x="503" d="M61 162q0 92 58.5 140t132.5 48t132 -48t58 -140t-58 -140.5t-132 -48.5t-132.5 48.5t-58.5 140.5z" />
<glyph unicode="/" horiz-adv-x="894" d="M-51 -379l688 1983h309l-684 -1983h-313z" />
<glyph unicode="0" d="M70 713q0 178 35.5 315t103 231.5t162 143.5t211.5 49q240 0 376 -191.5t136 -547.5q0 -358 -136.5 -550t-375.5 -192q-240 0 -376 191.5t-136 550.5zM381 713q0 -106 10 -194.5t34 -152t61.5 -98.5t95.5 -35q57 0 96 35t61.5 98.5t32.5 152t10 194.5t-10 193 t-32.5 150.5t-61.5 98.5t-96 35t-95 -35t-62 -98.5t-34 -150.5t-10 -193z" />
<glyph unicode="1" d="M143 1124q59 25 124 56.5t126.5 69.5t117.5 80t101 89h213v-1419h-305v1022q-61 -41 -137 -75t-147 -58z" />
<glyph unicode="2" d="M72 1260q90 84 209.5 138t257.5 54q125 0 215 -28.5t148.5 -81t86 -127t27.5 -168.5q0 -78 -31 -150t-80 -138.5t-110.5 -128t-120.5 -116.5q-31 -29 -67 -64.5t-68.5 -72.5t-58 -69t-31.5 -52h610v-256h-946q-4 23 -4 57.5v48.5q0 98 31.5 180.5t82.5 153t115.5 132 t128.5 122.5q49 47 92 89t76 82t51.5 80t18.5 81q0 90 -51.5 127t-127.5 37q-55 0 -103 -17.5t-88 -41t-68.5 -47t-43.5 -37.5z" />
<glyph unicode="3" d="M53 49l60 254q53 -23 136 -48.5t206 -25.5q141 0 206.5 53.5t65.5 143.5q0 55 -23.5 93t-64.5 61.5t-97.5 33t-119.5 9.5h-119v245h135q45 0 87 8.5t75 28t52.5 53t19.5 84.5q0 39 -16.5 68t-42 47.5t-59.5 27.5t-69 9q-88 0 -162.5 -26.5t-136.5 -65.5l-108 223 q33 20 77 43t97 41t113.5 30.5t128.5 12.5q125 0 216 -29.5t150.5 -84t88 -127t28.5 -158.5q0 -84 -47 -163t-127 -120q111 -45 171 -134t60 -214q0 -98 -32.5 -181t-102.5 -143.5t-177.5 -94.5t-254.5 -34q-55 0 -116.5 7.5t-119 19.5t-104.5 26.5t-74 26.5z" />
<glyph unicode="4" d="M41 317v224q45 90 112.5 203.5t150.5 233.5t175 234.5t185 206.5h288v-852h152v-250h-152v-317h-299v317h-612zM332 567h321v480q-80 -104 -170 -227.5t-151 -252.5z" />
<glyph unicode="5" d="M78 43l55 252q51 -23 135 -44.5t201 -21.5q141 0 204.5 56.5t63.5 132.5q0 59 -26.5 105t-92 77t-176 47.5t-278.5 16.5q23 190 37 386.5t22 368.5h768v-256h-512q-4 -74 -9 -142.5t-11 -119.5q299 -20 441 -140t142 -333q0 -98 -34.5 -182t-104 -145.5t-175 -96.5 t-247.5 -35q-55 0 -115.5 7.5t-116.5 18.5t-101.5 23.5t-69.5 24.5z" />
<glyph unicode="6" d="M76 580q0 205 63.5 362.5t181 265t284.5 163.5t376 59q4 -63 8 -124t8 -126q-104 -2 -195 -19.5t-166 -54.5t-130 -95.5t-86 -142.5q45 20 92 28.5t88 8.5q133 0 226.5 -35.5t152.5 -97t86 -143.5t27 -174q0 -82 -30 -168t-90.5 -156t-153.5 -115t-220 -45 q-256 0 -389 162t-133 447zM383 584q0 -74 10 -139.5t35 -114.5t66 -77t102 -28q51 0 87 21.5t59.5 54.5t34.5 73t11 77q0 104 -52 160.5t-175 56.5q-45 0 -96 -8.5t-80 -22.5q0 -8 -1 -24.5t-1 -28.5z" />
<glyph unicode="7" d="M88 1161v260h993v-221q-66 -72 -144.5 -199t-149 -287.5t-123 -345t-64.5 -368.5h-309q12 150 51 311.5t95.5 317t126 294t141.5 238.5h-617z" />
<glyph unicode="8" d="M80 371q0 61 16.5 112.5t45 93t64.5 76.5t77 66q-88 72 -130 141.5t-42 177.5q0 82 33.5 158t95 132t148.5 90t194 34q125 0 214 -36t146 -91t83 -123.5t26 -136.5q0 -98 -52.5 -183t-130.5 -134q117 -76 167 -159t50 -194q0 -92 -31.5 -168.5t-95 -134t-158.5 -89.5 t-220 -32q-143 0 -238.5 40t-154 99.5t-83 130t-24.5 130.5zM377 383q0 -25 11 -54.5t36 -54t63.5 -41t94.5 -16.5q106 0 155 51t49 115q0 47 -21.5 84t-58 64.5t-87 49t-107.5 40.5q-57 -43 -96 -100.5t-39 -137.5zM408 1053q0 -66 48 -123.5t165 -98.5q66 41 100.5 94.5 t34.5 131.5q0 23 -9.5 48t-31 48t-54 38t-79.5 15q-45 0 -78 -14t-54.5 -38t-31.5 -50.5t-10 -50.5z" />
<glyph unicode="9" d="M57 967q0 82 28 168t87 156.5t150.5 116.5t220.5 46q264 0 398 -163t134 -468q0 -201 -52 -354.5t-161.5 -258t-279.5 -158.5t-404 -56q-2 66 -6 127t-8 127q117 2 211 17.5t165.5 51t121.5 95t79 149.5q-43 -20 -95 -29.5t-89 -9.5q-133 0 -227 32t-154.5 90.5 t-89.5 140t-29 180.5zM360 979q0 -111 54.5 -164t177.5 -53q47 0 98 9t76 26v18q0 8 1 16.5t1 14.5q-2 74 -12.5 138.5t-35 112.5t-66.5 75.5t-103 27.5q-49 0 -86 -20.5t-59.5 -53t-34 -71.5t-11.5 -76z" />
<glyph unicode=":" horiz-adv-x="503" d="M61 162q0 92 58.5 140t132.5 48t132 -48t58 -140t-58 -140.5t-132 -48.5t-132.5 48.5t-58.5 140.5zM61 889q0 92 58.5 140t132.5 48t132 -48t58 -140t-58 -140.5t-132 -48.5t-132.5 48.5t-58.5 140.5z" />
<glyph unicode=";" horiz-adv-x="503" d="M35 -274q47 135 65.5 253.5t18.5 196.5q0 12 -1 31.5t-2 40t-2 39t-1 24.5h319q4 -49 5 -69.5t1 -34.5q0 -61 -11 -130t-31.5 -138.5t-51.5 -139t-70 -133.5zM61 889q0 92 58.5 140t132.5 48t132 -48t58 -140t-58 -140.5t-132 -48.5t-132.5 48.5t-58.5 140.5z" />
<glyph unicode="&#x3c;" d="M102 485v254l881 371l80 -248l-643 -250l643 -250l-80 -247z" />
<glyph unicode="=" d="M100 258v256h963v-256h-963zM100 707v256h963v-256h-963z" />
<glyph unicode="&#x3e;" d="M100 362l643 250l-643 250l80 248l881 -371v-254l-881 -370z" />
<glyph unicode="?" horiz-adv-x="931" d="M41 1346q80 51 188.5 78.5t212.5 27.5q131 0 216 -35t134.5 -88t69 -116.5t19.5 -120.5t-17.5 -105.5t-44 -89.5t-61.5 -78t-68 -69q-25 -25 -51.5 -52.5t-48 -58.5t-35.5 -64.5t-14 -68.5v-24.5t2 -28.5h-271q-4 20 -5 45.5t-1 44.5q0 57 14.5 103t38 86t54 75t63.5 67 q47 47 86 93.5t39 105.5q0 49 -37.5 84t-111.5 35t-139.5 -18.5t-145.5 -59.5zM221 162q0 90 58.5 139t132.5 49t132 -49t58 -139t-58.5 -139.5t-131.5 -49.5q-74 0 -132.5 49.5t-58.5 139.5z" />
<glyph unicode="@" horiz-adv-x="1994" d="M102 557q0 227 78 398t205 286t290 172t335 57q188 0 350 -52t279.5 -152.5t185 -249t67.5 -340.5q0 -150 -31.5 -265.5t-92 -193.5t-144.5 -119t-188 -41q-59 0 -119 14.5t-98 51.5q-90 -63 -228 -64q-102 0 -187 32t-145.5 95.5t-94.5 156.5t-34 216q0 104 34 194.5 t99.5 157t161 105.5t215.5 39q121 0 214.5 -21.5t154.5 -44.5v-643q0 -47 19.5 -63.5t56.5 -16.5q33 0 64.5 18.5t54 66.5t37 131t14.5 210t-44 232.5t-127 180.5t-203 117t-271 42q-145 0 -267 -49.5t-211.5 -140.5t-139.5 -222t-50 -295q0 -170 49 -298t140.5 -214 t221.5 -130t294 -44q102 0 193 13.5t153 25.5l30 -193q-51 -16 -155.5 -34.5t-220.5 -18.5q-197 0 -369 51.5t-300 160t-202 277.5t-74 404zM807 559q0 -59 10 -109.5t36 -87t69 -58t106 -21.5q37 0 70 5t49 13q-4 16 -6 48t-2 61v417q-41 8 -86 9q-68 0 -115 -21.5 t-75.5 -58.5t-42 -87.5t-13.5 -109.5z" />
<glyph unicode="A" horiz-adv-x="1476" d="M20 0q80 229 152 423.5t140.5 367t135 327t138.5 301.5h305q70 -147 137.5 -301.5t136 -327t140 -367t151.5 -423.5h-342q-23 74 -50.5 151.5t-53.5 155.5h-553q-27 -78 -54.5 -155.5t-50.5 -151.5h-332zM530 561h404q-33 92 -63.5 176t-58.5 154t-48.5 123t-32.5 84 q-10 -31 -30.5 -84t-47 -123t-58.5 -154t-65 -176z" />
<glyph unicode="B" horiz-adv-x="1376" d="M164 23v1376q98 18 215 27.5t217 9.5q135 0 248.5 -19.5t195.5 -66.5t128 -124t46 -190q0 -170 -163 -268q135 -51 184 -139t49 -199q0 -223 -162.5 -335.5t-484.5 -112.5q-117 0 -232.5 7t-240.5 34zM475 252q35 -4 76 -6t90 -2q143 0 231.5 41t88.5 151q0 98 -74 140 t-211 42h-201v-366zM475 862h156q147 0 210.5 38t63.5 122q0 86 -65.5 121t-192.5 35q-41 0 -88 -1t-84 -6v-309z" />
<glyph unicode="C" horiz-adv-x="1327" d="M102 711q0 176 55.5 314t152 233.5t229.5 144.5t288 49q90 0 164 -13.5t129.5 -30.5t92 -35.5t53.5 -29.5l-92 -258q-66 35 -153 59.5t-198 24.5q-74 0 -144.5 -24.5t-124.5 -78.5t-87 -140t-33 -209q0 -98 21.5 -183t69.5 -146.5t126 -97.5t189 -36q70 0 125 8t98 19.5 t75.5 26t59.5 26.5l88 -256q-68 -41 -190.5 -74t-284.5 -33q-346 0 -527.5 192.5t-181.5 547.5z" />
<glyph unicode="D" horiz-adv-x="1509" d="M164 18v1383q111 20 230.5 27.5t205.5 7.5q182 0 330.5 -41t255 -129t164 -225t57.5 -330q0 -184 -57.5 -321.5t-163 -227.5t-257 -135t-339.5 -45q-86 0 -201 7t-225 29zM483 260q23 -2 52.5 -3t70.5 -1q240 0 355.5 121t115.5 334q0 223 -110.5 337.5t-350.5 114.5 q-33 0 -67.5 -1t-65.5 -5v-897z" />
<glyph unicode="E" horiz-adv-x="1241" d="M164 0v1419h958v-268h-639v-279h568v-262h-568v-342h686v-268h-1005z" />
<glyph unicode="F" horiz-adv-x="1175" d="M164 0v1419h950v-268h-631v-297h561v-268h-561v-586h-319z" />
<glyph unicode="G" horiz-adv-x="1437" d="M102 711q0 178 55.5 315t152 232.5t226.5 144.5t277 49q100 0 181 -13.5t139.5 -30.5t96.5 -35.5t54 -29.5l-92 -256q-66 35 -151 61t-185 26q-223 0 -322.5 -124t-99.5 -339q0 -104 24.5 -189.5t74 -147t123 -95t171.5 -33.5q53 0 91 2t67 8v494h320v-703 q-57 -23 -184.5 -48.5t-313.5 -25.5q-160 0 -290 49.5t-222 143.5t-142.5 231.5t-50.5 313.5z" />
<glyph unicode="H" horiz-adv-x="1503" d="M164 0v1419h319v-542h537v542h319v-1419h-319v602h-537v-602h-319z" />
<glyph unicode="I" horiz-adv-x="647" d="M164 0v1419h319v-1419h-319z" />
<glyph unicode="J" horiz-adv-x="1083" d="M20 84l107 248q59 -35 127 -61.5t147 -26.5q117 0 168.5 57t51.5 193v925h319v-942q0 -104 -23.5 -196.5t-82 -161t-157.5 -108.5t-251 -40q-139 0 -239.5 34t-166.5 79z" />
<glyph unicode="K" horiz-adv-x="1400" d="M164 0v1419h319v-536q125 131 251 273t235 263h379q-145 -172 -292 -331.5t-308 -321.5q170 -141 328.5 -335.5t303.5 -430.5h-379q-43 70 -101 150.5t-126.5 161.5t-142.5 156t-148 132v-600h-319z" />
<glyph unicode="L" horiz-adv-x="1153" d="M164 0v1419h319v-1145h629v-274h-948z" />
<glyph unicode="M" horiz-adv-x="1837" d="M123 0q8 168 19.5 358.5t24.5 380t30.5 364.5t38.5 316h305q37 -68 85 -167t100 -214.5t103.5 -236.5t96.5 -228q45 106 96 227.5t103.5 237t100.5 215t85 166.5h291q20 -141 37.5 -316t30.5 -364.5t24.5 -380t19.5 -358.5h-311q-6 207 -16.5 450.5t-30.5 491.5 q-37 -86 -82 -190.5t-89 -209t-85 -199.5t-70 -163h-223q-29 68 -70 163t-85 199.5t-89 209t-82 190.5q-20 -248 -30.5 -491.5t-16.5 -450.5h-311z" />
<glyph unicode="N" horiz-adv-x="1548" d="M164 0v1419h260q68 -68 149.5 -166t166.5 -209.5t169 -231.5t158 -230v837h317v-1419h-268q-137 244 -297 481.5t-340 448.5v-930h-315z" />
<glyph unicode="O" horiz-adv-x="1617" d="M102 711q0 182 57.5 320t155 232.5t225.5 141.5t269 47q145 0 274 -47t225.5 -141.5t152 -232.5t55.5 -320t-54.5 -320.5t-148.5 -231.5t-224.5 -140.5t-279.5 -47.5q-145 0 -274 47.5t-225.5 140.5t-152 231t-55.5 321zM432 711q0 -104 25.5 -188.5t74 -145t118 -93 t159.5 -32.5q88 0 158.5 32.5t119 93t74 144.5t25.5 189q0 104 -25.5 189t-74 145.5t-119 93t-158.5 32.5q-90 0 -159.5 -33.5t-118 -94t-74 -145.5t-25.5 -187z" />
<glyph unicode="P" horiz-adv-x="1318" d="M164 0v1399q104 20 221 28.5t205 8.5q317 0 487 -112t170 -366q0 -256 -172 -369.5t-491 -113.5h-101v-475h-319zM483 748h101q166 0 250 45t84 168q0 59 -22 98t-61.5 62.5t-97 32.5t-127.5 9q-35 0 -68.5 -2t-58.5 -4v-409z" />
<glyph unicode="Q" horiz-adv-x="1617" d="M102 711q0 182 57.5 320t155 232.5t225.5 141.5t269 47q145 0 274 -47t225.5 -141.5t152 -232.5t55.5 -320q0 -150 -37 -269.5t-103.5 -210t-159 -149.5t-202.5 -86q10 -41 45 -66.5t88 -42t123.5 -24.5t152.5 -15l-65 -233q-160 6 -273.5 31.5t-192.5 70.5t-125 111 t-71 152q-127 16 -235.5 70t-188 146.5t-125 221.5t-45.5 293zM432 711q0 -104 25.5 -188.5t74 -145t118 -93t159.5 -32.5q88 0 158.5 32.5t119 93t74 144.5t25.5 189q0 104 -25.5 189t-74 145.5t-119 93t-158.5 32.5q-90 0 -159.5 -33.5t-118 -94t-74 -145.5t-25.5 -187z " />
<glyph unicode="R" horiz-adv-x="1366" d="M164 0v1399q104 20 216 28.5t200 8.5q319 0 489 -114t170 -353q0 -150 -68.5 -243t-197.5 -146q43 -53 90 -122t93 -143.5t89 -155.5t80 -159h-358q-39 70 -79 141.5t-82 139t-83 128t-82 109.5h-158v-518h-319zM483 772h90q180 0 258 45t78 154q0 104 -78.5 148 t-232.5 44q-35 0 -62.5 -2t-52.5 -4v-385z" />
<glyph unicode="S" horiz-adv-x="1191" d="M76 80l90 254q63 -35 156.5 -62.5t228.5 -27.5q68 0 112 11t70.5 30.5t36.5 46t10 59.5q0 70 -65.5 116t-225.5 99q-70 25 -139 56.5t-124.5 80t-90.5 117t-35 166.5t37 177t104.5 134.5t164 85t217.5 29.5q143 0 247.5 -30.5t171.5 -67.5l-92 -252q-59 31 -132 54.5 t-175 23.5q-115 0 -165 -32t-50 -97q0 -39 18.5 -66t52 -48.5t78 -38.5t97.5 -36q111 -41 192.5 -81t136 -93t81 -124.5t26.5 -174.5q0 -199 -139.5 -308.5t-419.5 -109.5q-94 0 -170 11.5t-134.5 28t-100.5 34.5t-70 35z" />
<glyph unicode="T" horiz-adv-x="1257" d="M41 1147v272h1176v-272h-429v-1147h-319v1147h-428z" />
<glyph unicode="U" horiz-adv-x="1447" d="M143 537v882h320v-856q0 -86 19.5 -146.5t53 -98t82 -54t105.5 -16.5q117 0 189.5 71.5t72.5 243.5v856h320v-882q0 -125 -35 -229.5t-106.5 -179.5t-182 -116t-262.5 -41q-150 0 -258.5 42t-179 117t-104.5 178.5t-34 228.5z" />
<glyph unicode="V" horiz-adv-x="1478" d="M20 1419h353q41 -123 89 -265t98 -283.5t97.5 -270.5t87.5 -223q39 94 87.5 223t98.5 270.5t98 283.5t89 265h340q-47 -135 -108.5 -307t-135 -360.5t-155.5 -381t-166 -370.5h-307q-84 178 -166 370.5t-156 381t-136 360.5t-108 307z" />
<glyph unicode="W" horiz-adv-x="1941" d="M41 1419h346q20 -121 45 -251t51.5 -261t54 -254t56.5 -227q37 100 71.5 213t67.5 225.5t60.5 217t50.5 186.5h272q25 -84 54.5 -189.5t63.5 -218t68.5 -225.5t67.5 -209q27 104 54.5 227t52 254t48 261t44.5 251h332q-72 -356 -165.5 -722.5t-201.5 -696.5h-293 q-72 197 -139.5 413t-132.5 433q-66 -217 -131.5 -433t-137.5 -413h-292q-109 330 -202 696.5t-165 722.5z" />
<glyph unicode="X" horiz-adv-x="1382" d="M20 0q90 166 210 348t268 387l-457 684h387l277 -442l270 442h364l-450 -688q172 -223 288.5 -412.5t184.5 -318.5h-383q-59 119 -130 240.5t-163 248.5q-29 -37 -73 -102t-88 -139t-83 -141.5t-57 -106.5h-365z" />
<glyph unicode="Y" horiz-adv-x="1353" d="M10 1419h371q66 -147 141.5 -292.5t161.5 -290.5q86 145 164 290.5t143 292.5h352q-117 -229 -241.5 -444t-263.5 -432v-543h-320v539q-139 217 -265 434t-243 446z" />
<glyph unicode="Z" horiz-adv-x="1249" d="M72 0v190q57 104 140 232.5t173 258.5t183.5 252t172.5 218h-637v268h1061v-221q-66 -68 -155.5 -175.5t-186 -234t-190.5 -262t-168 -258.5h723v-268h-1116z" />
<glyph unicode="[" horiz-adv-x="759" d="M184 -379v1983h555v-244h-264v-1495h264v-244h-555z" />
<glyph unicode="\" horiz-adv-x="894" d="M-51 1604h309l688 -1983h-313z" />
<glyph unicode="]" horiz-adv-x="759" d="M20 -135h265v1495h-265v244h555v-1983h-555v244z" />
<glyph unicode="^" d="M37 717l424 710h241l424 -710l-256 -125l-288 502l-289 -502z" />
<glyph unicode="_" horiz-adv-x="1024" d="M0 -125h1024v-254h-1024v254z" />
<glyph unicode="`" horiz-adv-x="585" d="M41 1464l186 172l291 -331l-139 -125z" />
<glyph unicode="a" horiz-adv-x="1132" d="M72 332q0 94 37.5 159.5t101 104.5t145.5 56.5t170 17.5q59 0 105.5 -5.5t75.5 -13.5v29q0 78 -47.5 125t-163.5 47q-78 0 -154 -11.5t-131 -31.5l-39 246q27 8 67 17t87 16.5t99 12.5t105 5q135 0 225.5 -31t144.5 -88t76.5 -139t22.5 -182v-635q-66 -14 -182.5 -34 t-281.5 -20q-104 0 -189.5 18.5t-147 60.5t-94 109.5t-32.5 166.5zM375 340q0 -72 48 -99.5t132 -27.5q45 0 86 2t66 6v232q-18 4 -55.5 8t-67.5 4q-43 0 -81 -5t-66.5 -19.5t-45 -39t-16.5 -61.5z" />
<glyph unicode="b" horiz-adv-x="1236" d="M143 35v1505l306 49v-536q51 23 106 35t119 12q115 0 203 -40t147 -114t90 -177t31 -230q0 -129 -38 -233.5t-109.5 -178.5t-176 -114t-237.5 -40q-55 0 -115.5 5.5t-119 13.5t-112 19.5t-94.5 23.5zM449 244q23 -4 57.5 -7.5t75.5 -3.5q125 0 188.5 86.5t63.5 227.5 q0 295 -218 295q-47 0 -93 -12.5t-74 -30.5v-555z" />
<glyph unicode="c" horiz-adv-x="1024" d="M92 539q0 117 38 220t109.5 180t174 122t233.5 45q86 0 158 -15.5t139 -43.5l-63 -244q-43 16 -94.5 28.5t-114.5 12.5q-135 0 -202 -84t-67 -221q0 -145 62.5 -225.5t218.5 -80.5q55 0 118.5 10.5t117.5 32.5l43 -249q-53 -23 -133 -39.5t-177 -16.5q-147 0 -253.5 44 t-175 120t-100.5 179.5t-32 224.5z" />
<glyph unicode="d" horiz-adv-x="1236" d="M92 532q0 131 33 236.5t94.5 179.5t150.5 113t203 39q63 0 113.5 -12.5t101.5 -34.5v487l306 49v-1554q-41 -12 -94.5 -23.5t-112 -19.5t-118.5 -13.5t-116 -5.5q-133 0 -237.5 39t-176 112t-109.5 176t-38 232zM403 547q0 -141 63.5 -227.5t188.5 -86.5q41 0 76 3.5 t57 7.5v555q-29 18 -74.5 30.5t-92.5 12.5q-218 0 -218 -295z" />
<glyph unicode="e" horiz-adv-x="1196" d="M92 530q0 143 44 251t116 179.5t165 108.5t191 37q229 0 362.5 -140.5t133.5 -412.5q0 -27 -2 -58.5t-4 -56.5h-692q10 -94 87.5 -149.5t208.5 -55.5q84 0 165 15.5t132 38.5l41 -248q-25 -12 -65.5 -24.5t-90.5 -21.5t-107.5 -15.5t-114.5 -6.5q-145 0 -253 43 t-178.5 118t-104.5 177t-34 221zM406 647h403q-2 39 -13.5 76t-35 65.5t-59 47t-89.5 18.5q-51 0 -88 -17.5t-61.5 -46t-37.5 -66.5t-19 -77z" />
<glyph unicode="f" horiz-adv-x="864" d="M143 0v1135q0 209 118 331.5t349 122.5q86 0 158 -17.5t111 -33.5l-60 -244q-41 18 -89 27.5t-89 9.5q-55 0 -93 -15.5t-59.5 -42t-30.5 -63t-9 -80.5v-53h376v-254h-376v-823h-306z" />
<glyph unicode="g" horiz-adv-x="1216" d="M92 573q0 119 37 216.5t107.5 167t173 107.5t231.5 38q55 0 113.5 -5t116 -13.5t109.5 -19.5t93 -24v-905q0 -264 -134 -392t-415 -128q-98 0 -196.5 17.5t-182.5 45.5l54 256q72 -29 150.5 -45t178.5 -16q131 0 185.5 57.5t54.5 147.5v39q-49 -23 -101.5 -34t-113.5 -11 q-223 0 -342 132t-119 369zM403 573q0 -256 207 -256q47 0 88 12.5t70 28.5v488q-23 4 -53.5 7t-71.5 3q-121 0 -180.5 -80t-59.5 -203z" />
<glyph unicode="h" horiz-adv-x="1206" d="M143 0v1540l306 49v-516q31 10 78.5 19.5t93.5 9.5q131 0 218 -36t139 -101.5t73.5 -155.5t21.5 -201v-608h-305v571q0 147 -38 209t-140 62q-41 0 -77 -7.5t-64 -15.5v-819h-306z" />
<glyph unicode="i" horiz-adv-x="591" d="M113 1389q0 84 54 132t128 48t128 -48t54 -132t-54 -132.5t-128 -48.5t-128 48.5t-54 132.5zM143 0v1077h306v-1077h-306z" />
<glyph unicode="j" horiz-adv-x="591" d="M-147 -350l41 248q31 -10 56 -14.5t64 -4.5q72 0 100.5 43t28.5 125v1030h306v-1038q0 -213 -104.5 -315.5t-301.5 -102.5q-29 0 -85 6t-105 23zM113 1389q0 84 54 132t128 48t128 -48t54 -132t-54 -132.5t-128 -48.5t-128 48.5t-54 132.5z" />
<glyph unicode="k" horiz-adv-x="1185" d="M143 0v1540l306 49v-901q45 49 93 102.5t93 105.5t85 99t68 82h363q-109 -125 -212 -238.5t-226 -234.5q61 -55 126.5 -132t127 -159t112.5 -163.5t86 -149.5h-350q-33 53 -75 117.5t-89 129t-99 124t-103 100.5v-471h-306z" />
<glyph unicode="l" horiz-adv-x="647" d="M143 354v1186l306 49v-1173q0 -41 6 -74t23.5 -55.5t50 -36.5t87.5 -19l-43 -251q-133 2 -216 28.5t-131 74.5t-65.5 116.5t-17.5 154.5z" />
<glyph unicode="m" horiz-adv-x="1765" d="M143 0v1040q39 10 91.5 21.5t110.5 21t119.5 14.5t121.5 5q117 0 189.5 -30t119.5 -71q66 47 150.5 74t156.5 27q129 0 212 -36t132 -101.5t67.5 -155.5t18.5 -201v-608h-305v571q0 147 -38 209t-130 62q-25 0 -68.5 -12.5t-72.5 -30.5q14 -47 18 -99.5t4 -111.5v-588 h-305v571q0 147 -38 209t-130 62q-29 0 -59.5 -3t-58.5 -8v-831h-306z" />
<glyph unicode="n" horiz-adv-x="1206" d="M143 0v1040q78 23 201 42.5t258 19.5q137 0 228.5 -36t144.5 -101.5t75.5 -155.5t22.5 -201v-608h-305v571q0 147 -39 209t-145 62q-33 0 -70 -3t-65 -8v-831h-306z" />
<glyph unicode="o" horiz-adv-x="1243" d="M92 541q0 127 40 231.5t110.5 178t168 114.5t212.5 41q117 0 214 -41t166.5 -114.5t108.5 -178t39 -231.5t-37 -232.5t-106.5 -180.5t-167 -116t-217.5 -41q-119 0 -216.5 41t-167 116t-108.5 180.5t-39 232.5zM403 541q0 -141 57.5 -224.5t162.5 -83.5q104 0 160.5 83.5 t56.5 224.5t-56.5 222t-160.5 81t-162 -81t-58 -222z" />
<glyph unicode="p" horiz-adv-x="1236" d="M143 -379v1419q41 12 94.5 23.5t112 20t118.5 13.5t116 5q133 0 237.5 -40t176 -112.5t109.5 -177t38 -233.5q0 -125 -31 -227.5t-90 -176.5t-147.5 -114.5t-202.5 -40.5q-63 0 -118.5 12t-106.5 35v-406h-306zM449 281q29 -18 74.5 -30.5t92.5 -12.5h1q217 0 217 292 q0 141 -63.5 226.5t-188.5 85.5q-41 0 -76 -3t-57 -8v-550z" />
<glyph unicode="q" horiz-adv-x="1236" d="M92 539q0 125 38 228t109.5 178t176 116t237.5 41q55 0 115.5 -5t119 -13.5t112 -19.5t94.5 -24v-1419h-306v406q-51 -23 -106 -35t-119 -12q-229 0 -350 150.5t-121 408.5zM403 530q0 -292 217 -292h1q47 0 93 12t74 31v550q-23 4 -57.5 7.5t-75.5 3.5 q-125 0 -188.5 -85.5t-63.5 -226.5z" />
<glyph unicode="r" horiz-adv-x="864" d="M143 0v1020q82 29 194 54.5t249 25.5q25 0 59.5 -3t69.5 -8.5t69.5 -12.5t59.5 -17l-51 -252q-41 10 -96.5 21.5t-118.5 11.5q-29 0 -69 -5.5t-60 -11.5v-823h-306z" />
<glyph unicode="s" horiz-adv-x="993" d="M80 37l51 248q78 -31 160 -48.5t162 -17.5q84 0 118.5 16.5t34.5 63.5q0 37 -45 64.5t-137 62.5q-72 27 -130 55.5t-99 68.5t-63.5 95t-22.5 133q0 152 112.5 240t308.5 88q98 0 188.5 -17.5t143.5 -37.5l-53 -238q-53 18 -115.5 32.5t-140.5 14.5q-143 0 -143 -80 q0 -18 6 -32.5t24.5 -27.5t50 -28.5t80.5 -34.5q100 -37 166 -72.5t104 -77.5t53 -93.5t15 -118.5q0 -160 -119.5 -242t-338.5 -82q-143 0 -238.5 24.5t-132.5 41.5z" />
<glyph unicode="t" horiz-adv-x="909" d="M133 440v906l305 49v-318h367v-254h-367v-379q0 -96 34 -153.5t136 -57.5q49 0 101.5 9.5t95.5 25.5l43 -237q-55 -23 -123 -39.5t-166 -16.5q-125 0 -207 34t-131 94.5t-68.5 146.5t-19.5 190z" />
<glyph unicode="u" horiz-adv-x="1206" d="M133 477v600h305v-563q0 -147 39 -212.5t146 -65.5q33 0 69.5 3t65.5 7v831h305v-1040q-78 -23 -201 -42.5t-258 -19.5q-137 0 -228 37t-144.5 103.5t-76 159t-22.5 202.5z" />
<glyph unicode="v" horiz-adv-x="1126" d="M20 1077h324q20 -80 48 -173t57.5 -188t59.5 -185.5t58 -163.5q27 74 57.5 164t60.5 185t57.5 188t48.5 173h315q-96 -324 -203.5 -598t-212.5 -479h-254q-104 205 -211.5 479.5t-204.5 597.5z" />
<glyph unicode="w" horiz-adv-x="1605" d="M20 1077h322q16 -74 35.5 -161t41 -179t45 -185t48.5 -179q27 90 52.5 184t49 185.5t45 176.5t37.5 158h222q16 -74 36.5 -158.5t43 -176t47 -185.5t51.5 -184q25 86 49 179t45.5 185t41 179.5t36.5 160.5h317q-45 -160 -93 -316.5t-94 -296.5t-90 -259t-81 -205h-238 q-51 156 -100 309t-90 309q-41 -156 -87 -309t-98 -309h-237q-37 86 -80 205t-89 259t-94 297t-94 316z" />
<glyph unicode="x" horiz-adv-x="1134" d="M20 0q25 51 68 120.5t93 144.5t105.5 149.5t108.5 140.5q-92 129 -184 261t-182 261h329l213 -327l213 327h316l-369 -518q123 -154 223.5 -299t159.5 -260h-328q-16 31 -40.5 77t-55.5 96t-64.5 100.5t-66.5 91.5q-31 -39 -62.5 -86.5t-60 -95.5t-56.5 -95t-48 -88h-312 z" />
<glyph unicode="y" horiz-adv-x="1120" d="M10 -336l53 244q47 -16 84 -22.5t78 -6.5q82 0 126 44t75 122q-104 205 -208.5 460t-197.5 572h324q20 -80 48 -173t58.5 -188t62.5 -185.5t60 -163.5q27 74 55.5 164t56.5 185t53.5 188t45.5 173h316q-92 -311 -192.5 -583.5t-219.5 -528.5q-43 -92 -86 -156.5 t-94 -106.5t-115.5 -61.5t-150.5 -19.5q-72 0 -132.5 13.5t-99.5 29.5z" />
<glyph unicode="z" horiz-adv-x="1024" d="M72 0v180q43 74 103.5 159t127 171t132 167t120.5 146h-459v254h856v-205q-37 -41 -99 -110.5t-134 -153.5t-144.5 -176t-132.5 -178h521v-254h-891z" />
<glyph unicode="{" horiz-adv-x="759" d="M72 492v241q92 0 133 53.5t41 139.5v309q0 88 22.5 156.5t74.5 115.5t139 72t216 25h41v-244h-51q-92 0 -124 -41t-32 -127v-272q0 -106 -28.5 -180t-114.5 -128q86 -53 114.5 -126.5t28.5 -180.5v-272q0 -86 32 -127t124 -41h51v-244h-41q-129 0 -216 24.5t-139 71.5 t-74.5 116t-22.5 157v309q0 86 -41 139.5t-133 53.5z" />
<glyph unicode="|" horiz-adv-x="659" d="M184 -379v1983h291v-1983h-291z" />
<glyph unicode="}" horiz-adv-x="759" d="M20 -135h52q92 0 123.5 41t31.5 127v272q0 106 29 180t115 127q-86 53 -115 127t-29 181v272q0 86 -31.5 127t-123.5 41h-52v244h41q129 0 216.5 -25t139.5 -72t74.5 -115.5t22.5 -156.5v-309q0 -86 41 -139.5t133 -53.5v-241q-92 0 -133 -53.5t-41 -139.5v-309 q0 -88 -22.5 -157t-74.5 -116t-139.5 -71.5t-216.5 -24.5h-41v244z" />
<glyph unicode="~" d="M47 465q10 57 32.5 120.5t60.5 117t95.5 88t137.5 34.5q61 0 115.5 -24.5t104.5 -53t96 -53t91 -24.5q16 0 33.5 4t34 19.5t32 45t27.5 80.5l209 -57q-10 -57 -32.5 -120.5t-61.5 -117t-95 -88.5t-136 -35q-61 0 -115.5 25t-105 53.5t-96.5 53t-91 24.5q-16 0 -33.5 -4 t-34 -19.5t-32 -45t-27.5 -80.5z" />
<glyph unicode="&#xa1;" horiz-adv-x="585" d="M102 889q0 90 58.5 139t132.5 49t132 -49t58 -139t-58 -139.5t-132 -49.5t-132.5 49.5t-58.5 139.5zM131 8q0 166 13.5 309.5t33.5 290.5h234q20 -147 32.5 -290.5t12.5 -309.5v-377h-326v377z" />
<glyph unicode="&#xa2;" d="M92 600q0 90 24.5 173t73 150.5t116 118t153.5 76.5v299h291v-276q53 -8 101 -20.5t95 -33.5l-67 -243q-43 16 -93.5 28.5t-113.5 12.5q-129 0 -199 -77t-70 -208q0 -139 66 -212t215 -73q55 0 118.5 10.5t117.5 32.5l43 -249q-41 -16 -94.5 -29.5t-118.5 -22.5v-280 h-291v299q-188 51 -277.5 192.5t-89.5 331.5z" />
<glyph unicode="&#xa3;" d="M47 559v248h178v108q0 154 37 255.5t101.5 163t154.5 87t197 25.5q82 0 153.5 -16.5t139.5 -45.5l-76 -239q-47 23 -89 33t-102 12q-49 0 -89 -10.5t-68.5 -39t-45 -77.5t-16.5 -123v-133h340v-248h-340q-2 -72 -9 -153.5t-17 -149.5h534v-256h-870q37 164 49 303t16 256 h-178z" />
<glyph unicode="&#xa4;" d="M51 385l148 141q-43 80 -43 185q0 104 43 184l-148 141l193 189l151 -148q43 20 88.5 28.5t98.5 8.5q51 0 97 -8t91 -29l152 146l190 -187l-147 -141q43 -80 43 -184t-43 -185l147 -141l-192 -188l-152 147q-43 -20 -88 -28.5t-98 -8.5q-51 0 -97.5 8.5t-91.5 28.5 l-151 -145zM414 711q0 -88 49 -133.5t119 -45.5t119 45.5t49 133.5t-49.5 133t-118.5 45q-70 0 -119 -45t-49 -133z" />
<glyph unicode="&#xa5;" d="M-10 1419h348q63 -141 121.5 -283.5t128.5 -277.5q68 135 123 279.5t116 281.5h347q-78 -158 -161 -322.5t-177 -330.5h204v-203h-303v-133h303v-203h-303v-227h-309v227h-303v203h303v133h-303v203h205q-43 78 -90 164t-92.5 172t-86.5 168t-71 149z" />
<glyph unicode="&#xa6;" horiz-adv-x="618" d="M164 -379v813h291v-813h-291zM164 791v813h291v-813h-291z" />
<glyph unicode="&#xa7;" horiz-adv-x="1054" d="M57 -133l76 244q47 -18 89 -33t84 -24t87 -14t101 -5q76 0 114.5 17.5t38.5 60.5q0 20 -8 35.5t-29.5 30t-57.5 28.5t-91 33q-80 27 -150.5 56.5t-123 73.5t-82 106.5t-29.5 154.5q0 96 46 161.5t111 129.5q-98 82 -98 196q0 158 116 246t333 88q94 0 193.5 -20.5 t178.5 -53.5l-75 -245q-66 29 -136.5 45t-162.5 16q-78 0 -110 -17.5t-32 -52.5q0 -43 35 -68.5t127 -56.5q90 -31 162 -63.5t121 -75.5t75.5 -102.5t26.5 -143.5q0 -45 -10 -80.5t-29.5 -66.5t-48.5 -60.5t-63 -60.5q63 -47 89.5 -103.5t26.5 -131.5q0 -334 -442 -334 q-158 0 -269.5 26.5t-183.5 63.5zM350 653q0 -78 68.5 -118.5t210.5 -88.5q33 29 54.5 64t21.5 70q0 39 -17.5 66.5t-51.5 51t-87 43.5t-123 45q-33 -29 -54.5 -63.5t-21.5 -69.5z" />
<glyph unicode="&#xa8;" horiz-adv-x="1097" d="M162 1389q0 72 47 113.5t110 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110 42t-47 114zM618 1389q0 72 47.5 113.5t110.5 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110.5 42t-47.5 114z" />
<glyph unicode="&#xa9;" horiz-adv-x="1630" d="M102 711q0 174 58.5 312t157 233.5t227.5 145.5t270 50t270 -50t227.5 -145.5t157 -233.5t58.5 -312t-58.5 -312.5t-157 -233.5t-227.5 -145.5t-270 -50.5t-270 50.5t-227.5 145.5t-157 233.5t-58.5 312.5zM311 711q0 -123 37 -223.5t103.5 -172t160 -110.5t203.5 -39 q111 0 204 39t159.5 110.5t103.5 172t37 223.5t-37 223t-103.5 172t-159.5 110.5t-204 38.5t-204 -38.5t-159.5 -110.5t-103.5 -172t-37 -223zM473 715q0 184 101.5 284.5t267.5 100.5q98 0 158.5 -26.5t82.5 -39.5l-65 -180q-29 16 -64.5 27.5t-93.5 11.5q-68 0 -108.5 -43 t-40.5 -125q0 -37 6 -70.5t22.5 -59.5t45 -42t75.5 -16q61 0 102.5 14t73.5 27l58 -187q-27 -14 -90.5 -38.5t-155.5 -24.5q-182 0 -278.5 100t-96.5 287z" />
<glyph unicode="&#xaa;" horiz-adv-x="817" d="M49 889q0 70 27.5 116t74 74.5t109 41t129.5 12.5q41 0 75 -4.5t56 -8.5v21q0 57 -34.5 89t-118.5 32q-57 0 -114.5 -8.5t-98.5 -22.5l-31 182q39 12 115.5 24.5t152.5 12.5q98 0 165 -21.5t108 -62.5t58 -98.5t17 -130.5v-465q-47 -10 -136 -25.5t-208 -15.5 q-162 0 -254 57t-92 201zM279 895q0 -53 36 -71.5t95 -18.5q33 0 62.5 2t47.5 4v160q-14 4 -40.5 7t-49.5 3q-61 0 -106 -16.5t-45 -69.5z" />
<glyph unicode="&#xab;" horiz-adv-x="1222" d="M41 582l336 491l231 -108l-207 -383l207 -383l-231 -109zM594 582l336 491l231 -108l-207 -383l207 -383l-231 -109z" />
<glyph unicode="&#xac;" d="M102 575v256h963v-735h-279v479h-684z" />
<glyph unicode="&#xad;" horiz-adv-x="696" d="M51 473v277h594v-277h-594z" />
<glyph unicode="&#xae;" horiz-adv-x="1630" d="M102 711q0 174 58.5 312t157 233.5t227.5 145.5t270 50t270 -50t227.5 -145.5t157 -233.5t58.5 -312q0 -176 -58.5 -313.5t-157 -232.5t-227.5 -145.5t-270 -50.5t-270 50.5t-227.5 145.5t-157 232.5t-58.5 313.5zM311 711q0 -123 37 -223.5t103.5 -172t160 -110.5 t203.5 -39q111 0 204 39t159.5 110.5t103.5 172t37 223.5t-37 223t-103.5 172t-159.5 110.5t-204 38.5t-204 -38.5t-159.5 -110.5t-103.5 -172t-37 -223zM545 362v701q70 16 130 23.5t105 7.5q330 0 330 -248q0 -127 -111 -187q23 -35 40.5 -66.5t35 -65t34.5 -73.5t40 -92 h-209q-35 78 -59.5 136.5t-55.5 105.5h-82v-242h-198zM743 764h37q66 0 98.5 17.5t32.5 66.5q0 41 -28.5 57.5t-83.5 16.5q-14 0 -29.5 -1t-26.5 -3v-154z" />
<glyph unicode="&#xaf;" horiz-adv-x="770" d="M55 1276v209h660v-209h-660z" />
<glyph unicode="&#xb0;" horiz-adv-x="743" d="M41 1282q0 74 26.5 132.5t71.5 100t105.5 63t126.5 21.5t126 -21.5t105 -63t71.5 -100t26.5 -132.5t-26.5 -132t-71.5 -100t-105.5 -63.5t-125.5 -21.5q-66 0 -126.5 21.5t-105.5 63.5t-71.5 100t-26.5 132zM258 1282q0 -53 35 -85t78 -32t77.5 32t34.5 85t-34.5 85 t-77.5 32t-78 -32t-35 -85z" />
<glyph unicode="&#xb1;" d="M100 0v256h963v-256h-963zM100 676v256h342v348h279v-348h342v-256h-342v-348h-279v348h-342z" />
<glyph unicode="&#xb2;" horiz-adv-x="743" d="M33 1331q53 45 136 83t175 38q80 0 136.5 -18.5t91 -52t51 -80t16.5 -101.5q0 -45 -14.5 -82t-38 -68.5t-54 -59t-61.5 -54.5q-35 -29 -72 -64.5t-59 -66.5h319v-187h-587q-2 16 -2 30v28q0 100 54 181t159 169q55 47 89 82t34 72q0 31 -18.5 49t-53.5 18 q-59 0 -109.5 -23.5t-85.5 -52.5z" />
<glyph unicode="&#xb3;" horiz-adv-x="743" d="M35 657l47 183q70 -29 118 -37t105 -8q72 0 100.5 21.5t28.5 53.5q0 84 -143 84h-84v168h84q47 0 77.5 18.5t30.5 55.5q0 66 -90 66q-47 0 -94 -15.5t-90 -36.5l-78 162q51 33 128 56.5t151 23.5q82 0 138 -18.5t90 -50t48 -73.5t14 -87q0 -43 -19 -82t-67 -76 q66 -31 95.5 -82t29.5 -115q0 -55 -18.5 -104t-59 -85t-107.5 -56.5t-163 -20.5q-35 0 -72.5 4t-74.5 12.5t-69 17.5t-56 21z" />
<glyph unicode="&#xb4;" horiz-adv-x="585" d="M53 1305l291 331l186 -172l-337 -284z" />
<glyph unicode="&#xb5;" horiz-adv-x="1216" d="M143 -340v1417h306v-577q0 -78 11 -129.5t34.5 -81t58.5 -41.5t82 -12q33 0 68.5 3t64.5 7v831h305v-1040q-80 -23 -191.5 -40t-240.5 -17q-53 0 -108.5 10t-94.5 28q4 -41 7.5 -82.5t3.5 -117.5v-158h-306z" />
<glyph unicode="&#xb6;" horiz-adv-x="1454" d="M82 975q0 223 175 342t505 119q63 0 134 -4.5t140.5 -10.5t135 -16t118.5 -23v-1763h-289v1557q-29 4 -82 6t-98 2v-1565h-291v909q-229 31 -338.5 136.5t-109.5 310.5z" />
<glyph unicode="&#xb7;" horiz-adv-x="503" d="M61 604q0 92 58.5 140t132.5 48t132 -48t58 -140t-58 -140.5t-132 -48.5t-132.5 48.5t-58.5 140.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="667" d="M92 -426l37 178q14 -4 52 -13t75 -9q29 0 50.5 11t23.5 40q2 25 -19.5 45t-68.5 31l-25 6q6 20 17.5 47t23.5 52.5t22.5 47t16.5 31.5h203q-12 -23 -31 -60.5t-29 -62.5q66 -33 90.5 -78t24.5 -92q0 -98 -77 -149.5t-206 -51.5q-45 0 -90 7.5t-90 19.5z" />
<glyph unicode="&#xb9;" horiz-adv-x="743" d="M78 1266q86 35 167 79t140 91h178v-818h-233v553q-41 -23 -84 -42t-105 -42z" />
<glyph unicode="&#xba;" horiz-adv-x="935" d="M68 1036q0 98 29.5 175t82.5 130.5t127 82t162 28.5t162 -28.5t126 -82t81.5 -130.5t29.5 -175q0 -96 -29.5 -172t-81.5 -128t-126 -80.5t-162 -28.5t-162 28.5t-127 80.5t-82.5 128t-29.5 172zM301 1036q0 -92 44 -148.5t124 -56.5t123 56.5t43 148.5q0 98 -43 154.5 t-123 56.5q-78 0 -123 -56t-45 -155z" />
<glyph unicode="&#xbb;" horiz-adv-x="1222" d="M61 199l207 383l-207 383l232 108l336 -491l-336 -492zM614 199l207 383l-207 383l232 108l336 -491l-336 -492z" />
<glyph unicode="&#xbc;" horiz-adv-x="1798" d="M41 1266q86 35 167 79t140 91h178v-818h-233v553q-41 -23 -84 -42t-105 -42zM379 0l731 1419h279l-728 -1419h-282zM1096 179v147q86 145 175 263t198 233h202v-473h95v-170h-95v-177h-192v177h-383zM1311 349h168v245q-47 -53 -89 -113.5t-79 -131.5z" />
<glyph unicode="&#xbd;" horiz-adv-x="1798" d="M41 1266q86 35 167 79t140 91h178v-818h-233v553q-41 -23 -84 -42t-105 -42zM322 0l731 1419h279l-728 -1419h-282zM1088 717q53 45 136 83t175 38q80 0 136.5 -18.5t91 -52t51 -80t16.5 -101.5q0 -45 -14.5 -82t-38 -68.5t-54 -59t-61.5 -54.5q-35 -29 -72 -64.5 t-59 -66.5h319v-187h-587q-2 16 -2 30v28q0 100 54 181t159 169q55 47 89 82t34 72q0 31 -18.5 49t-53.5 18q-59 0 -109.5 -23.5t-85.5 -52.5z" />
<glyph unicode="&#xbe;" horiz-adv-x="1798" d="M47 657l47 183q70 -29 118 -37t105 -8q72 0 100.5 21.5t28.5 53.5q0 84 -143 84h-84v168h84q47 0 77.5 18.5t30.5 55.5q0 66 -90 66q-47 0 -94 -15.5t-90 -36.5l-78 162q51 33 128 56.5t151 23.5q82 0 138 -18.5t90 -50t48 -73.5t14 -87q0 -43 -19 -82t-67 -76 q66 -31 95.5 -82t29.5 -115q0 -55 -18.5 -104t-59 -85t-107.5 -56.5t-163 -20.5q-35 0 -72.5 4t-74.5 12.5t-69 17.5t-56 21zM422 0l731 1419h279l-728 -1419h-282zM1096 179v147q86 145 175 263t198 233h202v-473h95v-170h-95v-177h-192v177h-383zM1311 349h168v245 q-47 -53 -89 -113.5t-79 -131.5z" />
<glyph unicode="&#xbf;" horiz-adv-x="931" d="M51 -43q0 57 17.5 105.5t44 89.5t60.5 77.5t69 69.5q25 25 51 52.5t47.5 58t36 64.5t14.5 69v24.5t-2 28.5h270q4 -20 5.5 -46t1.5 -44q0 -57 -14.5 -104.5t-38 -87.5t-54.5 -73.5t-63 -66.5q-47 -47 -86 -93t-39 -106q0 -49 37.5 -83.5t111.5 -34.5t139.5 18.5 t145.5 58.5l86 -231q-80 -51 -188.5 -78.5t-213.5 -27.5q-131 0 -216 34.5t-134 88t-68.5 117t-19.5 120.5zM330 887q0 90 58 139t132 49t132.5 -49t58.5 -139t-58.5 -139.5t-132.5 -49.5t-132 49.5t-58 139.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="1476" d="M20 0q80 229 152 423.5t140.5 367t135 327t138.5 301.5h305q70 -147 137.5 -301.5t136 -327t140 -367t151.5 -423.5h-342q-23 74 -50.5 151.5t-53.5 155.5h-553q-27 -78 -54.5 -155.5t-50.5 -151.5h-332zM485 1792l186 172l291 -331l-139 -125zM530 561h404 q-33 92 -63.5 176t-58.5 154t-48.5 123t-32.5 84q-10 -31 -30.5 -84t-47 -123t-58.5 -154t-65 -176z" />
<glyph unicode="&#xc1;" horiz-adv-x="1476" d="M20 0q80 229 152 423.5t140.5 367t135 327t138.5 301.5h305q70 -147 137.5 -301.5t136 -327t140 -367t151.5 -423.5h-342q-23 74 -50.5 151.5t-53.5 155.5h-553q-27 -78 -54.5 -155.5t-50.5 -151.5h-332zM497 1633l291 331l186 -172l-337 -284zM530 561h404 q-33 92 -63.5 176t-58.5 154t-48.5 123t-32.5 84q-10 -31 -30.5 -84t-47 -123t-58.5 -154t-65 -176z" />
<glyph unicode="&#xc2;" horiz-adv-x="1476" d="M20 0q80 229 152 423.5t140.5 367t135 327t138.5 301.5h305q70 -147 137.5 -301.5t136 -327t140 -367t151.5 -423.5h-342q-23 74 -50.5 151.5t-53.5 155.5h-553q-27 -78 -54.5 -155.5t-50.5 -151.5h-332zM416 1661l322 281l317 -281l-113 -135l-204 157l-205 -157z M530 561h404q-33 92 -63.5 176t-58.5 154t-48.5 123t-32.5 84q-10 -31 -30.5 -84t-47 -123t-58.5 -154t-65 -176z" />
<glyph unicode="&#xc3;" horiz-adv-x="1476" d="M20 0q80 229 152 423.5t140.5 367t135 327t138.5 301.5h305q70 -147 137.5 -301.5t136 -327t140 -367t151.5 -423.5h-342q-23 74 -50.5 151.5t-53.5 155.5h-553q-27 -78 -54.5 -155.5t-50.5 -151.5h-332zM364 1659q12 31 33.5 68t52.5 68.5t70 52t86 20.5 q37 0 68.5 -11.5t62.5 -24.5t60.5 -24.5t64.5 -11.5q41 0 67.5 27t42.5 61l150 -98q-12 -31 -33.5 -67.5t-52.5 -68.5t-70 -52.5t-86 -20.5q-37 0 -68.5 11.5t-62.5 24.5t-61.5 24.5t-63.5 11.5q-41 0 -67.5 -26.5t-42.5 -61.5zM530 561h404q-33 92 -63.5 176t-58.5 154 t-48.5 123t-32.5 84q-10 -31 -30.5 -84t-47 -123t-58.5 -154t-65 -176z" />
<glyph unicode="&#xc4;" horiz-adv-x="1476" d="M20 0q80 229 152 423.5t140.5 367t135 327t138.5 301.5h305q70 -147 137.5 -301.5t136 -327t140 -367t151.5 -423.5h-342q-23 74 -50.5 151.5t-53.5 155.5h-553q-27 -78 -54.5 -155.5t-50.5 -151.5h-332zM338 1717q0 72 47 113.5t110 41.5t110.5 -42t47.5 -113 q0 -72 -47.5 -114t-110.5 -42t-110 42t-47 114zM530 561h404q-33 92 -63.5 176t-58.5 154t-48.5 123t-32.5 84q-10 -31 -30.5 -84t-47 -123t-58.5 -154t-65 -176zM794 1717q0 72 47.5 113.5t110.5 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110.5 42 t-47.5 114z" />
<glyph unicode="&#xc5;" horiz-adv-x="1476" d="M20 0q78 223 148 414.5t137.5 360.5t133 320.5t134.5 297.5q-35 31 -55 74.5t-20 103.5q0 57 19.5 101t53 74t76.5 45t90 15q49 0 93 -15t78 -45t53.5 -74t19.5 -101q0 -59 -21.5 -104.5t-56.5 -73.5q68 -145 134.5 -297t133 -321t137 -360.5t148.5 -414.5h-342 q-23 74 -50.5 151.5t-53.5 155.5h-553q-27 -78 -54.5 -155.5t-50.5 -151.5h-332zM530 561h404q-33 92 -63.5 176t-58.5 154t-48.5 123t-32.5 84q-10 -31 -30.5 -84t-47 -123t-58.5 -154t-65 -176zM645 1571q0 -47 28.5 -71.5t63.5 -24.5q37 0 65.5 24.5t28.5 71.5 t-28.5 71.5t-65.5 24.5q-35 0 -63.5 -24.5t-28.5 -71.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="2035" d="M20 0q250 449 465 800t408 619h1024v-268h-604v-279h532v-262h-532v-342h651v-268h-960v336h-451q-43 -78 -94 -168t-88 -168h-351zM682 590h322v520q-33 -47 -74 -112.5t-85 -136t-86 -142.5t-77 -129z" />
<glyph unicode="&#xc7;" horiz-adv-x="1327" d="M102 711q0 176 55.5 314t152 233.5t229.5 144.5t288 49q90 0 164 -13.5t129.5 -30.5t92 -35.5t53.5 -29.5l-92 -258q-66 35 -153 59.5t-198 24.5q-74 0 -144.5 -24.5t-124.5 -78.5t-87 -140t-33 -209q0 -98 21.5 -183t69.5 -146.5t126 -97.5t189 -36q70 0 125 8t98 19.5 t75.5 26t59.5 26.5l88 -256q-59 -37 -162.5 -67t-238.5 -38q-8 -16 -14.5 -30.5t-10.5 -24.5q66 -33 90.5 -78t24.5 -92q0 -98 -78 -149.5t-205 -51.5q-45 0 -90 7.5t-90 19.5l37 178q14 -4 52 -13t75 -9q29 0 50.5 11t23.5 40q2 25 -19.5 45t-68.5 31l-25 6q8 25 21.5 56.5 t25.5 60.5q-287 35 -434.5 222t-147.5 509z" />
<glyph unicode="&#xc8;" horiz-adv-x="1241" d="M164 0v1419h958v-268h-639v-279h568v-262h-568v-342h686v-268h-1005zM391 1792l186 172l291 -331l-139 -125z" />
<glyph unicode="&#xc9;" horiz-adv-x="1241" d="M164 0v1419h958v-268h-639v-279h568v-262h-568v-342h686v-268h-1005zM424 1633l291 331l186 -172l-337 -284z" />
<glyph unicode="&#xca;" horiz-adv-x="1241" d="M164 0v1419h958v-268h-639v-279h568v-262h-568v-342h686v-268h-1005zM326 1661l322 281l317 -281l-113 -135l-204 157l-205 -157z" />
<glyph unicode="&#xcb;" horiz-adv-x="1241" d="M164 0v1419h958v-268h-639v-279h568v-262h-568v-342h686v-268h-1005zM252 1717q0 72 47 113.5t110 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110 42t-47 114zM708 1717q0 72 47.5 113.5t110.5 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42 t-110.5 42t-47.5 114z" />
<glyph unicode="&#xcc;" horiz-adv-x="647" d="M72 1792l186 172l291 -331l-139 -125zM164 0v1419h319v-1419h-319z" />
<glyph unicode="&#xcd;" horiz-adv-x="647" d="M102 1633l291 331l186 -172l-337 -284zM164 0v1419h319v-1419h-319z" />
<glyph unicode="&#xce;" horiz-adv-x="647" d="M8 1661l322 281l317 -281l-113 -135l-204 157l-205 -157zM164 0v1419h319v-1419h-319z" />
<glyph unicode="&#xcf;" horiz-adv-x="647" d="M-57 1717q0 72 47 113.5t110 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110 42t-47 114zM164 0v1419h319v-1419h-319zM399 1717q0 72 47.5 113.5t110.5 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110.5 42t-47.5 114z" />
<glyph unicode="&#xd0;" horiz-adv-x="1531" d="M20 618v236h166v547q111 20 231 27.5t206 7.5q182 0 330.5 -41t255 -129t164 -225t57.5 -330q0 -184 -57.5 -321.5t-163 -227.5t-257 -135t-340.5 -45q-86 0 -200.5 7t-225.5 29v600h-166zM506 260q23 -2 52.5 -3t70.5 -1q240 0 355.5 121t115.5 334q0 223 -110.5 337.5 t-350.5 114.5q-33 0 -67.5 -1t-65.5 -5v-303h258v-236h-258v-358z" />
<glyph unicode="&#xd1;" horiz-adv-x="1548" d="M164 0v1419h260q68 -68 149.5 -166t166.5 -209.5t169 -231.5t158 -230v837h317v-1419h-268q-137 244 -297 481.5t-340 448.5v-930h-315zM399 1659q12 31 33.5 68t52.5 68.5t70 52t86 20.5q37 0 68.5 -11.5t62.5 -24.5t60.5 -24.5t64.5 -11.5q41 0 67.5 27t42.5 61 l150 -98q-12 -31 -33.5 -67.5t-52.5 -68.5t-70 -52.5t-86 -20.5q-37 0 -68.5 11.5t-62.5 24.5t-61.5 24.5t-63.5 11.5q-41 0 -67.5 -26.5t-42.5 -61.5z" />
<glyph unicode="&#xd2;" horiz-adv-x="1617" d="M102 711q0 182 57.5 320t155 232.5t225.5 141.5t269 47q145 0 274 -47t225.5 -141.5t152 -232.5t55.5 -320t-54.5 -320.5t-148.5 -231.5t-224.5 -140.5t-279.5 -47.5q-145 0 -274 47.5t-225.5 140.5t-152 231t-55.5 321zM432 711q0 -104 25.5 -188.5t74 -145t118 -93 t159.5 -32.5q88 0 158.5 32.5t119 93t74 144.5t25.5 189q0 104 -25.5 189t-74 145.5t-119 93t-158.5 32.5q-90 0 -159.5 -33.5t-118 -94t-74 -145.5t-25.5 -187zM557 1792l186 172l291 -331l-139 -125z" />
<glyph unicode="&#xd3;" horiz-adv-x="1617" d="M102 711q0 182 57.5 320t155 232.5t225.5 141.5t269 47q145 0 274 -47t225.5 -141.5t152 -232.5t55.5 -320t-54.5 -320.5t-148.5 -231.5t-224.5 -140.5t-279.5 -47.5q-145 0 -274 47.5t-225.5 140.5t-152 231t-55.5 321zM432 711q0 -104 25.5 -188.5t74 -145t118 -93 t159.5 -32.5q88 0 158.5 32.5t119 93t74 144.5t25.5 189q0 104 -25.5 189t-74 145.5t-119 93t-158.5 32.5q-90 0 -159.5 -33.5t-118 -94t-74 -145.5t-25.5 -187zM573 1633l291 331l186 -172l-337 -284z" />
<glyph unicode="&#xd4;" horiz-adv-x="1617" d="M102 711q0 182 57.5 320t155 232.5t225.5 141.5t269 47q145 0 274 -47t225.5 -141.5t152 -232.5t55.5 -320t-54.5 -320.5t-148.5 -231.5t-224.5 -140.5t-279.5 -47.5q-145 0 -274 47.5t-225.5 140.5t-152 231t-55.5 321zM432 711q0 -104 25.5 -188.5t74 -145t118 -93 t159.5 -32.5q88 0 158.5 32.5t119 93t74 144.5t25.5 189q0 104 -25.5 189t-74 145.5t-119 93t-158.5 32.5q-90 0 -159.5 -33.5t-118 -94t-74 -145.5t-25.5 -187zM489 1661l322 281l317 -281l-113 -135l-204 157l-205 -157z" />
<glyph unicode="&#xd5;" horiz-adv-x="1617" d="M102 711q0 182 57.5 320t155 232.5t225.5 141.5t269 47q145 0 274 -47t225.5 -141.5t152 -232.5t55.5 -320t-54.5 -320.5t-148.5 -231.5t-224.5 -140.5t-279.5 -47.5q-145 0 -274 47.5t-225.5 140.5t-152 231t-55.5 321zM432 711q0 -104 25.5 -188.5t74 -145t118 -93 t159.5 -32.5q88 0 158.5 32.5t119 93t74 144.5t25.5 189q0 104 -25.5 189t-74 145.5t-119 93t-158.5 32.5q-90 0 -159.5 -33.5t-118 -94t-74 -145.5t-25.5 -187zM434 1659q12 31 33.5 68t52.5 68.5t70 52t86 20.5q37 0 68.5 -11.5t62.5 -24.5t60.5 -24.5t64.5 -11.5 q41 0 67.5 27t42.5 61l150 -98q-12 -31 -33.5 -67.5t-52.5 -68.5t-70 -52.5t-86 -20.5q-37 0 -68.5 11.5t-62.5 24.5t-61.5 24.5t-63.5 11.5q-41 0 -67.5 -26.5t-42.5 -61.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="1617" d="M102 711q0 182 57.5 320t155 232.5t225.5 141.5t269 47q145 0 274 -47t225.5 -141.5t152 -232.5t55.5 -320t-54.5 -320.5t-148.5 -231.5t-224.5 -140.5t-279.5 -47.5q-145 0 -274 47.5t-225.5 140.5t-152 231t-55.5 321zM410 1717q0 72 47 113.5t110 41.5t110.5 -42 t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110 42t-47 114zM432 711q0 -104 25.5 -188.5t74 -145t118 -93t159.5 -32.5q88 0 158.5 32.5t119 93t74 144.5t25.5 189q0 104 -25.5 189t-74 145.5t-119 93t-158.5 32.5q-90 0 -159.5 -33.5t-118 -94t-74 -145.5t-25.5 -187z M866 1717q0 72 47.5 113.5t110.5 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110.5 42t-47.5 114z" />
<glyph unicode="&#xd7;" d="M127 336l279 276l-279 279l178 178l277 -278l278 278l178 -178l-278 -279l278 -276l-178 -178l-278 278l-277 -278z" />
<glyph unicode="&#xd8;" horiz-adv-x="1617" d="M102 711q0 182 57.5 320t155 232.5t225.5 141.5t269 47q203 0 365 -90l114 149l174 -129l-121 -155q82 -92 128.5 -221t46.5 -295q0 -182 -54.5 -320.5t-148.5 -231.5t-224.5 -140.5t-279.5 -47.5q-102 0 -195.5 21.5t-173.5 68.5l-118 -153l-175 129l125 160 q-80 92 -125 220t-45 294zM432 711q0 -141 47 -246l512 661q-80 45 -182 45q-90 0 -159.5 -33.5t-118 -94t-74 -145.5t-25.5 -187zM623 297q84 -45 186 -45q88 0 158.5 32.5t119 93t74 145t25.5 188.5q0 147 -49 252z" />
<glyph unicode="&#xd9;" horiz-adv-x="1447" d="M143 537v882h320v-856q0 -86 19.5 -146.5t53 -98t82 -54t105.5 -16.5q117 0 189.5 71.5t72.5 243.5v856h320v-882q0 -125 -35 -229.5t-106.5 -179.5t-182 -116t-262.5 -41q-150 0 -258.5 42t-179 117t-104.5 178.5t-34 228.5zM430 1792l186 172l291 -331l-139 -125z" />
<glyph unicode="&#xda;" horiz-adv-x="1447" d="M143 537v882h320v-856q0 -86 19.5 -146.5t53 -98t82 -54t105.5 -16.5q117 0 189.5 71.5t72.5 243.5v856h320v-882q0 -125 -35 -229.5t-106.5 -179.5t-182 -116t-262.5 -41q-150 0 -258.5 42t-179 117t-104.5 178.5t-34 228.5zM524 1633l291 331l186 -172l-337 -284z" />
<glyph unicode="&#xdb;" horiz-adv-x="1447" d="M143 537v882h320v-856q0 -86 19.5 -146.5t53 -98t82 -54t105.5 -16.5q117 0 189.5 71.5t72.5 243.5v856h320v-882q0 -125 -35 -229.5t-106.5 -179.5t-182 -116t-262.5 -41q-150 0 -258.5 42t-179 117t-104.5 178.5t-34 228.5zM401 1661l322 281l317 -281l-113 -135 l-204 157l-205 -157z" />
<glyph unicode="&#xdc;" horiz-adv-x="1447" d="M143 537v882h320v-856q0 -86 19.5 -146.5t53 -98t82 -54t105.5 -16.5q117 0 189.5 71.5t72.5 243.5v856h320v-882q0 -125 -35 -229.5t-106.5 -179.5t-182 -116t-262.5 -41q-150 0 -258.5 42t-179 117t-104.5 178.5t-34 228.5zM338 1717q0 72 47 113.5t110 41.5t110.5 -42 t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110 42t-47 114zM794 1717q0 72 47.5 113.5t110.5 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110.5 42t-47.5 114z" />
<glyph unicode="&#xdd;" horiz-adv-x="1353" d="M10 1419h371q66 -147 141.5 -292.5t161.5 -290.5q86 145 164 290.5t143 292.5h352q-117 -229 -241.5 -444t-263.5 -432v-543h-320v539q-139 217 -265 434t-243 446zM487 1633l291 331l186 -172l-337 -284z" />
<glyph unicode="&#xde;" horiz-adv-x="1318" d="M164 0v1419h319v-205q14 2 35 3.5t41.5 2.5t40 1h31.5q135 0 248.5 -26t195.5 -83t127 -147.5t45 -219.5q0 -256 -172 -369.5t-491 -113.5h-101v-262h-319zM483 535h101q166 0 250 45t84 168q0 59 -22 98t-61.5 62.5t-97 32.5t-127.5 9q-35 0 -68.5 -2t-58.5 -4v-409z " />
<glyph unicode="&#xdf;" horiz-adv-x="1349" d="M143 0v1092q0 109 29 199.5t89.5 157.5t151.5 103.5t216 36.5q119 0 205 -27.5t141 -73.5t80.5 -108.5t25.5 -130.5q0 -86 -17 -137t-58 -98q-45 -53 -81 -98.5t-36 -102.5q0 -31 16.5 -53.5t43 -43t62.5 -41t76 -42.5q74 -43 129.5 -112.5t55.5 -180.5 q0 -176 -100.5 -270.5t-331.5 -94.5q-113 0 -180.5 20.5t-108.5 37.5l51 246q20 -8 49 -17.5t59.5 -17.5t62.5 -13.5t61 -5.5q137 0 137 111q0 53 -55.5 95t-141.5 81q-59 27 -95 57.5t-54.5 64.5t-24.5 68.5t-6 71.5q0 86 39 144.5t92 119.5q35 41 53.5 76t18.5 88 q0 68 -43 100.5t-117 32.5q-98 0 -146.5 -61.5t-48.5 -175.5v-1098h-299z" />
<glyph unicode="&#xe0;" horiz-adv-x="1132" d="M72 332q0 94 37.5 159.5t101 104.5t145.5 56.5t170 17.5q59 0 105.5 -5.5t75.5 -13.5v29q0 78 -47.5 125t-163.5 47q-78 0 -154 -11.5t-131 -31.5l-39 246q27 8 67 17t87 16.5t99 12.5t105 5q135 0 225.5 -31t144.5 -88t76.5 -139t22.5 -182v-635q-66 -14 -182.5 -34 t-281.5 -20q-104 0 -189.5 18.5t-147 60.5t-94 109.5t-32.5 166.5zM293 1464l186 172l291 -331l-139 -125zM375 340q0 -72 48 -99.5t132 -27.5q45 0 86 2t66 6v232q-18 4 -55.5 8t-67.5 4q-43 0 -81 -5t-66.5 -19.5t-45 -39t-16.5 -61.5z" />
<glyph unicode="&#xe1;" horiz-adv-x="1132" d="M72 332q0 94 37.5 159.5t101 104.5t145.5 56.5t170 17.5q59 0 105.5 -5.5t75.5 -13.5v29q0 78 -47.5 125t-163.5 47q-78 0 -154 -11.5t-131 -31.5l-39 246q27 8 67 17t87 16.5t99 12.5t105 5q135 0 225.5 -31t144.5 -88t76.5 -139t22.5 -182v-635q-66 -14 -182.5 -34 t-281.5 -20q-104 0 -189.5 18.5t-147 60.5t-94 109.5t-32.5 166.5zM336 1305l291 331l186 -172l-337 -284zM375 340q0 -72 48 -99.5t132 -27.5q45 0 86 2t66 6v232q-18 4 -55.5 8t-67.5 4q-43 0 -81 -5t-66.5 -19.5t-45 -39t-16.5 -61.5z" />
<glyph unicode="&#xe2;" horiz-adv-x="1132" d="M72 332q0 94 37.5 159.5t101 104.5t145.5 56.5t170 17.5q59 0 105.5 -5.5t75.5 -13.5v29q0 78 -47.5 125t-163.5 47q-78 0 -154 -11.5t-131 -31.5l-39 246q27 8 67 17t87 16.5t99 12.5t105 5q135 0 225.5 -31t144.5 -88t76.5 -139t22.5 -182v-635q-66 -14 -182.5 -34 t-281.5 -20q-104 0 -189.5 18.5t-147 60.5t-94 109.5t-32.5 166.5zM213 1321l322 281l317 -281l-113 -135l-204 157l-205 -157zM375 340q0 -72 48 -99.5t132 -27.5q45 0 86 2t66 6v232q-18 4 -55.5 8t-67.5 4q-43 0 -81 -5t-66.5 -19.5t-45 -39t-16.5 -61.5z" />
<glyph unicode="&#xe3;" horiz-adv-x="1132" d="M72 332q0 94 37.5 159.5t101 104.5t145.5 56.5t170 17.5q59 0 105.5 -5.5t75.5 -13.5v29q0 78 -47.5 125t-163.5 47q-78 0 -154 -11.5t-131 -31.5l-39 246q27 8 67 17t87 16.5t99 12.5t105 5q135 0 225.5 -31t144.5 -88t76.5 -139t22.5 -182v-635q-66 -14 -182.5 -34 t-281.5 -20q-104 0 -189.5 18.5t-147 60.5t-94 109.5t-32.5 166.5zM174 1325q12 31 33.5 68t52.5 68.5t70 52t86 20.5q37 0 68.5 -11.5t62.5 -24.5t60.5 -24.5t64.5 -11.5q41 0 67.5 27t42.5 61l150 -98q-12 -31 -33.5 -67.5t-52.5 -68.5t-70 -52.5t-86 -20.5 q-37 0 -68.5 11.5t-62.5 24.5t-61.5 24.5t-63.5 11.5q-41 0 -67.5 -26.5t-42.5 -61.5zM375 340q0 -72 48 -99.5t132 -27.5q45 0 86 2t66 6v232q-18 4 -55.5 8t-67.5 4q-43 0 -81 -5t-66.5 -19.5t-45 -39t-16.5 -61.5z" />
<glyph unicode="&#xe4;" horiz-adv-x="1132" d="M72 332q0 94 37.5 159.5t101 104.5t145.5 56.5t170 17.5q59 0 105.5 -5.5t75.5 -13.5v29q0 78 -47.5 125t-163.5 47q-78 0 -154 -11.5t-131 -31.5l-39 246q27 8 67 17t87 16.5t99 12.5t105 5q135 0 225.5 -31t144.5 -88t76.5 -139t22.5 -182v-635q-66 -14 -182.5 -34 t-281.5 -20q-104 0 -189.5 18.5t-147 60.5t-94 109.5t-32.5 166.5zM158 1389q0 72 47 113.5t110 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110 42t-47 114zM375 340q0 -72 48 -99.5t132 -27.5q45 0 86 2t66 6v232q-18 4 -55.5 8t-67.5 4q-43 0 -81 -5 t-66.5 -19.5t-45 -39t-16.5 -61.5zM614 1389q0 72 47.5 113.5t110.5 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110.5 42t-47.5 114z" />
<glyph unicode="&#xe5;" horiz-adv-x="1132" d="M72 332q0 94 37.5 159.5t101 104.5t145.5 56.5t170 17.5q59 0 105.5 -5.5t75.5 -13.5v29q0 78 -47.5 125t-163.5 47q-78 0 -154 -11.5t-131 -31.5l-39 246q27 8 67 17t87 16.5t99 12.5t105 5q135 0 225.5 -31t144.5 -88t76.5 -139t22.5 -182v-635q-66 -14 -182.5 -34 t-281.5 -20q-104 0 -189.5 18.5t-147 60.5t-94 109.5t-32.5 166.5zM326 1432q0 57 19.5 101t53.5 73.5t77 45t90 15.5q49 0 93 -15.5t78 -45t53 -73.5t19 -101t-19 -101.5t-53 -74t-78 -45t-93 -15.5q-47 0 -90 15.5t-77 45t-53.5 73.5t-19.5 102zM375 340q0 -72 48 -99.5 t132 -27.5q45 0 86 2t66 6v232q-18 4 -55.5 8t-67.5 4q-43 0 -81 -5t-66.5 -19.5t-45 -39t-16.5 -61.5zM474 1432q0 -47 28.5 -72t63.5 -25q37 0 65.5 25t28.5 72t-28.5 71.5t-65.5 24.5q-35 0 -63.5 -24.5t-28.5 -71.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1789" d="M72 332q0 94 37.5 159.5t101 104.5t145.5 56.5t170 17.5q59 0 105.5 -5.5t75.5 -13.5v35q0 78 -47.5 122t-163.5 44q-78 0 -154 -11.5t-131 -31.5l-39 246q27 8 67 17t87 16.5t99 12.5t105 5q63 0 114.5 -8t91.5 -25.5t73 -45.5t61 -66q70 78 158 111.5t189 33.5 q111 0 198.5 -36t151 -105.5t97.5 -173t34 -238.5q0 -27 -2 -58.5t-4 -56.5h-693q10 -94 88 -149.5t209 -55.5q84 0 165 15.5t132 38.5l41 -248q-25 -12 -65.5 -24.5t-91 -21.5t-107.5 -15.5t-115 -6.5q-121 0 -211 27t-153 70q-154 -90 -356 -91q-104 0 -189.5 18.5 t-147 60.5t-94 109.5t-32.5 166.5zM375 340q0 -72 48 -99.5t132 -27.5q63 0 115.5 7t91.5 24q-29 74 -39 120t-12 93q-29 4 -68 6t-59 2q-43 0 -81 -5t-66.5 -19.5t-45 -39t-16.5 -61.5zM997 647h404q-2 39 -13.5 76t-35 65.5t-59.5 47t-89 18.5q-51 0 -88 -17.5t-61.5 -46 t-37.5 -66.5t-20 -77z" />
<glyph unicode="&#xe7;" horiz-adv-x="1024" d="M92 539q0 117 38 220t109.5 180t174 122t233.5 45q86 0 158 -15.5t139 -43.5l-63 -244q-43 16 -94.5 28.5t-114.5 12.5q-135 0 -202 -84t-67 -221q0 -145 62.5 -225.5t218.5 -80.5q55 0 118.5 10.5t117.5 32.5l43 -249q-47 -20 -116 -35t-151 -19q-8 -16 -14 -30.5 t-10 -24.5q66 -33 90 -78t24 -92q0 -98 -77.5 -149.5t-204.5 -51.5q-45 0 -90 7.5t-90 19.5l36 178q14 -4 52.5 -13t74.5 -9q29 0 50.5 11t23.5 40q2 25 -19.5 45t-68.5 31l-24 6q8 27 22 60.5t29 62.5q-106 20 -183 70t-127.5 123t-74 165t-23.5 195z" />
<glyph unicode="&#xe8;" horiz-adv-x="1196" d="M92 530q0 143 44 251t116 179.5t165 108.5t191 37q229 0 362.5 -140.5t133.5 -412.5q0 -27 -2 -58.5t-4 -56.5h-692q10 -94 87.5 -149.5t208.5 -55.5q84 0 165 15.5t132 38.5l41 -248q-25 -12 -65.5 -24.5t-90.5 -21.5t-107.5 -15.5t-114.5 -6.5q-145 0 -253 43 t-178.5 118t-104.5 177t-34 221zM346 1464l186 172l291 -331l-139 -125zM406 647h403q-2 39 -13.5 76t-35 65.5t-59 47t-89.5 18.5q-51 0 -88 -17.5t-61.5 -46t-37.5 -66.5t-19 -77z" />
<glyph unicode="&#xe9;" horiz-adv-x="1196" d="M92 530q0 143 44 251t116 179.5t165 108.5t191 37q229 0 362.5 -140.5t133.5 -412.5q0 -27 -2 -58.5t-4 -56.5h-692q10 -94 87.5 -149.5t208.5 -55.5q84 0 165 15.5t132 38.5l41 -248q-25 -12 -65.5 -24.5t-90.5 -21.5t-107.5 -15.5t-114.5 -6.5q-145 0 -253 43 t-178.5 118t-104.5 177t-34 221zM379 1305l291 331l186 -172l-337 -284zM406 647h403q-2 39 -13.5 76t-35 65.5t-59 47t-89.5 18.5q-51 0 -88 -17.5t-61.5 -46t-37.5 -66.5t-19 -77z" />
<glyph unicode="&#xea;" horiz-adv-x="1196" d="M92 530q0 143 44 251t116 179.5t165 108.5t191 37q229 0 362.5 -140.5t133.5 -412.5q0 -27 -2 -58.5t-4 -56.5h-692q10 -94 87.5 -149.5t208.5 -55.5q84 0 165 15.5t132 38.5l41 -248q-25 -12 -65.5 -24.5t-90.5 -21.5t-107.5 -15.5t-114.5 -6.5q-145 0 -253 43 t-178.5 118t-104.5 177t-34 221zM276 1321l322 281l317 -281l-113 -135l-204 157l-205 -157zM406 647h403q-2 39 -13.5 76t-35 65.5t-59 47t-89.5 18.5q-51 0 -88 -17.5t-61.5 -46t-37.5 -66.5t-19 -77z" />
<glyph unicode="&#xeb;" horiz-adv-x="1196" d="M92 530q0 143 44 251t116 179.5t165 108.5t191 37q229 0 362.5 -140.5t133.5 -412.5q0 -27 -2 -58.5t-4 -56.5h-692q10 -94 87.5 -149.5t208.5 -55.5q84 0 165 15.5t132 38.5l41 -248q-25 -12 -65.5 -24.5t-90.5 -21.5t-107.5 -15.5t-114.5 -6.5q-145 0 -253 43 t-178.5 118t-104.5 177t-34 221zM223 1389q0 72 47 113.5t110 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110 42t-47 114zM406 647h403q-2 39 -13.5 76t-35 65.5t-59 47t-89.5 18.5q-51 0 -88 -17.5t-61.5 -46t-37.5 -66.5t-19 -77zM679 1389q0 72 47.5 113.5 t110.5 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110.5 42t-47.5 114z" />
<glyph unicode="&#xec;" horiz-adv-x="591" d="M45 1464l186 172l291 -331l-139 -125zM143 0v1077h306v-1077h-306z" />
<glyph unicode="&#xed;" horiz-adv-x="591" d="M84 1305l291 331l186 -172l-337 -284zM143 0v1077h306v-1077h-306z" />
<glyph unicode="&#xee;" horiz-adv-x="591" d="M-20 1321l322 281l317 -281l-113 -135l-204 157l-205 -157zM143 0v1077h306v-1077h-306z" />
<glyph unicode="&#xef;" horiz-adv-x="591" d="M-98 1389q0 72 47 113.5t110 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110 42t-47 114zM143 0v1077h306v-1077h-306zM358 1389q0 72 47.5 113.5t110.5 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110.5 42t-47.5 114z" />
<glyph unicode="&#xf0;" horiz-adv-x="1230" d="M92 479q0 113 32 207t95.5 159.5t159.5 102.5t225 37q53 0 111.5 -15.5t99.5 -35.5q-25 109 -88 211l-227 -76l-60 172l164 55q-35 35 -73.5 66t-79.5 59l170 176q72 -41 136 -93t120 -115l235 79l59 -172l-182 -61q72 -123 113 -266.5t41 -304.5q0 -154 -32 -281 t-97.5 -218t-167 -142.5t-240.5 -51.5q-129 0 -225 41t-160.5 109.5t-96.5 161t-32 196.5zM399 483q0 -111 50.5 -179t156.5 -68q68 0 112 33.5t69.5 90t34.5 130t9 151.5v13.5t-2 19.5q-51 41 -103 54t-101 13q-66 0 -109 -20.5t-69.5 -55t-37 -81.5t-10.5 -101z" />
<glyph unicode="&#xf1;" horiz-adv-x="1206" d="M143 0v1040q78 23 201 42.5t258 19.5q137 0 228.5 -36t144.5 -101.5t75.5 -155.5t22.5 -201v-608h-305v571q0 147 -39 209t-145 62q-33 0 -70 -3t-65 -8v-831h-306zM229 1325q12 31 33.5 68t52.5 68.5t70 52t86 20.5q37 0 68.5 -11.5t62.5 -24.5t60.5 -24.5t64.5 -11.5 q41 0 67.5 27t42.5 61l150 -98q-12 -31 -33.5 -67.5t-52.5 -68.5t-70 -52.5t-86 -20.5q-37 0 -68.5 11.5t-62.5 24.5t-61.5 24.5t-63.5 11.5q-41 0 -67.5 -26.5t-42.5 -61.5z" />
<glyph unicode="&#xf2;" horiz-adv-x="1243" d="M92 541q0 127 40 231.5t110.5 178t168 114.5t212.5 41q117 0 214 -41t166.5 -114.5t108.5 -178t39 -231.5t-37 -232.5t-106.5 -180.5t-167 -116t-217.5 -41q-119 0 -216.5 41t-167 116t-108.5 180.5t-39 232.5zM369 1464l186 172l291 -331l-139 -125zM403 541 q0 -141 57.5 -224.5t162.5 -83.5q104 0 160.5 83.5t56.5 224.5t-56.5 222t-160.5 81t-162 -81t-58 -222z" />
<glyph unicode="&#xf3;" horiz-adv-x="1243" d="M92 541q0 127 40 231.5t110.5 178t168 114.5t212.5 41q117 0 214 -41t166.5 -114.5t108.5 -178t39 -231.5t-37 -232.5t-106.5 -180.5t-167 -116t-217.5 -41q-119 0 -216.5 41t-167 116t-108.5 180.5t-39 232.5zM397 1305l291 331l186 -172l-337 -284zM403 541 q0 -141 57.5 -224.5t162.5 -83.5q104 0 160.5 83.5t56.5 224.5t-56.5 222t-160.5 81t-162 -81t-58 -222z" />
<glyph unicode="&#xf4;" horiz-adv-x="1243" d="M92 541q0 127 40 231.5t110.5 178t168 114.5t212.5 41q117 0 214 -41t166.5 -114.5t108.5 -178t39 -231.5t-37 -232.5t-106.5 -180.5t-167 -116t-217.5 -41q-119 0 -216.5 41t-167 116t-108.5 180.5t-39 232.5zM299 1321l322 281l317 -281l-113 -135l-204 157l-205 -157z M403 541q0 -141 57.5 -224.5t162.5 -83.5q104 0 160.5 83.5t56.5 224.5t-56.5 222t-160.5 81t-162 -81t-58 -222z" />
<glyph unicode="&#xf5;" horiz-adv-x="1243" d="M92 541q0 127 40 231.5t110.5 178t168 114.5t212.5 41q117 0 214 -41t166.5 -114.5t108.5 -178t39 -231.5t-37 -232.5t-106.5 -180.5t-167 -116t-217.5 -41q-119 0 -216.5 41t-167 116t-108.5 180.5t-39 232.5zM241 1325q12 31 33.5 68t52.5 68.5t70 52t86 20.5 q37 0 68.5 -11.5t62.5 -24.5t60.5 -24.5t64.5 -11.5q41 0 67.5 27t42.5 61l150 -98q-12 -31 -33.5 -67.5t-52.5 -68.5t-70 -52.5t-86 -20.5q-37 0 -68.5 11.5t-62.5 24.5t-61.5 24.5t-63.5 11.5q-41 0 -67.5 -26.5t-42.5 -61.5zM403 541q0 -141 57.5 -224.5t162.5 -83.5 q104 0 160.5 83.5t56.5 224.5t-56.5 222t-160.5 81t-162 -81t-58 -222z" />
<glyph unicode="&#xf6;" horiz-adv-x="1243" d="M92 541q0 127 40 231.5t110.5 178t168 114.5t212.5 41q117 0 214 -41t166.5 -114.5t108.5 -178t39 -231.5t-37 -232.5t-106.5 -180.5t-167 -116t-217.5 -41q-119 0 -216.5 41t-167 116t-108.5 180.5t-39 232.5zM236 1389q0 72 47 113.5t110 41.5t110.5 -42t47.5 -113 q0 -72 -47.5 -114t-110.5 -42t-110 42t-47 114zM403 541q0 -141 57.5 -224.5t162.5 -83.5q104 0 160.5 83.5t56.5 224.5t-56.5 222t-160.5 81t-162 -81t-58 -222zM692 1389q0 72 47.5 113.5t110.5 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110.5 42 t-47.5 114z" />
<glyph unicode="&#xf7;" d="M100 483v256h963v-256h-963zM410 197q0 86 53 129t121 43t121 -43t53 -129t-53.5 -129t-120.5 -43q-68 0 -121 43t-53 129zM410 1026q0 86 53 129t121 43t121 -43t53 -129t-53.5 -129t-120.5 -43q-68 0 -121 43t-53 129z" />
<glyph unicode="&#xf8;" horiz-adv-x="1243" d="M92 541q0 127 40 231.5t110.5 178t168 114.5t212.5 41q76 0 143.5 -17.5t124.5 -50.5l94 121l143 -108l-100 -129q59 -72 91 -168t32 -213q0 -127 -37 -232.5t-106.5 -180.5t-167 -116t-217.5 -41q-154 0 -275 70l-92 -121l-143 109l100 129q-59 74 -90 170t-31 213z M387 541q0 -39 3 -73t13 -62l328 425q-47 29 -108 29q-111 0 -173.5 -84.5t-62.5 -234.5zM510 248q49 -31 113 -31q111 0 172 87t61 237q0 70 -16 135z" />
<glyph unicode="&#xf9;" horiz-adv-x="1206" d="M133 477v600h305v-563q0 -147 39 -212.5t146 -65.5q33 0 69.5 3t65.5 7v831h305v-1040q-78 -23 -201 -42.5t-258 -19.5q-137 0 -228 37t-144.5 103.5t-76 159t-22.5 202.5zM303 1464l186 172l291 -331l-139 -125z" />
<glyph unicode="&#xfa;" horiz-adv-x="1206" d="M133 477v600h305v-563q0 -147 39 -212.5t146 -65.5q33 0 69.5 3t65.5 7v831h305v-1040q-78 -23 -201 -42.5t-258 -19.5q-137 0 -228 37t-144.5 103.5t-76 159t-22.5 202.5zM383 1305l291 331l186 -172l-337 -284z" />
<glyph unicode="&#xfb;" horiz-adv-x="1206" d="M133 477v600h305v-563q0 -147 39 -212.5t146 -65.5q33 0 69.5 3t65.5 7v831h305v-1040q-78 -23 -201 -42.5t-258 -19.5q-137 0 -228 37t-144.5 103.5t-76 159t-22.5 202.5zM281 1321l322 281l317 -281l-113 -135l-204 157l-205 -157z" />
<glyph unicode="&#xfc;" horiz-adv-x="1206" d="M133 477v600h305v-563q0 -147 39 -212.5t146 -65.5q33 0 69.5 3t65.5 7v831h305v-1040q-78 -23 -201 -42.5t-258 -19.5q-137 0 -228 37t-144.5 103.5t-76 159t-22.5 202.5zM213 1389q0 72 47 113.5t110 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110 42 t-47 114zM669 1389q0 72 47.5 113.5t110.5 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110.5 42t-47.5 114z" />
<glyph unicode="&#xfd;" horiz-adv-x="1120" d="M10 -336l53 244q47 -16 84 -22.5t78 -6.5q82 0 126 44t75 122q-104 205 -208.5 460t-197.5 572h324q20 -80 48 -173t58.5 -188t62.5 -185.5t60 -163.5q27 74 55.5 164t56.5 185t53.5 188t45.5 173h316q-92 -311 -192.5 -583.5t-219.5 -528.5q-43 -92 -86 -156.5 t-94 -106.5t-115.5 -61.5t-150.5 -19.5q-72 0 -132.5 13.5t-99.5 29.5zM403 1305l291 331l186 -172l-337 -284z" />
<glyph unicode="&#xfe;" horiz-adv-x="1236" d="M143 -379v1919l306 49v-508q35 10 77.5 15.5t75.5 5.5q125 0 225.5 -40t171 -112.5t108.5 -177t38 -233.5q0 -125 -31 -227.5t-90 -176.5t-147.5 -114.5t-202.5 -40.5q-63 0 -118.5 12t-106.5 35v-406h-306zM449 281q29 -18 74.5 -30.5t92.5 -12.5q218 -1 218 292 q0 141 -62.5 226.5t-179.5 85.5q-41 0 -81 -5.5t-62 -13.5v-542z" />
<glyph unicode="&#xff;" horiz-adv-x="1120" d="M10 -336l53 244q47 -16 84 -22.5t78 -6.5q82 0 126 44t75 122q-104 205 -208.5 460t-197.5 572h324q20 -80 48 -173t58.5 -188t62.5 -185.5t60 -163.5q27 74 55.5 164t56.5 185t53.5 188t45.5 173h316q-92 -311 -192.5 -583.5t-219.5 -528.5q-43 -92 -86 -156.5 t-94 -106.5t-115.5 -61.5t-150.5 -19.5q-72 0 -132.5 13.5t-99.5 29.5zM180 1389q0 72 47 113.5t110 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110 42t-47 114zM636 1389q0 72 47.5 113.5t110.5 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42 t-110.5 42t-47.5 114z" />
<glyph unicode="&#x152;" horiz-adv-x="2041" d="M102 711q0 186 58.5 321t164 224.5t254 132.5t328.5 43q47 0 105.5 -3.5t105.5 -9.5h805v-268h-604v-279h532v-262h-532v-342h651v-268h-852q-47 -6 -105.5 -9t-105.5 -3q-180 0 -328.5 43t-254 132t-164 225t-58.5 323zM430 711q0 -219 116.5 -336t356.5 -117h46.5 t60.5 2v899q-41 2 -59.5 2h-47.5q-240 0 -356.5 -115.5t-116.5 -334.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1918" d="M92 541q0 127 38 231.5t107.5 178t166 114.5t210.5 41q238 0 371 -176q74 98 164 137t182 39q229 0 362.5 -139.5t133.5 -413.5q0 -27 -2 -58.5t-4 -56.5h-693q10 -94 88 -149.5t209 -55.5q86 0 166 15.5t131 38.5l41 -248q-25 -12 -65.5 -24.5t-91 -21.5t-108 -15.5 t-114.5 -6.5q-131 0 -236.5 42t-176.5 128q-68 -82 -160 -126t-197 -44q-119 0 -215 41t-164.5 116t-105.5 180.5t-37 232.5zM403 541q0 -141 57.5 -224.5t162.5 -83.5q104 0 160.5 83.5t56.5 224.5t-56.5 222t-160.5 81t-162 -81t-58 -222zM1128 647h404q-2 39 -13.5 76 t-35 65.5t-60.5 47t-88 18.5q-53 0 -89 -17.5t-60.5 -46t-37.5 -66.5t-20 -77z" />
<glyph unicode="&#x178;" horiz-adv-x="1353" d="M10 1419h371q66 -147 141.5 -292.5t161.5 -290.5q86 145 164 290.5t143 292.5h352q-117 -229 -241.5 -444t-263.5 -432v-543h-320v539q-139 217 -265 434t-243 446zM291 1717q0 72 47 113.5t110 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110 42t-47 114z M747 1717q0 72 47.5 113.5t110.5 41.5t110.5 -42t47.5 -113q0 -72 -47.5 -114t-110.5 -42t-110.5 42t-47.5 114z" />
<glyph unicode="&#x2c6;" horiz-adv-x="815" d="M86 1321l322 281l317 -281l-113 -135l-204 157l-205 -157z" />
<glyph unicode="&#x2dc;" horiz-adv-x="763" d="M8 1325q12 31 33.5 68t52.5 68.5t70 52t86 20.5q37 0 68.5 -11.5t62.5 -24.5t60.5 -24.5t64.5 -11.5q41 0 67.5 27t42.5 61l150 -98q-12 -31 -33.5 -67.5t-52.5 -68.5t-70 -52.5t-86 -20.5q-37 0 -68.5 11.5t-62.5 24.5t-61.5 24.5t-63.5 11.5q-41 0 -67.5 -26.5 t-42.5 -61.5z" />
<glyph unicode="&#x2000;" horiz-adv-x="982" />
<glyph unicode="&#x2001;" horiz-adv-x="1964" />
<glyph unicode="&#x2002;" horiz-adv-x="982" />
<glyph unicode="&#x2003;" horiz-adv-x="1964" />
<glyph unicode="&#x2004;" horiz-adv-x="654" />
<glyph unicode="&#x2005;" horiz-adv-x="491" />
<glyph unicode="&#x2006;" horiz-adv-x="327" />
<glyph unicode="&#x2007;" horiz-adv-x="327" />
<glyph unicode="&#x2008;" horiz-adv-x="245" />
<glyph unicode="&#x2009;" horiz-adv-x="392" />
<glyph unicode="&#x200a;" horiz-adv-x="109" />
<glyph unicode="&#x2010;" horiz-adv-x="696" d="M51 473v277h594v-277h-594z" />
<glyph unicode="&#x2011;" horiz-adv-x="696" d="M51 473v277h594v-277h-594z" />
<glyph unicode="&#x2012;" horiz-adv-x="696" d="M51 473v277h594v-277h-594z" />
<glyph unicode="&#x2013;" horiz-adv-x="1024" d="M0 485v254h1024v-254h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2048" d="M0 485v254h2048v-254h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="497" d="M82 1118q0 137 49 261t121 210l215 -49q-39 -88 -62.5 -198.5t-23.5 -211.5v-17t1 -30.5t2 -36t3 -32.5h-297q-4 29 -6 59.5t-2 44.5z" />
<glyph unicode="&#x2019;" horiz-adv-x="497" d="M31 1044q39 88 62.5 199t23.5 211v17.5t-1 31t-2 35.5t-3 33h297q4 -29 6 -59.5t2 -45.5q0 -137 -49.5 -261t-120.5 -210z" />
<glyph unicode="&#x201a;" horiz-adv-x="497" d="M31 -211q39 88 62.5 199t23.5 211v17.5t-1 31t-2 35.5t-3 33h297q4 -29 6 -59.5t2 -45.5q0 -137 -49.5 -261t-120.5 -210z" />
<glyph unicode="&#x201c;" horiz-adv-x="929" d="M82 1118q0 137 49 261t121 210l207 -47q-39 -88 -62.5 -199.5t-23.5 -212.5v-17t1 -30.5t2 -36t3 -32.5h-289q-4 29 -6 59.5t-2 44.5zM522 1118q0 137 49.5 261t120.5 210l207 -47q-39 -88 -62.5 -199.5t-23.5 -212.5v-17t1 -30.5t2 -36t3 -32.5h-289q-4 29 -6 59.5 t-2 44.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="929" d="M31 1042q39 88 62.5 200t23.5 212v17.5t-1 31t-2 35.5t-3 33h288q4 -29 6.5 -59.5t2.5 -45.5q0 -137 -49.5 -261t-120.5 -210zM471 1042q39 88 62.5 200t23.5 212v17.5t-1 31t-2 35.5t-3 33h289q4 -29 6 -59.5t2 -45.5q0 -137 -49 -261t-121 -210z" />
<glyph unicode="&#x201e;" horiz-adv-x="929" d="M31 -213q39 88 62.5 200t23.5 212v17.5t-1 31t-2 35.5t-3 33h288q4 -29 6.5 -59.5t2.5 -45.5q0 -137 -49.5 -261t-120.5 -210zM471 -213q39 88 62.5 200t23.5 212v17.5t-1 31t-2 35.5t-3 33h289q4 -29 6 -59.5t2 -45.5q0 -137 -49 -261t-121 -210z" />
<glyph unicode="&#x2022;" horiz-adv-x="745" d="M78 723q0 57 20.5 110.5t59.5 93.5t93 63.5t122 23.5t122 -23.5t93 -63.5t59.5 -93.5t20.5 -110.5q0 -59 -20.5 -111.5t-59.5 -92.5t-93.5 -63.5t-121.5 -23.5q-68 0 -122 23.5t-93 63.5t-59.5 92.5t-20.5 111.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="2048" d="M122 162q0 92 58.5 140t132.5 48t132 -48t58 -140t-58 -140.5t-132 -48.5t-132.5 48.5t-58.5 140.5zM833 162q0 92 58.5 140t132.5 48t132 -48t58 -140t-58 -140.5t-132 -48.5t-132.5 48.5t-58.5 140.5zM1544 162q0 92 58.5 140t132.5 48t132 -48t58 -140t-58 -140.5 t-132 -48.5t-132.5 48.5t-58.5 140.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="392" />
<glyph unicode="&#x2039;" horiz-adv-x="669" d="M41 582l336 491l231 -108l-207 -383l207 -383l-231 -109z" />
<glyph unicode="&#x203a;" horiz-adv-x="669" d="M61 199l207 383l-207 383l232 108l336 -491l-336 -492z" />
<glyph unicode="&#x205f;" horiz-adv-x="491" />
<glyph unicode="&#x20ac;" d="M94 446v203h129q-2 23 -2 35v29v34.5t2 34.5h-129v203h156q53 229 202.5 346t383.5 117q92 0 157.5 -14.5t130.5 -38.5l-63 -238q-49 16 -100.5 27.5t-126.5 11.5q-123 0 -192 -53t-99 -158h454l-39 -203h-442q-2 -23 -2 -38v-31v-28.5t2 -35.5h418l-39 -203h-352 q35 -123 104.5 -171t173.5 -48q68 0 137.5 12.5t139.5 41.5l59 -236q-55 -29 -142 -50.5t-204 -21.5q-256 0 -391 124t-178 349h-148z" />
<glyph unicode="&#x2122;" horiz-adv-x="1748" d="M61 1208v211h660v-211h-209v-510h-242v510h-209zM801 698q14 254 30.5 427t32.5 294h217q43 -92 82 -187t80 -188q41 92 83 194.5t75 180.5h219q20 -121 35.5 -294t32.5 -427h-236l-8 383l-121 -307h-162l-121 305q0 -66 -1 -130t-2 -117.5t-2 -90t-1 -43.5h-233z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1075" d="M0 0v1075h1075v-1075h-1075z" />
<hkern u1="K" u2="&#xef;" k="-88" />
<hkern u1="T" u2="&#xef;" k="-94" />
<hkern u1="T" u2="&#xee;" k="-55" />
<hkern u1="T" u2="&#xec;" k="18" />
<hkern u1="V" u2="&#xef;" k="-104" />
<hkern u1="V" u2="&#xec;" k="-41" />
<hkern u1="W" u2="&#xef;" k="-94" />
<hkern u1="X" u2="&#xef;" k="-82" />
<hkern u1="Y" u2="&#xef;" k="-109" />
<hkern u1="Y" u2="&#xe4;" k="109" />
<hkern u1="Z" u2="&#xef;" k="-74" />
<hkern u1="Z" u2="&#xee;" k="-20" />
<hkern u1="f" u2="&#xef;" k="-106" />
<hkern u1="f" u2="&#xec;" k="-41" />
<hkern u1="&#xdd;" u2="&#xef;" k="-109" />
<hkern u1="&#xdd;" u2="&#xe4;" k="109" />
<hkern u1="&#x178;" u2="&#xef;" k="-109" />
<hkern u1="&#x178;" u2="&#xe4;" k="109" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="parenright" 	k="43" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="asterisk" 	k="61" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="question" 	k="68" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="backslash" 	k="82" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="bracketright" 	k="70" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="v" 	k="27" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="w" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="y,yacute,ydieresis" 	k="27" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quotesinglbase,quotedblbase" 	k="-25" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteleft,quotedblleft" 	k="45" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteright,quotedblright" 	k="45" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="braceright" 	k="37" />
<hkern g1="b" 	g2="parenright" 	k="51" />
<hkern g1="b" 	g2="asterisk" 	k="61" />
<hkern g1="b" 	g2="question" 	k="57" />
<hkern g1="b" 	g2="backslash" 	k="102" />
<hkern g1="b" 	g2="bracketright" 	k="74" />
<hkern g1="b" 	g2="v" 	k="29" />
<hkern g1="b" 	g2="w" 	k="25" />
<hkern g1="b" 	g2="y,yacute,ydieresis" 	k="29" />
<hkern g1="b" 	g2="quoteleft,quotedblleft" 	k="53" />
<hkern g1="b" 	g2="quoteright,quotedblright" 	k="51" />
<hkern g1="b" 	g2="quotedbl,quotesingle" 	k="39" />
<hkern g1="b" 	g2="braceright" 	k="39" />
<hkern g1="b" 	g2="hyphen,endash,emdash" 	k="-25" />
<hkern g1="b" 	g2="comma,period,ellipsis" 	k="61" />
<hkern g1="b" 	g2="slash" 	k="57" />
<hkern g1="b" 	g2="x" 	k="37" />
<hkern g1="b" 	g2="z" 	k="20" />
<hkern g1="b" 	g2="guillemotleft,guilsinglleft" 	k="-23" />
<hkern g1="c,ccedilla" 	g2="question" 	k="27" />
<hkern g1="c,ccedilla" 	g2="bracketright" 	k="74" />
<hkern g1="c,ccedilla" 	g2="v" 	k="-16" />
<hkern g1="c,ccedilla" 	g2="w" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="y,yacute,ydieresis" 	k="-16" />
<hkern g1="c,ccedilla" 	g2="quotesinglbase,quotedblbase" 	k="-49" />
<hkern g1="c,ccedilla" 	g2="quoteleft,quotedblleft" 	k="-23" />
<hkern g1="c,ccedilla" 	g2="quoteright,quotedblright" 	k="-51" />
<hkern g1="c,ccedilla" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="c,ccedilla" 	g2="comma,period,ellipsis" 	k="-35" />
<hkern g1="c,ccedilla" 	g2="x" 	k="-25" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="31" />
<hkern g1="c,ccedilla" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="at" 	k="20" />
<hkern g1="c,ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="51" />
<hkern g1="c,ccedilla" 	g2="c,ccedilla" 	k="51" />
<hkern g1="c,ccedilla" 	g2="d" 	k="51" />
<hkern g1="c,ccedilla" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="51" />
<hkern g1="c,ccedilla" 	g2="g" 	k="51" />
<hkern g1="c,ccedilla" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="c,ccedilla" 	g2="q" 	k="51" />
<hkern g1="c,ccedilla" 	g2="eth" 	k="61" />
<hkern g1="d" 	g2="parenright" 	k="43" />
<hkern g1="d" 	g2="asterisk" 	k="51" />
<hkern g1="d" 	g2="question" 	k="76" />
<hkern g1="d" 	g2="backslash" 	k="123" />
<hkern g1="d" 	g2="bracketright" 	k="70" />
<hkern g1="d" 	g2="v" 	k="33" />
<hkern g1="d" 	g2="w" 	k="20" />
<hkern g1="d" 	g2="y,yacute,ydieresis" 	k="33" />
<hkern g1="d" 	g2="quotesinglbase,quotedblbase" 	k="-20" />
<hkern g1="d" 	g2="quoteleft,quotedblleft" 	k="-27" />
<hkern g1="d" 	g2="quoteright,quotedblright" 	k="-29" />
<hkern g1="d" 	g2="hyphen,endash,emdash" 	k="-45" />
<hkern g1="f" 	g2="parenright" 	k="-109" />
<hkern g1="f" 	g2="asterisk" 	k="-47" />
<hkern g1="f" 	g2="question" 	k="-57" />
<hkern g1="f" 	g2="backslash" 	k="-164" />
<hkern g1="f" 	g2="bracketright" 	k="-106" />
<hkern g1="f" 	g2="v" 	k="-55" />
<hkern g1="f" 	g2="w" 	k="-55" />
<hkern g1="f" 	g2="y,yacute,ydieresis" 	k="-55" />
<hkern g1="f" 	g2="quotesinglbase,quotedblbase" 	k="117" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-78" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-113" />
<hkern g1="f" 	g2="braceright" 	k="-61" />
<hkern g1="f" 	g2="comma,period,ellipsis" 	k="123" />
<hkern g1="f" 	g2="slash" 	k="127" />
<hkern g1="f" 	g2="x" 	k="-41" />
<hkern g1="f" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="f" 	g2="colon,semicolon" 	k="-37" />
<hkern g1="f" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="f" 	g2="c,ccedilla" 	k="16" />
<hkern g1="f" 	g2="d" 	k="16" />
<hkern g1="f" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="16" />
<hkern g1="f" 	g2="g" 	k="16" />
<hkern g1="f" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="f" 	g2="q" 	k="16" />
<hkern g1="f" 	g2="eth" 	k="41" />
<hkern g1="f" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="-25" />
<hkern g1="f" 	g2="j" 	k="-23" />
<hkern g1="f" 	g2="guillemotright,guilsinglright" 	k="-37" />
<hkern g1="g" 	g2="parenright" 	k="27" />
<hkern g1="g" 	g2="asterisk" 	k="51" />
<hkern g1="g" 	g2="question" 	k="47" />
<hkern g1="g" 	g2="backslash" 	k="61" />
<hkern g1="g" 	g2="bracketright" 	k="70" />
<hkern g1="g" 	g2="v" 	k="33" />
<hkern g1="g" 	g2="w" 	k="20" />
<hkern g1="g" 	g2="y,yacute,ydieresis" 	k="33" />
<hkern g1="g" 	g2="quotesinglbase,quotedblbase" 	k="-20" />
<hkern g1="g" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="g" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="g" 	g2="j" 	k="-53" />
<hkern g1="h" 	g2="parenright" 	k="43" />
<hkern g1="h" 	g2="asterisk" 	k="51" />
<hkern g1="h" 	g2="question" 	k="76" />
<hkern g1="h" 	g2="backslash" 	k="123" />
<hkern g1="h" 	g2="bracketright" 	k="70" />
<hkern g1="h" 	g2="v" 	k="33" />
<hkern g1="h" 	g2="w" 	k="20" />
<hkern g1="h" 	g2="y,yacute,ydieresis" 	k="33" />
<hkern g1="h" 	g2="quotesinglbase,quotedblbase" 	k="-25" />
<hkern g1="h" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="h" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="h" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="h" 	g2="braceright" 	k="37" />
<hkern g1="i,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="parenright" 	k="-23" />
<hkern g1="i,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="asterisk" 	k="51" />
<hkern g1="i,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="question" 	k="76" />
<hkern g1="i,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="backslash" 	k="123" />
<hkern g1="i,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="bracketright" 	k="70" />
<hkern g1="i,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="v" 	k="33" />
<hkern g1="i,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="w" 	k="20" />
<hkern g1="i,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="y,yacute,ydieresis" 	k="33" />
<hkern g1="i,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="quotesinglbase,quotedblbase" 	k="-20" />
<hkern g1="i,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="i,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="j" 	g2="parenright" 	k="-25" />
<hkern g1="j" 	g2="asterisk" 	k="51" />
<hkern g1="j" 	g2="question" 	k="76" />
<hkern g1="j" 	g2="backslash" 	k="123" />
<hkern g1="j" 	g2="bracketright" 	k="-20" />
<hkern g1="j" 	g2="v" 	k="33" />
<hkern g1="j" 	g2="w" 	k="20" />
<hkern g1="j" 	g2="y,yacute,ydieresis" 	k="33" />
<hkern g1="j" 	g2="quotesinglbase,quotedblbase" 	k="-20" />
<hkern g1="j" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="j" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="j" 	g2="j" 	k="-61" />
<hkern g1="k" 	g2="asterisk" 	k="-27" />
<hkern g1="k" 	g2="bracketright" 	k="53" />
<hkern g1="k" 	g2="v" 	k="-41" />
<hkern g1="k" 	g2="w" 	k="-41" />
<hkern g1="k" 	g2="y,yacute,ydieresis" 	k="-41" />
<hkern g1="k" 	g2="quotesinglbase,quotedblbase" 	k="-45" />
<hkern g1="k" 	g2="quoteleft,quotedblleft" 	k="-25" />
<hkern g1="k" 	g2="quoteright,quotedblright" 	k="-49" />
<hkern g1="k" 	g2="hyphen,endash,emdash" 	k="90" />
<hkern g1="k" 	g2="comma,period,ellipsis" 	k="-25" />
<hkern g1="k" 	g2="slash" 	k="-43" />
<hkern g1="k" 	g2="x" 	k="-41" />
<hkern g1="k" 	g2="z" 	k="-31" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="72" />
<hkern g1="k" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="k" 	g2="at" 	k="45" />
<hkern g1="k" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="k" 	g2="c,ccedilla" 	k="74" />
<hkern g1="k" 	g2="d" 	k="74" />
<hkern g1="k" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="74" />
<hkern g1="k" 	g2="g" 	k="74" />
<hkern g1="k" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="74" />
<hkern g1="k" 	g2="q" 	k="74" />
<hkern g1="k" 	g2="eth" 	k="66" />
<hkern g1="k" 	g2="ampersand" 	k="41" />
<hkern g1="k" 	g2="parenleft" 	k="25" />
<hkern g1="k" 	g2="t" 	k="16" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="k" 	g2="braceleft" 	k="33" />
<hkern g1="l,uniFB02,uniFB04" 	g2="parenright" 	k="43" />
<hkern g1="l,uniFB02,uniFB04" 	g2="asterisk" 	k="49" />
<hkern g1="l,uniFB02,uniFB04" 	g2="question" 	k="29" />
<hkern g1="l,uniFB02,uniFB04" 	g2="backslash" 	k="123" />
<hkern g1="l,uniFB02,uniFB04" 	g2="bracketright" 	k="70" />
<hkern g1="l,uniFB02,uniFB04" 	g2="v" 	k="23" />
<hkern g1="l,uniFB02,uniFB04" 	g2="w" 	k="23" />
<hkern g1="l,uniFB02,uniFB04" 	g2="y,yacute,ydieresis" 	k="23" />
<hkern g1="l,uniFB02,uniFB04" 	g2="quotesinglbase,quotedblbase" 	k="-63" />
<hkern g1="l,uniFB02,uniFB04" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="l,uniFB02,uniFB04" 	g2="quoteright,quotedblright" 	k="53" />
<hkern g1="l,uniFB02,uniFB04" 	g2="comma,period,ellipsis" 	k="-49" />
<hkern g1="l,uniFB02,uniFB04" 	g2="x" 	k="-43" />
<hkern g1="l,uniFB02,uniFB04" 	g2="z" 	k="-31" />
<hkern g1="l,uniFB02,uniFB04" 	g2="guillemotleft,guilsinglleft" 	k="29" />
<hkern g1="l,uniFB02,uniFB04" 	g2="colon,semicolon" 	k="-41" />
<hkern g1="l,uniFB02,uniFB04" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-31" />
<hkern g1="l,uniFB02,uniFB04" 	g2="guillemotright,guilsinglright" 	k="-41" />
<hkern g1="m,n,ntilde" 	g2="parenright" 	k="43" />
<hkern g1="m,n,ntilde" 	g2="asterisk" 	k="51" />
<hkern g1="m,n,ntilde" 	g2="question" 	k="76" />
<hkern g1="m,n,ntilde" 	g2="backslash" 	k="123" />
<hkern g1="m,n,ntilde" 	g2="bracketright" 	k="70" />
<hkern g1="m,n,ntilde" 	g2="v" 	k="33" />
<hkern g1="m,n,ntilde" 	g2="w" 	k="20" />
<hkern g1="m,n,ntilde" 	g2="y,yacute,ydieresis" 	k="33" />
<hkern g1="m,n,ntilde" 	g2="quotesinglbase,quotedblbase" 	k="-25" />
<hkern g1="m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="m,n,ntilde" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="m,n,ntilde" 	g2="quotedbl,quotesingle" 	k="37" />
<hkern g1="m,n,ntilde" 	g2="braceright" 	k="37" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="parenright" 	k="51" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="asterisk" 	k="61" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="question" 	k="57" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="backslash" 	k="102" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="bracketright" 	k="74" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="v" 	k="29" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="w" 	k="25" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="y,yacute,ydieresis" 	k="29" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteleft,quotedblleft" 	k="53" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteright,quotedblright" 	k="51" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotedbl,quotesingle" 	k="37" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="braceright" 	k="47" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="hyphen,endash,emdash" 	k="-25" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="comma,period,ellipsis" 	k="61" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="slash" 	k="57" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="x" 	k="37" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="z" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="guillemotleft,guilsinglleft" 	k="-23" />
<hkern g1="p" 	g2="parenright" 	k="51" />
<hkern g1="p" 	g2="asterisk" 	k="61" />
<hkern g1="p" 	g2="question" 	k="57" />
<hkern g1="p" 	g2="backslash" 	k="102" />
<hkern g1="p" 	g2="bracketright" 	k="74" />
<hkern g1="p" 	g2="v" 	k="29" />
<hkern g1="p" 	g2="w" 	k="25" />
<hkern g1="p" 	g2="y,yacute,ydieresis" 	k="29" />
<hkern g1="p" 	g2="quoteleft,quotedblleft" 	k="53" />
<hkern g1="p" 	g2="quoteright,quotedblright" 	k="51" />
<hkern g1="p" 	g2="quotedbl,quotesingle" 	k="39" />
<hkern g1="p" 	g2="braceright" 	k="39" />
<hkern g1="p" 	g2="hyphen,endash,emdash" 	k="-25" />
<hkern g1="p" 	g2="comma,period,ellipsis" 	k="61" />
<hkern g1="p" 	g2="slash" 	k="57" />
<hkern g1="p" 	g2="x" 	k="37" />
<hkern g1="p" 	g2="z" 	k="20" />
<hkern g1="p" 	g2="guillemotleft,guilsinglleft" 	k="-23" />
<hkern g1="q" 	g2="parenright" 	k="43" />
<hkern g1="q" 	g2="asterisk" 	k="51" />
<hkern g1="q" 	g2="question" 	k="47" />
<hkern g1="q" 	g2="backslash" 	k="61" />
<hkern g1="q" 	g2="bracketright" 	k="70" />
<hkern g1="q" 	g2="v" 	k="33" />
<hkern g1="q" 	g2="w" 	k="20" />
<hkern g1="q" 	g2="y,yacute,ydieresis" 	k="33" />
<hkern g1="q" 	g2="quotesinglbase,quotedblbase" 	k="-20" />
<hkern g1="q" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="q" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="q" 	g2="slash" 	k="-43" />
<hkern g1="q" 	g2="j" 	k="-86" />
<hkern g1="r" 	g2="asterisk" 	k="-35" />
<hkern g1="r" 	g2="question" 	k="-20" />
<hkern g1="r" 	g2="backslash" 	k="-20" />
<hkern g1="r" 	g2="bracketright" 	k="20" />
<hkern g1="r" 	g2="v" 	k="-63" />
<hkern g1="r" 	g2="w" 	k="-63" />
<hkern g1="r" 	g2="y,yacute,ydieresis" 	k="-63" />
<hkern g1="r" 	g2="quotesinglbase,quotedblbase" 	k="115" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-57" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-98" />
<hkern g1="r" 	g2="comma,period,ellipsis" 	k="123" />
<hkern g1="r" 	g2="slash" 	k="115" />
<hkern g1="r" 	g2="x" 	k="-41" />
<hkern g1="r" 	g2="z" 	k="-20" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="29" />
<hkern g1="r" 	g2="colon,semicolon" 	k="-37" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="r" 	g2="c,ccedilla" 	k="20" />
<hkern g1="r" 	g2="d" 	k="20" />
<hkern g1="r" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="20" />
<hkern g1="r" 	g2="g" 	k="20" />
<hkern g1="r" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="r" 	g2="q" 	k="20" />
<hkern g1="r" 	g2="eth" 	k="29" />
<hkern g1="r" 	g2="guillemotright,guilsinglright" 	k="-41" />
<hkern g1="r" 	g2="ampersand" 	k="41" />
<hkern g1="s" 	g2="parenright" 	k="27" />
<hkern g1="s" 	g2="asterisk" 	k="20" />
<hkern g1="s" 	g2="question" 	k="74" />
<hkern g1="s" 	g2="backslash" 	k="61" />
<hkern g1="s" 	g2="bracketright" 	k="70" />
<hkern g1="s" 	g2="quotesinglbase,quotedblbase" 	k="-29" />
<hkern g1="s" 	g2="braceright" 	k="37" />
<hkern g1="s" 	g2="slash" 	k="23" />
<hkern g1="s" 	g2="z" 	k="16" />
<hkern g1="t" 	g2="question" 	k="31" />
<hkern g1="t" 	g2="backslash" 	k="41" />
<hkern g1="t" 	g2="bracketright" 	k="68" />
<hkern g1="t" 	g2="v" 	k="-20" />
<hkern g1="t" 	g2="w" 	k="-20" />
<hkern g1="t" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="t" 	g2="quotesinglbase,quotedblbase" 	k="-47" />
<hkern g1="t" 	g2="quoteleft,quotedblleft" 	k="-33" />
<hkern g1="t" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="t" 	g2="comma,period,ellipsis" 	k="-35" />
<hkern g1="t" 	g2="x" 	k="-37" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="39" />
<hkern g1="t" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="t" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="t" 	g2="c,ccedilla" 	k="41" />
<hkern g1="t" 	g2="d" 	k="41" />
<hkern g1="t" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="41" />
<hkern g1="t" 	g2="g" 	k="41" />
<hkern g1="t" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="t" 	g2="q" 	k="41" />
<hkern g1="t" 	g2="eth" 	k="20" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="parenright" 	k="41" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="asterisk" 	k="51" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="question" 	k="47" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="backslash" 	k="123" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="bracketright" 	k="59" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="v" 	k="33" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="w" 	k="20" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="y,yacute,ydieresis" 	k="33" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="quotesinglbase,quotedblbase" 	k="-20" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="v" 	g2="asterisk" 	k="-33" />
<hkern g1="v" 	g2="bracketright" 	k="59" />
<hkern g1="v" 	g2="v" 	k="-41" />
<hkern g1="v" 	g2="w" 	k="-41" />
<hkern g1="v" 	g2="y,yacute,ydieresis" 	k="-41" />
<hkern g1="v" 	g2="quotesinglbase,quotedblbase" 	k="84" />
<hkern g1="v" 	g2="quoteleft,quotedblleft" 	k="-43" />
<hkern g1="v" 	g2="quoteright,quotedblright" 	k="-80" />
<hkern g1="v" 	g2="comma,period,ellipsis" 	k="115" />
<hkern g1="v" 	g2="slash" 	k="82" />
<hkern g1="v" 	g2="x" 	k="-49" />
<hkern g1="v" 	g2="z" 	k="-27" />
<hkern g1="v" 	g2="colon,semicolon" 	k="-31" />
<hkern g1="v" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="v" 	g2="c,ccedilla" 	k="29" />
<hkern g1="v" 	g2="d" 	k="29" />
<hkern g1="v" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="29" />
<hkern g1="v" 	g2="g" 	k="29" />
<hkern g1="v" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="v" 	g2="q" 	k="29" />
<hkern g1="v" 	g2="eth" 	k="39" />
<hkern g1="v" 	g2="guillemotright,guilsinglright" 	k="-35" />
<hkern g1="v" 	g2="ampersand" 	k="20" />
<hkern g1="w" 	g2="asterisk" 	k="-35" />
<hkern g1="w" 	g2="bracketright" 	k="57" />
<hkern g1="w" 	g2="v" 	k="-41" />
<hkern g1="w" 	g2="w" 	k="-41" />
<hkern g1="w" 	g2="y,yacute,ydieresis" 	k="-41" />
<hkern g1="w" 	g2="quotesinglbase,quotedblbase" 	k="55" />
<hkern g1="w" 	g2="quoteleft,quotedblleft" 	k="-49" />
<hkern g1="w" 	g2="quoteright,quotedblright" 	k="-82" />
<hkern g1="w" 	g2="hyphen,endash,emdash" 	k="-16" />
<hkern g1="w" 	g2="comma,period,ellipsis" 	k="109" />
<hkern g1="w" 	g2="slash" 	k="70" />
<hkern g1="w" 	g2="x" 	k="-41" />
<hkern g1="w" 	g2="z" 	k="-29" />
<hkern g1="w" 	g2="colon,semicolon" 	k="-33" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="w" 	g2="c,ccedilla" 	k="25" />
<hkern g1="w" 	g2="d" 	k="25" />
<hkern g1="w" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="25" />
<hkern g1="w" 	g2="g" 	k="25" />
<hkern g1="w" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="w" 	g2="q" 	k="25" />
<hkern g1="w" 	g2="eth" 	k="29" />
<hkern g1="w" 	g2="guillemotright,guilsinglright" 	k="-39" />
<hkern g1="w" 	g2="ampersand" 	k="20" />
<hkern g1="x" 	g2="asterisk" 	k="-27" />
<hkern g1="x" 	g2="bracketright" 	k="59" />
<hkern g1="x" 	g2="v" 	k="-49" />
<hkern g1="x" 	g2="w" 	k="-41" />
<hkern g1="x" 	g2="y,yacute,ydieresis" 	k="-49" />
<hkern g1="x" 	g2="quotesinglbase,quotedblbase" 	k="-43" />
<hkern g1="x" 	g2="quoteleft,quotedblleft" 	k="-31" />
<hkern g1="x" 	g2="quoteright,quotedblright" 	k="-57" />
<hkern g1="x" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="x" 	g2="comma,period,ellipsis" 	k="-39" />
<hkern g1="x" 	g2="slash" 	k="-43" />
<hkern g1="x" 	g2="x" 	k="-66" />
<hkern g1="x" 	g2="z" 	k="-39" />
<hkern g1="x" 	g2="guillemotleft,guilsinglleft" 	k="55" />
<hkern g1="x" 	g2="colon,semicolon" 	k="-29" />
<hkern g1="x" 	g2="at" 	k="20" />
<hkern g1="x" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="49" />
<hkern g1="x" 	g2="c,ccedilla" 	k="49" />
<hkern g1="x" 	g2="d" 	k="49" />
<hkern g1="x" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="49" />
<hkern g1="x" 	g2="g" 	k="49" />
<hkern g1="x" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="49" />
<hkern g1="x" 	g2="q" 	k="49" />
<hkern g1="x" 	g2="eth" 	k="47" />
<hkern g1="x" 	g2="ampersand" 	k="41" />
<hkern g1="x" 	g2="parenleft" 	k="25" />
<hkern g1="x" 	g2="braceleft" 	k="25" />
<hkern g1="y,yacute,ydieresis" 	g2="asterisk" 	k="-33" />
<hkern g1="y,yacute,ydieresis" 	g2="bracketright" 	k="59" />
<hkern g1="y,yacute,ydieresis" 	g2="v" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="w" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="y,yacute,ydieresis" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="84" />
<hkern g1="y,yacute,ydieresis" 	g2="quoteleft,quotedblleft" 	k="-43" />
<hkern g1="y,yacute,ydieresis" 	g2="quoteright,quotedblright" 	k="-66" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,ellipsis" 	k="115" />
<hkern g1="y,yacute,ydieresis" 	g2="slash" 	k="82" />
<hkern g1="y,yacute,ydieresis" 	g2="x" 	k="-49" />
<hkern g1="y,yacute,ydieresis" 	g2="z" 	k="-27" />
<hkern g1="y,yacute,ydieresis" 	g2="colon,semicolon" 	k="-31" />
<hkern g1="y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="c,ccedilla" 	k="29" />
<hkern g1="y,yacute,ydieresis" 	g2="d" 	k="29" />
<hkern g1="y,yacute,ydieresis" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="29" />
<hkern g1="y,yacute,ydieresis" 	g2="g" 	k="29" />
<hkern g1="y,yacute,ydieresis" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="y,yacute,ydieresis" 	g2="q" 	k="29" />
<hkern g1="y,yacute,ydieresis" 	g2="eth" 	k="39" />
<hkern g1="y,yacute,ydieresis" 	g2="guillemotright,guilsinglright" 	k="-35" />
<hkern g1="y,yacute,ydieresis" 	g2="ampersand" 	k="20" />
<hkern g1="z" 	g2="question" 	k="23" />
<hkern g1="z" 	g2="bracketright" 	k="57" />
<hkern g1="z" 	g2="v" 	k="-39" />
<hkern g1="z" 	g2="w" 	k="-39" />
<hkern g1="z" 	g2="y,yacute,ydieresis" 	k="-39" />
<hkern g1="z" 	g2="quotesinglbase,quotedblbase" 	k="-57" />
<hkern g1="z" 	g2="quoteleft,quotedblleft" 	k="-31" />
<hkern g1="z" 	g2="quoteright,quotedblright" 	k="-53" />
<hkern g1="z" 	g2="braceright" 	k="25" />
<hkern g1="z" 	g2="comma,period,ellipsis" 	k="-49" />
<hkern g1="z" 	g2="slash" 	k="-20" />
<hkern g1="z" 	g2="x" 	k="-45" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="33" />
<hkern g1="z" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="z" 	g2="c,ccedilla" 	k="31" />
<hkern g1="z" 	g2="d" 	k="31" />
<hkern g1="z" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="31" />
<hkern g1="z" 	g2="g" 	k="31" />
<hkern g1="z" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="z" 	g2="q" 	k="31" />
<hkern g1="z" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="germandbls" 	g2="parenright" 	k="49" />
<hkern g1="germandbls" 	g2="asterisk" 	k="152" />
<hkern g1="germandbls" 	g2="question" 	k="92" />
<hkern g1="germandbls" 	g2="backslash" 	k="102" />
<hkern g1="germandbls" 	g2="bracketright" 	k="45" />
<hkern g1="germandbls" 	g2="v" 	k="72" />
<hkern g1="germandbls" 	g2="w" 	k="63" />
<hkern g1="germandbls" 	g2="y,yacute,ydieresis" 	k="72" />
<hkern g1="germandbls" 	g2="quoteleft,quotedblleft" 	k="129" />
<hkern g1="germandbls" 	g2="quoteright,quotedblright" 	k="119" />
<hkern g1="germandbls" 	g2="quotedbl,quotesingle" 	k="76" />
<hkern g1="germandbls" 	g2="braceright" 	k="45" />
<hkern g1="germandbls" 	g2="hyphen,endash,emdash" 	k="-14" />
<hkern g1="germandbls" 	g2="x" 	k="20" />
<hkern g1="germandbls" 	g2="bracketleft" 	k="23" />
<hkern g1="eth" 	g2="parenright" 	k="-33" />
<hkern g1="eth" 	g2="question" 	k="-39" />
<hkern g1="eth" 	g2="backslash" 	k="-41" />
<hkern g1="eth" 	g2="bracketright" 	k="-45" />
<hkern g1="eth" 	g2="quoteleft,quotedblleft" 	k="-25" />
<hkern g1="eth" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="eth" 	g2="braceright" 	k="-20" />
<hkern g1="eth" 	g2="hyphen,endash,emdash" 	k="-25" />
<hkern g1="eth" 	g2="comma,period,ellipsis" 	k="61" />
<hkern g1="eth" 	g2="slash" 	k="53" />
<hkern g1="eth" 	g2="guillemotleft,guilsinglleft" 	k="-25" />
<hkern g1="thorn" 	g2="parenright" 	k="43" />
<hkern g1="thorn" 	g2="asterisk" 	k="31" />
<hkern g1="thorn" 	g2="question" 	k="51" />
<hkern g1="thorn" 	g2="bracketright" 	k="61" />
<hkern g1="thorn" 	g2="v" 	k="20" />
<hkern g1="thorn" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="thorn" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="thorn" 	g2="quoteright,quotedblright" 	k="33" />
<hkern g1="thorn" 	g2="quotedbl,quotesingle" 	k="51" />
<hkern g1="thorn" 	g2="braceright" 	k="39" />
<hkern g1="thorn" 	g2="slash" 	k="43" />
<hkern g1="thorn" 	g2="x" 	k="25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk" 	k="164" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="66" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="164" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="bracketright" 	k="53" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v" 	k="72" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="53" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="y,yacute,ydieresis" 	k="72" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotesinglbase,quotedblbase" 	k="-86" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="156" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="133" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle" 	k="104" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="comma,period,ellipsis" 	k="-61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="slash" 	k="-49" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="x" 	k="-51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="z" 	k="-33" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guillemotleft,guilsinglleft" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="colon,semicolon" 	k="-39" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="23" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="c,ccedilla" 	k="23" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="d" 	k="23" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="23" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="g" 	k="23" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="23" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="q" 	k="23" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="eth" 	k="29" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guillemotright,guilsinglright" 	k="-25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="parenleft" 	k="37" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="braceleft" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="s" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-76" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,Ccedilla" 	k="51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="G" 	k="51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="J" 	k="-68" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Q" 	k="51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="S" 	k="-39" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="168" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="133" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="W" 	k="82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="X" 	k="-76" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="205" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Z" 	k="-49" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="AE" 	k="-41" />
<hkern g1="B" 	g2="parenright" 	k="27" />
<hkern g1="B" 	g2="asterisk" 	k="25" />
<hkern g1="B" 	g2="question" 	k="41" />
<hkern g1="B" 	g2="backslash" 	k="41" />
<hkern g1="B" 	g2="bracketright" 	k="47" />
<hkern g1="B" 	g2="v" 	k="20" />
<hkern g1="B" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="B" 	g2="quotesinglbase,quotedblbase" 	k="-23" />
<hkern g1="B" 	g2="braceright" 	k="20" />
<hkern g1="B" 	g2="hyphen,endash,emdash" 	k="-23" />
<hkern g1="B" 	g2="slash" 	k="61" />
<hkern g1="B" 	g2="x" 	k="16" />
<hkern g1="B" 	g2="z" 	k="20" />
<hkern g1="B" 	g2="guillemotleft,guilsinglleft" 	k="-20" />
<hkern g1="B" 	g2="colon,semicolon" 	k="-23" />
<hkern g1="B" 	g2="guillemotright,guilsinglright" 	k="-23" />
<hkern g1="B" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="B" 	g2="T" 	k="37" />
<hkern g1="B" 	g2="V" 	k="61" />
<hkern g1="B" 	g2="W" 	k="20" />
<hkern g1="B" 	g2="X" 	k="45" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="70" />
<hkern g1="B" 	g2="AE" 	k="53" />
<hkern g1="C,Ccedilla" 	g2="parenright" 	k="-31" />
<hkern g1="C,Ccedilla" 	g2="asterisk" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="question" 	k="-33" />
<hkern g1="C,Ccedilla" 	g2="backslash" 	k="-41" />
<hkern g1="C,Ccedilla" 	g2="bracketright" 	k="-53" />
<hkern g1="C,Ccedilla" 	g2="quotesinglbase,quotedblbase" 	k="-80" />
<hkern g1="C,Ccedilla" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="C,Ccedilla" 	g2="quoteright,quotedblright" 	k="-74" />
<hkern g1="C,Ccedilla" 	g2="hyphen,endash,emdash" 	k="23" />
<hkern g1="C,Ccedilla" 	g2="comma,period,ellipsis" 	k="-47" />
<hkern g1="C,Ccedilla" 	g2="slash" 	k="-27" />
<hkern g1="C,Ccedilla" 	g2="x" 	k="-49" />
<hkern g1="C,Ccedilla" 	g2="z" 	k="-33" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="49" />
<hkern g1="C,Ccedilla" 	g2="colon,semicolon" 	k="-39" />
<hkern g1="C,Ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="c,ccedilla" 	k="25" />
<hkern g1="C,Ccedilla" 	g2="d" 	k="25" />
<hkern g1="C,Ccedilla" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="25" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="25" />
<hkern g1="C,Ccedilla" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="C,Ccedilla" 	g2="q" 	k="25" />
<hkern g1="C,Ccedilla" 	g2="eth" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="guillemotright,guilsinglright" 	k="-29" />
<hkern g1="C,Ccedilla" 	g2="ampersand" 	k="-16" />
<hkern g1="C,Ccedilla" 	g2="parenleft" 	k="25" />
<hkern g1="C,Ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="27" />
<hkern g1="C,Ccedilla" 	g2="s" 	k="-12" />
<hkern g1="C,Ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-57" />
<hkern g1="C,Ccedilla" 	g2="C,Ccedilla" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="G" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="J" 	k="-63" />
<hkern g1="C,Ccedilla" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="Q" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="S" 	k="-35" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="-43" />
<hkern g1="C,Ccedilla" 	g2="V" 	k="-41" />
<hkern g1="C,Ccedilla" 	g2="W" 	k="-39" />
<hkern g1="C,Ccedilla" 	g2="X" 	k="-53" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="-49" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="-37" />
<hkern g1="C,Ccedilla" 	g2="AE" 	k="-53" />
<hkern g1="D,Eth" 	g2="parenright" 	k="41" />
<hkern g1="D,Eth" 	g2="question" 	k="39" />
<hkern g1="D,Eth" 	g2="bracketright" 	k="41" />
<hkern g1="D,Eth" 	g2="quotesinglbase,quotedblbase" 	k="25" />
<hkern g1="D,Eth" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="D,Eth" 	g2="braceright" 	k="33" />
<hkern g1="D,Eth" 	g2="hyphen,endash,emdash" 	k="-41" />
<hkern g1="D,Eth" 	g2="comma,period,ellipsis" 	k="82" />
<hkern g1="D,Eth" 	g2="slash" 	k="106" />
<hkern g1="D,Eth" 	g2="guillemotleft,guilsinglleft" 	k="-20" />
<hkern g1="D,Eth" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="D,Eth" 	g2="J" 	k="47" />
<hkern g1="D,Eth" 	g2="T" 	k="47" />
<hkern g1="D,Eth" 	g2="V" 	k="51" />
<hkern g1="D,Eth" 	g2="W" 	k="20" />
<hkern g1="D,Eth" 	g2="X" 	k="78" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="92" />
<hkern g1="D,Eth" 	g2="Z" 	k="20" />
<hkern g1="D,Eth" 	g2="AE" 	k="123" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="quotesinglbase,quotedblbase" 	k="-82" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="quoteleft,quotedblleft" 	k="-27" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="quoteright,quotedblright" 	k="-37" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="x" 	k="-43" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,ccedilla" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="d" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="g" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="q" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="eth" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guillemotright,guilsinglright" 	k="-27" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="J" 	k="-49" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="37" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Q" 	k="37" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="V" 	k="-23" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="X" 	k="-41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Z" 	k="-23" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="AE" 	k="-37" />
<hkern g1="F" 	g2="parenright" 	k="-43" />
<hkern g1="F" 	g2="question" 	k="-43" />
<hkern g1="F" 	g2="backslash" 	k="-61" />
<hkern g1="F" 	g2="bracketright" 	k="-53" />
<hkern g1="F" 	g2="quotesinglbase,quotedblbase" 	k="129" />
<hkern g1="F" 	g2="quoteleft,quotedblleft" 	k="-82" />
<hkern g1="F" 	g2="quoteright,quotedblright" 	k="-76" />
<hkern g1="F" 	g2="hyphen,endash,emdash" 	k="-31" />
<hkern g1="F" 	g2="comma,period,ellipsis" 	k="127" />
<hkern g1="F" 	g2="slash" 	k="160" />
<hkern g1="F" 	g2="x" 	k="20" />
<hkern g1="F" 	g2="z" 	k="41" />
<hkern g1="F" 	g2="at" 	k="20" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="49" />
<hkern g1="F" 	g2="c,ccedilla" 	k="20" />
<hkern g1="F" 	g2="d" 	k="20" />
<hkern g1="F" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="20" />
<hkern g1="F" 	g2="g" 	k="20" />
<hkern g1="F" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="F" 	g2="q" 	k="20" />
<hkern g1="F" 	g2="eth" 	k="20" />
<hkern g1="F" 	g2="guillemotright,guilsinglright" 	k="23" />
<hkern g1="F" 	g2="ampersand" 	k="41" />
<hkern g1="F" 	g2="parenleft" 	k="29" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="F" 	g2="s" 	k="27" />
<hkern g1="F" 	g2="m,n,ntilde" 	k="41" />
<hkern g1="F" 	g2="p" 	k="41" />
<hkern g1="F" 	g2="r" 	k="41" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="98" />
<hkern g1="F" 	g2="C,Ccedilla" 	k="16" />
<hkern g1="F" 	g2="G" 	k="16" />
<hkern g1="F" 	g2="J" 	k="154" />
<hkern g1="F" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="F" 	g2="Q" 	k="16" />
<hkern g1="F" 	g2="T" 	k="-43" />
<hkern g1="F" 	g2="V" 	k="-53" />
<hkern g1="F" 	g2="W" 	k="-43" />
<hkern g1="F" 	g2="X" 	k="-25" />
<hkern g1="F" 	g2="Y,Yacute,Ydieresis" 	k="-57" />
<hkern g1="F" 	g2="AE" 	k="240" />
<hkern g1="F" 	g2="M" 	k="20" />
<hkern g1="G" 	g2="asterisk" 	k="53" />
<hkern g1="G" 	g2="v" 	k="23" />
<hkern g1="G" 	g2="y,yacute,ydieresis" 	k="23" />
<hkern g1="G" 	g2="quotesinglbase,quotedblbase" 	k="-47" />
<hkern g1="G" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="G" 	g2="hyphen,endash,emdash" 	k="-35" />
<hkern g1="G" 	g2="comma,period,ellipsis" 	k="-41" />
<hkern g1="G" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="G" 	g2="T" 	k="20" />
<hkern g1="G" 	g2="V" 	k="41" />
<hkern g1="G" 	g2="W" 	k="41" />
<hkern g1="G" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="H" 	g2="slash" 	k="20" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="slash" 	k="20" />
<hkern g1="J" 	g2="backslash" 	k="-20" />
<hkern g1="J" 	g2="quoteleft,quotedblleft" 	k="-31" />
<hkern g1="J" 	g2="quoteright,quotedblright" 	k="-29" />
<hkern g1="J" 	g2="slash" 	k="63" />
<hkern g1="J" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="J" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="39" />
<hkern g1="J" 	g2="J" 	k="27" />
<hkern g1="J" 	g2="AE" 	k="82" />
<hkern g1="K" 	g2="parenright" 	k="-49" />
<hkern g1="K" 	g2="asterisk" 	k="82" />
<hkern g1="K" 	g2="backslash" 	k="-41" />
<hkern g1="K" 	g2="bracketright" 	k="-57" />
<hkern g1="K" 	g2="v" 	k="94" />
<hkern g1="K" 	g2="w" 	k="94" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="94" />
<hkern g1="K" 	g2="quotesinglbase,quotedblbase" 	k="-63" />
<hkern g1="K" 	g2="hyphen,endash,emdash" 	k="102" />
<hkern g1="K" 	g2="comma,period,ellipsis" 	k="-63" />
<hkern g1="K" 	g2="slash" 	k="-47" />
<hkern g1="K" 	g2="x" 	k="-70" />
<hkern g1="K" 	g2="z" 	k="-43" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="104" />
<hkern g1="K" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="K" 	g2="at" 	k="29" />
<hkern g1="K" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="K" 	g2="c,ccedilla" 	k="82" />
<hkern g1="K" 	g2="d" 	k="82" />
<hkern g1="K" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="82" />
<hkern g1="K" 	g2="g" 	k="82" />
<hkern g1="K" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="K" 	g2="q" 	k="82" />
<hkern g1="K" 	g2="eth" 	k="70" />
<hkern g1="K" 	g2="ampersand" 	k="41" />
<hkern g1="K" 	g2="parenleft" 	k="39" />
<hkern g1="K" 	g2="t" 	k="20" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="70" />
<hkern g1="K" 	g2="braceleft" 	k="27" />
<hkern g1="K" 	g2="s" 	k="-20" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-51" />
<hkern g1="K" 	g2="C,Ccedilla" 	k="113" />
<hkern g1="K" 	g2="G" 	k="113" />
<hkern g1="K" 	g2="J" 	k="-41" />
<hkern g1="K" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="113" />
<hkern g1="K" 	g2="Q" 	k="113" />
<hkern g1="K" 	g2="T" 	k="-47" />
<hkern g1="K" 	g2="V" 	k="-37" />
<hkern g1="K" 	g2="W" 	k="-27" />
<hkern g1="K" 	g2="X" 	k="-74" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="-37" />
<hkern g1="K" 	g2="Z" 	k="-49" />
<hkern g1="K" 	g2="AE" 	k="-53" />
<hkern g1="L" 	g2="asterisk" 	k="209" />
<hkern g1="L" 	g2="question" 	k="74" />
<hkern g1="L" 	g2="backslash" 	k="164" />
<hkern g1="L" 	g2="bracketright" 	k="66" />
<hkern g1="L" 	g2="v" 	k="96" />
<hkern g1="L" 	g2="w" 	k="59" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="96" />
<hkern g1="L" 	g2="quotesinglbase,quotedblbase" 	k="-96" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="131" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="113" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="199" />
<hkern g1="L" 	g2="braceright" 	k="25" />
<hkern g1="L" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="L" 	g2="comma,period,ellipsis" 	k="-82" />
<hkern g1="L" 	g2="slash" 	k="-35" />
<hkern g1="L" 	g2="x" 	k="-59" />
<hkern g1="L" 	g2="z" 	k="-33" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="39" />
<hkern g1="L" 	g2="colon,semicolon" 	k="-49" />
<hkern g1="L" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-25" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="-43" />
<hkern g1="L" 	g2="ampersand" 	k="-20" />
<hkern g1="L" 	g2="braceleft" 	k="33" />
<hkern g1="L" 	g2="s" 	k="-16" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-63" />
<hkern g1="L" 	g2="C,Ccedilla" 	k="41" />
<hkern g1="L" 	g2="G" 	k="41" />
<hkern g1="L" 	g2="J" 	k="-63" />
<hkern g1="L" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="L" 	g2="Q" 	k="41" />
<hkern g1="L" 	g2="S" 	k="-35" />
<hkern g1="L" 	g2="T" 	k="160" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="L" 	g2="V" 	k="174" />
<hkern g1="L" 	g2="W" 	k="102" />
<hkern g1="L" 	g2="X" 	k="-63" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="205" />
<hkern g1="L" 	g2="Z" 	k="-37" />
<hkern g1="L" 	g2="AE" 	k="-63" />
<hkern g1="L" 	g2="M" 	k="-20" />
<hkern g1="L" 	g2="exclam" 	k="-20" />
<hkern g1="M" 	g2="asterisk" 	k="20" />
<hkern g1="M" 	g2="question" 	k="33" />
<hkern g1="M" 	g2="backslash" 	k="41" />
<hkern g1="M" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="M" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="M" 	g2="slash" 	k="20" />
<hkern g1="M" 	g2="T" 	k="20" />
<hkern g1="M" 	g2="V" 	k="20" />
<hkern g1="M" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="N,Ntilde" 	g2="slash" 	k="20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="parenright" 	k="41" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="question" 	k="39" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="bracketright" 	k="41" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotesinglbase,quotedblbase" 	k="25" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="braceright" 	k="27" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="hyphen,endash,emdash" 	k="-41" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,ellipsis" 	k="82" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="106" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="guillemotleft,guilsinglleft" 	k="-20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="47" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="47" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="51" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="78" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="92" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="123" />
<hkern g1="P" 	g2="parenright" 	k="16" />
<hkern g1="P" 	g2="bracketright" 	k="27" />
<hkern g1="P" 	g2="v" 	k="-41" />
<hkern g1="P" 	g2="w" 	k="-41" />
<hkern g1="P" 	g2="y,yacute,ydieresis" 	k="-41" />
<hkern g1="P" 	g2="quotesinglbase,quotedblbase" 	k="188" />
<hkern g1="P" 	g2="quoteleft,quotedblleft" 	k="-59" />
<hkern g1="P" 	g2="quoteright,quotedblright" 	k="-59" />
<hkern g1="P" 	g2="braceright" 	k="20" />
<hkern g1="P" 	g2="comma,period,ellipsis" 	k="213" />
<hkern g1="P" 	g2="slash" 	k="162" />
<hkern g1="P" 	g2="x" 	k="-25" />
<hkern g1="P" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="P" 	g2="colon,semicolon" 	k="-27" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="P" 	g2="c,ccedilla" 	k="31" />
<hkern g1="P" 	g2="d" 	k="31" />
<hkern g1="P" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="31" />
<hkern g1="P" 	g2="g" 	k="31" />
<hkern g1="P" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="P" 	g2="q" 	k="31" />
<hkern g1="P" 	g2="eth" 	k="47" />
<hkern g1="P" 	g2="guillemotright,guilsinglright" 	k="-27" />
<hkern g1="P" 	g2="ampersand" 	k="41" />
<hkern g1="P" 	g2="bracketleft" 	k="29" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="139" />
<hkern g1="P" 	g2="J" 	k="182" />
<hkern g1="P" 	g2="T" 	k="-27" />
<hkern g1="P" 	g2="X" 	k="45" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="P" 	g2="AE" 	k="256" />
<hkern g1="Q" 	g2="parenright" 	k="41" />
<hkern g1="Q" 	g2="question" 	k="39" />
<hkern g1="Q" 	g2="bracketright" 	k="41" />
<hkern g1="Q" 	g2="quotesinglbase,quotedblbase" 	k="25" />
<hkern g1="Q" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="Q" 	g2="hyphen,endash,emdash" 	k="-41" />
<hkern g1="Q" 	g2="comma,period,ellipsis" 	k="82" />
<hkern g1="Q" 	g2="slash" 	k="106" />
<hkern g1="Q" 	g2="guillemotleft,guilsinglleft" 	k="-20" />
<hkern g1="Q" 	g2="j" 	k="-31" />
<hkern g1="Q" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="Q" 	g2="J" 	k="47" />
<hkern g1="Q" 	g2="T" 	k="47" />
<hkern g1="Q" 	g2="V" 	k="51" />
<hkern g1="Q" 	g2="W" 	k="20" />
<hkern g1="Q" 	g2="X" 	k="78" />
<hkern g1="Q" 	g2="Y,Yacute,Ydieresis" 	k="92" />
<hkern g1="Q" 	g2="Z" 	k="20" />
<hkern g1="Q" 	g2="AE" 	k="123" />
<hkern g1="R" 	g2="asterisk" 	k="20" />
<hkern g1="R" 	g2="question" 	k="31" />
<hkern g1="R" 	g2="backslash" 	k="20" />
<hkern g1="R" 	g2="bracketright" 	k="47" />
<hkern g1="R" 	g2="quotesinglbase,quotedblbase" 	k="-66" />
<hkern g1="R" 	g2="quoteleft,quotedblleft" 	k="-27" />
<hkern g1="R" 	g2="quoteright,quotedblright" 	k="-23" />
<hkern g1="R" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="R" 	g2="comma,period,ellipsis" 	k="-49" />
<hkern g1="R" 	g2="slash" 	k="-37" />
<hkern g1="R" 	g2="x" 	k="-37" />
<hkern g1="R" 	g2="z" 	k="-20" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="45" />
<hkern g1="R" 	g2="colon,semicolon" 	k="-25" />
<hkern g1="R" 	g2="at" 	k="20" />
<hkern g1="R" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="51" />
<hkern g1="R" 	g2="c,ccedilla" 	k="51" />
<hkern g1="R" 	g2="d" 	k="51" />
<hkern g1="R" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="51" />
<hkern g1="R" 	g2="g" 	k="51" />
<hkern g1="R" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="R" 	g2="q" 	k="51" />
<hkern g1="R" 	g2="eth" 	k="57" />
<hkern g1="R" 	g2="t" 	k="20" />
<hkern g1="R" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-63" />
<hkern g1="R" 	g2="C,Ccedilla" 	k="37" />
<hkern g1="R" 	g2="G" 	k="37" />
<hkern g1="R" 	g2="J" 	k="-53" />
<hkern g1="R" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="37" />
<hkern g1="R" 	g2="Q" 	k="37" />
<hkern g1="R" 	g2="T" 	k="20" />
<hkern g1="R" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="16" />
<hkern g1="R" 	g2="V" 	k="31" />
<hkern g1="R" 	g2="X" 	k="-41" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="68" />
<hkern g1="R" 	g2="Z" 	k="-37" />
<hkern g1="R" 	g2="AE" 	k="-41" />
<hkern g1="S" 	g2="parenright" 	k="16" />
<hkern g1="S" 	g2="asterisk" 	k="72" />
<hkern g1="S" 	g2="question" 	k="20" />
<hkern g1="S" 	g2="v" 	k="33" />
<hkern g1="S" 	g2="w" 	k="27" />
<hkern g1="S" 	g2="y,yacute,ydieresis" 	k="33" />
<hkern g1="S" 	g2="quotesinglbase,quotedblbase" 	k="-37" />
<hkern g1="S" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="S" 	g2="hyphen,endash,emdash" 	k="-57" />
<hkern g1="S" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="S" 	g2="slash" 	k="31" />
<hkern g1="S" 	g2="x" 	k="23" />
<hkern g1="S" 	g2="guillemotleft,guilsinglleft" 	k="-31" />
<hkern g1="S" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-25" />
<hkern g1="S" 	g2="T" 	k="41" />
<hkern g1="S" 	g2="V" 	k="20" />
<hkern g1="S" 	g2="W" 	k="20" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="S" 	g2="AE" 	k="41" />
<hkern g1="T" 	g2="parenright" 	k="-51" />
<hkern g1="T" 	g2="question" 	k="-53" />
<hkern g1="T" 	g2="backslash" 	k="-82" />
<hkern g1="T" 	g2="bracketright" 	k="-61" />
<hkern g1="T" 	g2="v" 	k="154" />
<hkern g1="T" 	g2="w" 	k="154" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="154" />
<hkern g1="T" 	g2="quotesinglbase,quotedblbase" 	k="109" />
<hkern g1="T" 	g2="quoteleft,quotedblleft" 	k="-57" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="T" 	g2="hyphen,endash,emdash" 	k="113" />
<hkern g1="T" 	g2="comma,period,ellipsis" 	k="166" />
<hkern g1="T" 	g2="slash" 	k="201" />
<hkern g1="T" 	g2="x" 	k="154" />
<hkern g1="T" 	g2="z" 	k="145" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="154" />
<hkern g1="T" 	g2="colon,semicolon" 	k="125" />
<hkern g1="T" 	g2="at" 	k="74" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="154" />
<hkern g1="T" 	g2="c,ccedilla" 	k="154" />
<hkern g1="T" 	g2="d" 	k="154" />
<hkern g1="T" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="154" />
<hkern g1="T" 	g2="g" 	k="154" />
<hkern g1="T" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="154" />
<hkern g1="T" 	g2="q" 	k="154" />
<hkern g1="T" 	g2="eth" 	k="188" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="135" />
<hkern g1="T" 	g2="ampersand" 	k="41" />
<hkern g1="T" 	g2="parenleft" 	k="37" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="141" />
<hkern g1="T" 	g2="braceleft" 	k="37" />
<hkern g1="T" 	g2="s" 	k="147" />
<hkern g1="T" 	g2="m,n,ntilde" 	k="141" />
<hkern g1="T" 	g2="p" 	k="141" />
<hkern g1="T" 	g2="r" 	k="141" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="168" />
<hkern g1="T" 	g2="C,Ccedilla" 	k="47" />
<hkern g1="T" 	g2="G" 	k="47" />
<hkern g1="T" 	g2="J" 	k="170" />
<hkern g1="T" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="47" />
<hkern g1="T" 	g2="Q" 	k="47" />
<hkern g1="T" 	g2="T" 	k="-53" />
<hkern g1="T" 	g2="V" 	k="-63" />
<hkern g1="T" 	g2="W" 	k="-53" />
<hkern g1="T" 	g2="X" 	k="-53" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-70" />
<hkern g1="T" 	g2="Z" 	k="-20" />
<hkern g1="T" 	g2="AE" 	k="236" />
<hkern g1="T" 	g2="M" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="backslash" 	k="-20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="quoteleft,quotedblleft" 	k="-10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="quoteright,quotedblright" 	k="-47" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,ellipsis" 	k="61" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="slash" 	k="111" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="39" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="113" />
<hkern g1="V" 	g2="parenright" 	k="-66" />
<hkern g1="V" 	g2="question" 	k="-57" />
<hkern g1="V" 	g2="backslash" 	k="-82" />
<hkern g1="V" 	g2="bracketright" 	k="-76" />
<hkern g1="V" 	g2="quotesinglbase,quotedblbase" 	k="129" />
<hkern g1="V" 	g2="quoteleft,quotedblleft" 	k="-57" />
<hkern g1="V" 	g2="quoteright,quotedblright" 	k="-76" />
<hkern g1="V" 	g2="quotedbl,quotesingle" 	k="-37" />
<hkern g1="V" 	g2="hyphen,endash,emdash" 	k="61" />
<hkern g1="V" 	g2="comma,period,ellipsis" 	k="164" />
<hkern g1="V" 	g2="slash" 	k="193" />
<hkern g1="V" 	g2="z" 	k="25" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="V" 	g2="colon,semicolon" 	k="25" />
<hkern g1="V" 	g2="at" 	k="59" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="84" />
<hkern g1="V" 	g2="c,ccedilla" 	k="113" />
<hkern g1="V" 	g2="d" 	k="113" />
<hkern g1="V" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="113" />
<hkern g1="V" 	g2="g" 	k="113" />
<hkern g1="V" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="V" 	g2="q" 	k="113" />
<hkern g1="V" 	g2="eth" 	k="125" />
<hkern g1="V" 	g2="ampersand" 	k="61" />
<hkern g1="V" 	g2="parenleft" 	k="63" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="43" />
<hkern g1="V" 	g2="braceleft" 	k="37" />
<hkern g1="V" 	g2="s" 	k="78" />
<hkern g1="V" 	g2="m,n,ntilde" 	k="47" />
<hkern g1="V" 	g2="p" 	k="47" />
<hkern g1="V" 	g2="r" 	k="47" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="133" />
<hkern g1="V" 	g2="C,Ccedilla" 	k="51" />
<hkern g1="V" 	g2="G" 	k="51" />
<hkern g1="V" 	g2="J" 	k="178" />
<hkern g1="V" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="V" 	g2="Q" 	k="51" />
<hkern g1="V" 	g2="T" 	k="-66" />
<hkern g1="V" 	g2="V" 	k="-76" />
<hkern g1="V" 	g2="W" 	k="-66" />
<hkern g1="V" 	g2="X" 	k="-66" />
<hkern g1="V" 	g2="Y,Yacute,Ydieresis" 	k="-80" />
<hkern g1="V" 	g2="Z" 	k="-33" />
<hkern g1="V" 	g2="AE" 	k="303" />
<hkern g1="V" 	g2="M" 	k="20" />
<hkern g1="W" 	g2="parenright" 	k="-57" />
<hkern g1="W" 	g2="question" 	k="-51" />
<hkern g1="W" 	g2="comma,period,ellipsis" 	k="90" />
<hkern g1="W" 	g2="slash" 	k="127" />
<hkern g1="W" 	g2="at" 	k="23" />
<hkern g1="W" 	g2="ampersand" 	k="41" />
<hkern g1="W" 	g2="parenleft" 	k="23" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="82" />
<hkern g1="W" 	g2="C,Ccedilla" 	k="20" />
<hkern g1="W" 	g2="G" 	k="20" />
<hkern g1="W" 	g2="J" 	k="96" />
<hkern g1="W" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="W" 	g2="Q" 	k="20" />
<hkern g1="W" 	g2="T" 	k="-55" />
<hkern g1="W" 	g2="V" 	k="-66" />
<hkern g1="W" 	g2="W" 	k="-55" />
<hkern g1="W" 	g2="X" 	k="-55" />
<hkern g1="W" 	g2="Y,Yacute,Ydieresis" 	k="-72" />
<hkern g1="W" 	g2="Z" 	k="-23" />
<hkern g1="W" 	g2="backslash" 	k="-82" />
<hkern g1="W" 	g2="bracketright" 	k="-66" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="57" />
<hkern g1="W" 	g2="c,ccedilla" 	k="68" />
<hkern g1="W" 	g2="d" 	k="68" />
<hkern g1="W" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="68" />
<hkern g1="W" 	g2="g" 	k="68" />
<hkern g1="W" 	g2="m,n,ntilde" 	k="31" />
<hkern g1="W" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="68" />
<hkern g1="W" 	g2="p" 	k="31" />
<hkern g1="W" 	g2="q" 	k="68" />
<hkern g1="W" 	g2="r" 	k="31" />
<hkern g1="W" 	g2="s" 	k="33" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="29" />
<hkern g1="W" 	g2="v" 	k="-25" />
<hkern g1="W" 	g2="w" 	k="-25" />
<hkern g1="W" 	g2="x" 	k="-20" />
<hkern g1="W" 	g2="y,yacute,ydieresis" 	k="-25" />
<hkern g1="W" 	g2="quotesinglbase,quotedblbase" 	k="49" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="33" />
<hkern g1="W" 	g2="quoteleft,quotedblleft" 	k="-57" />
<hkern g1="W" 	g2="quoteright,quotedblright" 	k="-82" />
<hkern g1="W" 	g2="AE" 	k="109" />
<hkern g1="W" 	g2="eth" 	k="74" />
<hkern g1="W" 	g2="quotedbl,quotesingle" 	k="-39" />
<hkern g1="W" 	g2="braceleft" 	k="20" />
<hkern g1="X" 	g2="backslash" 	k="-61" />
<hkern g1="X" 	g2="bracketright" 	k="-63" />
<hkern g1="X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="61" />
<hkern g1="X" 	g2="c,ccedilla" 	k="61" />
<hkern g1="X" 	g2="d" 	k="61" />
<hkern g1="X" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="61" />
<hkern g1="X" 	g2="g" 	k="61" />
<hkern g1="X" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="X" 	g2="q" 	k="61" />
<hkern g1="X" 	g2="s" 	k="-20" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="49" />
<hkern g1="X" 	g2="v" 	k="49" />
<hkern g1="X" 	g2="w" 	k="49" />
<hkern g1="X" 	g2="x" 	k="-49" />
<hkern g1="X" 	g2="y,yacute,ydieresis" 	k="49" />
<hkern g1="X" 	g2="quotesinglbase,quotedblbase" 	k="-70" />
<hkern g1="X" 	g2="guillemotleft,guilsinglleft" 	k="76" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="X" 	g2="quoteright,quotedblright" 	k="-33" />
<hkern g1="X" 	g2="AE" 	k="-74" />
<hkern g1="X" 	g2="eth" 	k="43" />
<hkern g1="X" 	g2="braceleft" 	k="27" />
<hkern g1="X" 	g2="ampersand" 	k="41" />
<hkern g1="X" 	g2="parenleft" 	k="39" />
<hkern g1="X" 	g2="parenright" 	k="-53" />
<hkern g1="X" 	g2="asterisk" 	k="53" />
<hkern g1="X" 	g2="hyphen,endash,emdash" 	k="66" />
<hkern g1="X" 	g2="comma,period,ellipsis" 	k="-51" />
<hkern g1="X" 	g2="slash" 	k="-47" />
<hkern g1="X" 	g2="colon,semicolon" 	k="-35" />
<hkern g1="X" 	g2="question" 	k="-41" />
<hkern g1="X" 	g2="at" 	k="29" />
<hkern g1="X" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-74" />
<hkern g1="X" 	g2="C,Ccedilla" 	k="78" />
<hkern g1="X" 	g2="G" 	k="78" />
<hkern g1="X" 	g2="J" 	k="-51" />
<hkern g1="X" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="78" />
<hkern g1="X" 	g2="Q" 	k="78" />
<hkern g1="X" 	g2="S" 	k="-35" />
<hkern g1="X" 	g2="T" 	k="-53" />
<hkern g1="X" 	g2="V" 	k="-63" />
<hkern g1="X" 	g2="W" 	k="-53" />
<hkern g1="X" 	g2="X" 	k="-74" />
<hkern g1="X" 	g2="Y,Yacute,Ydieresis" 	k="-68" />
<hkern g1="X" 	g2="Z" 	k="-49" />
<hkern g1="X" 	g2="z" 	k="-31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="backslash" 	k="-82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="bracketright" 	k="-80" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="150" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,ccedilla" 	k="180" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d" 	k="180" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="180" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="180" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,ntilde" 	k="76" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="180" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="p" 	k="76" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="q" 	k="180" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="r" 	k="76" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="135" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="162" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteleft,quotedblleft" 	k="-51" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-63" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="295" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="eth" 	k="184" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="braceleft" 	k="31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="ampersand" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="parenleft" 	k="80" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="parenright" 	k="-70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="asterisk" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,endash,emdash" 	k="137" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,ellipsis" 	k="195" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="205" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="question" 	k="-59" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="at" 	k="121" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="205" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,Ccedilla" 	k="92" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="G" 	k="92" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="172" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="92" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Q" 	k="92" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="V" 	k="-80" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="W" 	k="-70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="X" 	k="-70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="-84" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Z" 	k="-37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="72" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="exclam" 	k="-23" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="M" 	k="31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="53" />
<hkern g1="Z" 	g2="backslash" 	k="-41" />
<hkern g1="Z" 	g2="bracketright" 	k="-43" />
<hkern g1="Z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-23" />
<hkern g1="Z" 	g2="c,ccedilla" 	k="20" />
<hkern g1="Z" 	g2="d" 	k="20" />
<hkern g1="Z" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="20" />
<hkern g1="Z" 	g2="g" 	k="20" />
<hkern g1="Z" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="Z" 	g2="q" 	k="20" />
<hkern g1="Z" 	g2="x" 	k="-49" />
<hkern g1="Z" 	g2="quotesinglbase,quotedblbase" 	k="-88" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="63" />
<hkern g1="Z" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="Z" 	g2="quoteright,quotedblright" 	k="-59" />
<hkern g1="Z" 	g2="AE" 	k="-53" />
<hkern g1="Z" 	g2="ampersand" 	k="20" />
<hkern g1="Z" 	g2="parenright" 	k="-33" />
<hkern g1="Z" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="Z" 	g2="comma,period,ellipsis" 	k="-55" />
<hkern g1="Z" 	g2="colon,semicolon" 	k="-33" />
<hkern g1="Z" 	g2="question" 	k="-31" />
<hkern g1="Z" 	g2="at" 	k="20" />
<hkern g1="Z" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-53" />
<hkern g1="Z" 	g2="C,Ccedilla" 	k="25" />
<hkern g1="Z" 	g2="G" 	k="25" />
<hkern g1="Z" 	g2="J" 	k="-53" />
<hkern g1="Z" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="25" />
<hkern g1="Z" 	g2="Q" 	k="25" />
<hkern g1="Z" 	g2="S" 	k="-25" />
<hkern g1="Z" 	g2="T" 	k="-31" />
<hkern g1="Z" 	g2="V" 	k="-43" />
<hkern g1="Z" 	g2="W" 	k="-31" />
<hkern g1="Z" 	g2="X" 	k="-53" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="-47" />
<hkern g1="Z" 	g2="Z" 	k="-27" />
<hkern g1="Z" 	g2="z" 	k="-23" />
<hkern g1="Z" 	g2="guillemotright,guilsinglright" 	k="-33" />
<hkern g1="Thorn" 	g2="backslash" 	k="41" />
<hkern g1="Thorn" 	g2="bracketright" 	k="72" />
<hkern g1="Thorn" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="Thorn" 	g2="guillemotleft,guilsinglleft" 	k="-31" />
<hkern g1="Thorn" 	g2="AE" 	k="109" />
<hkern g1="Thorn" 	g2="parenright" 	k="49" />
<hkern g1="Thorn" 	g2="hyphen,endash,emdash" 	k="-31" />
<hkern g1="Thorn" 	g2="comma,period,ellipsis" 	k="115" />
<hkern g1="Thorn" 	g2="slash" 	k="125" />
<hkern g1="Thorn" 	g2="colon,semicolon" 	k="-25" />
<hkern g1="Thorn" 	g2="question" 	k="55" />
<hkern g1="Thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="57" />
<hkern g1="Thorn" 	g2="J" 	k="84" />
<hkern g1="Thorn" 	g2="T" 	k="41" />
<hkern g1="Thorn" 	g2="V" 	k="45" />
<hkern g1="Thorn" 	g2="W" 	k="20" />
<hkern g1="Thorn" 	g2="X" 	k="72" />
<hkern g1="Thorn" 	g2="Y,Yacute,Ydieresis" 	k="78" />
<hkern g1="Thorn" 	g2="Z" 	k="47" />
<hkern g1="Thorn" 	g2="braceright" 	k="41" />
<hkern g1="parenleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="51" />
<hkern g1="parenleft" 	g2="c,ccedilla" 	k="61" />
<hkern g1="parenleft" 	g2="d" 	k="61" />
<hkern g1="parenleft" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="61" />
<hkern g1="parenleft" 	g2="g" 	k="61" />
<hkern g1="parenleft" 	g2="m,n,ntilde" 	k="43" />
<hkern g1="parenleft" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="parenleft" 	g2="p" 	k="43" />
<hkern g1="parenleft" 	g2="q" 	k="61" />
<hkern g1="parenleft" 	g2="r" 	k="43" />
<hkern g1="parenleft" 	g2="s" 	k="37" />
<hkern g1="parenleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="51" />
<hkern g1="parenleft" 	g2="v" 	k="20" />
<hkern g1="parenleft" 	g2="w" 	k="20" />
<hkern g1="parenleft" 	g2="y,yacute,ydieresis" 	k="-61" />
<hkern g1="parenleft" 	g2="eth" 	k="61" />
<hkern g1="parenleft" 	g2="parenleft" 	k="59" />
<hkern g1="parenleft" 	g2="parenright" 	k="-164" />
<hkern g1="parenleft" 	g2="asterisk" 	k="41" />
<hkern g1="parenleft" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="parenleft" 	g2="at" 	k="61" />
<hkern g1="parenleft" 	g2="C,Ccedilla" 	k="41" />
<hkern g1="parenleft" 	g2="G" 	k="41" />
<hkern g1="parenleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="parenleft" 	g2="Q" 	k="41" />
<hkern g1="parenleft" 	g2="T" 	k="-51" />
<hkern g1="parenleft" 	g2="V" 	k="-63" />
<hkern g1="parenleft" 	g2="W" 	k="-53" />
<hkern g1="parenleft" 	g2="X" 	k="-53" />
<hkern g1="parenleft" 	g2="Y,Yacute,Ydieresis" 	k="-68" />
<hkern g1="parenleft" 	g2="Z" 	k="-20" />
<hkern g1="parenleft" 	g2="j" 	k="-174" />
<hkern g1="parenleft" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="27" />
<hkern g1="parenleft" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="-25" />
<hkern g1="parenleft" 	g2="germandbls" 	k="33" />
<hkern g1="parenright" 	g2="x" 	k="29" />
<hkern g1="parenright" 	g2="AE" 	k="41" />
<hkern g1="parenright" 	g2="parenleft" 	k="47" />
<hkern g1="parenright" 	g2="asterisk" 	k="39" />
<hkern g1="parenright" 	g2="hyphen,endash,emdash" 	k="-45" />
<hkern g1="parenright" 	g2="at" 	k="27" />
<hkern g1="parenright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="parenright" 	g2="J" 	k="27" />
<hkern g1="parenright" 	g2="T" 	k="39" />
<hkern g1="parenright" 	g2="V" 	k="68" />
<hkern g1="parenright" 	g2="W" 	k="27" />
<hkern g1="parenright" 	g2="X" 	k="41" />
<hkern g1="parenright" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="parenright" 	g2="Z" 	k="23" />
<hkern g1="bracketleft" 	g2="bracketright" 	k="-184" />
<hkern g1="bracketleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="72" />
<hkern g1="bracketleft" 	g2="c,ccedilla" 	k="74" />
<hkern g1="bracketleft" 	g2="d" 	k="74" />
<hkern g1="bracketleft" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="74" />
<hkern g1="bracketleft" 	g2="g" 	k="74" />
<hkern g1="bracketleft" 	g2="m,n,ntilde" 	k="70" />
<hkern g1="bracketleft" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="74" />
<hkern g1="bracketleft" 	g2="p" 	k="70" />
<hkern g1="bracketleft" 	g2="q" 	k="74" />
<hkern g1="bracketleft" 	g2="r" 	k="70" />
<hkern g1="bracketleft" 	g2="s" 	k="68" />
<hkern g1="bracketleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="72" />
<hkern g1="bracketleft" 	g2="v" 	k="61" />
<hkern g1="bracketleft" 	g2="w" 	k="61" />
<hkern g1="bracketleft" 	g2="x" 	k="61" />
<hkern g1="bracketleft" 	g2="y,yacute,ydieresis" 	k="-61" />
<hkern g1="bracketleft" 	g2="AE" 	k="57" />
<hkern g1="bracketleft" 	g2="eth" 	k="82" />
<hkern g1="bracketleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="57" />
<hkern g1="bracketleft" 	g2="C,Ccedilla" 	k="41" />
<hkern g1="bracketleft" 	g2="G" 	k="41" />
<hkern g1="bracketleft" 	g2="J" 	k="57" />
<hkern g1="bracketleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="bracketleft" 	g2="Q" 	k="41" />
<hkern g1="bracketleft" 	g2="T" 	k="-61" />
<hkern g1="bracketleft" 	g2="V" 	k="-72" />
<hkern g1="bracketleft" 	g2="W" 	k="-61" />
<hkern g1="bracketleft" 	g2="X" 	k="-61" />
<hkern g1="bracketleft" 	g2="Y,Yacute,Ydieresis" 	k="-78" />
<hkern g1="bracketleft" 	g2="Z" 	k="-29" />
<hkern g1="bracketleft" 	g2="z" 	k="63" />
<hkern g1="bracketleft" 	g2="j" 	k="-193" />
<hkern g1="bracketleft" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="-20" />
<hkern g1="bracketright" 	g2="j" 	k="-68" />
<hkern g1="braceleft" 	g2="C,Ccedilla" 	k="25" />
<hkern g1="braceleft" 	g2="G" 	k="33" />
<hkern g1="braceleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="33" />
<hkern g1="braceleft" 	g2="Q" 	k="33" />
<hkern g1="braceleft" 	g2="braceright" 	k="-184" />
<hkern g1="braceright" 	g2="AE" 	k="27" />
<hkern g1="braceright" 	g2="J" 	k="27" />
<hkern g1="braceright" 	g2="T" 	k="37" />
<hkern g1="braceright" 	g2="V" 	k="35" />
<hkern g1="braceright" 	g2="X" 	k="27" />
<hkern g1="braceright" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="asterisk" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="35" />
<hkern g1="asterisk" 	g2="c,ccedilla" 	k="61" />
<hkern g1="asterisk" 	g2="d" 	k="61" />
<hkern g1="asterisk" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="61" />
<hkern g1="asterisk" 	g2="g" 	k="61" />
<hkern g1="asterisk" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="asterisk" 	g2="q" 	k="61" />
<hkern g1="asterisk" 	g2="s" 	k="20" />
<hkern g1="asterisk" 	g2="v" 	k="-33" />
<hkern g1="asterisk" 	g2="w" 	k="-33" />
<hkern g1="asterisk" 	g2="x" 	k="-25" />
<hkern g1="asterisk" 	g2="y,yacute,ydieresis" 	k="-33" />
<hkern g1="asterisk" 	g2="AE" 	k="285" />
<hkern g1="asterisk" 	g2="eth" 	k="96" />
<hkern g1="asterisk" 	g2="at" 	k="39" />
<hkern g1="asterisk" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="164" />
<hkern g1="asterisk" 	g2="J" 	k="213" />
<hkern g1="asterisk" 	g2="X" 	k="45" />
<hkern g1="asterisk" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="asterisk" 	g2="M" 	k="20" />
<hkern g1="asterisk" 	g2="j" 	k="20" />
<hkern g1="quotedbl,quotesingle" 	g2="AE" 	k="221" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="102" />
<hkern g1="quotedbl,quotesingle" 	g2="J" 	k="178" />
<hkern g1="quotedbl,quotesingle" 	g2="V" 	k="-35" />
<hkern g1="quotedbl,quotesingle" 	g2="W" 	k="-39" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="57" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,ccedilla" 	k="66" />
<hkern g1="quoteleft,quotedblleft" 	g2="d" 	k="86" />
<hkern g1="quoteleft,quotedblleft" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="66" />
<hkern g1="quoteleft,quotedblleft" 	g2="g" 	k="66" />
<hkern g1="quoteleft,quotedblleft" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="86" />
<hkern g1="quoteleft,quotedblleft" 	g2="q" 	k="66" />
<hkern g1="quoteleft,quotedblleft" 	g2="s" 	k="41" />
<hkern g1="quoteleft,quotedblleft" 	g2="v" 	k="-25" />
<hkern g1="quoteleft,quotedblleft" 	g2="w" 	k="-25" />
<hkern g1="quoteleft,quotedblleft" 	g2="y,yacute,ydieresis" 	k="-25" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="399" />
<hkern g1="quoteleft,quotedblleft" 	g2="eth" 	k="143" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="168" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,Ccedilla" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="G" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="195" />
<hkern g1="quoteleft,quotedblleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="Q" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="S" 	k="-33" />
<hkern g1="quoteleft,quotedblleft" 	g2="T" 	k="-39" />
<hkern g1="quoteleft,quotedblleft" 	g2="V" 	k="-57" />
<hkern g1="quoteleft,quotedblleft" 	g2="W" 	k="-68" />
<hkern g1="quoteleft,quotedblleft" 	g2="X" 	k="-29" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-53" />
<hkern g1="quoteleft,quotedblleft" 	g2="Z" 	k="-45" />
<hkern g1="quoteleft,quotedblleft" 	g2="M" 	k="41" />
<hkern g1="quoteleft,quotedblleft" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-37" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="129" />
<hkern g1="quoteright,quotedblright" 	g2="c,ccedilla" 	k="147" />
<hkern g1="quoteright,quotedblright" 	g2="d" 	k="147" />
<hkern g1="quoteright,quotedblright" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="147" />
<hkern g1="quoteright,quotedblright" 	g2="g" 	k="147" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,ntilde" 	k="86" />
<hkern g1="quoteright,quotedblright" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="147" />
<hkern g1="quoteright,quotedblright" 	g2="p" 	k="86" />
<hkern g1="quoteright,quotedblright" 	g2="q" 	k="147" />
<hkern g1="quoteright,quotedblright" 	g2="r" 	k="86" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="158" />
<hkern g1="quoteright,quotedblright" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="68" />
<hkern g1="quoteright,quotedblright" 	g2="v" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="w" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="264" />
<hkern g1="quoteright,quotedblright" 	g2="eth" 	k="150" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="201" />
<hkern g1="quoteright,quotedblright" 	g2="C,Ccedilla" 	k="76" />
<hkern g1="quoteright,quotedblright" 	g2="G" 	k="76" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="215" />
<hkern g1="quoteright,quotedblright" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="76" />
<hkern g1="quoteright,quotedblright" 	g2="Q" 	k="76" />
<hkern g1="quoteright,quotedblright" 	g2="S" 	k="41" />
<hkern g1="quoteright,quotedblright" 	g2="T" 	k="-35" />
<hkern g1="quoteright,quotedblright" 	g2="V" 	k="-55" />
<hkern g1="quoteright,quotedblright" 	g2="W" 	k="-59" />
<hkern g1="quoteright,quotedblright" 	g2="X" 	k="-29" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="-45" />
<hkern g1="quoteright,quotedblright" 	g2="Z" 	k="-35" />
<hkern g1="quoteright,quotedblright" 	g2="z" 	k="68" />
<hkern g1="quoteright,quotedblright" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="39" />
<hkern g1="quoteright,quotedblright" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="germandbls" 	k="41" />
<hkern g1="quoteright,quotedblright" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-27" />
<hkern g1="quoteright,quotedblright" 	g2="b" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="h" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="k" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="l" 	k="20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="m,n,ntilde" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="p" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="r" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="s" 	k="-23" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="v" 	k="84" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="w" 	k="59" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="x" 	k="-43" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="y,yacute,ydieresis" 	k="84" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="AE" 	k="-47" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-43" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,Ccedilla" 	k="68" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="G" 	k="68" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="J" 	k="-45" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="68" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Q" 	k="68" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="S" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="156" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="V" 	k="256" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="156" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="X" 	k="-47" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="221" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Z" 	k="-41" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="z" 	k="-49" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="j" 	k="-63" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="41" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="b" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="h" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="k" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="l" 	k="-20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="v" 	k="-35" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="w" 	k="-35" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="y,yacute,ydieresis" 	k="-35" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-23" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="J" 	k="-35" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="137" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="55" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Z" 	k="-25" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="z" 	k="-16" />
<hkern g1="guillemotright,guilsinglright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-20" />
<hkern g1="guillemotright,guilsinglright" 	g2="c,ccedilla" 	k="-20" />
<hkern g1="guillemotright,guilsinglright" 	g2="d" 	k="-20" />
<hkern g1="guillemotright,guilsinglright" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="-20" />
<hkern g1="guillemotright,guilsinglright" 	g2="g" 	k="-20" />
<hkern g1="guillemotright,guilsinglright" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="guillemotright,guilsinglright" 	g2="q" 	k="-20" />
<hkern g1="guillemotright,guilsinglright" 	g2="x" 	k="57" />
<hkern g1="guillemotright,guilsinglright" 	g2="AE" 	k="90" />
<hkern g1="guillemotright,guilsinglright" 	g2="eth" 	k="-20" />
<hkern g1="guillemotright,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="33" />
<hkern g1="guillemotright,guilsinglright" 	g2="C,Ccedilla" 	k="-20" />
<hkern g1="guillemotright,guilsinglright" 	g2="G" 	k="-20" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="61" />
<hkern g1="guillemotright,guilsinglright" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="guillemotright,guilsinglright" 	g2="Q" 	k="-20" />
<hkern g1="guillemotright,guilsinglright" 	g2="S" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="154" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="84" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="39" />
<hkern g1="guillemotright,guilsinglright" 	g2="X" 	k="78" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="164" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="49" />
<hkern g1="guillemotright,guilsinglright" 	g2="z" 	k="35" />
<hkern g1="hyphen,endash,emdash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-31" />
<hkern g1="hyphen,endash,emdash" 	g2="c,ccedilla" 	k="-25" />
<hkern g1="hyphen,endash,emdash" 	g2="d" 	k="-25" />
<hkern g1="hyphen,endash,emdash" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="-25" />
<hkern g1="hyphen,endash,emdash" 	g2="g" 	k="-25" />
<hkern g1="hyphen,endash,emdash" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-25" />
<hkern g1="hyphen,endash,emdash" 	g2="q" 	k="-25" />
<hkern g1="hyphen,endash,emdash" 	g2="w" 	k="-16" />
<hkern g1="hyphen,endash,emdash" 	g2="x" 	k="41" />
<hkern g1="hyphen,endash,emdash" 	g2="AE" 	k="92" />
<hkern g1="hyphen,endash,emdash" 	g2="eth" 	k="-25" />
<hkern g1="hyphen,endash,emdash" 	g2="C,Ccedilla" 	k="-41" />
<hkern g1="hyphen,endash,emdash" 	g2="G" 	k="-41" />
<hkern g1="hyphen,endash,emdash" 	g2="J" 	k="31" />
<hkern g1="hyphen,endash,emdash" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-41" />
<hkern g1="hyphen,endash,emdash" 	g2="Q" 	k="-41" />
<hkern g1="hyphen,endash,emdash" 	g2="T" 	k="113" />
<hkern g1="hyphen,endash,emdash" 	g2="V" 	k="61" />
<hkern g1="hyphen,endash,emdash" 	g2="X" 	k="66" />
<hkern g1="hyphen,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="137" />
<hkern g1="hyphen,endash,emdash" 	g2="Z" 	k="20" />
<hkern g1="hyphen,endash,emdash" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="-43" />
<hkern g1="comma,period,ellipsis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="c,ccedilla" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="d" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="g" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="q" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="v" 	k="117" />
<hkern g1="comma,period,ellipsis" 	g2="w" 	k="113" />
<hkern g1="comma,period,ellipsis" 	g2="x" 	k="-41" />
<hkern g1="comma,period,ellipsis" 	g2="y,yacute,ydieresis" 	k="117" />
<hkern g1="comma,period,ellipsis" 	g2="AE" 	k="-51" />
<hkern g1="comma,period,ellipsis" 	g2="eth" 	k="41" />
<hkern g1="comma,period,ellipsis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-61" />
<hkern g1="comma,period,ellipsis" 	g2="C,Ccedilla" 	k="82" />
<hkern g1="comma,period,ellipsis" 	g2="G" 	k="82" />
<hkern g1="comma,period,ellipsis" 	g2="J" 	k="-82" />
<hkern g1="comma,period,ellipsis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="82" />
<hkern g1="comma,period,ellipsis" 	g2="Q" 	k="82" />
<hkern g1="comma,period,ellipsis" 	g2="S" 	k="-27" />
<hkern g1="comma,period,ellipsis" 	g2="T" 	k="166" />
<hkern g1="comma,period,ellipsis" 	g2="V" 	k="168" />
<hkern g1="comma,period,ellipsis" 	g2="W" 	k="86" />
<hkern g1="comma,period,ellipsis" 	g2="X" 	k="-51" />
<hkern g1="comma,period,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="195" />
<hkern g1="comma,period,ellipsis" 	g2="Z" 	k="-45" />
<hkern g1="comma,period,ellipsis" 	g2="z" 	k="-37" />
<hkern g1="comma,period,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="61" />
<hkern g1="colon,semicolon" 	g2="v" 	k="-29" />
<hkern g1="colon,semicolon" 	g2="w" 	k="-29" />
<hkern g1="colon,semicolon" 	g2="x" 	k="-27" />
<hkern g1="colon,semicolon" 	g2="y,yacute,ydieresis" 	k="-29" />
<hkern g1="colon,semicolon" 	g2="AE" 	k="-31" />
<hkern g1="colon,semicolon" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-35" />
<hkern g1="colon,semicolon" 	g2="J" 	k="-49" />
<hkern g1="colon,semicolon" 	g2="T" 	k="127" />
<hkern g1="colon,semicolon" 	g2="V" 	k="27" />
<hkern g1="colon,semicolon" 	g2="X" 	k="-31" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="78" />
<hkern g1="colon,semicolon" 	g2="Z" 	k="-25" />
<hkern g1="colon,semicolon" 	g2="z" 	k="-20" />
<hkern g1="backslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="51" />
<hkern g1="backslash" 	g2="c,ccedilla" 	k="51" />
<hkern g1="backslash" 	g2="d" 	k="51" />
<hkern g1="backslash" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="51" />
<hkern g1="backslash" 	g2="g" 	k="51" />
<hkern g1="backslash" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="backslash" 	g2="q" 	k="51" />
<hkern g1="backslash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="backslash" 	g2="v" 	k="61" />
<hkern g1="backslash" 	g2="w" 	k="41" />
<hkern g1="backslash" 	g2="y,yacute,ydieresis" 	k="-123" />
<hkern g1="backslash" 	g2="AE" 	k="-41" />
<hkern g1="backslash" 	g2="eth" 	k="41" />
<hkern g1="backslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-41" />
<hkern g1="backslash" 	g2="C,Ccedilla" 	k="82" />
<hkern g1="backslash" 	g2="G" 	k="82" />
<hkern g1="backslash" 	g2="J" 	k="-20" />
<hkern g1="backslash" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="82" />
<hkern g1="backslash" 	g2="Q" 	k="82" />
<hkern g1="backslash" 	g2="T" 	k="184" />
<hkern g1="backslash" 	g2="V" 	k="225" />
<hkern g1="backslash" 	g2="W" 	k="143" />
<hkern g1="backslash" 	g2="X" 	k="-41" />
<hkern g1="backslash" 	g2="Y,Yacute,Ydieresis" 	k="225" />
<hkern g1="backslash" 	g2="Z" 	k="-20" />
<hkern g1="backslash" 	g2="M" 	k="61" />
<hkern g1="backslash" 	g2="j" 	k="-287" />
<hkern g1="backslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="82" />
<hkern g1="backslash" 	g2="B" 	k="61" />
<hkern g1="backslash" 	g2="D,Eth" 	k="61" />
<hkern g1="backslash" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="61" />
<hkern g1="backslash" 	g2="F" 	k="61" />
<hkern g1="backslash" 	g2="H" 	k="61" />
<hkern g1="backslash" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="61" />
<hkern g1="backslash" 	g2="K" 	k="61" />
<hkern g1="backslash" 	g2="L" 	k="61" />
<hkern g1="backslash" 	g2="N,Ntilde" 	k="61" />
<hkern g1="backslash" 	g2="P" 	k="61" />
<hkern g1="backslash" 	g2="R" 	k="61" />
<hkern g1="backslash" 	g2="t" 	k="20" />
<hkern g1="slash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="80" />
<hkern g1="slash" 	g2="c,ccedilla" 	k="119" />
<hkern g1="slash" 	g2="d" 	k="119" />
<hkern g1="slash" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="119" />
<hkern g1="slash" 	g2="g" 	k="119" />
<hkern g1="slash" 	g2="m,n,ntilde" 	k="66" />
<hkern g1="slash" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="119" />
<hkern g1="slash" 	g2="p" 	k="66" />
<hkern g1="slash" 	g2="q" 	k="119" />
<hkern g1="slash" 	g2="r" 	k="66" />
<hkern g1="slash" 	g2="s" 	k="63" />
<hkern g1="slash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="39" />
<hkern g1="slash" 	g2="AE" 	k="246" />
<hkern g1="slash" 	g2="eth" 	k="96" />
<hkern g1="slash" 	g2="at" 	k="72" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="133" />
<hkern g1="slash" 	g2="C,Ccedilla" 	k="49" />
<hkern g1="slash" 	g2="G" 	k="49" />
<hkern g1="slash" 	g2="J" 	k="145" />
<hkern g1="slash" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="49" />
<hkern g1="slash" 	g2="Q" 	k="49" />
<hkern g1="slash" 	g2="T" 	k="-88" />
<hkern g1="slash" 	g2="V" 	k="-98" />
<hkern g1="slash" 	g2="W" 	k="-88" />
<hkern g1="slash" 	g2="X" 	k="-68" />
<hkern g1="slash" 	g2="Y,Yacute,Ydieresis" 	k="-102" />
<hkern g1="slash" 	g2="Z" 	k="-35" />
<hkern g1="slash" 	g2="z" 	k="20" />
<hkern g1="slash" 	g2="j" 	k="-31" />
<hkern g1="slash" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="-33" />
<hkern g1="slash" 	g2="b" 	k="-35" />
<hkern g1="slash" 	g2="h" 	k="-35" />
<hkern g1="slash" 	g2="k" 	k="-35" />
<hkern g1="slash" 	g2="l" 	k="-35" />
<hkern g1="ampersand" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="ampersand" 	g2="c,ccedilla" 	k="41" />
<hkern g1="ampersand" 	g2="d" 	k="41" />
<hkern g1="ampersand" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="41" />
<hkern g1="ampersand" 	g2="g" 	k="41" />
<hkern g1="ampersand" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="ampersand" 	g2="q" 	k="41" />
<hkern g1="ampersand" 	g2="v" 	k="76" />
<hkern g1="ampersand" 	g2="w" 	k="61" />
<hkern g1="ampersand" 	g2="x" 	k="-20" />
<hkern g1="ampersand" 	g2="y,yacute,ydieresis" 	k="76" />
<hkern g1="ampersand" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-61" />
<hkern g1="ampersand" 	g2="C,Ccedilla" 	k="41" />
<hkern g1="ampersand" 	g2="G" 	k="41" />
<hkern g1="ampersand" 	g2="J" 	k="-41" />
<hkern g1="ampersand" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="ampersand" 	g2="Q" 	k="41" />
<hkern g1="ampersand" 	g2="T" 	k="164" />
<hkern g1="ampersand" 	g2="V" 	k="143" />
<hkern g1="ampersand" 	g2="W" 	k="82" />
<hkern g1="ampersand" 	g2="X" 	k="-20" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ydieresis" 	k="205" />
<hkern g1="exclamdown" 	g2="T" 	k="129" />
<hkern g1="exclamdown" 	g2="V" 	k="68" />
<hkern g1="exclamdown" 	g2="W" 	k="20" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="100" />
<hkern g1="questiondown" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="80" />
<hkern g1="questiondown" 	g2="c,ccedilla" 	k="82" />
<hkern g1="questiondown" 	g2="d" 	k="82" />
<hkern g1="questiondown" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="82" />
<hkern g1="questiondown" 	g2="g" 	k="82" />
<hkern g1="questiondown" 	g2="m,n,ntilde" 	k="68" />
<hkern g1="questiondown" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="questiondown" 	g2="p" 	k="68" />
<hkern g1="questiondown" 	g2="q" 	k="82" />
<hkern g1="questiondown" 	g2="r" 	k="68" />
<hkern g1="questiondown" 	g2="s" 	k="76" />
<hkern g1="questiondown" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="78" />
<hkern g1="questiondown" 	g2="v" 	k="47" />
<hkern g1="questiondown" 	g2="w" 	k="47" />
<hkern g1="questiondown" 	g2="x" 	k="63" />
<hkern g1="questiondown" 	g2="y,yacute,ydieresis" 	k="-31" />
<hkern g1="questiondown" 	g2="AE" 	k="147" />
<hkern g1="questiondown" 	g2="eth" 	k="88" />
<hkern g1="questiondown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="86" />
<hkern g1="questiondown" 	g2="C,Ccedilla" 	k="82" />
<hkern g1="questiondown" 	g2="G" 	k="82" />
<hkern g1="questiondown" 	g2="J" 	k="113" />
<hkern g1="questiondown" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="82" />
<hkern g1="questiondown" 	g2="Q" 	k="82" />
<hkern g1="questiondown" 	g2="S" 	k="70" />
<hkern g1="questiondown" 	g2="T" 	k="215" />
<hkern g1="questiondown" 	g2="V" 	k="127" />
<hkern g1="questiondown" 	g2="W" 	k="86" />
<hkern g1="questiondown" 	g2="X" 	k="127" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="160" />
<hkern g1="questiondown" 	g2="Z" 	k="102" />
<hkern g1="questiondown" 	g2="z" 	k="68" />
<hkern g1="questiondown" 	g2="M" 	k="41" />
<hkern g1="questiondown" 	g2="j" 	k="-141" />
<hkern g1="questiondown" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="68" />
<hkern g1="questiondown" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="68" />
<hkern g1="questiondown" 	g2="germandbls" 	k="68" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="68" />
<hkern g1="questiondown" 	g2="b" 	k="68" />
<hkern g1="questiondown" 	g2="h" 	k="68" />
<hkern g1="questiondown" 	g2="k" 	k="68" />
<hkern g1="questiondown" 	g2="l" 	k="68" />
<hkern g1="questiondown" 	g2="B" 	k="63" />
<hkern g1="questiondown" 	g2="D,Eth" 	k="63" />
<hkern g1="questiondown" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="63" />
<hkern g1="questiondown" 	g2="F" 	k="63" />
<hkern g1="questiondown" 	g2="H" 	k="63" />
<hkern g1="questiondown" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="63" />
<hkern g1="questiondown" 	g2="K" 	k="63" />
<hkern g1="questiondown" 	g2="L" 	k="63" />
<hkern g1="questiondown" 	g2="N,Ntilde" 	k="63" />
<hkern g1="questiondown" 	g2="P" 	k="63" />
<hkern g1="questiondown" 	g2="R" 	k="63" />
<hkern g1="questiondown" 	g2="t" 	k="70" />
<hkern g1="at" 	g2="x" 	k="20" />
<hkern g1="at" 	g2="AE" 	k="113" />
<hkern g1="at" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="at" 	g2="J" 	k="35" />
<hkern g1="at" 	g2="T" 	k="74" />
<hkern g1="at" 	g2="V" 	k="59" />
<hkern g1="at" 	g2="W" 	k="23" />
<hkern g1="at" 	g2="X" 	k="51" />
<hkern g1="at" 	g2="Y,Yacute,Ydieresis" 	k="86" />
<hkern g1="at" 	g2="Z" 	k="25" />
</font>
</defs></svg> 