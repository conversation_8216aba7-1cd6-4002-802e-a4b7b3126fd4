import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import {
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { BusinessOwner } from '../../model/business-owner';
import { LoanRequest } from '../../model/loan-request';
import { LoanRequestService } from '../../service/loan-request.service';
import { ActivatedRoute, Router } from '@angular/router';
import { BusinessService } from '../../service/business.service';
import { Business } from '../../model/business';
import { IdResponse } from '../../model/id-response';
import { LOAN_PAGES } from '../../layouts/loan/loan-pages';
import { AppValidators } from '../../../shared/validators/validators';
import { LanguageService } from '../../../language/language.service';

import * as HttpStatus from 'http-status-codes';
import { ApiErrorsResponse } from '../../../shared/models/api-errors-response';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'business-owners',
  templateUrl: './business-owners.component.html',
  styleUrls: ['./business-owners.component.scss'],
})
export class BusinessOwnersComponent implements OnInit {
  @ViewChild('BusinessOwnerstemplate')
  formTemplate: TemplateRef<any>;

  modalRef: BsModalRef;
  isEmptyList = true;

  businessOwners: Map<number, BusinessOwner>;

  loanRequest: LoanRequest;

  businessOwnersForm: UntypedFormGroup;
  ownersAsArray: BusinessOwner[];
  apiErrorResponse: ApiErrorsResponse;

  private business: Business;

  constructor(
    private modalService: BsModalService,
    private fb: UntypedFormBuilder,
    private languageService: LanguageService,
    private loanRequestService: LoanRequestService,
    private businessService: BusinessService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit() {
    this.loanRequest = this.loanRequestService.getCurrentLoanRequest();
    this.business = this.loanRequest.business;
    this.businessOwners = new Map<number, BusinessOwner>();
    this.business.owners.forEach((owner: BusinessOwner) =>
      this.businessOwners.set(owner.id, owner)
    );
    this.createForm();
    this.updateOwnersAsArray();
  }

  openBzModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(
      template,
      Object.assign({}, { class: 'bz-modal' })
    );
  }

  createForm() {
    this.businessOwnersForm = this.fb.group({
      id: [],
      documents: [],
      person: new UntypedFormGroup({
        id: new UntypedFormControl(),
        firstName: new UntypedFormControl(null, Validators.required),
        lastName: new UntypedFormControl(null, Validators.required),
        cellPhone: new UntypedFormControl(null, Validators.required),
        identity: new UntypedFormControl(null, [
          Validators.required,
          AppValidators.tzValidator,
        ]),
        gender: new UntypedFormControl(null, Validators.required),
        birthDate: new UntypedFormControl(null, Validators.required),
        jobTitle: new UntypedFormControl(null, Validators.required),
        businessEmail: new UntypedFormControl(null, [
          AppValidators.getValidator('email'),
          Validators.required,
        ]),
        personalEmail: new UntypedFormControl(null),
        address: new UntypedFormGroup({
          countryCode: new UntypedFormControl('il'),
          id: new UntypedFormControl(),
          city: new UntypedFormControl(null, Validators.required),
          street: new UntypedFormControl(null, Validators.required),
          streetNumber: new UntypedFormControl(null, Validators.required),
          zipcode: new UntypedFormControl(
            null,
            AppValidators.getValidator('zip-code')
          ),
        }),
      }),
      ownershipPercentage: [null, Validators.required],
    });
  }

  saveOrCreateBusinessOwner() {
    let businessOwnerToSave = {} as BusinessOwner;
    businessOwnerToSave = this.businessOwnersForm.value;
    businessOwnerToSave.ownershipPercentage =
      +this.businessOwnersForm.value.ownershipPercentage / 100;
    businessOwnerToSave.person.address.countryCode = 'il';
    businessOwnerToSave.person.address.city =
      this.businessOwnersForm.get('person.address.city').value.name ||
      this.businessOwnersForm.get('person.address.city').value;
    businessOwnerToSave.person.address.street =
      this.businessOwnersForm.get('person.address.street').value.name ||
      this.businessOwnersForm.get('person.address.city').value;

    const birthDate = this.businessOwnersForm.get('person.birthDate').value;

    const tzOffset = birthDate.getTimezoneOffset() * 60000;
    const date = new Date(birthDate - tzOffset);
    businessOwnerToSave.person.birthDate = date
      .toISOString()
      .split('T')
      .shift();

    let success: (res?: IdResponse | string) => void;
    const fail = (res: HttpErrorResponse) => {
      if (res.status === HttpStatus.UNPROCESSABLE_ENTITY) {
        this.apiErrorResponse = res.error;
      }
    };

    if (this.businessOwnersForm.value.id) {
      success = () => {
        this.updateBusinessOwnersListAfterEditing(businessOwnerToSave);
      };
    } else {
      success = (res: IdResponse) => {
        businessOwnerToSave.id = res.id;
        this.updateBusinessOwnersListAfterEditing(businessOwnerToSave);
      };
    }

    if (this.businessOwnersForm.value.id) {
      this.businessService
        .updateBusinessOwner(this.business.id, businessOwnerToSave)
        .subscribe(success, fail);
    } else {
      this.businessService
        .createBusinessOwner(this.business.id, businessOwnerToSave)
        .subscribe(success, fail);
    }
  }

  toggleUpdateBusinessOwner(ownerId: number) {
    const ownerToEdit = this.businessOwners.get(ownerId);
    if (!ownerToEdit.person.personalEmail) {
      ownerToEdit.person.personalEmail = null;
    }
    if (!ownerToEdit.person.address.zipcode) {
      ownerToEdit.person.address.zipcode = null;
    }
    if (ownerToEdit.ownershipPercentage <= 1) {
      ownerToEdit.ownershipPercentage = ownerToEdit.ownershipPercentage * 100;
    }
    if (!ownerToEdit.person.gender) {
      ownerToEdit.person.gender = null;
    }
    if (!ownerToEdit.person.birthDate) {
      ownerToEdit.person.birthDate = null;
    } else {
      ownerToEdit.person.birthDate = new Date(
        Date.parse(ownerToEdit.person.birthDate as string)
      );
    }

    this.businessOwnersForm.setValue(ownerToEdit);
    this.openBzModal(this.formTemplate);
  }

  toggleCancelUpdateBusinessOwner(event: Event) {
    this.businessOwnersForm.reset();
    this.modalRef.hide();
  }

  deleteBusinessOwner(ownerId: number) {
    this.businessService
      .deleteBusinessOwner(this.business.id, ownerId)
      .subscribe(() => {
        this.businessOwners.delete(ownerId);
        this.updateOwnersAsArray();
        if (this.businessOwners.size === 0) {
          this.isEmptyList = true;
        }
      });
  }

  submit() {
    this.router.navigate([LOAN_PAGES[7]], {
      relativeTo: this.route.parent.parent,
    });
  }

  private updateBusinessOwnersListAfterEditing(businessOwner: BusinessOwner) {
    this.businessOwners.set(businessOwner.id, businessOwner);
    this.businessOwnersForm.reset();
    this.updateOwnersAsArray();
    this.isEmptyList = false;
    this.modalRef.hide();
  }

  private updateOwnersAsArray() {
    this.ownersAsArray = Array.from(this.businessOwners.values());
  }
}
