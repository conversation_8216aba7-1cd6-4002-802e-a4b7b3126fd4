@import 'styles/vendors/bootstrap/share';
@import 'styles/variables';

.photo-row {
  margin-bottom: 20px;
  font-size: 14px;
  @include media-breakpoint-up(md) {
    margin-bottom: 40px;
  }
}

.photo-box {
  //overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 37px;
  height: 80px;
  width: 80px;
  position: relative;
  border: 1px solid #979797;
  color: #d2d2d2;
  border-radius: 50%;
  background: #f3f3f3;
}

.description-text {
  font-size: 12px;
  color: #b2b2b2;
  display: block;
}

:host ::ng-deep .has-counter {
  position: relative;

  .descr-counter {
    position: absolute;
    right: 0;
    top: 0;

    .rtl & {
      right: auto;
      left: 0;
    }
  }
}
