import { Component } from '@angular/core';
import { Location } from '@angular/common';
import { Router } from '@angular/router';
import { GlobalService } from '~tarya-shared/services/global.service';
import { PAGES } from '~tarya/app.routes';
import { DomainDataDto } from '~tarya-layouts/app-component/domain-data';
import { DomainService } from '~tarya-layouts/app-component/domain.service';
import { LanguageSwitchService } from '~tarya-language/language-switch.service';

import { TranslationsService } from '~tarya/modules/core/services/translations.service';
import { LenderUserService } from '~tarya-lender/services/lender-user.service';
import { User } from '~tarya/modules/core/models/user';
import { LoginService } from '~tarya/modules/core/services/login.service';

@Component({
  selector: 'tar-appheader',
  templateUrl: './header.html',
  styleUrls: ['./header.scss'],
})
export class AppHeader {
  readonly loginAndRecoverPages = [
    `/${PAGES.loginPath}`,
    `/${PAGES.recoverPath}`,
  ];

  loginPath = PAGES.loginPath;
  homePageUrl: string;
  contactPage: string;
  lang: string;

  user: User;
  userName: string;
  loginState: boolean;
  userId: string;
  isTransactions = true;
  userRole: string;

  constructor(
    private languageSwitchService: LanguageSwitchService,
    private translationsService: TranslationsService,
    private domainService: DomainService,
    public global: GlobalService,
    private loginService: LoginService,
    private router: Router,
    private lenderService: LenderUserService
  ) {
    this.lang = this.languageSwitchService.currentLanguage;
  }

  trans(sentence: string) {
    return this.translationsService.getTranslations('header', sentence);
  }

  ngOnInit() {
    this.languageSwitchService.onLanguageChange.subscribe((lang) => {
      this.lang = lang;
    });
    if (window.location === window.parent.location) {
      this.loginService.isUserLoggedOut().subscribe((state) => {
        this.getUserInfoData(state);
      });
    }

    this.getDomainData();
    this.contactPage = PAGES.contact_us;
  }

  isFileUploadPage(): boolean {
    return this.router.url.indexOf('file-upload/workflow') !== -1;
  }

  getDomainData() {
    this.domainService.getDomainData().subscribe((response: DomainDataDto) => {
      this.homePageUrl = response.homePage;
    });
  }

  getUserInfoData(state: boolean) {
    const isFileUpload = this.isFileUploadPage();
    this.loginState = state;
    if (!this.loginState && !isFileUpload) {
      this.loginService.getUser().subscribe((data) => {
        this.user = data;
        this.userName = data.firstName;
        this.userId = data.userId;
        this.userRole = data.userRole;
        if (data.userRole === 'LENDER') {
          this.getLender();
        }
      });
    }
  }

  switchToEnglish() {
    this.languageSwitchService.changeLanguage('en');
  }

  switchToHebrew() {
    this.languageSwitchService.changeLanguage('he');
  }

  isLoginOrRecoverPages(): boolean {
    return this.loginAndRecoverPages.indexOf(this.router.url) >= 0;
  }

  goToHomePage() {
    this.loginService.navigateToPageByUserRole(this.user);
  }

  goToIndexPage() {
    window.location.href = this.homePageUrl;
  }

  logout() {
    this.loginService.logout().subscribe((data) => {
      if (data) {
        sessionStorage.clear();
        window.location.href = '/login.php';
      }
    });
  }

  isHomePage(): boolean {
    return this.router.url === '/';
  }

  getLender() {
    this.lenderService.getUserDetails().subscribe((details) => {
      if (details && details.transactions && details.transactions.length > 0) {
        this.isTransactions = true;
      } else {
        this.isTransactions = false;
      }
    });
  }
}
