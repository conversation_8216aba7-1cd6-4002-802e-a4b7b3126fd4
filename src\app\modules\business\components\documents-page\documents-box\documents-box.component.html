<div class="upload-inner">
  <div class="upload-col">
      <div class="upload-content">
        <h6>{{'loan-request.documents.' + documentTitle | translate }}</h6>
        <div class="content-wrap" [formGroup]="parentFilesForm">
        <div *ngFor="let document of documentsInfo" class="d-flex">
            <div class="form-group">
                <div class="checkbox-row">
                    <input 
                      class="checkbox"
                      type="checkbox"
                      formControlName="checkedFile" 
                      (change)="onChange(document.type, $event)"
                      [required]="!document.type.isDocOptional"
                      [checked]="document.type.checked">
                </div>
            </div>
            <p>{{document.type.he}}<span class="required-document" *ngIf="!document.type.isDocOptional"> * </span><p>
        </div>
        </div>
      </div>
  </div>
  <div class="row" *ngIf="fileStackConfig">
      <app-file-picker
        class="col-lg-9 col-6 icon-cloud"
        [apiKey]="fileStackConfig.fileStackApiKey" 
        [clientOptions]="clientOptions" 
        [pickerOptions]="pickerOptions"
        (onUploadIsDone)="onUploadIsDone($event)"
        (onUploadError)="showError()">
      </app-file-picker>
      <span class="col-lg-3 col-5" [ngClass]="{'uploaded': isUploaded}">{{ (!isUploaded  ? 'loan-request.documents.no-uploaded'  : 'loan-request.documents.uploaded') | translate }}</span>
  </div>
  <p class="uploadError" *ngIf="uploadError">
    {{ 'loan-request.documents.erroUpload' | translate }}
  </p>
</div>
