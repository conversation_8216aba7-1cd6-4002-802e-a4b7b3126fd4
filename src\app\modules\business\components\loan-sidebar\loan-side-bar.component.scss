@import 'styles/vendors/bootstrap/share';
@import 'styles/variables';

.steps-progress-bar-holder {
  border: 1px solid #dbdbdb;
  background-color: rgba(248, 251, 255, 0.43);
  padding: 30px 15px;
  color: #575757;
}

h6 {
  color: inherit;
  font-size: 14px;
  line-height: 14px;
  margin-bottom: 15px;
}

.details-list {
  padding: 25px 0;
  @include media-breakpoint-up(md) {
    font-size: 16px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  li {
    margin-bottom: 4px;

    span {
      display: inline-block;
      vertical-align: top;
    }

    strong {
      color: #000;
      padding: 0 5px;
      display: inline-block;
      vertical-align: top;
    }
  }
}

.content-row {
  border-bottom: 1px solid #d0d0d0;
  text-align: center;

  p {
    margin: 0;

    &:first-child {
      font-size: 16px;
    }

    strong {
      color: #000;
    }
  }
}

.add-info {
  padding: 25px 0;

  p {
    margin: 0;
  }

  .link-span-red {
    color: #ff0000;
  }
}

:host ::ng-deep .btn.btn-secondary {
  font-weight: bold;
  padding: 15px;
  font-size: 16px;
  display: block;
  width: 100%;
  color: #fff;
  background: #00b28d;
  border: 1px solid #00b28d;

  &:disabled {
    color: #979797;
    border: 1px solid #b1b1b1;
    background: transparent;
  }
}

.engage-holder {
  text-align: center;
  padding: 10px 0;
  margin: 0 -10px;
}

.wrapper-gauge {
  position: relative;
  display: inline-block;
  vertical-align: top;
  margin: 0 5px 10px;
  width: 110px;
  height: 110px;

  label {
    width: 100%;
    position: absolute;
    left: 0;
    top: 70px;
  }
}
.meter {
  width: 100%;
  height: 100%;
  transform: rotateX(180deg);
}
.circle {
  fill: none;
}

#mask {
  stroke: #f1f1f1;
  stroke-width: 65;
}

.outline {
  stroke: rgba(248, 251, 255, 0.43);
  stroke-width: 10;
}

.gauge-arrow {
  transition: all 0.3s ease-in-out;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 2px 24px 2px;
  margin: 0 1px 0 -1px;
  border-color: transparent transparent #000 transparent;
  position: absolute;
  left: 50%;
  z-index: 3;
  bottom: 57px;
  transform-origin: bottom center;
}
.active strong {
  color: #ff0000 !important;
}
