import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FileUploadInfo } from './general-files-info.class';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { EnumService } from '../../service/enum.service';
import { LoanRequestService } from '../../service/loan-request.service';
import { ActivatedRoute } from '@angular/router';
import { FileUploadStatus } from './document-uploader/file-upload-status';
import { Subscription } from 'rxjs';
import { RequiredDocumentResponse } from '../../model/required-documents-response';
import { TranslateService } from '@ngx-translate/core';
import { LanguageService } from '../../../language/language.service';

@Component({
  selector: 'app-signature-uploaded',
  templateUrl: './documents-general.component.html',
  styleUrls: ['./documents-general.component.scss'],
})
export class DocumentsGeneralComponent implements OnInit, OnDestroy {
  uploadFilesInfo: FileUploadInfo[] = [];
  uploadFileForm: UntypedFormGroup;
  uploadFileRadio: any;
  documentsTypeTranslationKey: string;

  private id: number;
  private fileUploadEndpoint: string;
  private routeSubscription: Subscription;
  private onTranslationChangeSubscription: Subscription;
  private requiredDocuments: RequiredDocumentResponse;

  constructor(
    private fb: UntypedFormBuilder,
    private enumService: EnumService,
    private loanRequestService: LoanRequestService,
    private route: ActivatedRoute,
    private languageService: LanguageService,
    private translateService: TranslateService
  ) {}

  ngOnInit() {
    this.createForm();
    this.routeSubscription = this.route.data.subscribe((result) => {
      this.requiredDocuments = result.requiredDocuments;
      if (result.documents.entity === 'business')
        this.id = this.loanRequestService.getCurrentLoanRequest().business.id;
      else if (result.documents.entity === 'loan-request')
        this.id = this.loanRequestService.getCurrentLoanRequest().id;
      this.documentsTypeTranslationKey = result.documents.translationKey;
      this.fileUploadEndpoint = result.documents.fileUploadResource;
      this.fillFilesUploadInfo();
    });
    this.onTranslationChangeSubscription =
      this.translateService.onTranslationChange.subscribe(() =>
        this.fillFilesUploadInfo()
      );
  }

  ngOnDestroy() {
    this.routeSubscription.unsubscribe();
    this.onTranslationChangeSubscription.unsubscribe();
  }

  fillFilesUploadInfo() {
    const currentLang = this.languageService.currentLang;
    for (const requiredDocument of this.requiredDocuments.requiredDocuments) {
      this.uploadFilesInfo.push({
        fileTitle: requiredDocument.type[currentLang],
        isDocOptional: requiredDocument.type.isDocOptional,
        fileStatus: requiredDocument.isUploaded
          ? FileUploadStatus.FILE_IS_BEING_CHECKED
          : FileUploadStatus.FILE_WAS_NOT_UPLOADED,
        typeEnumModelId: requiredDocument.type.id,
        uploadFileForm: this.uploadFileForm,
        uploadUrl: this.fileUploadEndpoint,
        id: this.id,
      });
    }
  }

  createForm() {
    this.uploadFileForm = this.fb.group({
      uploadFileRadio: [this.uploadFileRadio, [Validators.required]],
    });
  }
}
