<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="ubunturegular" horiz-adv-x="1155" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="473" />
<glyph unicode="&#xfb01;" horiz-adv-x="1316" d="M164 0v1161q0 205 100.5 316.5t315.5 111.5q72 0 123 -9t73 -17l-26 -164q-23 8 -62 17t-94 9q-133 0 -186.5 -72.5t-53.5 -195.5v-92h410v-160h-410v-905h-190zM934 1382q0 57 36 91t87 34t87 -33.5t36 -91.5q0 -57 -36 -91t-87 -34t-87 34t-36 91zM963 0v1065h190 v-1065h-190z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1357" d="M164 0v1161q0 205 100.5 316.5t315.5 111.5q72 0 123 -9t73 -17l-26 -164q-23 8 -62 17t-94 9q-133 0 -186.5 -72.5t-53.5 -195.5v-92h410v-160h-410v-905h-190zM958 279v1277l191 33v-1280q0 -47 8 -77.5t26.5 -49t49.5 -28t76 -15.5l-27 -159q-176 4 -250 75.5 t-74 223.5z" />
<glyph unicode="&#xfb03;" horiz-adv-x="2115" d="M164 0v1161q0 205 100.5 316.5t315.5 111.5q84 0 144 -12t85 -23l-35 -163q-25 12 -70 23t-110 11q-133 0 -186.5 -72.5t-53.5 -195.5v-92h410v-160h-410v-905h-190zM963 0v1161q0 205 100.5 316.5t315.5 111.5q72 0 123 -9t73 -17l-26 -164q-23 8 -62 17t-94 9 q-133 0 -186.5 -72.5t-53.5 -195.5v-92h410v-160h-410v-905h-190zM1733 1382q0 57 36 91t87 34t87 -33.5t36 -91.5q0 -57 -36 -91t-87 -34t-87 34t-36 91zM1762 0v1065h190v-1065h-190z" />
<glyph unicode="&#xfb04;" horiz-adv-x="2156" d="M164 0v1161q0 205 100.5 316.5t315.5 111.5q84 0 144 -12t85 -23l-35 -163q-25 12 -70 23t-110 11q-133 0 -186.5 -72.5t-53.5 -195.5v-92h410v-160h-410v-905h-190zM963 0v1161q0 205 100.5 316.5t315.5 111.5q72 0 123 -9t73 -17l-26 -164q-23 8 -62 17t-94 9 q-133 0 -186.5 -72.5t-53.5 -195.5v-92h410v-160h-410v-905h-190zM1757 279v1277l191 33v-1280q0 -47 8 -77.5t26.5 -49t49.5 -28t76 -15.5l-27 -159q-176 4 -250 75.5t-74 223.5z" />
<glyph unicode="&#xd;" horiz-adv-x="473" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode=" "  horiz-adv-x="473" />
<glyph unicode="&#x09;" horiz-adv-x="473" />
<glyph unicode="&#xa0;" horiz-adv-x="473" />
<glyph unicode="!" horiz-adv-x="565" d="M145 111q0 55 37 96t101 41q63 0 100 -41t37 -96t-37 -96.5t-100 -41.5t-100.5 41t-37.5 97zM180 1034v385h205v-385q0 -172 -12.5 -312t-30.5 -280h-119q-18 139 -30.5 279.5t-12.5 312.5z" />
<glyph unicode="&#x22;" horiz-adv-x="856" d="M147 1456v100h199v-102q0 -100 -12 -209.5t-31 -218.5h-113q-18 109 -30.5 218.5t-12.5 211.5zM509 1456v100h199v-102q0 -100 -12 -209.5t-31 -218.5h-113q-18 109 -30.5 218.5t-12.5 211.5z" />
<glyph unicode="#" horiz-adv-x="1366" d="M100 375v151h209l70 367h-279v151h310l71 375h172l-71 -375h331l72 375h172l-72 -375h181v-151h-211l-70 -367h281v-151h-310l-71 -375h-172l71 375h-331l-72 -375h-172l72 375h-181zM481 526h332l72 367h-332z" />
<glyph unicode="$" d="M125 119l51 153q61 -29 147.5 -53t213.5 -24q158 0 221 50t63 128q0 57 -27.5 98t-73.5 71.5t-106.5 55.5t-124.5 47q-61 23 -121.5 50.5t-107.5 67.5t-76.5 96t-29.5 138q0 145 87 238.5t253 118.5v235h170v-229q90 -4 167.5 -22.5t122.5 -34.5l-39 -160 q-49 18 -122.5 37.5t-184.5 19.5q-123 0 -189.5 -47t-66.5 -133q0 -47 19.5 -80t56.5 -58.5t87 -46t112 -42.5q80 -31 151.5 -64t125.5 -79t87 -109.5t33 -153.5q0 -145 -88 -234.5t-272 -111.5v-264h-170v258q-143 4 -233.5 31.5t-135.5 52.5z" />
<glyph unicode="%" horiz-adv-x="1757" d="M102 1073q0 92 25 162t69 118t103 72.5t129 24.5t129 -24.5t103.5 -72.5t69 -118t24.5 -162t-24.5 -161.5t-69 -118t-103.5 -73t-129 -24.5t-129 24.5t-103 73t-69 118t-25 161.5zM262 1073q0 -111 43 -173t123 -62t123 62t43 173t-43 173.5t-123 62.5t-123 -62.5 t-43 -173.5zM385 0l801 1419h186l-801 -1419h-186zM1004 346q0 92 24.5 162t68.5 118t103.5 72.5t128.5 24.5q70 0 129.5 -24.5t103.5 -72.5t68.5 -118t24.5 -162t-24.5 -161.5t-68.5 -118t-103.5 -73t-129.5 -24.5t-129 24.5t-103 73t-68.5 118t-24.5 161.5zM1163 346 q0 -111 43 -173t123 -62t123 62t43 173t-43 173.5t-123 62.5t-123 -62.5t-43 -173.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1363" d="M94 377q0 80 24.5 142.5t66.5 112.5t97.5 88t114.5 66q-152 152 -151 314q0 82 27.5 147.5t75.5 110.5t114.5 69.5t146.5 24.5q82 0 146.5 -24.5t108.5 -65.5t66.5 -98.5t22.5 -120.5q0 -72 -23.5 -129.5t-63.5 -104.5t-91 -86t-108 -69l331 -338q70 129 91 286l157 -20 q-8 -84 -42 -183.5t-95 -197.5q72 -78 132.5 -153.5t98.5 -147.5h-200q-25 41 -61 84t-74 86q-84 -84 -195 -136t-254 -52q-133 0 -221 37.5t-142.5 96t-77 128.5t-22.5 133zM285 381q0 -37 14 -80t47 -79t85 -59.5t130 -23.5q100 0 185 37t151 105l-399 403 q-37 -18 -75 -43t-68.5 -60.5t-50 -84.5t-19.5 -115zM424 1114q0 -133 139 -260q45 23 85 49.5t70 61t47 77t17 95.5q0 70 -46 118t-126 48q-90 0 -138 -54.5t-48 -134.5z" />
<glyph unicode="'" horiz-adv-x="493" d="M147 1456v100h199v-102q0 -100 -12 -209.5t-31 -218.5h-113q-18 109 -30.5 218.5t-12.5 211.5z" />
<glyph unicode="(" horiz-adv-x="663" d="M160 612q0 291 97 544t257 448l135 -93q-147 -190 -223 -413.5t-76 -485.5t76 -485t223 -414l-135 -92q-160 195 -257 448t-97 543z" />
<glyph unicode=")" horiz-adv-x="663" d="M14 -287q147 190 223 413.5t76 485.5t-75.5 485.5t-223.5 413.5l136 93q160 -195 257 -448t97 -544t-97.5 -543.5t-256.5 -447.5z" />
<glyph unicode="*" horiz-adv-x="983" d="M76 1055l57 172l12 -4q84 -29 156 -68t145 -80q-16 84 -31.5 163t-15.5 165v16h185v-16q0 -86 -15.5 -165t-31.5 -163q74 41 145.5 80t155.5 68l12 4l57 -172l-16 -6q-82 -31 -164 -40.5t-168 -15.5q68 -57 127 -111.5t111 -125.5l10 -15l-150 -104l-8 12 q-49 74 -85 146.5t-72 150.5q-37 -78 -73 -150.5t-85 -146.5l-8 -12l-150 104l10 15q51 72 110.5 126t127.5 111q-86 6 -168 15.5t-164 40.5z" />
<glyph unicode="+" d="M109 532v162h387v422h163v-422h388v-162h-388v-423h-163v423h-387z" />
<glyph unicode="," horiz-adv-x="503" d="M78 -279q51 104 67.5 212t16.5 202q0 27 -1 51.5t-3 46.5h207q2 -16 2 -31.5v-31.5q0 -129 -38 -255t-104 -241z" />
<glyph unicode="-" horiz-adv-x="612" d="M53 524v178h506v-178h-506z" />
<glyph unicode="." horiz-adv-x="503" d="M115 111q0 55 36.5 96t100.5 41q63 0 100 -41t37 -96t-37 -96.5t-100 -41.5t-100 41t-37 97z" />
<glyph unicode="/" horiz-adv-x="786" d="M-49 -379l694 1983h191l-691 -1983h-194z" />
<glyph unicode="0" d="M102 711q0 356 126 548.5t350 192.5q223 0 349 -192.5t126 -548.5t-126 -549t-349 -193t-349.5 193t-126.5 549zM303 711q0 -117 13.5 -221.5t45 -182.5t85 -124t131.5 -46t131 46t84.5 124t45 182.5t13.5 221.5t-13.5 221t-45 182t-85 124t-130.5 46q-78 0 -131.5 -46 t-85 -124t-45 -182.5t-13.5 -220.5z" />
<glyph unicode="1" d="M190 1126q117 45 227.5 116t203.5 177h135v-1419h-191v1155q-25 -23 -60.5 -47.5t-77.5 -47t-88 -43t-89 -34.5z" />
<glyph unicode="2" d="M115 1282q18 20 55 50t88 56.5t114.5 45t137.5 18.5q223 0 334.5 -103.5t111.5 -291.5q0 -74 -29.5 -142.5t-78.5 -135t-110.5 -131t-123.5 -126.5q-35 -35 -81 -82t-86.5 -98t-68.5 -98t-28 -82h654v-162h-861q-2 12 -2 24.5v22.5q0 96 32 179t83 157t114.5 139.5 t125.5 126.5q51 49 97 97.5t82 96.5t57.5 99t21.5 109q0 61 -19.5 104t-52.5 71.5t-77 42t-93 13.5q-59 0 -108.5 -16.5t-88.5 -38t-65.5 -45t-40.5 -35.5z" />
<glyph unicode="3" d="M113 43l37 166q35 -16 115.5 -43t197.5 -27q182 0 261 72t79 192q0 78 -33 131.5t-86 85t-124 45t-144 13.5h-49v156h67q51 0 105.5 10t99.5 36.5t73.5 72.5t28.5 116q0 57 -20.5 98t-54 68t-77.5 38t-93 11q-98 0 -167 -28.5t-116 -59.5l-74 145q25 16 63 36t85 36.5 t101 27.5t114 11q113 0 194.5 -27.5t135 -78t80 -119t26.5 -148.5q0 -111 -63.5 -189.5t-153.5 -119.5q55 -16 106.5 -47t89 -78t61 -108.5t23.5 -137.5q0 -92 -31.5 -170.5t-98 -136t-167 -90.5t-237.5 -33q-53 0 -109.5 8.5t-105.5 20.5t-86 24.5t-53 20.5z" />
<glyph unicode="4" d="M82 362v130q39 90 106.5 209.5t152.5 246.5t182.5 250t195.5 221h182v-901h168v-156h-168v-362h-184v362h-635zM266 518h451v676q-61 -66 -124 -146.5t-122 -168.5t-111.5 -180.5t-93.5 -180.5z" />
<glyph unicode="5" d="M133 43l37 166q35 -16 113.5 -43t193.5 -27q92 0 154.5 20.5t101.5 55.5t56.5 81t17.5 97q0 78 -26.5 138.5t-94.5 101.5t-180.5 62.5t-284.5 21.5q12 100 19.5 188t13.5 172t10 167t8 175h684v-162h-518q-2 -31 -6 -83t-8 -108t-8 -109.5t-8 -84.5q309 -12 453.5 -135 t144.5 -334q0 -94 -32 -173.5t-96.5 -137t-163 -90.5t-233.5 -33q-53 0 -108.5 8.5t-102.5 20.5t-83.5 24.5t-53.5 20.5z" />
<glyph unicode="6" d="M129 569q0 207 57.5 366t163 267.5t256 164.5t338.5 58l14 -159q-121 -2 -220 -27t-177 -78t-131 -138t-80 -210q53 25 120 41t138 16q121 0 205 -36.5t137.5 -97t77 -139.5t23.5 -165q0 -78 -27 -160t-81 -149.5t-138 -110.5t-199 -43q-233 0 -355 159.5t-122 440.5z M328 569q0 -90 12 -170t44 -140t85 -96t135 -36q68 0 115 28.5t77.5 73.5t44 97.5t13.5 101.5q0 139 -66.5 213t-199.5 74q-72 0 -133.5 -13.5t-122.5 -39.5q-2 -23 -3 -46.5t-1 -46.5z" />
<glyph unicode="7" d="M123 1253v166h903v-157q-68 -78 -149.5 -214.5t-154.5 -305.5t-125 -360.5t-65 -381.5h-198q12 164 58 345t113.5 352t148.5 317.5t159 238.5h-690z" />
<glyph unicode="8" d="M113 358q0 59 19.5 113.5t51 102t73.5 85t89 66.5q-201 115 -201 338q0 78 31 148.5t87 124t136 85t179 31.5q115 0 196.5 -35t134 -89t76 -119.5t23.5 -127.5q0 -59 -17.5 -111t-47.5 -97.5t-67.5 -80t-78.5 -61.5q241 -114 245 -362q0 -86 -29.5 -159t-88 -127 t-146.5 -84t-203 -30q-133 0 -222 38t-142 95.5t-75.5 126t-22.5 129.5zM305 356q0 -37 13.5 -76.5t46 -73.5t84 -56.5t129.5 -22.5q70 0 122 20.5t84.5 52t49 72.5t16.5 84q0 66 -24.5 115t-70.5 86t-110.5 62.5t-146.5 44.5q-92 -51 -142.5 -127.5t-50.5 -180.5zM336 1077 q0 -45 16.5 -90t51 -84t91 -69.5t134.5 -49.5q86 51 137 119.5t51 179.5q0 31 -13 68t-42 68.5t-74 53t-110 21.5q-66 0 -112 -20.5t-75 -52t-42 -69.5t-13 -75z" />
<glyph unicode="9" d="M104 991q0 76 27 158t81 149.5t138 110.5t199 43q117 0 206 -41t149.5 -119t91 -189.5t30.5 -250.5q0 -422 -210 -638t-623 -218l-7 160q129 0 232.5 24.5t181.5 78.5t130 140t77 211q-53 -25 -121 -40t-139 -15q-121 0 -205 36t-137 96.5t-77 139t-24 164.5zM301 995 q0 -139 66.5 -211.5t199.5 -72.5q72 0 135.5 13t122.5 40q2 23 2 44v44q0 88 -12 169t-44 141.5t-85 96t-135 35.5q-68 0 -115 -28.5t-77.5 -72.5t-44 -96.5t-13.5 -101.5z" />
<glyph unicode=":" horiz-adv-x="503" d="M115 111q0 55 36.5 96t100.5 41q63 0 100 -41t37 -96t-37 -96.5t-100 -41.5t-100 41t-37 97zM115 928q0 55 36.5 96t100.5 41q63 0 100 -41t37 -96t-37 -96.5t-100 -41.5t-100 41t-37 97z" />
<glyph unicode=";" horiz-adv-x="503" d="M78 -279q51 104 67.5 212t16.5 202q0 27 -1 51.5t-3 46.5h207q2 -16 2 -31.5v-31.5q0 -129 -38 -255t-104 -241zM115 928q0 55 36.5 96t100.5 41q63 0 100 -41t37 -96t-37 -96.5t-100 -41.5t-100 41t-37 97z" />
<glyph unicode="&#x3c;" d="M109 530v164l884 375l54 -158l-728 -299l728 -299l-54 -157z" />
<glyph unicode="=" d="M109 313v162h938v-162h-938zM109 750v161h938v-161h-938z" />
<glyph unicode="&#x3e;" d="M109 313l727 299l-727 299l53 158l885 -375v-164l-885 -374z" />
<glyph unicode="?" horiz-adv-x="827" d="M39 1370q70 37 157 59.5t185 22.5q117 0 191.5 -31.5t119.5 -81t62.5 -110t17.5 -115.5q0 -70 -26.5 -124t-65.5 -101t-86 -90t-86 -89t-65.5 -100.5t-26.5 -122.5v-22t2 -23h-160q-8 41 -8 86q0 66 23.5 119t60.5 99.5t78 86t77.5 80.5t60 86t23.5 99q0 86 -55.5 140 t-161.5 54q-135 0 -264 -69zM211 111q0 55 37 96t100 41t100 -41t37 -96t-36.5 -96.5t-100.5 -41.5q-63 0 -100 41t-37 97z" />
<glyph unicode="@" horiz-adv-x="1945" d="M133 569q0 221 69.5 387t187.5 275.5t272.5 165t326.5 55.5q168 0 317.5 -54.5t262 -157.5t178 -249.5t65.5 -331.5q0 -139 -31.5 -245.5t-85 -179t-127 -109.5t-155.5 -37q-66 0 -108.5 18.5t-71.5 49.5q-45 -25 -108.5 -42.5t-127.5 -17.5q-98 0 -179 31t-140.5 91.5 t-92 148.5t-32.5 204q0 98 30.5 185.5t91 152t147.5 101.5t198 37q104 0 191 -16.5t135 -33.5v-626q0 -66 22.5 -92.5t58.5 -26.5q53 0 93.5 37t66 96t37.5 134t12 153q0 129 -45 242.5t-129 199.5t-203.5 135t-269.5 49t-274.5 -50t-214.5 -143t-140.5 -228.5t-50.5 -307.5 q0 -180 53.5 -316t148.5 -227.5t225 -137.5t286 -46q121 0 199.5 14.5t101.5 22.5l20 -151q-35 -12 -126 -26.5t-195 -14.5q-182 0 -344 52t-283 159.5t-191.5 274.5t-70.5 396zM745 571q0 -145 70 -231t189 -86q43 0 87 9t78 28q-6 27 -8 53.5t-2 50.5v482q-12 4 -47 8 t-78 4q-145 0 -217 -90t-72 -228z" />
<glyph unicode="A" horiz-adv-x="1357" d="M16 0q82 225 154 416.5t140.5 363.5t136 329t141.5 310h182q74 -154 141.5 -310.5t136 -328.5t140.5 -363.5t153 -416.5h-217q-35 92 -65.5 181t-63.5 181h-643l-129 -362h-207zM410 526h528q-66 178 -130.5 345t-133.5 321q-72 -154 -136.5 -320.5t-127.5 -345.5z" />
<glyph unicode="B" horiz-adv-x="1316" d="M176 23v1376q41 10 90 16t99.5 10.5t99.5 6.5t92 2q123 0 230.5 -18.5t186.5 -62.5t124 -116t45 -176q0 -117 -55.5 -191.5t-147.5 -111.5q125 -37 198.5 -117t73.5 -225q0 -213 -156.5 -320.5t-496.5 -107.5q-43 0 -93 2t-100.5 6t-99.5 10t-90 17zM373 162q23 -2 53 -4 q27 -2 62.5 -3t82.5 -1q88 0 167 11t138.5 39.5t95.5 80t36 129.5q0 70 -27 118t-77 76.5t-119.5 41t-155.5 12.5h-256v-500zM373 821h209q74 0 139 10.5t113.5 37t76 69.5t27.5 109q0 61 -28.5 103t-79 67.5t-118 38t-143.5 12.5t-118.5 -2t-77.5 -6v-439z" />
<glyph unicode="C" horiz-adv-x="1269" d="M119 711q0 178 54 316t146.5 232.5t215 143.5t262.5 49q88 0 158.5 -12.5t122.5 -28.5t85 -32.5t45 -24.5l-57 -164q-18 12 -53 26.5t-79 29t-96.5 23.5t-105.5 9q-113 0 -203 -39t-153.5 -112.5t-97 -179t-33.5 -236.5q0 -127 29.5 -231.5t89 -179.5t148.5 -116t208 -41 q137 0 227 29t135 51l52 -164q-14 -10 -51.5 -25.5t-92.5 -29.5t-129 -24.5t-160 -10.5q-150 0 -272.5 49.5t-210.5 143.5t-136 232t-48 317z" />
<glyph unicode="D" horiz-adv-x="1460" d="M176 23v1376q84 20 187.5 27.5t189.5 7.5q174 0 319.5 -44t250 -133.5t161.5 -225.5t57 -320t-57 -320.5t-161.5 -225.5t-250 -133t-319.5 -44q-86 0 -189.5 7t-187.5 28zM375 168q18 -2 64 -4t130 -2q283 0 422 144.5t139 404.5t-139 404.5t-422 144.5q-84 0 -130 -2.5 t-64 -4.5v-1085z" />
<glyph unicode="E" horiz-adv-x="1169" d="M176 0v1419h866v-170h-667v-422h594v-165h-594v-492h719v-170h-918z" />
<glyph unicode="F" horiz-adv-x="1099" d="M176 0v1419h856v-170h-657v-428h583v-168h-583v-653h-199z" />
<glyph unicode="G" horiz-adv-x="1376" d="M119 711q0 178 54 316t146.5 232.5t216 143.5t265.5 49q96 0 171 -12.5t128 -28.5t87 -32.5t46 -24.5l-64 -166q-59 39 -157.5 64.5t-200.5 25.5q-109 0 -198 -39t-151.5 -112.5t-97 -179t-34.5 -236.5q0 -127 29.5 -231.5t90 -179.5t149.5 -116t210 -41q86 0 146.5 9.5 t86.5 17.5v528h199v-663q-25 -8 -71 -18.5t-106 -20.5t-132 -17.5t-146 -7.5q-150 0 -272.5 49.5t-210.5 143.5t-136 231t-48 316z" />
<glyph unicode="H" horiz-adv-x="1443" d="M176 0v1419h199v-592h694v592h199v-1419h-199v655h-694v-655h-199z" />
<glyph unicode="I" horiz-adv-x="550" d="M176 0v1419h199v-1419h-199z" />
<glyph unicode="J" horiz-adv-x="1024" d="M16 84l78 158q43 -31 111.5 -65t171.5 -34q147 0 214.5 75t67.5 257v944h199v-962q0 -102 -21.5 -190.5t-75.5 -155t-145.5 -104.5t-228.5 -38q-70 0 -128 11.5t-105.5 29t-82 37t-55.5 37.5z" />
<glyph unicode="K" horiz-adv-x="1288" d="M176 0v1419h199v-626q72 68 157.5 152.5t169.5 170.5t159 165t126 138h240q-63 -70 -143 -154.5t-167.5 -174t-173.5 -175.5t-161 -159q88 -61 186 -151.5t192.5 -193t177.5 -209t138 -202.5h-234q-61 96 -141 193.5t-169 186.5t-180 165t-177 129v-674h-199z" />
<glyph unicode="L" horiz-adv-x="1062" d="M176 0v1419h199v-1247h655v-172h-854z" />
<glyph unicode="M" horiz-adv-x="1783" d="M137 0q8 184 18.5 371.5t24 369t28.5 353.5t34 325h174q55 -90 118.5 -213t127 -257t123 -268t108.5 -245q49 111 108.5 245t123 268t127 257t118.5 213h174q70 -686 105 -1419h-195q-10 287 -24.5 568.5t-36.5 523.5q-20 -35 -54 -100.5t-73 -148.5t-83 -176.5 t-83 -181.5t-73 -163.5t-54 -124.5h-160q-20 49 -54 124.5t-73 163.5t-83 181.5t-83 176.5t-72.5 148.5t-54.5 100.5q-23 -242 -37 -523.5t-24 -568.5h-195z" />
<glyph unicode="N" horiz-adv-x="1490" d="M176 0v1419h158q96 -102 205.5 -238.5t217 -280.5t203 -283.5t160.5 -247.5v1050h195v-1419h-166q-45 76 -103.5 170t-126 195.5t-140 206t-144.5 201.5t-139.5 183t-124.5 152v-1108h-195z" />
<glyph unicode="O" horiz-adv-x="1593" d="M119 711q0 182 54 321t147.5 232.5t217.5 140.5t263 47q137 0 259 -47t214 -140.5t146.5 -232.5t54.5 -321t-54.5 -321.5t-146.5 -233t-214 -140.5t-259 -47q-139 0 -263 47t-217.5 140.5t-147.5 233t-54 321.5zM328 711q0 -129 32.5 -234.5t93 -179.5t147.5 -114 t196 -40t194.5 40t146 114t93.5 179.5t33 234.5t-33 234.5t-93.5 179t-146.5 113.5t-194 40q-109 0 -196 -40t-147.5 -113.5t-93 -179t-32.5 -234.5z" />
<glyph unicode="P" horiz-adv-x="1245" d="M176 0v1399q84 20 187.5 27.5t189.5 7.5q299 0 460 -114t161 -339q0 -123 -44.5 -210t-127 -141.5t-201.5 -79t-269 -24.5h-157v-526h-199zM375 696h149q102 0 184.5 13.5t138.5 45t87 87t31 141.5q0 82 -32 135t-86 85t-126 44.5t-152 12.5q-127 0 -194 -7v-557z" />
<glyph unicode="Q" horiz-adv-x="1593" d="M119 711q0 182 54 321t147.5 232.5t217.5 140.5t263 47q137 0 259 -47t214 -140.5t146.5 -232.5t54.5 -321q0 -160 -41 -285t-114 -216t-170 -147.5t-208 -76.5q2 -47 42 -83t101.5 -60.5t137 -41t151.5 -24.5l-47 -156q-106 14 -203.5 38t-176.5 62.5t-132 99t-72 150.5 q-129 8 -243.5 60.5t-198.5 145.5t-133 226.5t-49 307.5zM328 711q0 -129 32.5 -234.5t93 -179.5t147.5 -114t196 -40t194.5 40t146 114t93.5 179.5t33 234.5t-33 234.5t-93.5 179t-146.5 113.5t-194 40q-109 0 -196 -40t-147.5 -113.5t-93 -179t-32.5 -234.5z" />
<glyph unicode="R" horiz-adv-x="1288" d="M176 0v1399q84 20 187.5 27.5t189.5 7.5q299 0 455.5 -113t156.5 -336q0 -141 -75 -241.5t-220 -149.5q33 -41 83.5 -107.5t103.5 -147.5t105 -169t89 -170h-217q-41 78 -89 158t-97 152.5t-97.5 136t-86.5 110.5q-27 -2 -54.5 -2h-56.5h-178v-555h-199zM375 719h141 q102 0 184 10t138.5 39t87 81t30.5 138q0 80 -30.5 133.5t-82.5 84t-123 43t-151 12.5q-127 0 -194 -7v-534z" />
<glyph unicode="S" horiz-adv-x="1089" d="M68 70l59 163q47 -27 143.5 -59.5t235.5 -32.5q311 0 311 213q0 66 -27.5 112t-74.5 80t-107.5 58.5t-128.5 48.5q-78 27 -147.5 61t-120.5 80t-81 109.5t-30 153.5q0 186 127 290.5t351 104.5q129 0 234.5 -27.5t154.5 -60.5l-64 -162q-43 27 -128 52.5t-197 25.5 q-57 0 -106.5 -12.5t-86.5 -37t-58.5 -62.5t-21.5 -89q0 -57 22.5 -96t63.5 -68.5t95.5 -54t119.5 -49.5q92 -37 169 -74t133.5 -88t87 -121.5t30.5 -171.5q0 -186 -136 -286.5t-384 -100.5q-84 0 -154.5 11.5t-126 27t-95.5 32.5t-62 30z" />
<glyph unicode="T" horiz-adv-x="1157" d="M33 1247v172h1091v-172h-446v-1247h-199v1247h-446z" />
<glyph unicode="U" horiz-adv-x="1409" d="M166 528v891h199v-868q0 -213 94 -309.5t246 -96.5q76 0 138 24t107 73t69.5 125.5t24.5 183.5v868h199v-891q0 -119 -31.5 -221t-98 -177t-168 -118t-240.5 -43t-242 43t-168.5 118t-97 177.5t-31.5 220.5z" />
<glyph unicode="V" horiz-adv-x="1343" d="M20 1419h220q111 -324 217 -622.5t219 -560.5q113 260 219 560t215 623h213q-78 -225 -148.5 -416.5t-138 -363.5t-134 -328.5t-136.5 -310.5h-186q-70 154 -136.5 310.5t-135 328.5t-140.5 363.5t-148 416.5z" />
<glyph unicode="W" horiz-adv-x="1902" d="M57 1419h209q29 -160 59.5 -319.5t63.5 -310t65.5 -287.5t67.5 -254q82 215 174 472t172 531h172q80 -274 174.5 -531t176.5 -472q33 115 65.5 252t64 287.5t62.5 311.5t59 320h203q-74 -410 -156.5 -758t-189.5 -661h-199q-90 221 -176 455t-172 508q-86 -274 -174 -508 t-176 -455h-199q-109 313 -191.5 661.5t-154.5 757.5z" />
<glyph unicode="X" horiz-adv-x="1292" d="M47 0q92 180 214 366.5t259 383.5l-454 669h229l350 -528l346 528h228l-447 -661q139 -199 262 -387.5t217 -370.5h-223q-31 61 -74 136t-94 156t-106.5 162t-106.5 152q-51 -72 -106.5 -152.5t-105.5 -161.5t-94 -156t-75 -136h-219z" />
<glyph unicode="Y" horiz-adv-x="1224" d="M12 1419h234q78 -168 174 -341t198 -333q100 160 196.5 333t176.5 341h221q-111 -205 -235.5 -416.5t-263.5 -429.5v-573h-199v569q-141 219 -266 432t-236 418z" />
<glyph unicode="Z" horiz-adv-x="1173" d="M68 0v133q43 82 101 180.5t124.5 200.5t138.5 206.5t142.5 201t137 180.5t122.5 147h-730v170h973v-157q-47 -53 -109.5 -128t-132 -166t-143.5 -192.5t-144.5 -205t-134 -206t-114.5 -194.5h799v-170h-1030z" />
<glyph unicode="[" horiz-adv-x="673" d="M197 -379v1983h460v-150h-282v-1683h282v-150h-460z" />
<glyph unicode="\" horiz-adv-x="786" d="M-49 1604h190l695 -1983h-195z" />
<glyph unicode="]" horiz-adv-x="673" d="M16 -229h283v1683h-283v150h461v-1983h-461v150z" />
<glyph unicode="^" d="M96 723l398 696h168l397 -696l-152 -78l-329 578l-330 -578z" />
<glyph unicode="_" horiz-adv-x="1007" d="M-8 -217h1024v-162h-1024v162z" />
<glyph unicode="`" horiz-adv-x="770" d="M125 1468l123 121l262 -315l-96 -86z" />
<glyph unicode="a" horiz-adv-x="1069" d="M88 317q0 88 36 151.5t97.5 102.5t143 57.5t172.5 18.5q29 0 59.5 -3t58 -8t48 -9t28.5 -6v53q0 47 -10 93t-37 82t-73 57.5t-119 21.5q-94 0 -165 -13.5t-106 -27.5l-22 157q37 16 123 32t186 16q115 0 193.5 -30t127 -84t69 -129t20.5 -165v-666q-25 -4 -69 -11 t-99.5 -13t-120 -11.5t-127.5 -5.5q-90 0 -166 18.5t-131 58.5t-86 105.5t-31 157.5zM281 319q0 -102 65 -142t178 -40q68 0 120 3t87 12v317q-20 10 -66.5 17.5t-111.5 7.5q-43 0 -91 -6.5t-88 -26t-66.5 -53t-26.5 -89.5z" />
<glyph unicode="b" horiz-adv-x="1206" d="M164 31v1525l190 33v-571q35 23 105.5 47.5t163.5 24.5q115 0 203.5 -41t150 -115t93.5 -176.5t32 -225.5q0 -129 -38 -232t-107.5 -175t-168 -111t-221.5 -39q-133 0 -235.5 18.5t-167.5 37.5zM354 166q29 -8 81 -15.5t130 -7.5q154 0 246 101.5t92 287.5 q0 82 -16.5 154t-53 124t-95 82t-140.5 30q-78 0 -143.5 -27t-100.5 -55v-674z" />
<glyph unicode="c" horiz-adv-x="952" d="M104 530q0 123 36 225.5t101.5 177.5t161 117t211.5 42q72 0 143.5 -12.5t137.5 -39.5l-43 -161q-43 20 -99.5 32.5t-119.5 12.5q-160 0 -245 -100.5t-85 -293.5q0 -86 19.5 -157.5t61.5 -122.5t107.5 -79t159.5 -28q76 0 137.5 14.5t96.5 30.5l26 -159 q-16 -10 -46.5 -19.5t-69.5 -16.5t-83 -12.5t-85 -5.5q-129 0 -226.5 41t-164 115t-99.5 175.5t-33 223.5z" />
<glyph unicode="d" horiz-adv-x="1206" d="M104 532q0 123 32 225.5t93.5 176.5t150.5 115t204 41q92 0 162.5 -25t105.5 -47v538l190 33v-1558q-66 -18 -168 -37t-235 -19q-123 0 -221.5 39t-168 111t-107.5 175t-38 232zM303 532q0 -186 92 -287.5t246 -101.5q78 0 130 7.5t81 15.5v674q-35 29 -100.5 55.5 t-143.5 26.5q-82 0 -140 -30t-95 -82t-53.5 -124t-16.5 -154z" />
<glyph unicode="e" horiz-adv-x="1144" d="M104 530q0 141 41 247t109 175.5t156 104.5t180 35q215 0 329.5 -134.5t114.5 -408.5v-31.5t-2 -36.5h-729q12 -166 96 -252t263 -86q100 0 168.5 17.5t103.5 34.5l27 -160q-35 -18 -122 -39t-198 -21q-139 0 -240.5 42t-167 116t-97.5 175t-32 222zM307 635h529v7 q0 124 -64 205q-66 83 -184 83q-66 0 -116 -25.5t-85 -66.5t-54.5 -94.5t-25.5 -108.5z" />
<glyph unicode="f" horiz-adv-x="790" d="M164 0v1161q0 205 100.5 316.5t315.5 111.5q84 0 144 -12t85 -23l-35 -163q-25 12 -70 23t-110 11q-133 0 -186.5 -72.5t-53.5 -195.5v-92h410v-160h-410v-905h-190z" />
<glyph unicode="g" horiz-adv-x="1183" d="M104 555q0 117 35 214t101.5 168t163 110.5t217.5 39.5q133 0 232 -19t167 -36v-950q0 -246 -127 -356.5t-385 -110.5q-100 0 -189.5 16.5t-154.5 38.5l35 166q57 -23 140 -40t173 -17q170 0 244.5 67.5t74.5 214.5v45q-25 -16 -95 -41.5t-165 -25.5q-96 0 -181 30.5 t-148.5 95t-100.5 161t-37 229.5zM303 553q0 -92 23.5 -157.5t63.5 -108.5t92.5 -63.5t107.5 -20.5q76 0 139.5 21.5t99.5 49.5v623q-29 8 -76.5 15.5t-129.5 7.5q-154 0 -237 -100.5t-83 -266.5z" />
<glyph unicode="h" horiz-adv-x="1169" d="M164 0v1556l190 33v-545q53 20 113.5 31.5t120.5 11.5q127 0 211 -35.5t134 -100t70.5 -154.5t20.5 -199v-598h-190v557q0 98 -13.5 168t-44.5 113t-82 62.5t-127 19.5q-31 0 -63.5 -4.5t-62 -9.5t-53 -11t-34.5 -10v-885h-190z" />
<glyph unicode="i" horiz-adv-x="518" d="M135 1382q0 57 36 91t87 34t87 -33.5t36 -91.5q0 -57 -36 -91t-87 -34t-87 34t-36 91zM164 0v1065h190v-1065h-190z" />
<glyph unicode="j" horiz-adv-x="518" d="M-139 -360l24 155q20 -6 47 -10t50 -4q98 0 140 60.5t42 176.5v1047h190v-1045q0 -205 -93 -304t-271 -99q-25 0 -64 6t-65 17zM135 1382q0 57 36 91t87 34t87 -33.5t36 -91.5q0 -57 -36 -91t-87 -34t-87 34t-36 91z" />
<glyph unicode="k" horiz-adv-x="1069" d="M164 0v1556l190 33v-971l108.5 108.5t117 121t108.5 118t88 99.5h223q-47 -51 -102 -112.5t-114.5 -124t-120 -125t-113.5 -117.5q61 -47 130 -117t135.5 -150.5t125.5 -163.5t100 -155h-223q-43 72 -99 146.5t-117.5 143t-125 126t-121.5 96.5v-512h-190z" />
<glyph unicode="l" horiz-adv-x="559" d="M160 279v1277l190 33v-1280q0 -47 8.5 -77.5t26.5 -49t49 -28t76 -15.5l-27 -159q-176 4 -249.5 75.5t-73.5 223.5z" />
<glyph unicode="m" horiz-adv-x="1763" d="M164 0v1034q66 16 173 34.5t249 18.5q102 0 171.5 -27.5t116.5 -80.5q14 10 45 28.5t76 36t100.5 30.5t118.5 13q123 0 201 -35.5t122 -101t59.5 -155.5t15.5 -197v-598h-191v557q0 94 -9 161.5t-34.5 113t-69.5 67t-114 21.5q-96 0 -158.5 -26t-85.5 -46 q16 -53 24.5 -116.5t8.5 -133.5v-598h-190v557q0 94 -10.5 161.5t-36 113t-69.5 67t-112 21.5q-29 0 -61.5 -2.5t-62 -5.5t-54.5 -7t-33 -6v-899h-190z" />
<glyph unicode="n" horiz-adv-x="1175" d="M164 0v1034q66 16 174 34.5t250 18.5q127 0 211 -35.5t134 -100t70.5 -154.5t20.5 -199v-598h-190v557q0 98 -13.5 168t-44.5 113t-82 62.5t-127 19.5q-31 0 -63.5 -2.5t-62 -5.5t-53 -7t-34.5 -6v-899h-190z" />
<glyph unicode="o" horiz-adv-x="1208" d="M104 532q0 125 37 228.5t103.5 177.5t159 114t200.5 40q109 0 201 -40t158.5 -114t103.5 -177.5t37 -228.5q0 -127 -37 -229t-103.5 -176t-158.5 -114t-201 -40t-201 40t-158.5 114t-103.5 176.5t-37 228.5zM303 532q0 -180 81 -285.5t220 -105.5t220 105.5t81 285.5 t-81 286t-220 106t-220 -105.5t-81 -286.5z" />
<glyph unicode="p" horiz-adv-x="1206" d="M164 -379v1411q68 16 169 35.5t234 19.5q123 0 221.5 -38.5t168 -110.5t107.5 -175.5t38 -232.5q0 -121 -32 -223t-93.5 -176t-150.5 -115t-203 -41q-92 0 -163 25t-106 47v-426h-190zM354 225q35 -29 100.5 -55.5t143.5 -26.5q82 0 140.5 30t95 82t53 122.5t16.5 152.5 q0 186 -92 287t-246 101q-86 0 -134 -6.5t-77 -14.5v-672z" />
<glyph unicode="q" horiz-adv-x="1206" d="M104 530q0 129 38 232.5t107.5 175.5t168 110.5t221.5 38.5q133 0 234.5 -19t168.5 -36v-1411h-190v426q-35 -23 -105.5 -47.5t-162.5 -24.5q-115 0 -204 41t-150.5 115t-93.5 176t-32 223zM303 530q0 -82 16.5 -152.5t53.5 -122.5t95 -82t140 -30q78 0 143.5 27 t100.5 55v672q-29 8 -77 14.5t-134 6.5q-154 0 -246 -101t-92 -287z" />
<glyph unicode="r" horiz-adv-x="790" d="M164 0v1020q68 25 168 46t225 21q25 0 56.5 -3t62.5 -8t56.5 -10t37.5 -9l-33 -166q-23 8 -75 19.5t-134 11.5q-53 0 -105 -11.5t-69 -15.5v-895h-190z" />
<glyph unicode="s" horiz-adv-x="913" d="M82 35l35 164q33 -12 104.5 -37t190.5 -25q117 0 173 31t56 98q0 70 -55.5 111t-182.5 92q-61 25 -117.5 50.5t-97.5 60t-65.5 84t-24.5 120.5q0 141 104.5 224.5t284.5 83.5q45 0 90.5 -5.5t84 -12.5t68.5 -15t46 -15l-35 -163q-31 16 -96 33.5t-158 17.5 q-80 0 -139 -32t-59 -99q0 -35 13 -61.5t41 -48t69 -40t98 -39.5q76 -29 135.5 -56.5t101 -64t64.5 -89t23 -128.5q0 -147 -110 -223t-312 -76q-141 0 -221 24t-109 36z" />
<glyph unicode="t" horiz-adv-x="823" d="M152 412v950l190 33v-330h403v-160h-403v-491q0 -80 12.5 -132.5t37 -82t61 -42t86.5 -12.5q86 0 138 19.5t73 28.5l36 -158q-29 -14 -100.5 -36t-163.5 -22q-109 0 -179.5 28t-113.5 83t-60 136t-17 188z" />
<glyph unicode="u" horiz-adv-x="1175" d="M152 467v598h190v-557q0 -195 61.5 -279t206.5 -84q31 0 64 2.5t61.5 5.5t52 6t33.5 7v899h191v-1034q-66 -16 -173.5 -35t-248.5 -19q-123 0 -207 36t-135 101.5t-73.5 154.5t-22.5 198z" />
<glyph unicode="v" horiz-adv-x="1028" d="M33 1065h205q25 -100 58.5 -214t71.5 -225.5t76.5 -216t73.5 -186.5q35 82 74 186.5t77 216t71.5 225.5t58.5 214h196q-80 -299 -187.5 -573.5t-207.5 -491.5h-172q-100 217 -207.5 491.5t-187.5 573.5z" />
<glyph unicode="w" horiz-adv-x="1591" d="M37 1065h205q20 -96 48.5 -209t60.5 -225.5t67 -217t65 -182.5q35 96 69 204t64.5 217.5t58 215t48.5 197.5h158q18 -92 44.5 -197.5t57.5 -215t64.5 -217t68.5 -204.5q31 78 64.5 182.5t66.5 217t61.5 225.5t49.5 209h196q-80 -313 -170 -583.5t-184 -481.5h-166 q-57 147 -121.5 336.5t-117.5 394.5q-53 -205 -117 -394.5t-121 -336.5h-166q-94 211 -184 481.5t-170 583.5z" />
<glyph unicode="x" horiz-adv-x="1046" d="M33 0q72 139 171 283.5t197 275.5l-352 506h215l254 -373l256 373h203l-344 -494q98 -133 201.5 -281.5t175.5 -289.5h-209q-23 45 -55.5 100.5t-71.5 113.5t-80 115.5t-80 108.5q-39 -51 -80 -109.5t-79 -116.5t-70.5 -113.5t-55.5 -98.5h-196z" />
<glyph unicode="y" horiz-adv-x="1017" d="M4 -352l35 153q23 -10 58.5 -19t70.5 -9q113 0 176.5 50t114.5 163q-129 246 -241 521t-185 558h205q23 -92 54.5 -198.5t70 -219t84 -225.5t94.5 -217q78 215 135 426t108 434h197q-74 -301 -164 -578.5t-194 -519.5q-41 -92 -85 -158.5t-97.5 -109.5t-120 -63.5 t-150.5 -20.5q-23 0 -47.5 3t-48 8t-43 11.5t-27.5 10.5z" />
<glyph unicode="z" horiz-adv-x="964" d="M74 0v127q49 90 120.5 198.5t150.5 216t153.5 203t130.5 160.5h-527v160h768v-143q-43 -47 -113.5 -133.5t-151.5 -190.5t-162 -219t-142 -219h586v-160h-813z" />
<glyph unicode="{" horiz-adv-x="681" d="M82 539v147q49 0 84 17.5t58.5 45t34.5 63.5t11 73v368q0 84 18.5 149.5t63.5 109.5t120 68t185 24h9v-150q-117 0 -167 -42t-50 -161v-315q0 -135 -36 -209t-102 -115q66 -41 102 -114.5t36 -208.5v-316q0 -119 50 -160.5t167 -41.5v-150h-9q-111 0 -185.5 22.5 t-119.5 67.5t-63.5 110.5t-18.5 149.5v369q0 37 -11 73t-34.5 63.5t-58.5 45t-84 17.5z" />
<glyph unicode="|" horiz-adv-x="571" d="M197 -379v1983h178v-1983h-178z" />
<glyph unicode="}" horiz-adv-x="681" d="M16 -229q117 0 167 42t50 160v316q0 135 36 208.5t102 114.5q-66 41 -102 115t-36 209v315q0 119 -50 161t-167 42v150h9q111 0 185.5 -24t119.5 -68t63.5 -109.5t18.5 -149.5v-368q0 -37 11 -73t34.5 -63.5t58.5 -45t84 -17.5v-147q-49 0 -84 -17.5t-58.5 -45 t-34.5 -63.5t-11 -73v-369q0 -84 -18.5 -149.5t-63.5 -110.5t-120 -67.5t-185 -22.5h-9v150z" />
<glyph unicode="~" d="M86 496q8 39 26.5 89t50.5 94t82 73.5t122 29.5q61 0 113 -24.5t100 -52.5q55 -33 106 -65t109 -32q33 0 55 15.5t38.5 39t27 51t18.5 54.5l131 -37q-8 -41 -25.5 -90t-50.5 -93t-83 -74t-122 -30q-61 0 -113.5 25t-99.5 53q-55 33 -106 64.5t-109 31.5q-33 0 -55 -15 t-38.5 -38.5t-27 -51.5t-18.5 -54z" />
<glyph unicode="&#xa1;" horiz-adv-x="565" d="M145 928q0 55 37 96t101 41q63 0 100 -41t37 -96t-37 -96t-100 -41t-100.5 41t-37.5 96zM180 4q0 172 12.5 312.5t30.5 279.5h119q18 -139 30.5 -279.5t12.5 -312.5v-383h-205v383z" />
<glyph unicode="&#xa2;" d="M176 596q0 100 27.5 188t78 158t124 117t165.5 65v295h170v-284q119 -6 226 -48l-43 -157q-43 18 -99.5 30.5t-119.5 12.5q-160 0 -245 -96.5t-85 -280.5q0 -82 19.5 -150.5t61.5 -117.5t107.5 -76t159.5 -27q76 0 137.5 14.5t95.5 28.5l27 -155q-29 -16 -98.5 -31.5 t-143.5 -20.5v-284h-170v295q-98 18 -171.5 64t-123 114.5t-75 157t-25.5 188.5z" />
<glyph unicode="&#xa3;" d="M123 610v154h190v170q0 152 32 251t90.5 157.5t140 81t180.5 22.5q90 0 151.5 -16.5t118.5 -38.5l-47 -164q-109 51 -231 51q-55 0 -100.5 -16.5t-77 -55.5t-49 -104.5t-17.5 -163.5v-174h387v-154h-387v-16q0 -102 -8.5 -215t-24.5 -215h592v-164h-807q23 145 40 289.5 t17 290.5v30h-190z" />
<glyph unicode="&#xa4;" d="M90 342l150 150q-58 92 -58 219q0 125 58 217l-150 151l119 119l151 -154q96 59 218 60q121 0 215 -60l153 154l119 -119l-152 -151q29 -45 44.5 -99.5t15.5 -117.5q0 -66 -15.5 -119t-44.5 -98l152 -152l-119 -119l-153 152q-98 -57 -215 -58q-59 0 -114.5 14.5 t-103.5 43.5l-151 -152zM336 711q0 -59 19.5 -105.5t52 -80t76.5 -51t94 -17.5q49 0 93 17.5t76.5 51t52 80t19.5 105.5q0 57 -19.5 104t-52 81t-76.5 51.5t-93 17.5t-93.5 -17.5t-77 -51.5t-52 -81t-19.5 -104z" />
<glyph unicode="&#xa5;" d="M35 1419h211q74 -160 160 -319.5t178 -306.5q90 147 173 307t158 319h207q-90 -170 -189 -347t-210 -355h281v-146h-332v-209h332v-145h-332v-217h-187v217h-331v145h331v209h-331v146h278q-111 180 -210 357t-187 345z" />
<glyph unicode="&#xa6;" horiz-adv-x="571" d="M197 -379v785h178v-785h-178zM197 819v785h178v-785h-178z" />
<glyph unicode="&#xa7;" horiz-adv-x="1007" d="M88 633q0 53 17.5 99t43 84t58.5 65.5t63 46.5q-51 41 -80.5 94t-29.5 123q0 141 99 224t280 83q104 0 194 -20.5t144 -36.5l-43 -158q-49 18 -120 38.5t-177 20.5q-80 0 -138.5 -33.5t-58.5 -101.5q0 -37 12.5 -62.5t39 -47t67.5 -40t98 -36.5q74 -25 140.5 -53.5 t116.5 -69.5t79 -98.5t29 -139.5q0 -51 -16.5 -96t-41 -82t-56.5 -66.5t-63 -47.5q59 -43 96.5 -100.5t37.5 -133.5q0 -154 -108.5 -232.5t-309.5 -78.5q-137 0 -226.5 21.5t-142.5 47.5l47 152q61 -27 135 -45.5t189 -18.5t173 34t58 108q0 72 -55 111.5t-186 84.5 q-74 25 -140.5 53.5t-117 69.5t-79 98.5t-28.5 139.5zM262 651q0 -53 23.5 -89t65.5 -63.5t99.5 -49t124.5 -43.5q10 -4 19.5 -7.5t19.5 -7.5q59 39 96.5 90t37.5 115q0 53 -24 89t-65 62.5t-94 47.5t-114 41q-14 4 -27.5 9t-28.5 11q-59 -39 -96 -91t-37 -114z" />
<glyph unicode="&#xa8;" horiz-adv-x="770" d="M72 1382q0 53 35.5 87t82.5 34t83 -33.5t36 -87.5q0 -53 -36 -86.5t-83 -33.5t-82.5 33.5t-35.5 86.5zM461 1382q0 53 35.5 87t83.5 34q47 0 82.5 -33.5t35.5 -87.5q0 -53 -35.5 -86.5t-82.5 -33.5t-83 33.5t-36 86.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1667" d="M133 711q0 174 57.5 312t153.5 233.5t223 145.5t267 50q139 0 266 -50t223 -145.5t153.5 -233.5t57.5 -312t-57.5 -312.5t-153.5 -233.5t-223 -145.5t-266 -50.5t-266.5 50.5t-223.5 145.5t-153.5 233.5t-57.5 312.5zM283 711q0 -135 41 -247t113.5 -191.5t174 -123 t222.5 -43.5t222 43.5t174 123t113.5 191.5t40.5 247t-40.5 246.5t-113.5 191.5t-174.5 123t-221.5 43q-121 0 -222.5 -43t-174 -123t-113.5 -191.5t-41 -246.5zM465 711q0 100 31.5 178t85 132t125 83t151.5 29q53 0 97 -8.5t77 -19.5t54.5 -22.5t29.5 -17.5l-47 -123 q-25 14 -79 33.5t-126 19.5q-104 0 -173.5 -72.5t-69.5 -211.5q0 -127 63.5 -208t185.5 -81q80 0 132.5 17.5t80.5 31.5l39 -123q-10 -6 -33.5 -17.5t-58.5 -20.5t-79 -16t-93 -7q-88 0 -159.5 28.5t-124 82t-81 132t-28.5 181.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="796" d="M78 913q0 63 26.5 108.5t71.5 73t104.5 40t125.5 12.5q29 0 63.5 -2t77.5 -8v6q0 31 -6 61.5t-24.5 54t-51.5 39t-86 15.5q-47 0 -103.5 -6.5t-97.5 -20.5l-20 129q39 10 101 20.5t128 10.5q84 0 141.5 -20.5t93 -57.5t51 -89t15.5 -118v-459q-49 -10 -133 -19t-168 -9 q-68 0 -124 13t-97 42t-64.5 74t-23.5 110zM221 913q0 -61 48 -86.5t132 -25.5q45 0 90.5 2t55.5 6v205q-23 4 -57.5 6t-67.5 2q-35 0 -71 -4t-64.5 -16.5t-47 -34t-18.5 -54.5z" />
<glyph unicode="&#xab;" horiz-adv-x="1021" d="M61 582l320 452l133 -71l-227 -381l227 -381l-133 -72zM487 582l320 452l133 -71l-227 -381l227 -381l-133 -72z" />
<glyph unicode="&#xac;" d="M109 645v162h938v-698h-164v536h-774z" />
<glyph unicode="&#xad;" horiz-adv-x="612" d="M53 524v178h506v-178h-506z" />
<glyph unicode="&#xae;" horiz-adv-x="1667" d="M133 711q0 174 57.5 312t153.5 233.5t223 145.5t267 50q139 0 266 -50t223 -145.5t153.5 -233.5t57.5 -312t-57.5 -312.5t-153.5 -233.5t-223 -145.5t-266 -50.5t-266.5 50.5t-223.5 145.5t-153.5 233.5t-57.5 312.5zM283 711q0 -135 41 -247t113.5 -191.5t174 -123 t222.5 -43.5t222 43.5t174 123t113.5 191.5t40.5 247t-40.5 246.5t-113.5 191.5t-174.5 123t-221.5 43q-121 0 -222.5 -43t-174 -123t-113.5 -191.5t-41 -246.5zM547 303v795q55 12 116.5 18t112.5 6q176 0 268.5 -65.5t92.5 -198.5q0 -76 -40 -132t-114 -87 q20 -25 47 -61.5t55.5 -81.5t57 -94.5t51.5 -98.5h-158q-47 92 -105.5 175t-101.5 132h-137v-307h-145zM692 729h78q92 0 153.5 25.5t61.5 105.5q0 76 -58.5 105.5t-140.5 29.5q-23 0 -47 -1t-47 -3v-262z" />
<glyph unicode="&#xaf;" horiz-adv-x="770" d="M94 1309v147h582v-147h-582z" />
<glyph unicode="&#xb0;" horiz-adv-x="679" d="M53 1305q0 66 23.5 119t62.5 89.5t91.5 56t109.5 19.5t109.5 -19.5t91.5 -56t62.5 -89.5t23.5 -119t-23.5 -119t-62.5 -90t-91.5 -56.5t-109.5 -19.5t-109.5 19.5t-91.5 56.5t-62.5 90t-23.5 119zM190 1305q0 -72 44.5 -114t105.5 -42t105 42t44 114t-44 113.5t-105 41.5 t-105.5 -42t-44.5 -113z" />
<glyph unicode="&#xb1;" d="M109 0v162h938v-162h-938zM109 696v162h387v412h163v-412h388v-162h-388v-411h-163v411h-387z" />
<glyph unicode="&#xb2;" horiz-adv-x="735" d="M61 1343q35 37 106 73t163 36q141 0 208.5 -59t67.5 -168q0 -43 -19.5 -84t-52 -81t-75.5 -80t-92 -83q-35 -29 -69 -62.5t-40 -60.5h381v-129h-545q-6 68 9.5 120t45 95t68.5 79t82 73q70 59 114 109t44 100q0 53 -34 77.5t-93 24.5q-35 0 -66 -9t-55.5 -22.5t-44 -27 t-29.5 -21.5z" />
<glyph unicode="&#xb3;" horiz-adv-x="735" d="M66 676l30 123q45 -16 95.5 -29.5t103.5 -13.5q109 0 149.5 33.5t40.5 87.5q0 35 -18 59.5t-50 39.5t-73 22.5t-84 7.5h-39v116h51q29 0 61 5.5t57.5 17.5t42 33.5t16.5 52.5q0 51 -38 72.5t-98 21.5q-55 0 -102 -19.5t-82 -35.5l-51 112q33 23 101.5 46.5t137.5 23.5 q150 0 212.5 -60.5t62.5 -154.5q0 -100 -113 -164q72 -23 114 -75t42 -124q0 -53 -18.5 -97t-59.5 -77.5t-105.5 -52t-154.5 -18.5q-63 0 -129 14t-102 33z" />
<glyph unicode="&#xb4;" horiz-adv-x="770" d="M260 1274l262 315l123 -121l-289 -280z" />
<glyph unicode="&#xb5;" horiz-adv-x="1187" d="M164 -379v1444h190v-569q0 -195 63.5 -273t205.5 -78q31 0 63.5 2.5t61 5.5t52 6t34.5 7v899h190v-1034q-66 -16 -169 -35t-226 -19q-113 0 -179.5 24t-107.5 69q6 -57 8 -115.5t2 -122.5v-211h-188z" />
<glyph unicode="&#xb6;" horiz-adv-x="1323" d="M88 981q0 113 46 197t132 141t209 86t275 29q86 0 184 -7.5t182 -27.5v-1778h-160v1645q-29 4 -71.5 7t-93.5 3q-16 0 -33 -1t-29 -3v-1651h-160v916q-227 23 -354 129t-127 315z" />
<glyph unicode="&#xb7;" horiz-adv-x="503" d="M115 603q0 55 36.5 96t100.5 41q63 0 100 -41t37 -96t-37 -96.5t-100 -41.5t-100 41t-37 97z" />
<glyph unicode="&#xb8;" horiz-adv-x="770" d="M141 -360l23 116q25 -8 52.5 -14t62.5 -6q74 0 73 47q0 23 -22.5 38t-63.5 32l-14 6q6 20 18.5 47.5t24.5 54.5t23.5 48.5t17.5 31.5h131q-12 -23 -30.5 -57.5t-28.5 -59.5q59 -29 85.5 -62.5t26.5 -99.5q0 -23 -10 -49t-34.5 -48.5t-65.5 -37t-101 -14.5q-53 0 -97 8 t-71 19z" />
<glyph unicode="&#xb9;" horiz-adv-x="735" d="M102 1272q78 27 154 68.5t131 95.5h100v-791h-143v602q-45 -29 -103.5 -51t-99.5 -35z" />
<glyph unicode="&#xba;" horiz-adv-x="913" d="M88 1061q0 88 27.5 160.5t77 123t117 78t147.5 27.5t147.5 -27.5t116.5 -78t76.5 -123t27.5 -160.5t-27.5 -160t-76.5 -123t-116.5 -78.5t-147.5 -27.5t-147.5 27.5t-117 78.5t-77 123t-27.5 160zM240 1061q0 -113 56 -182.5t161 -69.5q104 0 160.5 69.5t56.5 182.5 q0 115 -56.5 183.5t-160.5 68.5q-102 0 -159.5 -68.5t-57.5 -183.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="1021" d="M82 201l227 381l-227 381l133 71l320 -452l-320 -453zM508 201l227 381l-227 381l133 71l320 -452l-320 -453z" />
<glyph unicode="&#xbc;" horiz-adv-x="1789" d="M102 1272q78 27 154 68.5t131 95.5h100v-791h-143v602q-45 -29 -103.5 -51t-99.5 -35zM401 0l801 1419h178l-801 -1419h-178zM1104 197v94q29 51 74 116.5t98 135t112.5 136t119.5 118.5h131v-480h102v-120h-102v-191h-140v191h-395zM1258 317h241v310 q-63 -66 -128.5 -149t-112.5 -161z" />
<glyph unicode="&#xbd;" horiz-adv-x="1789" d="M102 1272q78 27 154 68.5t131 95.5h100v-791h-143v602q-45 -29 -103.5 -51t-99.5 -35zM358 0l801 1419h178l-801 -1419h-178zM1116 704q35 37 106 73t163 36q141 0 208.5 -59t67.5 -168q0 -43 -19.5 -84t-52 -81t-75.5 -80t-92 -83q-35 -29 -69 -62.5t-40 -60.5h381v-129 h-545q-6 68 9.5 120t45 95t68.5 79t82 73q70 59 114 109t44 100q0 53 -34 77.5t-93 24.5q-35 0 -66 -9t-55.5 -22.5t-44 -27t-29.5 -21.5z" />
<glyph unicode="&#xbe;" horiz-adv-x="1789" d="M66 676l30 123q45 -16 95.5 -29.5t103.5 -13.5q109 0 149.5 33.5t40.5 87.5q0 35 -18 59.5t-50 39.5t-73 22.5t-84 7.5h-39v116h51q29 0 61 5.5t57.5 17.5t42 33.5t16.5 52.5q0 51 -38 72.5t-98 21.5q-55 0 -102 -19.5t-82 -35.5l-51 112q33 23 101.5 46.5t137.5 23.5 q150 0 212.5 -60.5t62.5 -154.5q0 -100 -113 -164q72 -23 114 -75t42 -124q0 -53 -18.5 -97t-59.5 -77.5t-105.5 -52t-154.5 -18.5q-63 0 -129 14t-102 33zM432 0l801 1419h178l-801 -1419h-178zM1104 197v94q29 51 74 116.5t98 135t112.5 136t119.5 118.5h131v-480h102 v-120h-102v-191h-140v191h-395zM1258 317h241v310q-63 -66 -128.5 -149t-112.5 -161z" />
<glyph unicode="&#xbf;" horiz-adv-x="827" d="M57 -70q0 68 26 121t64.5 101.5t82.5 91.5t83 91t64.5 101t25.5 121v19.5t-2 19.5h160q8 -41 8 -80q0 -66 -23.5 -119t-57 -99t-74.5 -87t-75 -82t-57.5 -84t-23.5 -92q0 -78 56.5 -128t156.5 -50q135 0 264 69l53 -147q-70 -37 -156.5 -59.5t-185.5 -22.5 q-111 0 -184.5 28.5t-119.5 73.5t-65.5 101.5t-19.5 111.5zM334 928q0 55 37 96t100 41t100 -41t37 -96t-36.5 -96t-100.5 -41q-63 0 -100 41t-37 96z" />
<glyph unicode="&#xc0;" horiz-adv-x="1357" d="M16 0q82 225 154 416.5t140.5 363.5t136 329t141.5 310h182q74 -154 141.5 -310.5t136 -328.5t140.5 -363.5t153 -416.5h-217q-35 92 -65.5 181t-63.5 181h-643l-129 -362h-207zM410 526h528q-66 178 -130.5 345t-133.5 321q-72 -154 -136.5 -320.5t-127.5 -345.5z M432 1787l123 121l262 -315l-96 -86z" />
<glyph unicode="&#xc1;" horiz-adv-x="1357" d="M16 0q82 225 154 416.5t140.5 363.5t136 329t141.5 310h182q74 -154 141.5 -310.5t136 -328.5t140.5 -363.5t153 -416.5h-217q-35 92 -65.5 181t-63.5 181h-643l-129 -362h-207zM410 526h528q-66 178 -130.5 345t-133.5 321q-72 -154 -136.5 -320.5t-127.5 -345.5z M551 1593l262 315l123 -121l-289 -280z" />
<glyph unicode="&#xc2;" horiz-adv-x="1357" d="M16 0q82 225 154 416.5t140.5 363.5t136 329t141.5 310h182q74 -154 141.5 -310.5t136 -328.5t140.5 -363.5t153 -416.5h-217q-35 92 -65.5 181t-63.5 181h-643l-129 -362h-207zM399 1601l279 293l279 -293l-80 -90l-199 180l-199 -180zM410 526h528q-66 178 -130.5 345 t-133.5 321q-72 -154 -136.5 -320.5t-127.5 -345.5z" />
<glyph unicode="&#xc3;" horiz-adv-x="1357" d="M16 0q82 225 154 416.5t140.5 363.5t136 329t141.5 310h182q74 -154 141.5 -310.5t136 -328.5t140.5 -363.5t153 -416.5h-217q-35 92 -65.5 181t-63.5 181h-643l-129 -362h-207zM342 1628q8 25 26.5 57.5t45 60t62.5 47t79 19.5q41 0 77 -14.5t69 -30.5q35 -16 59 -24.5 t47 -8.5q35 0 64.5 29t46.5 61l98 -49q-10 -25 -27.5 -57.5t-44 -60t-62.5 -47t-79 -19.5q-41 0 -77 14t-69 31q-35 16 -59 24.5t-47 8.5q-35 0 -64.5 -29t-46.5 -61zM410 526h528q-66 178 -130.5 345t-133.5 321q-72 -154 -136.5 -320.5t-127.5 -345.5z" />
<glyph unicode="&#xc4;" horiz-adv-x="1357" d="M16 0q82 225 154 416.5t140.5 363.5t136 329t141.5 310h182q74 -154 141.5 -310.5t136 -328.5t140.5 -363.5t153 -416.5h-217q-35 92 -65.5 181t-63.5 181h-643l-129 -362h-207zM365 1701q0 53 35.5 87t82.5 34t83 -33.5t36 -87.5q0 -53 -36 -86.5t-83 -33.5t-82.5 33.5 t-35.5 86.5zM410 526h528q-66 178 -130.5 345t-133.5 321q-72 -154 -136.5 -320.5t-127.5 -345.5zM754 1701q0 53 35.5 87t83.5 34q47 0 82.5 -33.5t35.5 -87.5q0 -53 -35.5 -86.5t-82.5 -33.5t-83 33.5t-36 86.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1357" d="M16 0q76 209 143.5 390t132 344t127 310.5t130.5 292.5q-39 29 -61.5 72t-22.5 100q0 49 17.5 87t46 66t66.5 42t81 14t81 -14t66.5 -42t46 -65.5t17.5 -87.5q0 -55 -22.5 -97t-59.5 -71q68 -145 131 -293.5t127.5 -311.5t132.5 -344t145 -392h-217q-35 92 -65.5 182 t-63.5 183h-643l-129 -365h-207zM410 526h528q-66 178 -130.5 345t-133.5 321q-72 -154 -136.5 -319.5t-127.5 -346.5zM567 1509q0 -53 32 -82.5t77 -29.5t76.5 29.5t31.5 82.5t-31.5 83t-76.5 30t-77 -29.5t-32 -83.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1914" d="M12 0q117 221 228.5 416.5t218 370t210 331t205.5 301.5h914v-166h-617v-428h543v-161h-543v-498h668v-166h-864v365h-559q-49 -90 -97.5 -181.5t-93.5 -183.5h-213zM508 526h467v738q-109 -147 -225.5 -334t-241.5 -404z" />
<glyph unicode="&#xc7;" horiz-adv-x="1269" d="M119 711q0 178 54 316t146.5 232.5t215 143.5t262.5 49q88 0 158.5 -12.5t122.5 -28.5t85 -32.5t45 -24.5l-57 -164q-18 12 -53 26.5t-79 29t-96.5 23.5t-105.5 9q-113 0 -203 -39t-153.5 -112.5t-97 -179t-33.5 -236.5q0 -127 29.5 -231.5t89 -179.5t148.5 -116t208 -41 q137 0 227 29t135 51l52 -164q-29 -23 -138.5 -54.5t-277.5 -35.5q-6 -12 -11.5 -23.5t-9.5 -21.5q59 -29 86 -62.5t27 -99.5q0 -23 -10.5 -49t-35 -48.5t-65.5 -37t-100 -14.5q-53 0 -97 8t-71 19l23 116q25 -8 52 -14t62 -6q74 0 74 47q0 23 -22.5 38t-63.5 32l-14 6 q8 25 21.5 57t27.5 61q-127 16 -229.5 72.5t-175 150t-112.5 221.5t-40 290z" />
<glyph unicode="&#xc8;" horiz-adv-x="1169" d="M176 0v1419h866v-170h-667v-422h594v-165h-594v-492h719v-170h-918zM410 1787l123 121l262 -315l-96 -86z" />
<glyph unicode="&#xc9;" horiz-adv-x="1169" d="M176 0v1419h866v-170h-667v-422h594v-165h-594v-492h719v-170h-918zM518 1593l262 315l123 -121l-289 -280z" />
<glyph unicode="&#xca;" horiz-adv-x="1169" d="M176 0v1419h866v-170h-667v-422h594v-165h-594v-492h719v-170h-918zM329 1601l279 293l279 -293l-80 -90l-199 180l-199 -180z" />
<glyph unicode="&#xcb;" horiz-adv-x="1169" d="M176 0v1419h866v-170h-667v-422h594v-165h-594v-492h719v-170h-918zM295 1701q0 53 35.5 87t82.5 34t83 -33.5t36 -87.5q0 -53 -36 -86.5t-83 -33.5t-82.5 33.5t-35.5 86.5zM684 1701q0 53 35.5 87t83.5 34q47 0 82.5 -33.5t35.5 -87.5q0 -53 -35.5 -86.5t-82.5 -33.5 t-83 33.5t-36 86.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="550" d="M31 1787l123 121l262 -315l-96 -86zM176 0v1419h199v-1419h-199z" />
<glyph unicode="&#xcd;" horiz-adv-x="550" d="M139 1593l262 315l123 -121l-289 -280zM176 0v1419h199v-1419h-199z" />
<glyph unicode="&#xce;" horiz-adv-x="550" d="M-3 1601l279 293l279 -293l-80 -90l-199 180l-199 -180zM176 0v1419h199v-1419h-199z" />
<glyph unicode="&#xcf;" horiz-adv-x="550" d="M-37 1701q0 53 35.5 87t82.5 34t83 -33.5t36 -87.5q0 -53 -36 -86.5t-83 -33.5t-82.5 33.5t-35.5 86.5zM176 0v1419h199v-1419h-199zM352 1701q0 53 35.5 87t83.5 34q47 0 82.5 -33.5t35.5 -87.5q0 -53 -35.5 -86.5t-82.5 -33.5t-83 33.5t-36 86.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1486" d="M39 664v153h164v582q84 20 187.5 27.5t189.5 7.5q174 0 319 -44t249.5 -133.5t162 -225.5t57.5 -320t-57.5 -320.5t-162 -225.5t-249.5 -133t-319 -44q-86 0 -189.5 7t-187.5 28v641h-164zM401 168q18 -2 64.5 -4t130.5 -2q283 0 422 144.5t139 404.5t-139 404.5 t-422 144.5q-84 0 -130 -2.5t-65 -4.5v-436h267v-153h-267v-496z" />
<glyph unicode="&#xd1;" horiz-adv-x="1490" d="M176 0v1419h158q96 -102 205.5 -238.5t217 -280.5t203 -283.5t160.5 -247.5v1050h195v-1419h-166q-45 76 -103.5 170t-126 195.5t-140 206t-144.5 201.5t-139.5 183t-124.5 152v-1108h-195zM409 1628q8 25 26.5 57.5t45 60t62.5 47t79 19.5q41 0 77 -14.5t69 -30.5 q35 -16 59 -24.5t47 -8.5q35 0 64.5 29t46.5 61l98 -49q-10 -25 -27.5 -57.5t-44 -60t-62.5 -47t-79 -19.5q-41 0 -77 14t-69 31q-35 16 -59 24.5t-47 8.5q-35 0 -64.5 -29t-46.5 -61z" />
<glyph unicode="&#xd2;" horiz-adv-x="1593" d="M119 711q0 182 54 321t147.5 232.5t217.5 140.5t263 47q137 0 259 -47t214 -140.5t146.5 -232.5t54.5 -321t-54.5 -321.5t-146.5 -233t-214 -140.5t-259 -47q-139 0 -263 47t-217.5 140.5t-147.5 233t-54 321.5zM328 711q0 -129 32.5 -234.5t93 -179.5t147.5 -114 t196 -40t194.5 40t146 114t93.5 179.5t33 234.5t-33 234.5t-93.5 179t-146.5 113.5t-194 40q-109 0 -196 -40t-147.5 -113.5t-93 -179t-32.5 -234.5zM551 1787l123 121l262 -315l-96 -86z" />
<glyph unicode="&#xd3;" horiz-adv-x="1593" d="M119 711q0 182 54 321t147.5 232.5t217.5 140.5t263 47q137 0 259 -47t214 -140.5t146.5 -232.5t54.5 -321t-54.5 -321.5t-146.5 -233t-214 -140.5t-259 -47q-139 0 -263 47t-217.5 140.5t-147.5 233t-54 321.5zM328 711q0 -129 32.5 -234.5t93 -179.5t147.5 -114 t196 -40t194.5 40t146 114t93.5 179.5t33 234.5t-33 234.5t-93.5 179t-146.5 113.5t-194 40q-109 0 -196 -40t-147.5 -113.5t-93 -179t-32.5 -234.5zM659 1593l262 315l123 -121l-289 -280z" />
<glyph unicode="&#xd4;" horiz-adv-x="1593" d="M119 711q0 182 54 321t147.5 232.5t217.5 140.5t263 47q137 0 259 -47t214 -140.5t146.5 -232.5t54.5 -321t-54.5 -321.5t-146.5 -233t-214 -140.5t-259 -47q-139 0 -263 47t-217.5 140.5t-147.5 233t-54 321.5zM328 711q0 -129 32.5 -234.5t93 -179.5t147.5 -114 t196 -40t194.5 40t146 114t93.5 179.5t33 234.5t-33 234.5t-93.5 179t-146.5 113.5t-194 40q-109 0 -196 -40t-147.5 -113.5t-93 -179t-32.5 -234.5zM518 1601l279 293l279 -293l-80 -90l-199 180l-199 -180z" />
<glyph unicode="&#xd5;" horiz-adv-x="1593" d="M119 711q0 182 54 321t147.5 232.5t217.5 140.5t263 47q137 0 259 -47t214 -140.5t146.5 -232.5t54.5 -321t-54.5 -321.5t-146.5 -233t-214 -140.5t-259 -47q-139 0 -263 47t-217.5 140.5t-147.5 233t-54 321.5zM328 711q0 -129 32.5 -234.5t93 -179.5t147.5 -114 t196 -40t194.5 40t146 114t93.5 179.5t33 234.5t-33 234.5t-93.5 179t-146.5 113.5t-194 40q-109 0 -196 -40t-147.5 -113.5t-93 -179t-32.5 -234.5zM461 1628q8 25 26.5 57.5t45 60t62.5 47t79 19.5q41 0 77 -14.5t69 -30.5q35 -16 59 -24.5t47 -8.5q35 0 64.5 29t46.5 61 l98 -49q-10 -25 -27.5 -57.5t-44 -60t-62.5 -47t-79 -19.5q-41 0 -77 14t-69 31q-35 16 -59 24.5t-47 8.5q-35 0 -64.5 -29t-46.5 -61z" />
<glyph unicode="&#xd6;" horiz-adv-x="1593" d="M119 711q0 182 54 321t147.5 232.5t217.5 140.5t263 47q137 0 259 -47t214 -140.5t146.5 -232.5t54.5 -321t-54.5 -321.5t-146.5 -233t-214 -140.5t-259 -47q-139 0 -263 47t-217.5 140.5t-147.5 233t-54 321.5zM328 711q0 -129 32.5 -234.5t93 -179.5t147.5 -114 t196 -40t194.5 40t146 114t93.5 179.5t33 234.5t-33 234.5t-93.5 179t-146.5 113.5t-194 40q-109 0 -196 -40t-147.5 -113.5t-93 -179t-32.5 -234.5zM484 1701q0 53 35.5 87t82.5 34t83 -33.5t36 -87.5q0 -53 -36 -86.5t-83 -33.5t-82.5 33.5t-35.5 86.5zM873 1701 q0 53 35.5 87t83.5 34q47 0 82.5 -33.5t35.5 -87.5q0 -53 -35.5 -86.5t-82.5 -33.5t-83 33.5t-36 86.5z" />
<glyph unicode="&#xd7;" d="M164 313l299 299l-299 299l115 115l299 -299l299 299l114 -115l-299 -299l299 -299l-114 -114l-299 299l-299 -299z" />
<glyph unicode="&#xd8;" horiz-adv-x="1593" d="M119 711q0 182 54 321t147.5 232.5t217.5 140.5t263 47q217 0 385 -115l121 158l127 -94l-131 -170q80 -92 126 -222t46 -298q0 -182 -54.5 -321.5t-146.5 -233t-214 -140.5t-259 -47q-109 0 -206 28t-181 83l-119 -154l-127 94l127 164q-82 92 -129 223.5t-47 303.5z M328 711q0 -223 90 -367l653 846q-113 88 -274 88q-109 0 -196 -40t-147.5 -113.5t-93 -179t-32.5 -234.5zM526 227q109 -84 271 -84q109 0 194.5 40t146 114t93.5 179.5t33 234.5q0 109 -22.5 197.5t-63.5 160.5z" />
<glyph unicode="&#xd9;" horiz-adv-x="1409" d="M166 528v891h199v-868q0 -213 94 -309.5t246 -96.5q76 0 138 24t107 73t69.5 125.5t24.5 183.5v868h199v-891q0 -119 -31.5 -221t-98 -177t-168 -118t-240.5 -43t-242 43t-168.5 118t-97 177.5t-31.5 220.5zM459 1787l123 121l262 -315l-96 -86z" />
<glyph unicode="&#xda;" horiz-adv-x="1409" d="M166 528v891h199v-868q0 -213 94 -309.5t246 -96.5q76 0 138 24t107 73t69.5 125.5t24.5 183.5v868h199v-891q0 -119 -31.5 -221t-98 -177t-168 -118t-240.5 -43t-242 43t-168.5 118t-97 177.5t-31.5 220.5zM567 1593l262 315l123 -121l-289 -280z" />
<glyph unicode="&#xdb;" horiz-adv-x="1409" d="M166 528v891h199v-868q0 -213 94 -309.5t246 -96.5q76 0 138 24t107 73t69.5 125.5t24.5 183.5v868h199v-891q0 -119 -31.5 -221t-98 -177t-168 -118t-240.5 -43t-242 43t-168.5 118t-97 177.5t-31.5 220.5zM425 1601l279 293l279 -293l-80 -90l-199 180l-199 -180z" />
<glyph unicode="&#xdc;" horiz-adv-x="1409" d="M166 528v891h199v-868q0 -213 94 -309.5t246 -96.5q76 0 138 24t107 73t69.5 125.5t24.5 183.5v868h199v-891q0 -119 -31.5 -221t-98 -177t-168 -118t-240.5 -43t-242 43t-168.5 118t-97 177.5t-31.5 220.5zM391 1701q0 53 35.5 87t82.5 34t83 -33.5t36 -87.5 q0 -53 -36 -86.5t-83 -33.5t-82.5 33.5t-35.5 86.5zM780 1701q0 53 35.5 87t83.5 34q47 0 82.5 -33.5t35.5 -87.5q0 -53 -35.5 -86.5t-82.5 -33.5t-83 33.5t-36 86.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1224" d="M12 1419h234q78 -168 174 -341t198 -333q100 160 196.5 333t176.5 341h221q-111 -205 -235.5 -416.5t-263.5 -429.5v-573h-199v569q-141 219 -266 432t-236 418zM477 1593l262 315l123 -121l-289 -280z" />
<glyph unicode="&#xde;" horiz-adv-x="1247" d="M176 0v1419h199v-231q39 4 81 5t81 1q637 0 637 -453q0 -123 -44.5 -207.5t-127 -139t-201.5 -79t-269 -24.5h-157v-291h-199zM375 455h149q102 0 184.5 13t138.5 46t87 88t31 141q0 82 -32 136.5t-89.5 86t-137 44t-176.5 12.5h-76.5t-78.5 -4v-563z" />
<glyph unicode="&#xdf;" horiz-adv-x="1275" d="M164 0v1135q0 102 28.5 186t84 143.5t137.5 92t188 32.5q111 0 190.5 -24.5t130 -68.5t74 -104t23.5 -130q0 -51 -14.5 -91.5t-36 -74t-47 -62t-48.5 -53.5q-47 -51 -79.5 -85t-32.5 -85q0 -31 15.5 -53.5t40 -39t54 -30.5t60.5 -29q55 -27 103 -57.5t84 -70t55.5 -94 t19.5 -130.5q0 -160 -99.5 -246t-308.5 -86q-43 0 -86 6.5t-79.5 15.5t-64.5 19.5t-40 16.5l33 166q12 -6 37.5 -16.5t59.5 -20.5t74 -17.5t81 -7.5q102 0 151 45t49 121q0 80 -47 131t-163 105q-68 31 -112 58.5t-68.5 56t-35 59t-10.5 71.5q0 82 45 137.5t97 108.5 q20 20 41.5 45t40 50.5t29.5 52t11 55.5q0 90 -53 140t-160 50q-137 0 -199.5 -77.5t-62.5 -215.5v-1130h-190z" />
<glyph unicode="&#xe0;" horiz-adv-x="1069" d="M88 317q0 88 36 151.5t97.5 102.5t143 57.5t172.5 18.5q29 0 59.5 -3t58 -8t48 -9t28.5 -6v53q0 47 -10 93t-37 82t-73 57.5t-119 21.5q-94 0 -165 -13.5t-106 -27.5l-22 157q37 16 123 32t186 16q115 0 193.5 -30t127 -84t69 -129t20.5 -165v-666q-25 -4 -69 -11 t-99.5 -13t-120 -11.5t-127.5 -5.5q-90 0 -166 18.5t-131 58.5t-86 105.5t-31 157.5zM281 319q0 -102 65 -142t178 -40q68 0 120 3t87 12v317q-20 10 -66.5 17.5t-111.5 7.5q-43 0 -91 -6.5t-88 -26t-66.5 -53t-26.5 -89.5zM293 1468l123 121l262 -315l-96 -86z" />
<glyph unicode="&#xe1;" horiz-adv-x="1069" d="M88 317q0 88 36 151.5t97.5 102.5t143 57.5t172.5 18.5q29 0 59.5 -3t58 -8t48 -9t28.5 -6v53q0 47 -10 93t-37 82t-73 57.5t-119 21.5q-94 0 -165 -13.5t-106 -27.5l-22 157q37 16 123 32t186 16q115 0 193.5 -30t127 -84t69 -129t20.5 -165v-666q-25 -4 -69 -11 t-99.5 -13t-120 -11.5t-127.5 -5.5q-90 0 -166 18.5t-131 58.5t-86 105.5t-31 157.5zM281 319q0 -102 65 -142t178 -40q68 0 120 3t87 12v317q-20 10 -66.5 17.5t-111.5 7.5q-43 0 -91 -6.5t-88 -26t-66.5 -53t-26.5 -89.5zM401 1274l262 315l123 -121l-289 -280z" />
<glyph unicode="&#xe2;" horiz-adv-x="1069" d="M88 317q0 88 36 151.5t97.5 102.5t143 57.5t172.5 18.5q29 0 59.5 -3t58 -8t48 -9t28.5 -6v53q0 47 -10 93t-37 82t-73 57.5t-119 21.5q-94 0 -165 -13.5t-106 -27.5l-22 157q37 16 123 32t186 16q115 0 193.5 -30t127 -84t69 -129t20.5 -165v-666q-25 -4 -69 -11 t-99.5 -13t-120 -11.5t-127.5 -5.5q-90 0 -166 18.5t-131 58.5t-86 105.5t-31 157.5zM260 1282l279 293l279 -293l-80 -90l-199 180l-199 -180zM281 319q0 -102 65 -142t178 -40q68 0 120 3t87 12v317q-20 10 -66.5 17.5t-111.5 7.5q-43 0 -91 -6.5t-88 -26t-66.5 -53 t-26.5 -89.5z" />
<glyph unicode="&#xe3;" horiz-adv-x="1069" d="M88 317q0 88 36 151.5t97.5 102.5t143 57.5t172.5 18.5q29 0 59.5 -3t58 -8t48 -9t28.5 -6v53q0 47 -10 93t-37 82t-73 57.5t-119 21.5q-94 0 -165 -13.5t-106 -27.5l-22 157q37 16 123 32t186 16q115 0 193.5 -30t127 -84t69 -129t20.5 -165v-666q-25 -4 -69 -11 t-99.5 -13t-120 -11.5t-127.5 -5.5q-90 0 -166 18.5t-131 58.5t-86 105.5t-31 157.5zM203 1309q8 25 26.5 57.5t45 60t62.5 47t79 19.5q41 0 77 -14.5t69 -30.5q35 -16 59 -24.5t47 -8.5q35 0 64.5 29t46.5 61l98 -49q-10 -25 -27.5 -57.5t-44 -60t-62.5 -47t-79 -19.5 q-41 0 -77 14t-69 31q-35 16 -59 24.5t-47 8.5q-35 0 -64.5 -29t-46.5 -61zM281 319q0 -102 65 -142t178 -40q68 0 120 3t87 12v317q-20 10 -66.5 17.5t-111.5 7.5q-43 0 -91 -6.5t-88 -26t-66.5 -53t-26.5 -89.5z" />
<glyph unicode="&#xe4;" horiz-adv-x="1069" d="M88 317q0 88 36 151.5t97.5 102.5t143 57.5t172.5 18.5q29 0 59.5 -3t58 -8t48 -9t28.5 -6v53q0 47 -10 93t-37 82t-73 57.5t-119 21.5q-94 0 -165 -13.5t-106 -27.5l-22 157q37 16 123 32t186 16q115 0 193.5 -30t127 -84t69 -129t20.5 -165v-666q-25 -4 -69 -11 t-99.5 -13t-120 -11.5t-127.5 -5.5q-90 0 -166 18.5t-131 58.5t-86 105.5t-31 157.5zM226 1382q0 53 35.5 87t82.5 34t83 -33.5t36 -87.5q0 -53 -36 -86.5t-83 -33.5t-82.5 33.5t-35.5 86.5zM281 319q0 -102 65 -142t178 -40q68 0 120 3t87 12v317q-20 10 -66.5 17.5 t-111.5 7.5q-43 0 -91 -6.5t-88 -26t-66.5 -53t-26.5 -89.5zM615 1382q0 53 35.5 87t83.5 34q47 0 82.5 -33.5t35.5 -87.5q0 -53 -35.5 -86.5t-82.5 -33.5t-83 33.5t-36 86.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1069" d="M88 317q0 88 36 151.5t97.5 102.5t143 57.5t172.5 18.5q29 0 59.5 -3t58 -8t48 -9t28.5 -6v53q0 47 -10 93t-37 82t-73 57.5t-119 21.5q-94 0 -165 -13.5t-106 -27.5l-22 157q37 16 123 32t186 16q115 0 193.5 -30t127 -84t69 -129t20.5 -165v-666q-25 -4 -69 -11 t-99.5 -13t-120 -11.5t-127.5 -5.5q-90 0 -166 18.5t-131 58.5t-86 105.5t-31 157.5zM281 319q0 -102 65 -142t178 -40q68 0 120 3t87 12v317q-20 10 -66.5 17.5t-111.5 7.5q-43 0 -91 -6.5t-88 -26t-66.5 -53t-26.5 -89.5zM328 1386q0 49 17.5 87t46 66t66.5 42t81 14 t81 -14t66.5 -42t46 -66t17.5 -87t-17.5 -86.5t-46 -65.5t-66.5 -42t-81 -14t-81 14t-66.5 42t-46 65.5t-17.5 86.5zM430 1386q0 -53 32 -82.5t77 -29.5t77 29.5t32 82.5t-32 83t-77 30t-77 -29.5t-32 -83.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1759" d="M90 317q0 88 36 151.5t97.5 102.5t143.5 57.5t172 18.5q29 0 59.5 -3t57 -8t47 -9t28.5 -6v53q0 47 -10 93t-37 82t-73 57.5t-119 21.5q-94 0 -165 -13.5t-106 -27.5l-22 157q37 16 122 32t183 16q141 0 224 -48.5t124 -134.5q68 92 160 137.5t192 45.5q213 0 329 -135.5 t116 -407.5q0 -16 -1 -27.5t-1 -21.5v-19h-729q12 -164 97 -251t261 -87q100 0 168.5 17.5t103.5 34.5l27 -160q-35 -18 -122 -39t-198 -21q-117 0 -203.5 28t-152.5 81q-20 -14 -54 -32.5t-81 -36t-105.5 -29t-128.5 -11.5q-102 0 -183 18.5t-138 59.5t-88 106.5t-31 157.5 zM283 319q0 -104 66.5 -143t185.5 -39q106 0 167.5 23.5t94.5 46.5q-31 57 -48.5 122.5t-19.5 139.5q-23 10 -65.5 17.5t-108.5 7.5q-43 0 -91 -6.5t-88 -26t-66.5 -53t-26.5 -89.5zM922 635h528q2 129 -64.5 212t-183.5 83q-66 0 -116 -25.5t-83.5 -66.5t-54 -94.5 t-26.5 -108.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="952" d="M104 530q0 123 36 225.5t101.5 177.5t161 117t211.5 42q72 0 143.5 -12.5t137.5 -39.5l-43 -161q-43 20 -99.5 32.5t-119.5 12.5q-160 0 -245 -100.5t-85 -293.5q0 -86 19.5 -157.5t61.5 -122.5t107.5 -79t159.5 -28q76 0 137.5 14.5t96.5 30.5l26 -159 q-16 -8 -43.5 -17.5t-62.5 -16.5t-75 -12.5t-79 -7.5l-22 -51q59 -29 85.5 -62.5t26.5 -99.5q0 -23 -10 -49t-34.5 -48.5t-65.5 -37t-101 -14.5q-53 0 -97 8t-71 19l23 116q25 -8 52.5 -14t62.5 -6q74 0 73 47q0 23 -22.5 38t-63.5 32l-14 6q8 25 22.5 59.5t30.5 65.5 q-104 14 -183 60t-132 117.5t-80 165t-27 203.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1144" d="M104 530q0 141 41 247t109 175.5t156 104.5t180 35q215 0 329.5 -134.5t114.5 -408.5v-31.5t-2 -36.5h-729q12 -166 96 -252t263 -86q100 0 168.5 17.5t103.5 34.5l27 -160q-35 -18 -122 -39t-198 -21q-139 0 -240.5 42t-167 116t-97.5 175t-32 222zM307 635h529 q2 129 -64.5 212t-183.5 83q-66 0 -116 -25.5t-85 -66.5t-54.5 -94.5t-25.5 -108.5zM340 1468l123 121l262 -315l-96 -86z" />
<glyph unicode="&#xe9;" horiz-adv-x="1144" d="M104 530q0 141 41 247t109 175.5t156 104.5t180 35q215 0 329.5 -134.5t114.5 -408.5v-31.5t-2 -36.5h-729q12 -166 96 -252t263 -86q100 0 168.5 17.5t103.5 34.5l27 -160q-35 -18 -122 -39t-198 -21q-139 0 -240.5 42t-167 116t-97.5 175t-32 222zM307 635h529 q2 129 -64.5 212t-183.5 83q-66 0 -116 -25.5t-85 -66.5t-54.5 -94.5t-25.5 -108.5zM448 1274l262 315l123 -121l-289 -280z" />
<glyph unicode="&#xea;" horiz-adv-x="1144" d="M104 530q0 141 41 247t109 175.5t156 104.5t180 35q215 0 329.5 -134.5t114.5 -408.5v-31.5t-2 -36.5h-729q12 -166 96 -252t263 -86q100 0 168.5 17.5t103.5 34.5l27 -160q-35 -18 -122 -39t-198 -21q-139 0 -240.5 42t-167 116t-97.5 175t-32 222zM307 635h529 q2 129 -64.5 212t-183.5 83q-66 0 -116 -25.5t-85 -66.5t-54.5 -94.5t-25.5 -108.5zM307 1282l279 293l279 -293l-80 -90l-199 180l-199 -180z" />
<glyph unicode="&#xeb;" horiz-adv-x="1144" d="M104 530q0 141 41 247t109 175.5t156 104.5t180 35q215 0 329.5 -134.5t114.5 -408.5v-31.5t-2 -36.5h-729q12 -166 96 -252t263 -86q100 0 168.5 17.5t103.5 34.5l27 -160q-35 -18 -122 -39t-198 -21q-139 0 -240.5 42t-167 116t-97.5 175t-32 222zM273 1382 q0 53 35.5 87t82.5 34t83 -33.5t36 -87.5q0 -53 -36 -86.5t-83 -33.5t-82.5 33.5t-35.5 86.5zM307 635h529q2 129 -64.5 212t-183.5 83q-66 0 -116 -25.5t-85 -66.5t-54.5 -94.5t-25.5 -108.5zM662 1382q0 53 35.5 87t83.5 34q47 0 82.5 -33.5t35.5 -87.5q0 -53 -35.5 -86.5 t-82.5 -33.5t-83 33.5t-36 86.5z" />
<glyph unicode="&#xec;" horiz-adv-x="518" d="M14 1468l123 121l262 -315l-96 -86zM164 0v1065h190v-1065h-190z" />
<glyph unicode="&#xed;" horiz-adv-x="518" d="M123 1274l262 315l123 -121l-289 -280zM164 0v1065h190v-1065h-190z" />
<glyph unicode="&#xee;" horiz-adv-x="518" d="M-19 1282l279 293l279 -293l-80 -90l-199 180l-199 -180zM164 0v1065h190v-1065h-190z" />
<glyph unicode="&#xef;" horiz-adv-x="518" d="M-53 1382q0 53 35.5 87t82.5 34t83 -33.5t36 -87.5q0 -53 -36 -86.5t-83 -33.5t-82.5 33.5t-35.5 86.5zM164 0v1065h190v-1065h-190zM336 1382q0 53 35.5 87t83.5 34q47 0 82.5 -33.5t35.5 -87.5q0 -53 -35.5 -86.5t-82.5 -33.5t-83 33.5t-36 86.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1206" d="M104 508q0 115 34 207t95.5 156.5t150.5 98.5t200 34q104 0 182 -33t115 -62q-25 164 -113 303l-246 -84l-47 129l209 72q-68 80 -172 156l121 104q45 -29 103.5 -79t117.5 -124l254 89l47 -129l-219 -76q66 -111 111 -258.5t45 -339.5q0 -135 -24 -261t-81 -223.5 t-154.5 -156t-244.5 -58.5q-121 0 -212 45t-151.5 119t-90.5 170t-30 201zM299 508q0 -74 18.5 -140.5t54.5 -116.5t90 -80t126 -30q94 0 154.5 45t94 119t47 168.5t13.5 192.5q0 16 -1 32.5t-1 32.5q-70 61 -145.5 84t-141.5 23q-84 0 -142 -26t-95 -71t-54.5 -104 t-17.5 -129z" />
<glyph unicode="&#xf1;" horiz-adv-x="1175" d="M164 0v1034q66 16 174 34.5t250 18.5q127 0 211 -35.5t134 -100t70.5 -154.5t20.5 -199v-598h-190v557q0 98 -13.5 168t-44.5 113t-82 62.5t-127 19.5q-31 0 -63.5 -2.5t-62 -5.5t-53 -7t-34.5 -6v-899h-190zM258 1309q8 25 26.5 57.5t45 60t62.5 47t79 19.5 q41 0 77 -14.5t69 -30.5q35 -16 59 -24.5t47 -8.5q35 0 64.5 29t46.5 61l98 -49q-10 -25 -27.5 -57.5t-44 -60t-62.5 -47t-79 -19.5q-41 0 -77 14t-69 31q-35 16 -59 24.5t-47 8.5q-35 0 -64.5 -29t-46.5 -61z" />
<glyph unicode="&#xf2;" horiz-adv-x="1208" d="M104 532q0 125 37 228.5t103.5 177.5t159 114t200.5 40q109 0 201 -40t158.5 -114t103.5 -177.5t37 -228.5q0 -127 -37 -229t-103.5 -176t-158.5 -114t-201 -40t-201 40t-158.5 114t-103.5 176.5t-37 228.5zM303 532q0 -180 81 -285.5t220 -105.5t220 105.5t81 285.5 t-81 286t-220 106t-220 -105.5t-81 -286.5zM358 1468l123 121l262 -315l-96 -86z" />
<glyph unicode="&#xf3;" horiz-adv-x="1208" d="M104 532q0 125 37 228.5t103.5 177.5t159 114t200.5 40q109 0 201 -40t158.5 -114t103.5 -177.5t37 -228.5q0 -127 -37 -229t-103.5 -176t-158.5 -114t-201 -40t-201 40t-158.5 114t-103.5 176.5t-37 228.5zM303 532q0 -180 81 -285.5t220 -105.5t220 105.5t81 285.5 t-81 286t-220 106t-220 -105.5t-81 -286.5zM467 1274l262 315l123 -121l-289 -280z" />
<glyph unicode="&#xf4;" horiz-adv-x="1208" d="M104 532q0 125 37 228.5t103.5 177.5t159 114t200.5 40q109 0 201 -40t158.5 -114t103.5 -177.5t37 -228.5q0 -127 -37 -229t-103.5 -176t-158.5 -114t-201 -40t-201 40t-158.5 114t-103.5 176.5t-37 228.5zM303 532q0 -180 81 -285.5t220 -105.5t220 105.5t81 285.5 t-81 286t-220 106t-220 -105.5t-81 -286.5zM325 1282l279 293l279 -293l-80 -90l-199 180l-199 -180z" />
<glyph unicode="&#xf5;" horiz-adv-x="1208" d="M104 532q0 125 37 228.5t103.5 177.5t159 114t200.5 40q109 0 201 -40t158.5 -114t103.5 -177.5t37 -228.5q0 -127 -37 -229t-103.5 -176t-158.5 -114t-201 -40t-201 40t-158.5 114t-103.5 176.5t-37 228.5zM268 1309q8 25 26.5 57.5t45 60t62.5 47t79 19.5 q41 0 77 -14.5t69 -30.5q35 -16 59 -24.5t47 -8.5q35 0 64.5 29t46.5 61l98 -49q-10 -25 -27.5 -57.5t-44 -60t-62.5 -47t-79 -19.5q-41 0 -77 14t-69 31q-35 16 -59 24.5t-47 8.5q-35 0 -64.5 -29t-46.5 -61zM303 532q0 -180 81 -285.5t220 -105.5t220 105.5t81 285.5 t-81 286t-220 106t-220 -105.5t-81 -286.5z" />
<glyph unicode="&#xf6;" horiz-adv-x="1208" d="M104 532q0 125 37 228.5t103.5 177.5t159 114t200.5 40q109 0 201 -40t158.5 -114t103.5 -177.5t37 -228.5q0 -127 -37 -229t-103.5 -176t-158.5 -114t-201 -40t-201 40t-158.5 114t-103.5 176.5t-37 228.5zM291 1382q0 53 35.5 87t82.5 34t83 -33.5t36 -87.5 q0 -53 -36 -86.5t-83 -33.5t-82.5 33.5t-35.5 86.5zM303 532q0 -180 81 -285.5t220 -105.5t220 105.5t81 285.5t-81 286t-220 106t-220 -105.5t-81 -286.5zM680 1382q0 53 35.5 87t83.5 34q47 0 82.5 -33.5t35.5 -87.5q0 -53 -35.5 -86.5t-82.5 -33.5t-83 33.5t-36 86.5z " />
<glyph unicode="&#xf7;" d="M109 532v162h938v-162h-938zM455 217q0 57 35.5 91t87.5 34q51 0 86.5 -34t35.5 -91t-35.5 -91t-86.5 -34t-87 34t-36 91zM455 1008q0 57 35.5 91t87.5 34q51 0 86.5 -34t35.5 -91t-35.5 -91t-86.5 -34t-87 33.5t-36 91.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1208" d="M104 532q0 125 37 228.5t103.5 177.5t159 114t200.5 40q80 0 150.5 -22.5t130.5 -63.5l100 131l109 -82l-111 -144q57 -72 89 -167t32 -212q0 -127 -37 -229t-103.5 -176t-158.5 -114t-201 -40q-162 0 -280 84l-101 -131l-108 82l110 144q-57 72 -89 168t-32 212z M301 532q0 -133 43 -225l434 561q-74 57 -174 58q-141 0 -222 -106t-81 -288zM430 195q70 -55 174 -56q141 0 222 105.5t81 287.5q0 127 -45 224z" />
<glyph unicode="&#xf9;" horiz-adv-x="1175" d="M152 467v598h190v-557q0 -195 61.5 -279t206.5 -84q31 0 64 2.5t61.5 5.5t52 6t33.5 7v899h191v-1034q-66 -16 -173.5 -35t-248.5 -19q-123 0 -207 36t-135 101.5t-73.5 154.5t-22.5 198zM336 1468l123 121l262 -315l-96 -86z" />
<glyph unicode="&#xfa;" horiz-adv-x="1175" d="M152 467v598h190v-557q0 -195 61.5 -279t206.5 -84q31 0 64 2.5t61.5 5.5t52 6t33.5 7v899h191v-1034q-66 -16 -173.5 -35t-248.5 -19q-123 0 -207 36t-135 101.5t-73.5 154.5t-22.5 198zM444 1274l262 315l123 -121l-289 -280z" />
<glyph unicode="&#xfb;" horiz-adv-x="1175" d="M152 467v598h190v-557q0 -195 61.5 -279t206.5 -84q31 0 64 2.5t61.5 5.5t52 6t33.5 7v899h191v-1034q-66 -16 -173.5 -35t-248.5 -19q-123 0 -207 36t-135 101.5t-73.5 154.5t-22.5 198zM303 1282l279 293l279 -293l-80 -90l-199 180l-199 -180z" />
<glyph unicode="&#xfc;" horiz-adv-x="1175" d="M152 467v598h190v-557q0 -195 61.5 -279t206.5 -84q31 0 64 2.5t61.5 5.5t52 6t33.5 7v899h191v-1034q-66 -16 -173.5 -35t-248.5 -19q-123 0 -207 36t-135 101.5t-73.5 154.5t-22.5 198zM269 1382q0 53 35.5 87t82.5 34t83 -33.5t36 -87.5q0 -53 -36 -86.5t-83 -33.5 t-82.5 33.5t-35.5 86.5zM658 1382q0 53 35.5 87t83.5 34q47 0 82.5 -33.5t35.5 -87.5q0 -53 -35.5 -86.5t-82.5 -33.5t-83 33.5t-36 86.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="1017" d="M4 -352l35 153q23 -10 58.5 -19t70.5 -9q113 0 176.5 50t114.5 163q-129 246 -241 521t-185 558h205q23 -92 54.5 -198.5t70 -219t84 -225.5t94.5 -217q78 215 135 426t108 434h197q-74 -301 -164 -578.5t-194 -519.5q-41 -92 -85 -158.5t-97.5 -109.5t-120 -63.5 t-150.5 -20.5q-23 0 -47.5 3t-48 8t-43 11.5t-27.5 10.5zM397 1274l262 315l123 -121l-289 -280z" />
<glyph unicode="&#xfe;" horiz-adv-x="1206" d="M164 -379v1935l190 33v-542q53 18 111.5 29t112.5 11q117 0 214 -38.5t165.5 -110.5t106.5 -175.5t38 -232.5q0 -121 -32 -223t-93.5 -176t-150.5 -115t-203 -41q-92 0 -163 25t-106 47v-426h-190zM354 225q35 -29 100.5 -55.5t143.5 -26.5q82 0 140.5 30t95 82t53 122.5 t16.5 152.5q0 186 -92 288t-246 102q-31 0 -63.5 -4.5t-62 -9.5t-52 -11t-33.5 -10v-660z" />
<glyph unicode="&#xff;" horiz-adv-x="1017" d="M4 -352l35 153q23 -10 58.5 -19t70.5 -9q113 0 176.5 50t114.5 163q-129 246 -241 521t-185 558h205q23 -92 54.5 -198.5t70 -219t84 -225.5t94.5 -217q78 215 135 426t108 434h197q-74 -301 -164 -578.5t-194 -519.5q-41 -92 -85 -158.5t-97.5 -109.5t-120 -63.5 t-150.5 -20.5q-23 0 -47.5 3t-48 8t-43 11.5t-27.5 10.5zM222 1382q0 53 35.5 87t82.5 34t83 -33.5t36 -87.5q0 -53 -36 -86.5t-83 -33.5t-82.5 33.5t-35.5 86.5zM611 1382q0 53 35.5 87t83.5 34q47 0 82.5 -33.5t35.5 -87.5q0 -53 -35.5 -86.5t-82.5 -33.5t-83 33.5 t-36 86.5z" />
<glyph unicode="&#x152;" horiz-adv-x="2033" d="M119 711q0 182 59.5 317t162.5 225t244.5 135.5t307.5 45.5q47 0 104.5 -4.5t108.5 -10.5h801v-166h-637v-428h563v-161h-563v-498h688v-166h-852q-51 -6 -109.5 -10t-105.5 -4q-166 0 -307.5 45t-244.5 135t-161.5 226t-58.5 319zM330 711q0 -131 39 -233.5 t111.5 -173.5t177 -108.5t233.5 -37.5q47 0 81 1t56 3q25 2 43 6v1083q-45 6 -90 8.5t-90 2.5q-129 0 -233.5 -38t-177 -109t-111.5 -173t-39 -231z" />
<glyph unicode="&#x153;" horiz-adv-x="1939" d="M104 532q0 125 37 228.5t103.5 177.5t159 114t200.5 40q129 0 230.5 -60.5t164.5 -165.5q68 115 171.5 170.5t213.5 55.5q213 0 329 -135.5t116 -407.5q0 -16 -1 -27.5t-1 -21.5v-19h-729q12 -164 97 -251t261 -87q100 0 169 17.5t104 34.5l26 -160q-35 -18 -121.5 -39 t-197.5 -21q-162 0 -269.5 59.5t-167.5 164.5q-66 -111 -166 -168.5t-229 -57.5q-109 0 -201 40t-158.5 114t-103.5 176.5t-37 228.5zM303 532q0 -180 81 -285.5t220 -105.5t220 105.5t81 285.5t-81 286t-220 106t-220 -105.5t-81 -286.5zM1102 635h528q2 129 -64.5 212 t-183.5 83q-66 0 -115.5 -25.5t-83.5 -66.5t-54.5 -94.5t-26.5 -108.5z" />
<glyph unicode="&#x178;" horiz-adv-x="1224" d="M12 1419h234q78 -168 174 -341t198 -333q100 160 196.5 333t176.5 341h221q-111 -205 -235.5 -416.5t-263.5 -429.5v-573h-199v569q-141 219 -266 432t-236 418zM299 1701q0 53 35.5 87t82.5 34t83 -33.5t36 -87.5q0 -53 -36 -86.5t-83 -33.5t-82.5 33.5t-35.5 86.5z M688 1701q0 53 35.5 87t83.5 34q47 0 82.5 -33.5t35.5 -87.5q0 -53 -35.5 -86.5t-82.5 -33.5t-83 33.5t-36 86.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="770" d="M106 1282l279 293l279 -293l-80 -90l-199 180l-199 -180z" />
<glyph unicode="&#x2dc;" horiz-adv-x="770" d="M47 1309q8 25 26.5 57.5t45 60t62.5 47t79 19.5q41 0 77 -14.5t69 -30.5q35 -16 59 -24.5t47 -8.5q35 0 64.5 29t46.5 61l98 -49q-10 -25 -27.5 -57.5t-44 -60t-62.5 -47t-79 -19.5q-41 0 -77 14t-69 31q-35 16 -59 24.5t-47 8.5q-35 0 -64.5 -29t-46.5 -61z" />
<glyph unicode="&#x2000;" horiz-adv-x="954" />
<glyph unicode="&#x2001;" horiz-adv-x="1908" />
<glyph unicode="&#x2002;" horiz-adv-x="954" />
<glyph unicode="&#x2003;" horiz-adv-x="1908" />
<glyph unicode="&#x2004;" horiz-adv-x="636" />
<glyph unicode="&#x2005;" horiz-adv-x="477" />
<glyph unicode="&#x2006;" horiz-adv-x="318" />
<glyph unicode="&#x2007;" horiz-adv-x="318" />
<glyph unicode="&#x2008;" horiz-adv-x="238" />
<glyph unicode="&#x2009;" horiz-adv-x="381" />
<glyph unicode="&#x200a;" horiz-adv-x="106" />
<glyph unicode="&#x2010;" horiz-adv-x="612" d="M53 524v178h506v-178h-506z" />
<glyph unicode="&#x2011;" horiz-adv-x="612" d="M53 524v178h506v-178h-506z" />
<glyph unicode="&#x2012;" horiz-adv-x="612" d="M53 524v178h506v-178h-506z" />
<glyph unicode="&#x2013;" horiz-adv-x="1015" d="M-4 532v162h1024v-162h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2039" d="M-4 532v162h2048v-162h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="481" d="M123 1065v44t2 42q8 106 47 210.5t94 194.5l148 -47q-43 -90 -64.5 -182t-21.5 -172q0 -20 1 -44t3 -46h-209z" />
<glyph unicode="&#x2019;" horiz-adv-x="481" d="M68 1092q43 92 64.5 183t21.5 171q0 20 -1 43.5t-3 46.5h208v-44.5t-2 -41.5q-8 -106 -47 -209.5t-94 -196.5z" />
<glyph unicode="&#x201a;" horiz-adv-x="481" d="M68 -211q43 90 64.5 182t21.5 172q0 20 -1 44t-3 46h208v-44t-2 -42q-8 -106 -47 -210.5t-94 -194.5z" />
<glyph unicode="&#x201c;" horiz-adv-x="854" d="M123 1065v44t2 42q8 106 47 210.5t94 194.5l148 -47q-43 -90 -64.5 -182t-21.5 -172q0 -20 1 -44t3 -46h-209zM496 1065v44t2 42q8 106 47 210.5t94 194.5l148 -47q-43 -90 -64.5 -182t-21.5 -172q0 -20 1 -44t3 -46h-209z" />
<glyph unicode="&#x201d;" horiz-adv-x="854" d="M68 1092q43 92 64.5 183t21.5 171q0 20 -1 43.5t-3 46.5h208v-44.5t-2 -41.5q-8 -106 -47 -209.5t-94 -196.5zM441 1092q43 92 64.5 183t21.5 171q0 20 -1 43.5t-3 46.5h208v-44.5t-2 -41.5q-8 -106 -47 -209.5t-94 -196.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="854" d="M68 -211q43 90 64.5 182t21.5 172q0 20 -1 44t-3 46h208v-44t-2 -42q-8 -106 -47 -210.5t-94 -194.5zM441 -211q43 90 64.5 182t21.5 172q0 20 -1 44t-3 46h208v-44t-2 -42q-8 -106 -47 -210.5t-94 -194.5z" />
<glyph unicode="&#x2022;" horiz-adv-x="737" d="M106 723q0 51 18.5 99t52.5 83t82 56.5t110 21.5q61 0 109 -21.5t82 -56.5t52.5 -83t18.5 -99q0 -53 -18.5 -100.5t-52.5 -82t-82 -56t-109 -21.5t-109.5 21.5t-82.5 56t-52.5 82t-18.5 100.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="2039" d="M201 111q0 55 36.5 96t100.5 41q63 0 100 -41t37 -96t-37 -96.5t-100 -41.5t-100 41t-37 97zM883 111q0 55 36.5 96t100.5 41q63 0 100 -41t37 -96t-37 -96.5t-100 -41.5t-100 41t-37 97zM1565 111q0 55 36.5 96t100.5 41q63 0 100 -41t37 -96t-37 -96.5t-100 -41.5 t-100 41t-37 97z" />
<glyph unicode="&#x202f;" horiz-adv-x="381" />
<glyph unicode="&#x2039;" horiz-adv-x="595" d="M61 582l320 452l133 -71l-227 -381l227 -381l-133 -72z" />
<glyph unicode="&#x203a;" horiz-adv-x="595" d="M82 201l227 381l-227 381l133 71l320 -452l-320 -453z" />
<glyph unicode="&#x205f;" horiz-adv-x="477" />
<glyph unicode="&#x20ac;" d="M92 465v147h168q-2 23 -2 47.5v51.5v55.5t2 52.5h-168v148h189q47 240 186 359.5t356 119.5q90 0 149.5 -11.5t119.5 -29.5l-41 -164q-49 18 -107.5 29.5t-120.5 11.5q-78 0 -136 -22.5t-100 -63.5t-68.5 -99.5t-41.5 -129.5h477l-28 -148h-469q-2 -27 -2 -53.5v-54.5 v-51.5t2 -47.5h428l-29 -147h-383q35 -182 126 -254t228 -72q86 0 148.5 16.5t111.5 39.5l41 -162q-35 -18 -120.5 -39t-190.5 -21q-240 0 -371 130t-170 362h-184z" />
<glyph unicode="&#x2122;" horiz-adv-x="1554" d="M47 1294v125h571v-125h-215v-557h-141v557h-215zM647 737q16 252 28.5 415t29.5 267h135q16 -35 40.5 -84t53.5 -106.5t58.5 -116.5t58.5 -115q27 55 57.5 114.5t59 117t53 106.5t41.5 84h135q16 -104 28.5 -267t28.5 -415h-143q-2 51 -4 109.5t-5.5 117t-7.5 116 t-8 106.5q-23 -39 -49.5 -91.5t-52 -103.5t-49 -96t-35.5 -72h-101q-12 27 -35.5 72t-49 96t-53 103.5t-48.5 91.5q-4 -49 -8 -106.5t-7 -116t-5 -117t-5 -109.5h-141z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1064" d="M0 0v1065h1065v-1065h-1065z" />
<hkern u1="K" u2="&#xef;" k="-43" />
<hkern u1="T" u2="&#xef;" k="-63" />
<hkern u1="T" u2="&#xee;" k="-43" />
<hkern u1="V" u2="&#xef;" k="-66" />
<hkern u1="V" u2="&#xec;" k="-23" />
<hkern u1="W" u2="&#xef;" k="-37" />
<hkern u1="Y" u2="&#xef;" k="-84" />
<hkern u1="Z" u2="&#xef;" k="-20" />
<hkern u1="f" u2="&#xef;" k="-82" />
<hkern u1="f" u2="&#xec;" k="-61" />
<hkern u1="&#xdd;" u2="&#xef;" k="-84" />
<hkern u1="&#x178;" u2="&#xef;" k="-84" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="v" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="w" 	k="25" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="y,yacute,ydieresis" 	k="31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quotedbl,quotesingle" 	k="55" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="parenright" 	k="49" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="asterisk" 	k="41" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="question" 	k="57" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="bracketright" 	k="61" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="braceright" 	k="43" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteright,quotedblright" 	k="49" />
<hkern g1="b" 	g2="v" 	k="27" />
<hkern g1="b" 	g2="w" 	k="25" />
<hkern g1="b" 	g2="y,yacute,ydieresis" 	k="27" />
<hkern g1="b" 	g2="quotedbl,quotesingle" 	k="76" />
<hkern g1="b" 	g2="parenright" 	k="55" />
<hkern g1="b" 	g2="question" 	k="57" />
<hkern g1="b" 	g2="bracketright" 	k="61" />
<hkern g1="b" 	g2="braceright" 	k="43" />
<hkern g1="b" 	g2="quoteright,quotedblright" 	k="68" />
<hkern g1="b" 	g2="slash" 	k="45" />
<hkern g1="b" 	g2="quoteleft,quotedblleft" 	k="68" />
<hkern g1="b" 	g2="x" 	k="35" />
<hkern g1="b" 	g2="z" 	k="41" />
<hkern g1="c,ccedilla" 	g2="v" 	k="-23" />
<hkern g1="c,ccedilla" 	g2="w" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="y,yacute,ydieresis" 	k="-23" />
<hkern g1="c,ccedilla" 	g2="bracketright" 	k="55" />
<hkern g1="c,ccedilla" 	g2="x" 	k="-33" />
<hkern g1="c,ccedilla" 	g2="hyphen,endash,emdash" 	k="66" />
<hkern g1="c,ccedilla" 	g2="braceleft" 	k="43" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="59" />
<hkern g1="c,ccedilla" 	g2="d" 	k="25" />
<hkern g1="c,ccedilla" 	g2="g" 	k="33" />
<hkern g1="c,ccedilla" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="c,ccedilla" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="f" 	g2="v" 	k="-39" />
<hkern g1="f" 	g2="w" 	k="-37" />
<hkern g1="f" 	g2="y,yacute,ydieresis" 	k="-39" />
<hkern g1="f" 	g2="parenright" 	k="-80" />
<hkern g1="f" 	g2="asterisk" 	k="-23" />
<hkern g1="f" 	g2="question" 	k="-49" />
<hkern g1="f" 	g2="bracketright" 	k="-80" />
<hkern g1="f" 	g2="braceright" 	k="-80" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="f" 	g2="slash" 	k="86" />
<hkern g1="f" 	g2="x" 	k="-31" />
<hkern g1="f" 	g2="hyphen,endash,emdash" 	k="72" />
<hkern g1="f" 	g2="guillemotleft,guilsinglleft" 	k="39" />
<hkern g1="f" 	g2="comma,period,ellipsis" 	k="106" />
<hkern g1="f" 	g2="eth" 	k="35" />
<hkern g1="f" 	g2="quotesinglbase,quotedblbase" 	k="109" />
<hkern g1="f" 	g2="guillemotright,guilsinglright" 	k="-18" />
<hkern g1="h" 	g2="v" 	k="31" />
<hkern g1="h" 	g2="w" 	k="27" />
<hkern g1="h" 	g2="y,yacute,ydieresis" 	k="35" />
<hkern g1="h" 	g2="quotedbl,quotesingle" 	k="76" />
<hkern g1="h" 	g2="parenright" 	k="49" />
<hkern g1="h" 	g2="asterisk" 	k="43" />
<hkern g1="h" 	g2="question" 	k="55" />
<hkern g1="h" 	g2="bracketright" 	k="59" />
<hkern g1="h" 	g2="braceright" 	k="43" />
<hkern g1="h" 	g2="quoteright,quotedblright" 	k="61" />
<hkern g1="h" 	g2="quoteleft,quotedblleft" 	k="61" />
<hkern g1="j" 	g2="j" 	k="-18" />
<hkern g1="k" 	g2="bracketright" 	k="51" />
<hkern g1="k" 	g2="slash" 	k="-23" />
<hkern g1="k" 	g2="x" 	k="-39" />
<hkern g1="k" 	g2="hyphen,endash,emdash" 	k="55" />
<hkern g1="k" 	g2="braceleft" 	k="39" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="70" />
<hkern g1="k" 	g2="d" 	k="53" />
<hkern g1="k" 	g2="g" 	k="59" />
<hkern g1="k" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="59" />
<hkern g1="k" 	g2="eth" 	k="55" />
<hkern g1="k" 	g2="c,ccedilla" 	k="53" />
<hkern g1="k" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="53" />
<hkern g1="k" 	g2="q" 	k="53" />
<hkern g1="k" 	g2="at" 	k="41" />
<hkern g1="m,n,ntilde" 	g2="v" 	k="33" />
<hkern g1="m,n,ntilde" 	g2="w" 	k="29" />
<hkern g1="m,n,ntilde" 	g2="y,yacute,ydieresis" 	k="37" />
<hkern g1="m,n,ntilde" 	g2="quotedbl,quotesingle" 	k="63" />
<hkern g1="m,n,ntilde" 	g2="parenright" 	k="51" />
<hkern g1="m,n,ntilde" 	g2="asterisk" 	k="45" />
<hkern g1="m,n,ntilde" 	g2="question" 	k="57" />
<hkern g1="m,n,ntilde" 	g2="bracketright" 	k="61" />
<hkern g1="m,n,ntilde" 	g2="braceright" 	k="43" />
<hkern g1="m,n,ntilde" 	g2="quoteright,quotedblright" 	k="53" />
<hkern g1="m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="45" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="v" 	k="29" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="w" 	k="27" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="y,yacute,ydieresis" 	k="29" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotedbl,quotesingle" 	k="66" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="parenright" 	k="55" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="asterisk" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="question" 	k="61" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="bracketright" 	k="61" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="braceright" 	k="43" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteright,quotedblright" 	k="55" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="slash" 	k="47" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteleft,quotedblleft" 	k="45" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="x" 	k="35" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="z" 	k="41" />
<hkern g1="p" 	g2="v" 	k="29" />
<hkern g1="p" 	g2="w" 	k="25" />
<hkern g1="p" 	g2="y,yacute,ydieresis" 	k="29" />
<hkern g1="p" 	g2="quotedbl,quotesingle" 	k="66" />
<hkern g1="p" 	g2="parenright" 	k="55" />
<hkern g1="p" 	g2="asterisk" 	k="41" />
<hkern g1="p" 	g2="question" 	k="57" />
<hkern g1="p" 	g2="bracketright" 	k="61" />
<hkern g1="p" 	g2="braceright" 	k="43" />
<hkern g1="p" 	g2="quoteright,quotedblright" 	k="55" />
<hkern g1="p" 	g2="slash" 	k="45" />
<hkern g1="p" 	g2="quoteleft,quotedblleft" 	k="45" />
<hkern g1="p" 	g2="x" 	k="35" />
<hkern g1="p" 	g2="z" 	k="37" />
<hkern g1="q" 	g2="j" 	k="-61" />
<hkern g1="r" 	g2="v" 	k="-43" />
<hkern g1="r" 	g2="w" 	k="-39" />
<hkern g1="r" 	g2="y,yacute,ydieresis" 	k="-43" />
<hkern g1="r" 	g2="asterisk" 	k="-25" />
<hkern g1="r" 	g2="question" 	k="88" />
<hkern g1="r" 	g2="bracketright" 	k="49" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-29" />
<hkern g1="r" 	g2="slash" 	k="94" />
<hkern g1="r" 	g2="x" 	k="-33" />
<hkern g1="r" 	g2="hyphen,endash,emdash" 	k="66" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="43" />
<hkern g1="r" 	g2="comma,period,ellipsis" 	k="113" />
<hkern g1="r" 	g2="eth" 	k="39" />
<hkern g1="r" 	g2="quotesinglbase,quotedblbase" 	k="106" />
<hkern g1="r" 	g2="q" 	k="25" />
<hkern g1="t" 	g2="bracketright" 	k="55" />
<hkern g1="t" 	g2="x" 	k="-35" />
<hkern g1="t" 	g2="hyphen,endash,emdash" 	k="80" />
<hkern g1="t" 	g2="braceleft" 	k="43" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="57" />
<hkern g1="t" 	g2="d" 	k="29" />
<hkern g1="t" 	g2="g" 	k="20" />
<hkern g1="t" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="t" 	g2="comma,period,ellipsis" 	k="-23" />
<hkern g1="t" 	g2="eth" 	k="25" />
<hkern g1="t" 	g2="c,ccedilla" 	k="27" />
<hkern g1="t" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="20" />
<hkern g1="t" 	g2="q" 	k="25" />
<hkern g1="v" 	g2="v" 	k="-37" />
<hkern g1="v" 	g2="w" 	k="-35" />
<hkern g1="v" 	g2="y,yacute,ydieresis" 	k="-37" />
<hkern g1="v" 	g2="asterisk" 	k="-18" />
<hkern g1="v" 	g2="question" 	k="80" />
<hkern g1="v" 	g2="bracketright" 	k="53" />
<hkern g1="v" 	g2="quoteright,quotedblright" 	k="-25" />
<hkern g1="v" 	g2="slash" 	k="66" />
<hkern g1="v" 	g2="x" 	k="-29" />
<hkern g1="v" 	g2="d" 	k="29" />
<hkern g1="v" 	g2="g" 	k="27" />
<hkern g1="v" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="v" 	g2="comma,period,ellipsis" 	k="72" />
<hkern g1="v" 	g2="eth" 	k="35" />
<hkern g1="v" 	g2="quotesinglbase,quotedblbase" 	k="74" />
<hkern g1="v" 	g2="c,ccedilla" 	k="31" />
<hkern g1="v" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="29" />
<hkern g1="v" 	g2="q" 	k="29" />
<hkern g1="w" 	g2="v" 	k="-37" />
<hkern g1="w" 	g2="w" 	k="-35" />
<hkern g1="w" 	g2="y,yacute,ydieresis" 	k="-37" />
<hkern g1="w" 	g2="asterisk" 	k="-18" />
<hkern g1="w" 	g2="question" 	k="72" />
<hkern g1="w" 	g2="bracketright" 	k="53" />
<hkern g1="w" 	g2="quoteright,quotedblright" 	k="-25" />
<hkern g1="w" 	g2="slash" 	k="57" />
<hkern g1="w" 	g2="x" 	k="-29" />
<hkern g1="w" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="w" 	g2="comma,period,ellipsis" 	k="66" />
<hkern g1="w" 	g2="eth" 	k="29" />
<hkern g1="w" 	g2="quotesinglbase,quotedblbase" 	k="61" />
<hkern g1="w" 	g2="c,ccedilla" 	k="27" />
<hkern g1="x" 	g2="bracketright" 	k="55" />
<hkern g1="x" 	g2="x" 	k="-35" />
<hkern g1="x" 	g2="braceleft" 	k="43" />
<hkern g1="x" 	g2="guillemotleft,guilsinglleft" 	k="59" />
<hkern g1="x" 	g2="d" 	k="43" />
<hkern g1="x" 	g2="g" 	k="43" />
<hkern g1="x" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="43" />
<hkern g1="x" 	g2="eth" 	k="49" />
<hkern g1="x" 	g2="c,ccedilla" 	k="43" />
<hkern g1="x" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="43" />
<hkern g1="x" 	g2="q" 	k="43" />
<hkern g1="z" 	g2="bracketright" 	k="70" />
<hkern g1="z" 	g2="braceright" 	k="43" />
<hkern g1="z" 	g2="braceleft" 	k="57" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="68" />
<hkern g1="z" 	g2="d" 	k="41" />
<hkern g1="z" 	g2="g" 	k="45" />
<hkern g1="z" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="z" 	g2="eth" 	k="37" />
<hkern g1="z" 	g2="c,ccedilla" 	k="39" />
<hkern g1="z" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="39" />
<hkern g1="z" 	g2="q" 	k="37" />
<hkern g1="z" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="25" />
<hkern g1="z" 	g2="t" 	k="27" />
<hkern g1="z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="27" />
<hkern g1="germandbls" 	g2="v" 	k="57" />
<hkern g1="germandbls" 	g2="w" 	k="51" />
<hkern g1="germandbls" 	g2="y,yacute,ydieresis" 	k="57" />
<hkern g1="germandbls" 	g2="quotedbl,quotesingle" 	k="82" />
<hkern g1="germandbls" 	g2="parenright" 	k="55" />
<hkern g1="germandbls" 	g2="asterisk" 	k="68" />
<hkern g1="germandbls" 	g2="question" 	k="41" />
<hkern g1="germandbls" 	g2="bracketright" 	k="43" />
<hkern g1="germandbls" 	g2="braceright" 	k="43" />
<hkern g1="germandbls" 	g2="quoteright,quotedblright" 	k="61" />
<hkern g1="germandbls" 	g2="quoteleft,quotedblleft" 	k="63" />
<hkern g1="eth" 	g2="question" 	k="-31" />
<hkern g1="eth" 	g2="bracketright" 	k="-27" />
<hkern g1="eth" 	g2="braceright" 	k="-27" />
<hkern g1="eth" 	g2="slash" 	k="43" />
<hkern g1="thorn" 	g2="v" 	k="29" />
<hkern g1="thorn" 	g2="w" 	k="25" />
<hkern g1="thorn" 	g2="y,yacute,ydieresis" 	k="29" />
<hkern g1="thorn" 	g2="quotedbl,quotesingle" 	k="82" />
<hkern g1="thorn" 	g2="parenright" 	k="55" />
<hkern g1="thorn" 	g2="asterisk" 	k="41" />
<hkern g1="thorn" 	g2="question" 	k="57" />
<hkern g1="thorn" 	g2="bracketright" 	k="61" />
<hkern g1="thorn" 	g2="braceright" 	k="43" />
<hkern g1="thorn" 	g2="quoteright,quotedblright" 	k="74" />
<hkern g1="thorn" 	g2="slash" 	k="45" />
<hkern g1="thorn" 	g2="quoteleft,quotedblleft" 	k="74" />
<hkern g1="thorn" 	g2="x" 	k="35" />
<hkern g1="thorn" 	g2="z" 	k="37" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v" 	k="43" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="39" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="y,yacute,ydieresis" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle" 	k="160" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk" 	k="129" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="bracketright" 	k="55" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="braceright" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="139" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="slash" 	k="-35" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="145" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="x" 	k="-53" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="z" 	k="-31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="braceleft" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guillemotleft,guilsinglleft" 	k="39" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="d" 	k="27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="g" 	k="33" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="comma,period,ellipsis" 	k="-53" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="eth" 	k="27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotesinglbase,quotedblbase" 	k="-51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="c,ccedilla" 	k="27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="q" 	k="27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="at" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="s" 	k="-27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,Ccedilla" 	k="39" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="G" 	k="39" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="39" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Q" 	k="39" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="131" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="127" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="W" 	k="37" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="162" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="parenleft" 	k="39" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-68" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="J" 	k="-59" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="S" 	k="-33" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="X" 	k="-51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Z" 	k="-39" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="AE" 	k="-70" />
<hkern g1="B" 	g2="quotedbl,quotesingle" 	k="23" />
<hkern g1="B" 	g2="parenright" 	k="51" />
<hkern g1="B" 	g2="asterisk" 	k="23" />
<hkern g1="B" 	g2="question" 	k="41" />
<hkern g1="B" 	g2="bracketright" 	k="70" />
<hkern g1="B" 	g2="braceright" 	k="45" />
<hkern g1="B" 	g2="slash" 	k="43" />
<hkern g1="B" 	g2="z" 	k="27" />
<hkern g1="B" 	g2="comma,period,ellipsis" 	k="20" />
<hkern g1="B" 	g2="colon,semicolon" 	k="23" />
<hkern g1="B" 	g2="V" 	k="39" />
<hkern g1="B" 	g2="W" 	k="27" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="51" />
<hkern g1="B" 	g2="parenleft" 	k="23" />
<hkern g1="B" 	g2="X" 	k="43" />
<hkern g1="B" 	g2="AE" 	k="25" />
<hkern g1="C,Ccedilla" 	g2="v" 	k="33" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="35" />
<hkern g1="C,Ccedilla" 	g2="y,yacute,ydieresis" 	k="33" />
<hkern g1="C,Ccedilla" 	g2="question" 	k="-31" />
<hkern g1="C,Ccedilla" 	g2="quoteright,quotedblright" 	k="-27" />
<hkern g1="C,Ccedilla" 	g2="x" 	k="-29" />
<hkern g1="C,Ccedilla" 	g2="hyphen,endash,emdash" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="braceleft" 	k="57" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="92" />
<hkern g1="C,Ccedilla" 	g2="d" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="comma,period,ellipsis" 	k="-45" />
<hkern g1="C,Ccedilla" 	g2="eth" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="quotesinglbase,quotedblbase" 	k="-37" />
<hkern g1="C,Ccedilla" 	g2="c,ccedilla" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="q" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="at" 	k="35" />
<hkern g1="C,Ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="C,Ccedilla" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="G" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="Q" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="-35" />
<hkern g1="C,Ccedilla" 	g2="V" 	k="-35" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="-35" />
<hkern g1="C,Ccedilla" 	g2="parenleft" 	k="47" />
<hkern g1="C,Ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-45" />
<hkern g1="C,Ccedilla" 	g2="J" 	k="-47" />
<hkern g1="C,Ccedilla" 	g2="S" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="X" 	k="-27" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="AE" 	k="-45" />
<hkern g1="C,Ccedilla" 	g2="bracketleft" 	k="20" />
<hkern g1="D,Eth" 	g2="quotedbl,quotesingle" 	k="53" />
<hkern g1="D,Eth" 	g2="parenright" 	k="68" />
<hkern g1="D,Eth" 	g2="question" 	k="80" />
<hkern g1="D,Eth" 	g2="bracketright" 	k="86" />
<hkern g1="D,Eth" 	g2="braceright" 	k="59" />
<hkern g1="D,Eth" 	g2="quoteright,quotedblright" 	k="25" />
<hkern g1="D,Eth" 	g2="slash" 	k="88" />
<hkern g1="D,Eth" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="D,Eth" 	g2="comma,period,ellipsis" 	k="68" />
<hkern g1="D,Eth" 	g2="quotesinglbase,quotedblbase" 	k="70" />
<hkern g1="D,Eth" 	g2="at" 	k="23" />
<hkern g1="D,Eth" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="29" />
<hkern g1="D,Eth" 	g2="T" 	k="55" />
<hkern g1="D,Eth" 	g2="V" 	k="35" />
<hkern g1="D,Eth" 	g2="W" 	k="35" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="84" />
<hkern g1="D,Eth" 	g2="parenleft" 	k="25" />
<hkern g1="D,Eth" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="39" />
<hkern g1="D,Eth" 	g2="J" 	k="51" />
<hkern g1="D,Eth" 	g2="S" 	k="23" />
<hkern g1="D,Eth" 	g2="X" 	k="49" />
<hkern g1="D,Eth" 	g2="Z" 	k="31" />
<hkern g1="D,Eth" 	g2="AE" 	k="86" />
<hkern g1="D,Eth" 	g2="bracketleft" 	k="23" />
<hkern g1="D,Eth" 	g2="exclam" 	k="29" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v" 	k="51" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="51" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="y,yacute,ydieresis" 	k="51" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="quotedbl,quotesingle" 	k="33" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="asterisk" 	k="27" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="bracketright" 	k="88" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="braceright" 	k="45" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="quoteleft,quotedblleft" 	k="27" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="x" 	k="-18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="braceleft" 	k="39" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guillemotleft,guilsinglleft" 	k="47" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="d" 	k="35" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="g" 	k="47" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="35" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="comma,period,ellipsis" 	k="-23" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="eth" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,ccedilla" 	k="33" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="33" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="q" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="at" 	k="33" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="29" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="39" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="C,Ccedilla" 	k="57" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="G" 	k="57" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="57" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Q" 	k="57" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="45" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="parenleft" 	k="43" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-35" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="J" 	k="-35" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="AE" 	k="-37" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="bracketleft" 	k="35" />
<hkern g1="F" 	g2="question" 	k="-27" />
<hkern g1="F" 	g2="bracketright" 	k="84" />
<hkern g1="F" 	g2="slash" 	k="123" />
<hkern g1="F" 	g2="x" 	k="74" />
<hkern g1="F" 	g2="z" 	k="53" />
<hkern g1="F" 	g2="d" 	k="29" />
<hkern g1="F" 	g2="g" 	k="29" />
<hkern g1="F" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="F" 	g2="comma,period,ellipsis" 	k="133" />
<hkern g1="F" 	g2="eth" 	k="31" />
<hkern g1="F" 	g2="quotesinglbase,quotedblbase" 	k="211" />
<hkern g1="F" 	g2="guillemotright,guilsinglright" 	k="43" />
<hkern g1="F" 	g2="c,ccedilla" 	k="29" />
<hkern g1="F" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="29" />
<hkern g1="F" 	g2="q" 	k="29" />
<hkern g1="F" 	g2="at" 	k="37" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="51" />
<hkern g1="F" 	g2="colon,semicolon" 	k="37" />
<hkern g1="F" 	g2="r" 	k="57" />
<hkern g1="F" 	g2="m,n,ntilde" 	k="57" />
<hkern g1="F" 	g2="p" 	k="57" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="104" />
<hkern g1="F" 	g2="C,Ccedilla" 	k="27" />
<hkern g1="F" 	g2="G" 	k="27" />
<hkern g1="F" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="27" />
<hkern g1="F" 	g2="Q" 	k="27" />
<hkern g1="F" 	g2="T" 	k="-29" />
<hkern g1="F" 	g2="V" 	k="-37" />
<hkern g1="F" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="F" 	g2="parenleft" 	k="49" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="94" />
<hkern g1="F" 	g2="J" 	k="164" />
<hkern g1="F" 	g2="AE" 	k="156" />
<hkern g1="F" 	g2="bracketleft" 	k="31" />
<hkern g1="F" 	g2="exclam" 	k="20" />
<hkern g1="G" 	g2="v" 	k="37" />
<hkern g1="G" 	g2="y,yacute,ydieresis" 	k="37" />
<hkern g1="G" 	g2="parenright" 	k="20" />
<hkern g1="G" 	g2="asterisk" 	k="51" />
<hkern g1="G" 	g2="bracketright" 	k="20" />
<hkern g1="G" 	g2="braceright" 	k="20" />
<hkern g1="J" 	g2="parenright" 	k="20" />
<hkern g1="J" 	g2="bracketright" 	k="70" />
<hkern g1="J" 	g2="braceright" 	k="39" />
<hkern g1="J" 	g2="slash" 	k="74" />
<hkern g1="J" 	g2="z" 	k="37" />
<hkern g1="J" 	g2="comma,period,ellipsis" 	k="20" />
<hkern g1="J" 	g2="quotesinglbase,quotedblbase" 	k="27" />
<hkern g1="J" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="J" 	g2="J" 	k="31" />
<hkern g1="J" 	g2="Z" 	k="35" />
<hkern g1="J" 	g2="AE" 	k="51" />
<hkern g1="K" 	g2="v" 	k="29" />
<hkern g1="K" 	g2="w" 	k="98" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="K" 	g2="asterisk" 	k="68" />
<hkern g1="K" 	g2="question" 	k="-23" />
<hkern g1="K" 	g2="bracketright" 	k="53" />
<hkern g1="K" 	g2="slash" 	k="-37" />
<hkern g1="K" 	g2="x" 	k="-55" />
<hkern g1="K" 	g2="z" 	k="-33" />
<hkern g1="K" 	g2="hyphen,endash,emdash" 	k="96" />
<hkern g1="K" 	g2="braceleft" 	k="39" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="125" />
<hkern g1="K" 	g2="d" 	k="43" />
<hkern g1="K" 	g2="g" 	k="43" />
<hkern g1="K" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="43" />
<hkern g1="K" 	g2="comma,period,ellipsis" 	k="-33" />
<hkern g1="K" 	g2="eth" 	k="51" />
<hkern g1="K" 	g2="quotesinglbase,quotedblbase" 	k="-31" />
<hkern g1="K" 	g2="c,ccedilla" 	k="43" />
<hkern g1="K" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="43" />
<hkern g1="K" 	g2="q" 	k="43" />
<hkern g1="K" 	g2="at" 	k="39" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="27" />
<hkern g1="K" 	g2="s" 	k="-29" />
<hkern g1="K" 	g2="C,Ccedilla" 	k="72" />
<hkern g1="K" 	g2="G" 	k="72" />
<hkern g1="K" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="72" />
<hkern g1="K" 	g2="Q" 	k="72" />
<hkern g1="K" 	g2="T" 	k="-35" />
<hkern g1="K" 	g2="V" 	k="-41" />
<hkern g1="K" 	g2="W" 	k="-20" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="-45" />
<hkern g1="K" 	g2="parenleft" 	k="37" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-70" />
<hkern g1="K" 	g2="J" 	k="-55" />
<hkern g1="K" 	g2="X" 	k="-53" />
<hkern g1="K" 	g2="Z" 	k="-43" />
<hkern g1="K" 	g2="AE" 	k="-72" />
<hkern g1="L" 	g2="v" 	k="82" />
<hkern g1="L" 	g2="w" 	k="76" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="258" />
<hkern g1="L" 	g2="asterisk" 	k="285" />
<hkern g1="L" 	g2="bracketright" 	k="66" />
<hkern g1="L" 	g2="braceright" 	k="31" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="242" />
<hkern g1="L" 	g2="slash" 	k="-25" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="246" />
<hkern g1="L" 	g2="x" 	k="-43" />
<hkern g1="L" 	g2="z" 	k="-20" />
<hkern g1="L" 	g2="hyphen,endash,emdash" 	k="141" />
<hkern g1="L" 	g2="braceleft" 	k="49" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="80" />
<hkern g1="L" 	g2="d" 	k="25" />
<hkern g1="L" 	g2="g" 	k="25" />
<hkern g1="L" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="L" 	g2="comma,period,ellipsis" 	k="-47" />
<hkern g1="L" 	g2="quotesinglbase,quotedblbase" 	k="-29" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="-23" />
<hkern g1="L" 	g2="c,ccedilla" 	k="25" />
<hkern g1="L" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="25" />
<hkern g1="L" 	g2="q" 	k="25" />
<hkern g1="L" 	g2="C,Ccedilla" 	k="92" />
<hkern g1="L" 	g2="G" 	k="92" />
<hkern g1="L" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="92" />
<hkern g1="L" 	g2="Q" 	k="92" />
<hkern g1="L" 	g2="T" 	k="236" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="37" />
<hkern g1="L" 	g2="V" 	k="205" />
<hkern g1="L" 	g2="W" 	k="92" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="236" />
<hkern g1="L" 	g2="parenleft" 	k="20" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-57" />
<hkern g1="L" 	g2="J" 	k="-57" />
<hkern g1="L" 	g2="S" 	k="-29" />
<hkern g1="L" 	g2="X" 	k="-41" />
<hkern g1="L" 	g2="Z" 	k="-29" />
<hkern g1="L" 	g2="AE" 	k="-59" />
<hkern g1="M" 	g2="V" 	k="12" />
<hkern g1="M" 	g2="Y,Yacute,Ydieresis" 	k="16" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="47" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="parenright" 	k="57" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="question" 	k="70" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="bracketright" 	k="86" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="braceright" 	k="59" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="86" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="33" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,ellipsis" 	k="68" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotesinglbase,quotedblbase" 	k="61" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="at" 	k="23" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="27" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="55" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="35" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="33" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="84" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="parenleft" 	k="25" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="39" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="51" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="S" 	k="20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="49" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="31" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="82" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="bracketleft" 	k="20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="exclam" 	k="29" />
<hkern g1="P" 	g2="v" 	k="-20" />
<hkern g1="P" 	g2="w" 	k="-18" />
<hkern g1="P" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="P" 	g2="parenright" 	k="49" />
<hkern g1="P" 	g2="bracketright" 	k="84" />
<hkern g1="P" 	g2="braceright" 	k="49" />
<hkern g1="P" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="P" 	g2="slash" 	k="127" />
<hkern g1="P" 	g2="guillemotleft,guilsinglleft" 	k="37" />
<hkern g1="P" 	g2="d" 	k="43" />
<hkern g1="P" 	g2="g" 	k="43" />
<hkern g1="P" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="43" />
<hkern g1="P" 	g2="comma,period,ellipsis" 	k="184" />
<hkern g1="P" 	g2="eth" 	k="33" />
<hkern g1="P" 	g2="quotesinglbase,quotedblbase" 	k="236" />
<hkern g1="P" 	g2="c,ccedilla" 	k="43" />
<hkern g1="P" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="66" />
<hkern g1="P" 	g2="q" 	k="23" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="39" />
<hkern g1="P" 	g2="parenleft" 	k="25" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="102" />
<hkern g1="P" 	g2="J" 	k="195" />
<hkern g1="P" 	g2="X" 	k="37" />
<hkern g1="P" 	g2="AE" 	k="172" />
<hkern g1="P" 	g2="bracketleft" 	k="31" />
<hkern g1="P" 	g2="exclam" 	k="23" />
<hkern g1="Q" 	g2="quotedbl,quotesingle" 	k="47" />
<hkern g1="Q" 	g2="question" 	k="70" />
<hkern g1="Q" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="Q" 	g2="quoteleft,quotedblleft" 	k="33" />
<hkern g1="Q" 	g2="comma,period,ellipsis" 	k="68" />
<hkern g1="Q" 	g2="quotesinglbase,quotedblbase" 	k="41" />
<hkern g1="Q" 	g2="j" 	k="-31" />
<hkern g1="Q" 	g2="at" 	k="23" />
<hkern g1="Q" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="29" />
<hkern g1="Q" 	g2="T" 	k="55" />
<hkern g1="Q" 	g2="V" 	k="35" />
<hkern g1="Q" 	g2="W" 	k="33" />
<hkern g1="Q" 	g2="Y,Yacute,Ydieresis" 	k="84" />
<hkern g1="Q" 	g2="parenleft" 	k="25" />
<hkern g1="Q" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="39" />
<hkern g1="Q" 	g2="J" 	k="51" />
<hkern g1="Q" 	g2="S" 	k="20" />
<hkern g1="Q" 	g2="X" 	k="49" />
<hkern g1="Q" 	g2="Z" 	k="31" />
<hkern g1="Q" 	g2="AE" 	k="82" />
<hkern g1="Q" 	g2="bracketleft" 	k="20" />
<hkern g1="Q" 	g2="exclam" 	k="29" />
<hkern g1="R" 	g2="question" 	k="27" />
<hkern g1="R" 	g2="bracketright" 	k="68" />
<hkern g1="R" 	g2="braceright" 	k="33" />
<hkern g1="R" 	g2="slash" 	k="-25" />
<hkern g1="R" 	g2="x" 	k="-43" />
<hkern g1="R" 	g2="z" 	k="-18" />
<hkern g1="R" 	g2="braceleft" 	k="35" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="R" 	g2="comma,period,ellipsis" 	k="-29" />
<hkern g1="R" 	g2="eth" 	k="31" />
<hkern g1="R" 	g2="quotesinglbase,quotedblbase" 	k="-29" />
<hkern g1="R" 	g2="at" 	k="31" />
<hkern g1="R" 	g2="C,Ccedilla" 	k="23" />
<hkern g1="R" 	g2="G" 	k="23" />
<hkern g1="R" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="23" />
<hkern g1="R" 	g2="Q" 	k="23" />
<hkern g1="R" 	g2="V" 	k="20" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="39" />
<hkern g1="R" 	g2="parenleft" 	k="25" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-57" />
<hkern g1="R" 	g2="J" 	k="-45" />
<hkern g1="R" 	g2="X" 	k="-39" />
<hkern g1="R" 	g2="Z" 	k="-29" />
<hkern g1="R" 	g2="AE" 	k="-59" />
<hkern g1="T" 	g2="v" 	k="45" />
<hkern g1="T" 	g2="w" 	k="166" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="45" />
<hkern g1="T" 	g2="parenright" 	k="-25" />
<hkern g1="T" 	g2="question" 	k="-45" />
<hkern g1="T" 	g2="bracketright" 	k="66" />
<hkern g1="T" 	g2="slash" 	k="176" />
<hkern g1="T" 	g2="x" 	k="37" />
<hkern g1="T" 	g2="z" 	k="61" />
<hkern g1="T" 	g2="hyphen,endash,emdash" 	k="100" />
<hkern g1="T" 	g2="braceleft" 	k="49" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="174" />
<hkern g1="T" 	g2="d" 	k="113" />
<hkern g1="T" 	g2="g" 	k="113" />
<hkern g1="T" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="T" 	g2="comma,period,ellipsis" 	k="154" />
<hkern g1="T" 	g2="eth" 	k="162" />
<hkern g1="T" 	g2="quotesinglbase,quotedblbase" 	k="137" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="156" />
<hkern g1="T" 	g2="c,ccedilla" 	k="113" />
<hkern g1="T" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="113" />
<hkern g1="T" 	g2="q" 	k="113" />
<hkern g1="T" 	g2="at" 	k="104" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="70" />
<hkern g1="T" 	g2="colon,semicolon" 	k="172" />
<hkern g1="T" 	g2="r" 	k="82" />
<hkern g1="T" 	g2="s" 	k="92" />
<hkern g1="T" 	g2="m,n,ntilde" 	k="82" />
<hkern g1="T" 	g2="p" 	k="82" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="92" />
<hkern g1="T" 	g2="C,Ccedilla" 	k="55" />
<hkern g1="T" 	g2="G" 	k="55" />
<hkern g1="T" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="55" />
<hkern g1="T" 	g2="Q" 	k="55" />
<hkern g1="T" 	g2="T" 	k="-49" />
<hkern g1="T" 	g2="V" 	k="-55" />
<hkern g1="T" 	g2="W" 	k="-35" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-59" />
<hkern g1="T" 	g2="parenleft" 	k="55" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="131" />
<hkern g1="T" 	g2="J" 	k="184" />
<hkern g1="T" 	g2="X" 	k="-31" />
<hkern g1="T" 	g2="AE" 	k="147" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="bracketright" 	k="76" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="braceright" 	k="45" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="slash" 	k="84" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z" 	k="37" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="braceleft" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,ellipsis" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="quotesinglbase,quotedblbase" 	k="43" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="colon,semicolon" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="33" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="parenleft" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="31" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="Z" 	k="33" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="70" />
<hkern g1="V" 	g2="d" 	k="92" />
<hkern g1="V" 	g2="g" 	k="92" />
<hkern g1="V" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="92" />
<hkern g1="V" 	g2="comma,period,ellipsis" 	k="154" />
<hkern g1="V" 	g2="c,ccedilla" 	k="92" />
<hkern g1="V" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="92" />
<hkern g1="V" 	g2="q" 	k="92" />
<hkern g1="V" 	g2="r" 	k="61" />
<hkern g1="V" 	g2="s" 	k="31" />
<hkern g1="V" 	g2="m,n,ntilde" 	k="61" />
<hkern g1="V" 	g2="p" 	k="61" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="51" />
<hkern g1="V" 	g2="C,Ccedilla" 	k="35" />
<hkern g1="V" 	g2="G" 	k="35" />
<hkern g1="V" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="35" />
<hkern g1="V" 	g2="Q" 	k="35" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="127" />
<hkern g1="V" 	g2="J" 	k="195" />
<hkern g1="V" 	g2="M" 	k="12" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="49" />
<hkern g1="V" 	g2="z" 	k="4" />
<hkern g1="V" 	g2="AE" 	k="168" />
<hkern g1="V" 	g2="parenleft" 	k="68" />
<hkern g1="V" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="V" 	g2="slash" 	k="137" />
<hkern g1="V" 	g2="colon,semicolon" 	k="41" />
<hkern g1="V" 	g2="at" 	k="78" />
<hkern g1="V" 	g2="bracketright" 	k="57" />
<hkern g1="V" 	g2="braceleft" 	k="49" />
<hkern g1="V" 	g2="quotesinglbase,quotedblbase" 	k="152" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="88" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="23" />
<hkern g1="V" 	g2="eth" 	k="104" />
<hkern g1="V" 	g2="quotedbl,quotesingle" 	k="-29" />
<hkern g1="V" 	g2="parenright" 	k="-33" />
<hkern g1="V" 	g2="question" 	k="-49" />
<hkern g1="V" 	g2="quoteleft,quotedblleft" 	k="-39" />
<hkern g1="V" 	g2="quoteright,quotedblright" 	k="-49" />
<hkern g1="V" 	g2="T" 	k="-57" />
<hkern g1="V" 	g2="V" 	k="-63" />
<hkern g1="V" 	g2="W" 	k="-43" />
<hkern g1="V" 	g2="X" 	k="-39" />
<hkern g1="V" 	g2="Y,Yacute,Ydieresis" 	k="-68" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="39" />
<hkern g1="W" 	g2="AE" 	k="100" />
<hkern g1="W" 	g2="parenleft" 	k="33" />
<hkern g1="W" 	g2="slash" 	k="96" />
<hkern g1="W" 	g2="colon,semicolon" 	k="23" />
<hkern g1="W" 	g2="at" 	k="39" />
<hkern g1="W" 	g2="bracketright" 	k="76" />
<hkern g1="W" 	g2="braceleft" 	k="35" />
<hkern g1="W" 	g2="quotesinglbase,quotedblbase" 	k="78" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="W" 	g2="eth" 	k="53" />
<hkern g1="W" 	g2="quotedbl,quotesingle" 	k="-27" />
<hkern g1="W" 	g2="question" 	k="-33" />
<hkern g1="W" 	g2="quoteleft,quotedblleft" 	k="-37" />
<hkern g1="W" 	g2="quoteright,quotedblright" 	k="-51" />
<hkern g1="W" 	g2="T" 	k="-39" />
<hkern g1="W" 	g2="V" 	k="-45" />
<hkern g1="W" 	g2="W" 	k="-25" />
<hkern g1="W" 	g2="X" 	k="-20" />
<hkern g1="W" 	g2="Y,Yacute,Ydieresis" 	k="-49" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="37" />
<hkern g1="W" 	g2="J" 	k="123" />
<hkern g1="W" 	g2="C,Ccedilla" 	k="33" />
<hkern g1="W" 	g2="G" 	k="33" />
<hkern g1="W" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="33" />
<hkern g1="W" 	g2="Q" 	k="33" />
<hkern g1="W" 	g2="comma,period,ellipsis" 	k="76" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="59" />
<hkern g1="W" 	g2="c,ccedilla" 	k="45" />
<hkern g1="W" 	g2="d" 	k="45" />
<hkern g1="W" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="45" />
<hkern g1="W" 	g2="g" 	k="53" />
<hkern g1="W" 	g2="m,n,ntilde" 	k="43" />
<hkern g1="W" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="45" />
<hkern g1="W" 	g2="p" 	k="43" />
<hkern g1="W" 	g2="q" 	k="45" />
<hkern g1="W" 	g2="r" 	k="43" />
<hkern g1="W" 	g2="s" 	k="35" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="X" 	g2="AE" 	k="-57" />
<hkern g1="X" 	g2="parenleft" 	k="51" />
<hkern g1="X" 	g2="hyphen,endash,emdash" 	k="57" />
<hkern g1="X" 	g2="slash" 	k="-20" />
<hkern g1="X" 	g2="at" 	k="53" />
<hkern g1="X" 	g2="bracketright" 	k="70" />
<hkern g1="X" 	g2="braceleft" 	k="53" />
<hkern g1="X" 	g2="quotesinglbase,quotedblbase" 	k="-20" />
<hkern g1="X" 	g2="guillemotleft,guilsinglleft" 	k="96" />
<hkern g1="X" 	g2="eth" 	k="59" />
<hkern g1="X" 	g2="T" 	k="-27" />
<hkern g1="X" 	g2="V" 	k="-35" />
<hkern g1="X" 	g2="X" 	k="-37" />
<hkern g1="X" 	g2="Y,Yacute,Ydieresis" 	k="-39" />
<hkern g1="X" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-55" />
<hkern g1="X" 	g2="J" 	k="-41" />
<hkern g1="X" 	g2="C,Ccedilla" 	k="49" />
<hkern g1="X" 	g2="G" 	k="49" />
<hkern g1="X" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="49" />
<hkern g1="X" 	g2="Q" 	k="49" />
<hkern g1="X" 	g2="comma,period,ellipsis" 	k="-27" />
<hkern g1="X" 	g2="c,ccedilla" 	k="37" />
<hkern g1="X" 	g2="d" 	k="37" />
<hkern g1="X" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="37" />
<hkern g1="X" 	g2="g" 	k="37" />
<hkern g1="X" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="X" 	g2="q" 	k="37" />
<hkern g1="X" 	g2="v" 	k="23" />
<hkern g1="X" 	g2="y,yacute,ydieresis" 	k="14" />
<hkern g1="X" 	g2="asterisk" 	k="53" />
<hkern g1="X" 	g2="w" 	k="76" />
<hkern g1="X" 	g2="x" 	k="-39" />
<hkern g1="X" 	g2="Z" 	k="-27" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="57" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="164" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="parenleft" 	k="86" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,endash,emdash" 	k="100" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="168" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="at" 	k="119" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="bracketright" 	k="53" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="braceleft" 	k="45" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="160" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="156" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="66" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="eth" 	k="160" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="parenright" 	k="-37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="question" 	k="-51" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteleft,quotedblleft" 	k="-23" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-35" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="V" 	k="-68" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="W" 	k="-47" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="X" 	k="-43" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="-72" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="162" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="236" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,Ccedilla" 	k="84" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="G" 	k="84" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="84" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Q" 	k="84" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,ellipsis" 	k="133" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="84" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,ccedilla" 	k="100" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d" 	k="100" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="100" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="100" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,ntilde" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="100" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="p" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="q" 	k="100" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="r" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Z" 	k="-23" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="M" 	k="16" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="39" />
<hkern g1="Z" 	g2="AE" 	k="-37" />
<hkern g1="Z" 	g2="parenleft" 	k="43" />
<hkern g1="Z" 	g2="hyphen,endash,emdash" 	k="123" />
<hkern g1="Z" 	g2="at" 	k="33" />
<hkern g1="Z" 	g2="bracketright" 	k="88" />
<hkern g1="Z" 	g2="braceleft" 	k="72" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="102" />
<hkern g1="Z" 	g2="eth" 	k="31" />
<hkern g1="Z" 	g2="V" 	k="-20" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="-25" />
<hkern g1="Z" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-35" />
<hkern g1="Z" 	g2="J" 	k="-35" />
<hkern g1="Z" 	g2="C,Ccedilla" 	k="74" />
<hkern g1="Z" 	g2="G" 	k="74" />
<hkern g1="Z" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="74" />
<hkern g1="Z" 	g2="Q" 	k="74" />
<hkern g1="Z" 	g2="comma,period,ellipsis" 	k="-23" />
<hkern g1="Z" 	g2="c,ccedilla" 	k="33" />
<hkern g1="Z" 	g2="d" 	k="35" />
<hkern g1="Z" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="33" />
<hkern g1="Z" 	g2="g" 	k="47" />
<hkern g1="Z" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="35" />
<hkern g1="Z" 	g2="q" 	k="31" />
<hkern g1="Z" 	g2="v" 	k="47" />
<hkern g1="Z" 	g2="y,yacute,ydieresis" 	k="47" />
<hkern g1="Z" 	g2="w" 	k="49" />
<hkern g1="Z" 	g2="x" 	k="-18" />
<hkern g1="Z" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="29" />
<hkern g1="Z" 	g2="braceright" 	k="31" />
<hkern g1="Z" 	g2="bracketleft" 	k="35" />
<hkern g1="Z" 	g2="t" 	k="27" />
<hkern g1="Thorn" 	g2="AE" 	k="100" />
<hkern g1="Thorn" 	g2="slash" 	k="84" />
<hkern g1="Thorn" 	g2="bracketright" 	k="86" />
<hkern g1="Thorn" 	g2="quotesinglbase,quotedblbase" 	k="117" />
<hkern g1="Thorn" 	g2="quotedbl,quotesingle" 	k="68" />
<hkern g1="Thorn" 	g2="parenright" 	k="80" />
<hkern g1="Thorn" 	g2="question" 	k="70" />
<hkern g1="Thorn" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="Thorn" 	g2="quoteright,quotedblright" 	k="33" />
<hkern g1="Thorn" 	g2="T" 	k="90" />
<hkern g1="Thorn" 	g2="V" 	k="43" />
<hkern g1="Thorn" 	g2="X" 	k="88" />
<hkern g1="Thorn" 	g2="Y,Yacute,Ydieresis" 	k="86" />
<hkern g1="Thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="55" />
<hkern g1="Thorn" 	g2="J" 	k="115" />
<hkern g1="Thorn" 	g2="comma,period,ellipsis" 	k="123" />
<hkern g1="Thorn" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="Thorn" 	g2="Z" 	k="96" />
<hkern g1="Thorn" 	g2="braceright" 	k="55" />
<hkern g1="Thorn" 	g2="bracketleft" 	k="33" />
<hkern g1="Thorn" 	g2="exclam" 	k="29" />
<hkern g1="parenleft" 	g2="parenright" 	k="-164" />
<hkern g1="parenleft" 	g2="T" 	k="-23" />
<hkern g1="parenleft" 	g2="V" 	k="-31" />
<hkern g1="parenleft" 	g2="Y,Yacute,Ydieresis" 	k="-35" />
<hkern g1="parenleft" 	g2="C,Ccedilla" 	k="61" />
<hkern g1="parenleft" 	g2="G" 	k="61" />
<hkern g1="parenleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="parenleft" 	g2="Q" 	k="61" />
<hkern g1="parenleft" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="parenleft" 	g2="S" 	k="37" />
<hkern g1="parenright" 	g2="AE" 	k="41" />
<hkern g1="parenright" 	g2="T" 	k="57" />
<hkern g1="parenright" 	g2="V" 	k="70" />
<hkern g1="parenright" 	g2="W" 	k="37" />
<hkern g1="parenright" 	g2="X" 	k="57" />
<hkern g1="parenright" 	g2="Y,Yacute,Ydieresis" 	k="88" />
<hkern g1="parenright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="parenright" 	g2="J" 	k="27" />
<hkern g1="parenright" 	g2="C,Ccedilla" 	k="25" />
<hkern g1="parenright" 	g2="G" 	k="25" />
<hkern g1="parenright" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="25" />
<hkern g1="parenright" 	g2="Q" 	k="25" />
<hkern g1="parenright" 	g2="Z" 	k="45" />
<hkern g1="parenright" 	g2="S" 	k="25" />
<hkern g1="bracketleft" 	g2="AE" 	k="55" />
<hkern g1="bracketleft" 	g2="bracketright" 	k="-164" />
<hkern g1="bracketleft" 	g2="T" 	k="68" />
<hkern g1="bracketleft" 	g2="V" 	k="59" />
<hkern g1="bracketleft" 	g2="W" 	k="76" />
<hkern g1="bracketleft" 	g2="X" 	k="74" />
<hkern g1="bracketleft" 	g2="Y,Yacute,Ydieresis" 	k="55" />
<hkern g1="bracketleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="57" />
<hkern g1="bracketleft" 	g2="J" 	k="57" />
<hkern g1="bracketleft" 	g2="C,Ccedilla" 	k="88" />
<hkern g1="bracketleft" 	g2="G" 	k="88" />
<hkern g1="bracketleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="88" />
<hkern g1="bracketleft" 	g2="Q" 	k="88" />
<hkern g1="bracketleft" 	g2="Z" 	k="84" />
<hkern g1="bracketleft" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="78" />
<hkern g1="bracketleft" 	g2="S" 	k="53" />
<hkern g1="bracketright" 	g2="W" 	k="20" />
<hkern g1="bracketright" 	g2="X" 	k="20" />
<hkern g1="bracketright" 	g2="C,Ccedilla" 	k="20" />
<hkern g1="bracketright" 	g2="G" 	k="20" />
<hkern g1="bracketright" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="bracketright" 	g2="Q" 	k="20" />
<hkern g1="bracketright" 	g2="Z" 	k="33" />
<hkern g1="braceleft" 	g2="AE" 	k="23" />
<hkern g1="braceleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="25" />
<hkern g1="braceleft" 	g2="J" 	k="25" />
<hkern g1="braceleft" 	g2="C,Ccedilla" 	k="53" />
<hkern g1="braceleft" 	g2="G" 	k="61" />
<hkern g1="braceleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="braceleft" 	g2="Q" 	k="61" />
<hkern g1="braceleft" 	g2="Z" 	k="37" />
<hkern g1="braceleft" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="47" />
<hkern g1="braceleft" 	g2="braceright" 	k="-164" />
<hkern g1="braceleft" 	g2="S" 	k="43" />
<hkern g1="braceright" 	g2="AE" 	k="41" />
<hkern g1="braceright" 	g2="T" 	k="51" />
<hkern g1="braceright" 	g2="V" 	k="51" />
<hkern g1="braceright" 	g2="W" 	k="39" />
<hkern g1="braceright" 	g2="X" 	k="59" />
<hkern g1="braceright" 	g2="Y,Yacute,Ydieresis" 	k="49" />
<hkern g1="braceright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="43" />
<hkern g1="braceright" 	g2="J" 	k="43" />
<hkern g1="braceright" 	g2="Z" 	k="70" />
<hkern g1="braceright" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="23" />
<hkern g1="braceright" 	g2="S" 	k="37" />
<hkern g1="asterisk" 	g2="AE" 	k="199" />
<hkern g1="asterisk" 	g2="X" 	k="51" />
<hkern g1="asterisk" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="131" />
<hkern g1="asterisk" 	g2="J" 	k="250" />
<hkern g1="asterisk" 	g2="C,Ccedilla" 	k="20" />
<hkern g1="asterisk" 	g2="G" 	k="20" />
<hkern g1="quotedbl,quotesingle" 	g2="AE" 	k="274" />
<hkern g1="quotedbl,quotesingle" 	g2="V" 	k="-27" />
<hkern g1="quotedbl,quotesingle" 	g2="W" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="162" />
<hkern g1="quotedbl,quotesingle" 	g2="J" 	k="252" />
<hkern g1="quotedbl,quotesingle" 	g2="C,Ccedilla" 	k="51" />
<hkern g1="quotedbl,quotesingle" 	g2="G" 	k="51" />
<hkern g1="quotedbl,quotesingle" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="quotedbl,quotesingle" 	g2="Q" 	k="51" />
<hkern g1="quotedbl,quotesingle" 	g2="Z" 	k="25" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="270" />
<hkern g1="quoteleft,quotedblleft" 	g2="V" 	k="-39" />
<hkern g1="quoteleft,quotedblleft" 	g2="W" 	k="-35" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-27" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="154" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="236" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,Ccedilla" 	k="43" />
<hkern g1="quoteleft,quotedblleft" 	g2="G" 	k="43" />
<hkern g1="quoteleft,quotedblleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="43" />
<hkern g1="quoteleft,quotedblleft" 	g2="Q" 	k="43" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="AE" 	k="-43" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="137" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="V" 	k="152" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="70" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="X" 	k="-25" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="158" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-53" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="J" 	k="-57" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,Ccedilla" 	k="51" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="G" 	k="51" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="57" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Q" 	k="63" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Z" 	k="-43" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="39" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="S" 	k="-33" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="158" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="25" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="68" />
<hkern g1="guillemotright,guilsinglright" 	g2="AE" 	k="90" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="176" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="90" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="43" />
<hkern g1="guillemotright,guilsinglright" 	g2="X" 	k="104" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="160" />
<hkern g1="guillemotright,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="86" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="88" />
<hkern g1="guillemotright,guilsinglright" 	g2="S" 	k="43" />
<hkern g1="hyphen,endash,emdash" 	g2="AE" 	k="29" />
<hkern g1="hyphen,endash,emdash" 	g2="T" 	k="102" />
<hkern g1="hyphen,endash,emdash" 	g2="V" 	k="25" />
<hkern g1="hyphen,endash,emdash" 	g2="X" 	k="63" />
<hkern g1="hyphen,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="102" />
<hkern g1="hyphen,endash,emdash" 	g2="J" 	k="57" />
<hkern g1="hyphen,endash,emdash" 	g2="Z" 	k="33" />
<hkern g1="comma,period,ellipsis" 	g2="AE" 	k="-39" />
<hkern g1="comma,period,ellipsis" 	g2="T" 	k="143" />
<hkern g1="comma,period,ellipsis" 	g2="V" 	k="158" />
<hkern g1="comma,period,ellipsis" 	g2="W" 	k="82" />
<hkern g1="comma,period,ellipsis" 	g2="X" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="164" />
<hkern g1="comma,period,ellipsis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-51" />
<hkern g1="comma,period,ellipsis" 	g2="J" 	k="-57" />
<hkern g1="comma,period,ellipsis" 	g2="C,Ccedilla" 	k="59" />
<hkern g1="comma,period,ellipsis" 	g2="G" 	k="59" />
<hkern g1="comma,period,ellipsis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="68" />
<hkern g1="comma,period,ellipsis" 	g2="Q" 	k="68" />
<hkern g1="comma,period,ellipsis" 	g2="Z" 	k="-39" />
<hkern g1="comma,period,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="47" />
<hkern g1="comma,period,ellipsis" 	g2="S" 	k="-29" />
<hkern g1="colon,semicolon" 	g2="T" 	k="174" />
<hkern g1="colon,semicolon" 	g2="V" 	k="43" />
<hkern g1="colon,semicolon" 	g2="W" 	k="27" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="colon,semicolon" 	g2="C,Ccedilla" 	k="23" />
<hkern g1="colon,semicolon" 	g2="G" 	k="23" />
<hkern g1="colon,semicolon" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="23" />
<hkern g1="colon,semicolon" 	g2="Q" 	k="23" />
<hkern g1="colon,semicolon" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="25" />
<hkern g1="slash" 	g2="AE" 	k="158" />
<hkern g1="slash" 	g2="T" 	k="-59" />
<hkern g1="slash" 	g2="V" 	k="-66" />
<hkern g1="slash" 	g2="W" 	k="-45" />
<hkern g1="slash" 	g2="X" 	k="-41" />
<hkern g1="slash" 	g2="Y,Yacute,Ydieresis" 	k="-70" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="102" />
<hkern g1="slash" 	g2="J" 	k="145" />
<hkern g1="slash" 	g2="C,Ccedilla" 	k="55" />
<hkern g1="slash" 	g2="G" 	k="55" />
<hkern g1="slash" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="53" />
<hkern g1="slash" 	g2="Q" 	k="53" />
<hkern g1="slash" 	g2="Z" 	k="-18" />
<hkern g1="exclamdown" 	g2="T" 	k="170" />
<hkern g1="exclamdown" 	g2="V" 	k="59" />
<hkern g1="exclamdown" 	g2="W" 	k="43" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="96" />
<hkern g1="exclamdown" 	g2="Z" 	k="23" />
<hkern g1="exclamdown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="23" />
<hkern g1="questiondown" 	g2="AE" 	k="145" />
<hkern g1="questiondown" 	g2="T" 	k="227" />
<hkern g1="questiondown" 	g2="V" 	k="96" />
<hkern g1="questiondown" 	g2="W" 	k="80" />
<hkern g1="questiondown" 	g2="X" 	k="141" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="135" />
<hkern g1="questiondown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="94" />
<hkern g1="questiondown" 	g2="J" 	k="127" />
<hkern g1="questiondown" 	g2="C,Ccedilla" 	k="76" />
<hkern g1="questiondown" 	g2="G" 	k="76" />
<hkern g1="questiondown" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="76" />
<hkern g1="questiondown" 	g2="Q" 	k="76" />
<hkern g1="questiondown" 	g2="Z" 	k="139" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="90" />
<hkern g1="questiondown" 	g2="S" 	k="63" />
<hkern g1="at" 	g2="AE" 	k="78" />
<hkern g1="at" 	g2="T" 	k="90" />
<hkern g1="at" 	g2="V" 	k="74" />
<hkern g1="at" 	g2="W" 	k="47" />
<hkern g1="at" 	g2="X" 	k="80" />
<hkern g1="at" 	g2="Y,Yacute,Ydieresis" 	k="111" />
<hkern g1="at" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="53" />
<hkern g1="at" 	g2="J" 	k="53" />
<hkern g1="at" 	g2="C,Ccedilla" 	k="20" />
<hkern g1="at" 	g2="G" 	k="20" />
<hkern g1="at" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="at" 	g2="Q" 	k="20" />
<hkern g1="at" 	g2="Z" 	k="70" />
<hkern g1="at" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="25" />
<hkern g1="at" 	g2="S" 	k="37" />
</font>
</defs></svg> 