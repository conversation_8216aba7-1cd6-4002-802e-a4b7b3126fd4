<div class="business-gallery-wrap">
  <div class="bz-gallery-options">
    <div class="filter-options">
      <form action="#" [formGroup]="BusinessOptionsForm">
        <div class="control-wrap filter-control">
          <button type="button" class="btn-search">
            <i class="icon icon-search" aria-hidden="true"></i>
          </button>
          <input
            type="search"
            class="filter-search"
            formControlName="filterSearch"
            (input)="searchFilter(BusinessOptionsForm.controls['filterSearch'])"
          />
        </div>
      </form>
    </div>
    <div class="btn-holder d-none">
      <a [routerLink]="['/' + newBusinessPath]" class="btn btn-primary"
        >עסק חדש</a
      >
    </div>
  </div>
  <div class="gallery-section">
    <div class="gallery-holder">
      <span class="no-results">לא נמצאו עסקים</span>
      <business-gallery-box
        class="bz-gallery-box"
        *ngFor="let businessItem of businessInfoData | filter: searchText"
        [businessItem]="businessItem"
      ></business-gallery-box>
    </div>
  </div>
</div>
