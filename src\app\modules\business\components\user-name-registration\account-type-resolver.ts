import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  Resolve,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import { Observable } from 'rxjs';
import { TypeEnum } from '../../model/type-enum';
import { EnumService } from '../../service/enum.service';

@Injectable()
export class AccountTypeResolver implements Resolve<TypeEnum[]> {
  constructor(private router: Router, private enumService: EnumService) {}

  resolve(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<TypeEnum[]> {
    return this.enumService.getCategoryOptions('accountType');
  }
}
