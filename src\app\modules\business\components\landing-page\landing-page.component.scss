@import 'styles/vendors/bootstrap/share';
@import 'styles/variables';

.homepage-wrap {
  background: #f8f8f8;
}

.flags {
  position: absolute;
  top: 0;
  right: 0;
  color: #fff;
  padding: 0 20px;
}

.homepage-visual {
  min-height: 600px;
  background-size: cover;
  position: relative;
  background-position: 50% 0;
  @include media-breakpoint-up(lg) {
    min-height: 800px;
  }

  &:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0.76;
    height: 100%;
    background-color: rgba(53, 53, 53, 0.75);
  }

  .visual-inner {
    padding: 80px 15px;
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    @include media-breakpoint-up(md) {
      padding: 120px 15px;
    }

    @include media-breakpoint-up(lg) {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .visual-content {
    color: #fff;
    line-height: 1.2;
    font-size: 16px;
    padding: 10px 0;
    margin-bottom: 40px;
    @include media-breakpoint-up(lg) {
      max-width: 510px;
      font-size: 20px;
      margin: 0;
      width: calc(100% - 520px);
    }

    h1 {
      font-size: 30px;
      margin: 0 0 20px;
      line-height: 1.1;
      font-weight: 900;
      color: inherit;
      @include media-breakpoint-up(md) {
        font-size: 50px;
      }
      @include media-breakpoint-up(xl) {
        font-size: 80px;
      }
    }

    p {
      margin: 0;
    }
  }
}

.visual-options {
  padding: 10px 0;
  @include media-breakpoint-up(lg) {
    padding: 20px 60px;
    width: 520px;
  }
}

.homepage-header {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 3;
  padding: 15px 0;
  @include media-breakpoint-up(md) {
    padding: 25px 0;
  }
}

.header-inner {
  max-width: 1200px;
  margin: 0 auto;
  padding: 5px 15px;
  display: flex;
  justify-content: space-between;

  .logo {
    width: 120px;
    margin-top: 3px;
    @include media-breakpoint-up(md) {
      width: 165px;
      margin-top: -3px;
    }

    a {
      display: block;
      height: 100%;
    }

    img {
      width: 100%;
    }
  }
}

.header-panel {
  ul {
    padding: 0;
    margin: 0 -3px;
  }

  li {
    padding: 0 3px;
    margin-bottom: 4px;
    display: inline-block;
    vertical-align: middle;
    @include media-breakpoint-up(md) {
      margin: 0;
    }
  }

  .btn {
    color: #fff;
    font-size: 14px;
    min-width: 83px;
    @include media-breakpoint-up(md) {
      min-width: 108px;
    }

    &.btn-home {
      background: #00f3ff;
      color: #000;
    }
  }
}

.btn-home {
  border-radius: 20px;
  padding: 10px;
  display: inline-block;
  vertical-align: top;
  font-size: 16px;
  background: #00bec7;
  // outline: none;
  min-width: 145px;
  color: #fff;
  text-decoration: none;

  &.btn-link {
    background: transparent;
    border: none;
    // outline: none;
  }

  &.btn-arcticle-home {
    background: #00b28d;
  }
}

h2 {
  margin: 0 0 20px;
  font-weight: 900;
  color: #4b7c96;
  font-size: 24px;
  @include media-breakpoint-up(md) {
    font-size: 34px;
  }
}

.home-intro {
  text-align: center;
  font-size: 16px;
  color: #6d6d6d;
  padding: 50px 15px;
  @include media-breakpoint-up(md) {
    font-size: 20px;
    padding: 90px 15px 70px;
  }

  .intro-inner {
    max-width: 780px;
    margin: 0 auto;

    h2 {
      color: #4b7c96;
    }
  }
}

.home-two-column {
  padding: 0 15px;
  margin: 0 auto 50px;
  max-width: 1170px;
  color: #fff;
  @include media-breakpoint-up(md) {
    margin-bottom: 100px;
  }
  @include media-breakpoint-up(lg) {
    margin-bottom: 170px;
  }

  h2 {
    color: inherit;
    font-weight: normal;
    min-height: 75px;
  }

  .two-column-inner {
    margin: 0 -8px;
    @include media-breakpoint-up(md) {
      display: flex;
    }
  }

  .line {
    display: block;
    width: 60px;
    margin: 0 auto 20px;
    height: 1px;
    background: #fff;
  }

  .column {
    display: flex;
    flex-wrap: wrap;
    min-height: 415px;
    padding: 8px;
    @include media-breakpoint-up(md) {
      width: 50%;
    }
  }

  .btn {
    border: 1px solid #fff;
    background: transparent;
  }

  p {
    margin: 0 0 30px;
  }

  .column-box {
    width: 100%;
    text-align: center;
    padding: 20px;
    background: #00bec7;
    @include media-breakpoint-up(md) {
      padding: 40px;
    }
    @include media-breakpoint-up(lg) {
      padding: 60px 85px;
    }

    &.dark-box {
      background: #386372;
    }
  }
}

.articles-inner {
  font-size: 16px;
  color: #5e5e5e;

  @include media-breakpoint-up(md) {
    display: flex;
  }

  &.reverse {
    @include media-breakpoint-up(md) {
      flex-direction: row-reverse;
    }
  }

  p {
    margin: 0 0 25px;
  }

  .btn {
    font-size: inherit;
  }
}

.article-col {
  padding: 30px;
  @include media-breakpoint-up(md) {
    width: 50%;
    padding: 40px;
  }
  @include media-breakpoint-up(lg) {
    padding: 80px;
  }

  &.img-hold {
    background-size: cover;
    background-position: 50% 50%;
    min-height: 500px;

    @include media-breakpoint-up(md) {
      display: flex;
    }

    @include media-breakpoint-up(lg) {
      min-height: 700px;
    }
  }

  &.content-hold {
    display: flex;
    align-items: center;
  }

  .article-content {
    margin: 0 auto;
    max-width: 550px;
  }

  .btn {
    min-width: 160px;
  }
}

.bottom-info-section {
  min-height: 450px;
  background-position: 50% 50%;
  background-size: cover;
  color: #fff;
  text-align: center;
  font-size: 16px;
  display: flex;
  flex-direction: column;
  @include media-breakpoint-up(md) {
    min-height: 570px;
  }

  .bottom-info-inner {
    min-height: 450px;
    width: 100%;
    padding: 30px 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    @include media-breakpoint-up(md) {
      min-height: 570px;
    }
  }

  h2 {
    color: inherit;
  }

  p {
    margin: 0 0 30px;
    @include media-breakpoint-up(md) {
      margin: 0 0 50px;
    }
  }

  .btn {
    padding: 17px 15px;
    border-radius: 27px;
    min-width: 200px;

    &.secondary-btn-home {
      border: 1px solid #fff;
      background: transparent;
    }
  }
}

.btn-row-box {
  margin: 0 -5px;

  .btn {
    margin: 0 5px 10px;
  }
}

.bottom-info-content {
  max-width: 760px;
}

.bottom-links-section {
  padding: 40px 0;
}

.bottom-links-inner {
  max-width: 1170px;
  margin: 0 auto;
  padding: 0 15px;
  color: #000;
}

.links-wrap {
  margin: 0 -5px;
  font-size: 0;
  line-height: 0;
  @include media-breakpoint-up(lg) {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
}

.link-column {
  display: inline-block;
  vertical-align: top;
  font-size: 14px;
  line-height: 1.1;
  width: 160px;
  margin-bottom: 30px;
  padding: 0 5px;

  h5 {
    font-size: inherit;
    color: inherit;
    font-weight: bold;
  }

  ul {
    margin: 0;
    padding: 0;
  }

  li {
    margin-bottom: 8px;
  }

  a {
    color: inherit;
  }
}

.add-bottom-info {
  background: #172e3b;
  padding: 10px 15px;
  color: #757677;
  text-align: center;

  span {
    display: inline-block;
    vertical-align: middle;
  }

  .btn-facebook {
    font-weight: bold;
    display: inline-block;
    vertical-align: middle;
    padding: 5px;
    background: #4469ae;
    color: #fff;
    border-radius: 3px;
    font-size: 12px;
    line-height: 1;
    // outline: none;
    border: none;
    cursor: pointer;
  }

  .separator {
    margin: 0 5px;
    width: 3px;
    background: #757677;
    border-radius: 50%;
    height: 3px;
  }
}
