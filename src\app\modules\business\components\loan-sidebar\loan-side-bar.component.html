<div class="steps-progress-bar-holder">
  <div class="steps-progress-wrap">
    <h6>{{ 'progress-sidebar.title' | translate }}</h6>
    <div class="bz-progress-box">
      <p-progressBar
        class="bz-progressbar"
        [value]="loanSideBarData.progressValue"
      ></p-progressBar>
    </div>
    <div class="details-list">
      <ul class="list-unstyled">
        <li>
          <span>סכום</span>
          <strong>{{ loanSideBarData.amount | number: '1.0-2' }} ₪</strong>
        </li>
        <li>
          <span>לפרק זמן של</span>
          <span
            [class.active]="loanSideBarData.period < loanSideBarData.minLoan"
          >
            <strong
              [class.active]="loanSideBarData.period < loanSideBarData.minLoan"
              >{{
                loanSideBarData.period < loanSideBarData.minLoan
                  ? loanSideBarData.minLoan
                  : loanSideBarData.period
              }}
              {{ 'global.calendar.months' | translate }}</strong
            >
          </span>
        </li>
      </ul>
    </div>
    <div class="content-row"></div>
    <div class="add-info">
      <p>
        <span> סטטוס: </span>
        <strong [ngClass]="{ 'link-span-red': loanCancelled }">{{
          'loan-request.status.' + loanSideBarData.loanRequestStatus | translate
        }}</strong>
      </p>
      <p *ngIf="!loanSideBarData.loanApproved">
        לאחר הזנה מספקת של נתונים, ניתן יהיה לשלוח לאישור ההלוואה.
      </p>
    </div>
    <div class="btn-holder">
      <button
        type="button"
        class="btn btn-secondary"
        [disabled]="!canSubmitLoan"
        (click)="submit($event)"
      >
        שליחה לאישור הלוואה
      </button>
    </div>
  </div>
</div>
