<p-accordionTab>
  <p-header>
    <div class="header-content">
      <span class="heading-content"
        >{{ form.additionalTitle }}
        {{ form.additionalTitle ? '' : ('global.calendar.year' | translate) }}
        {{
          (form.additionalTitle ? '(' : '') +
            form.year +
            (form.additionalTitle ? ')' : '')
        }}</span
      >
      <span>
        {{ numberOfNonNullFields }} מתוך {{ numberOfFields }} נתונים מולאו</span
      >
    </div>
  </p-header>
  <div class="accordion-inner">
    <form [formGroup]="form.formGroup" class="col-lg-11 no-padding">
      <div class="form-group-wrap">
        <div
          *ngFor="let formControlTemplate of form.formTemplate"
          class="form-group"
        >
          <div *ngIf="!formControlTemplate.hidden">
            <div class="label-holder">
              <label>{{
                'loan-request.financialInformation.' + formControlTemplate.label
                  | translate
              }}</label>
              <div class="tooltip-box" *ngIf="formControlTemplate.tooltip">
                <span
                  class="bz-info-tooltip"
                  [pTooltip]="formControlTemplate.tooltip"
                  tooltipPosition="top"
                  tooltipStyleClass="bz-tooltip"
                  ><i class="icon-info" aria-hidden="true"></i
                ></span>
              </div>
            </div>
            <div class="input-holder" *ngIf="!formControlTemplate.hidden">
              <input
                class="form-control"
                type="text"
                (blur)="
                  transformToNumbers(
                    form.formGroup.controls[formControlTemplate.label]
                  )
                "
                (input)="
                  onlyNumbers(
                    form.formGroup.controls[formControlTemplate.label]
                  )
                "
                formControlName="{{ formControlTemplate.label }}"
                [hidden]="formControlTemplate.hidden"
              />
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</p-accordionTab>

<!-- [options]="{ prefix: '', thousands: ',', decimal: '', precision: 0, allowNegative: true }"
currencyMask -->
