<div class="login-form-wrap">
  <div class="h3-hold">
    <h3>כניסה לחשבון</h3>
    <img src="/assets/img/biz/login-title-img.png" alt="login title" />
  </div>

  <div
    class="form-box"
    [ngClass]="{ 'disabled-form': selectedValue === 'smsForm' }"
  >
    <div class="form-inner-wrap">
      <div class="form-group radio-row">
        <div class="radio-box">
          <p-radioButton
            class="bz-tarya-radio"
            name="loginForm"
            value="mainForm"
            [(ngModel)]="selectedValue"
            id="main-form"
            label="באמצעות פרטי חשבון"
            (ngModelChange)="onFormChange($event)"
          >
          </p-radioButton>
        </div>
      </div>
      <form
        class="form-inner"
        [formGroup]="loginForm"
        (keyup.enter)="postPasswordLoginForm()"
      >
        <div class="form-group">
          <input
            type="email"
            class="form-control"
            placeholder="כתובת דוא״ל"
            formControlName="login"
          />
          <control-validation-errors
            [control]="loginForm.controls['login']"
            translateTag="error"
          ></control-validation-errors>
        </div>
        <div class="form-group">
          <input
            type="password"
            class="form-control"
            placeholder="סיסמה"
            formControlName="password"
            [autofocus]="sentSMS"
          />
          <control-validation-errors
            [control]="loginForm.controls['password']"
            translateTag="error"
          ></control-validation-errors>
        </div>
        <div class="form-group">
          <button
            type="button"
            class="btn-primary"
            [disabled]="!loginForm.valid"
            (click)="postPasswordLoginForm()"
          >
            התחבר
          </button>
        </div>
        <div class="form-group form-options">
          <div class="checkbox-row">
            <p-checkbox
              binary="true"
              class="bz-checkbox"
              formControlName="rememberLogin"
            ></p-checkbox>
            <span class="check-label">זכור דוא״ל</span>
          </div>
          <!--<div class="forgot-row">
                        <a [routerLink]="['forgot-password']">שכחת סיסמה?</a>
                    </div>-->
        </div>
        <control-validation-errors
          [control]="loginForm"
          translateTag="error"
        ></control-validation-errors>
        <div *ngIf="selectedValue === 'mainForm'">
          <div>{{ loginFormMessage | translate }}</div>
        </div>
      </form>
    </div>
  </div>
  <div
    class="form-box"
    [ngClass]="{ 'disabled-form': selectedValue === 'mainForm' }"
  >
    <div class="form-inner-wrap">
      <div class="form-group radio-row">
        <div class="radio-box">
          <p-radioButton
            class="bz-tarya-radio"
            name="loginForm"
            value="smsForm"
            [(ngModel)]="selectedValue"
            id="sms-form"
            label="באמצעות SMS"
            (ngModelChange)="onFormChange($event)"
          >
          </p-radioButton>
        </div>
      </div>
      <form
        class="form-inner"
        [formGroup]="smsLoginForm"
        (keyup.enter)="postSmsLoginForm()"
      >
        <div *ngIf="!sentSMS">
          <div class="form-group">
            <input
              type="text"
              class="form-control"
              placeholder="מספר תעודת זהות"
              formControlName="identity"
            />
            <control-validation-errors
              [control]="smsLoginForm.controls['identity']"
              translateTag="error"
            ></control-validation-errors>
          </div>
          <div class="form-group">
            <button
              type="button"
              [disabled]="!smsLoginForm.controls['identity'].valid"
              class="btn-primary"
              (click)="postSmsLoginForm()"
            >
              התחבר
            </button>
          </div>
          <div class="sms-info">
            <p>
              לאחר לחיצה על "שלח", תישלח סיסמה במסרון למספר הנייד שמעודכן
              במערכות טריא
            </p>
          </div>
        </div>
        <div *ngIf="sentSMS">
          <div class="form-group">
            <input
              name="smsCode"
              type="text"
              class="form-control"
              placeholder="סיסמה"
              formControlName="smsCode"
            />
            <control-validation-errors
              [control]="smsLoginForm.controls['smsCode']"
              translateTag="error"
            ></control-validation-errors>
          </div>
          <div
            class="form-sms-links form-group row d-flex flex-row flex-wrap justify-content-between"
          >
            <div class="col-auto">
              <a href="#" (click)="changeId($event)">{{
                'login.change_tz' | translate
              }}</a>
            </div>
            <div class="col-auto">
              <a href="#" (click)="resendSms($event)">{{
                'login.resend_sms' | translate
              }}</a>
            </div>
          </div>
          <div class="form-group">
            <button
              type="button"
              [disabled]="!smsLoginForm.valid"
              class="btn-primary"
              (click)="postSmsLoginForm()"
            >
              התחבר
            </button>
          </div>
        </div>
        <div *ngIf="smsLoginFormMessage">
          {{ smsLoginFormMessage | translate: smsLoginFormMessageParameter }}
        </div>
        <div *ngIf="selectedValue === 'smsForm'">
          <div>{{ loginFormMessage | translate }}</div>
        </div>
      </form>
    </div>
  </div>

  <re-captcha
    #captchaRef
    size="invisible"
    *ngIf="this.captcha?.required"
    siteKey="{{ this.captcha?.captchaSiteKey }}"
  >
  </re-captcha>
</div>
