import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { LanguageModule } from '../language/language.module';
import { LanguageService } from '../language/language.service';
import { AppSharedModule } from '../shared/app.shared.module';
import { BorrowersRoutingModule } from './borrowers-routing.module';
import { BorrowersSidebarComponent } from './components/borrowers-sidebar/borrowers-sidebar.component';
import { LoansListComponent } from './components/loans-list/loans-list.component';
import { BorrowersLoanDetailsComponent } from './components/borrowers-loan-details/borrowers-loan-detials.component';
import { BorrowersLayoutComponent } from './components/borrowers-layout/borrowers-layout.component';
import { BorrowersLoansComponent } from './components/borrowers-loans/borrowers-loans.component';
import { BorrowersCreditCardUpdateComponent } from './components/borrowers-credit-card-update/borrowers-credit-card-update.component';
import { AdditionalBorrowersComponent } from './components/additional-borrowers/additional-borrowers.component';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { PaymentMethodsComponent } from './components/borrowers-payment-methods/payment-methods.component';
import { CreditCardSuccessPageComponent } from './components/borrowers-credit-card-success-page/credit-card-success-page.component';
import { LoanCardComponent } from './components/loan-card/loan-card.component';
import { CreditCardLoanComponent } from './components/credit-card-loan/credit-card-loan.component';
import { UnderConstructionComponent } from './components/under-construction/under-construction.component';
import { BorrowersDocumentsComponent } from './components/borrowers-documents/borrowers-documents.component';
import { BorrowersCreditCardErrorPageComponent } from './components/borrowers-credit-card-error-page/borrowers-credit-card-error-page.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { OverlayModule } from '@angular/cdk/overlay';
import { MatPaginatorIntl } from '@angular/material/paginator';
import { DocumentsPaginatorIntlComponent } from './components/documents-paginator-intl/documents-paginator-intl.component';
import { DebtPaymentMethodComponent } from './components/debt-payment-method/debt-payment-method.component';
import { DebtPaymentComponent } from './components/debt-payment/debt-payment.component';
import { DebtPaymentAllLoansComponent } from './components/debt-payment-all-loans/debt-payment-all-loans.component';
import { DebtPaymentLoansListComponent } from './components/debt-payment-loans-list/debt-payment-loans-list.component';
import { DebtPaymentAddCreditCardComponent } from './components/debt-payment-add-credit-card/debt-payment-add-credit-card.component';
import { DebtPaymentWarningMessageComponent } from './components/debt-payment-warning-message/debt-payment-warning-message.component';
import { DebtPaymentSuccessfulPaymentComponent } from './components/debt-payment-successful-payment/debt-payment-successful-payment.component';
import { DebtPaymentSuccessComponent } from './components/debt-payment-success/debt-payment-success.component';
import { DebtPaymentErrorComponent } from './components/debt-payment-error/debt-payment-error.component';
import { LateLoanDetailsComponent } from './components/late-loan-details/late-loan-details.component';
import { LateLoanInCollectionMessageComponent } from './components/late-loan-in-collection-message/late-loan-in-collection-message.component';
import { LateLoanComponent } from './components/late-loan/late-loan.component';
export const modulesToLoadTranslationsFrom: string[] = ['borrowers'];

export function TaryaBorrowersHttpLoaderFactory(http: HttpClient) {
  return new MultiTranslateHttpLoader(http, [
    { prefix: '/i18n/borrowers/', suffix: '.json' },
    { prefix: '/i18n/tarya-spread/', suffix: '.json' },
    { prefix: '/i18n/header/', suffix: '.json' },
  ]);
}

@NgModule({
  declarations: [
    BorrowersSidebarComponent,
    BorrowersLoanDetailsComponent,
    LoansListComponent,
    BorrowersLayoutComponent,
    BorrowersLoansComponent,
    BorrowersCreditCardUpdateComponent,
    AdditionalBorrowersComponent,
    PaymentMethodsComponent,
    LoanCardComponent,
    CreditCardSuccessPageComponent,
    CreditCardLoanComponent,
    UnderConstructionComponent,
    BorrowersDocumentsComponent,
    BorrowersCreditCardErrorPageComponent,
    DocumentsPaginatorIntlComponent,
    DebtPaymentMethodComponent,
    DebtPaymentComponent,
    DebtPaymentAllLoansComponent,
    DebtPaymentLoansListComponent,
    DebtPaymentAddCreditCardComponent,
    DebtPaymentWarningMessageComponent,
    DebtPaymentSuccessfulPaymentComponent,
    DebtPaymentSuccessComponent,
    DebtPaymentErrorComponent,
    LateLoanDetailsComponent,
    LateLoanInCollectionMessageComponent,
    LateLoanComponent,
  ],
  imports: [
    CommonModule,
    BorrowersRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    OverlayModule,
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: TaryaBorrowersHttpLoaderFactory,
        deps: [HttpClient],
      },
      isolate: true,
    }),
    LanguageModule.forChild(),
    AppSharedModule,
  ],
  providers: [
    { provide: MatPaginatorIntl, useClass: DocumentsPaginatorIntlComponent },
  ],
})
export class BorrowersModule {
  constructor(private languageService: LanguageService) {
    this.languageService.setDefaultLang();
  }
}
