<header class="page-header">
  <h1 class="page-header-title">
    {{ 'password-update.title' | translate }}
  </h1>
</header>
<form [formGroup]="form" (submit)="update()">
  <div class="mb-5">
    <div class="row">
      <div class="col-lg-4 pt-3 order-2 order-lg-1">
        <app-tar-inputtext
          [formControlName]="passwordFormControlNames.OLD"
          label="password-update.password"
          type="password"
          errorsParentKey="error"
          [errors]="form.get(passwordFormControlNames.OLD).errors"
          [interpolateParams]="minMaxPasswordLength"
          class="mb-3"
        >
        </app-tar-inputtext>
        <app-tar-inputtext
          [formControlName]="passwordFormControlNames.NEW"
          label="password-update.new-password"
          type="password"
          errorsParentKey="error"
          [eyeToggle]="true"
          [errors]="form.get(passwordFormControlNames.NEW).errors"
          [interpolateParams]="minMaxPasswordLength"
          class="mb-3"
        >
        </app-tar-inputtext>
        <app-tar-inputtext
          [formControlName]="passwordFormControlNames.CONFIRM"
          label="password-update.confirm-new-password"
          type="password"
          errorsParentKey="error"
          [eyeToggle]="true"
          [errors]="form.get(passwordFormControlNames.CONFIRM).errors"
          class="mb-3"
        >
        </app-tar-inputtext>
      </div>
      <div class="col-lg-6 mb-3 mb-lg-0 order-1 order-lg-2 mt-lg-n5">
        <app-password-requirements
          [control]="form.get(passwordFormControlNames.NEW)"
        >
        </app-password-requirements>
      </div>
    </div>
    <footer>
      <button
        type="submit"
        class="btn btn-tar-mw btn-tar-primary btn-block btn-lg-block-none"
      >
        {{ 'password-update.btn' | translate }}
      </button>
      <button
        type="button"
        class="btn btn-tar-mw btn-tar-basic-primary btn-block btn-md-block-none mx-md-2"
        (click)="cancelSave()"
      >
        {{ 'account.cancel-btn' | translate }}
      </button>
    </footer>
  </div>
</form>
<tar-dialog
  [display]="confirmationDialog$"
  [toggle]="false"
  [modal]="true"
  styleClass="tar-p-dialog"
  dialogTitle="{{ 'account.confirm-changes.text' | translate }}"
  dialogOkButtonName="{{ 'account.confirm-changes.yes' | translate }}"
  dialogCancelButtonName="{{ 'account.confirm-changes.no' | translate }}"
  (onConfirmed)="cancelSave(true)"
  (onClose)="cancelSave(false)"
>
</tar-dialog>
