import { Component } from '@angular/core';
import { DomainService } from '~tarya-layouts/app-component/domain.service';
import { TranslationsService } from '~tarya/modules/core/services/translations.service';

@Component({
  selector: 'tar-appfooter',
  templateUrl: './footer.html',
  styleUrls: ['./footer.scss'],
})
export class AppFooter {
  homePage: string;
  termsOfServicePageLink: string;

  constructor(
    private translationsService: TranslationsService,
    private domainService: DomainService
  ) {
    this.domainService.getDomainData().subscribe((response) => {
      this.homePage = response.homePage;
      this.termsOfServicePageLink = response.termsOfServicePageLink;
    });
  }

  ngOnInit() {}

  trans(sentence: string) {
    return this.translationsService.getTranslations('footer', sentence);
  }

  goTermsOfUse() {
    window.location.href = this.termsOfServicePageLink;
  }
}
