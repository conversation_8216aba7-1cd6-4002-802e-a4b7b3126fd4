@import 'styles/vendors/bootstrap/share';
@import 'styles/variables';

.search-wrap {
  margin: 0 -5px 40px;
  @include media-breakpoint-up(sm) {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.owner-box {
  display: block;
  transition: background 0.2s ease-in-out;

  &:hover {
    background: #f8f8f8;
  }
}

.search-box,
.btn-box {
  padding: 5px;

  .btn-primary {
    padding: 7px 30px;
  }
}

:host ::ng-deep .search-inner {
  max-width: 235px;
  min-width: 200px;

  .input-wrap {
    position: relative;
  }

  .search-control {
    padding: 10px 45px 10px 10px;
    border: 1px solid #b2b2b2;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    .rtl & {
      padding: 10px 10px 10px 45px;
    }
  }

  .search-btn {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    width: 40px;

    .rtl & {
      right: auto;
      left: 0;
    }
  }
}

.list-wrap {
  margin-bottom: 40px;
  position: relative;
  height: 390px;
  overflow-y: auto;
  border-top: 1px solid #d8d8d8;
  border-bottom: 1px solid #d8d8d8;

  .empty-list {
    position: absolute;
    top: 50%;
    left: 50%;
    font-size: 16px;
    color: #b0b0b0;
    padding: 10px;
    width: 100%;
    text-align: center;
    transform: translate(-50%, -50%);
  }
}
