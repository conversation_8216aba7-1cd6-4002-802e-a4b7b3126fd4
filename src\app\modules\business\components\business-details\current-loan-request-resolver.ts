import {
  ActivatedRouteSnapshot,
  Resolve,
  RouterStateSnapshot,
} from '@angular/router';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { LoanRequest } from '../../model/loan-request';

@Injectable()
export class CurrentLoanRequestResolver implements Resolve<LoanRequest> {
  resolve(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<LoanRequest> | Promise<LoanRequest> | LoanRequest {
    return route.parent.parent.data.loanRequest;
  }
}
