import { Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import { DomainService } from '../../../../layouts/app-component/domain.service';
import { LanguageSwitchService } from '../../../language/language-switch.service';
import { MenuItem } from 'primeng/api';

export class ProgressPanelStep {
  label: string;
  navigateTo: string;
}

@Component({
  selector: 'steps-progress-panel',
  templateUrl: './steps-progress-panel.component.html',
  styleUrls: ['./steps-progress-panel.component.scss'],
})
export class StepsProgressPanelComponent implements OnInit {
  items: MenuItem[];
  activeIndex: number;

  @Input('steps')
  steps: ProgressPanelStep[];

  @Input('panelHeading')
  panelHeading: string;

  @Input('translation-tag')
  translationTag: string;

  registerMode: boolean;
  backLink: any;

  testMode: boolean;

  hidePanel: boolean;

  constructor(
    private languageSwitchService: LanguageSwitchService,
    private translateService: TranslateService,
    private route: ActivatedRoute,
    private router: Router,
    private domainService: DomainService
  ) {}

  ngOnInit() {
    this.setRegisterMode();
    this.setTranslations();
    this.translateService.onLangChange.subscribe((event: LangChangeEvent) => {
      this.setTranslations();
    });
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.activeIndex = this.steps.findIndex(
          (value) => value.navigateTo == this.router.routerState.snapshot.url
        );
        this.hideStepsPanel();
      }
    });
    this.activeIndex = this.steps.findIndex(
      (value) => value.navigateTo == this.router.routerState.snapshot.url
    );

    this.domainService.getDomainData().subscribe((domainData) => {
      this.testMode = domainData.testMode;
    });
    this.hideStepsPanel();
  }

  hideStepsPanel() {
    this.route.snapshot.children[0].data.hideStepsPanel
      ? (this.hidePanel = true)
      : (this.hidePanel = false);
  }

  switchToEnglish() {
    this.languageSwitchService.changeLanguage('he');
  }

  switchToHebrew() {
    this.languageSwitchService.changeLanguage('he');
  }

  setTranslations() {
    this.translateService.get(this.translationTag).subscribe((val) => {
      this.items = [];
      for (const i of this.steps) {
        this.items.push({
          label: val[i.label],
          command: (event: any) => this.router.navigate([i.navigateTo]),
        });
      }
    });
  }

  setRegisterMode() {
    try {
      this.registerMode = this.route.snapshot.url[0].path === 'register';
    } catch (e) {
      this.registerMode = false;
    }

    if (!this.registerMode) {
      this.backLink = '/loans';
    } else {
      this.backLink = '/';
    }
  }
}
