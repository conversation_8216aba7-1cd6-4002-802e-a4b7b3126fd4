<ng-template #mobileConfirmationDialog>
  <h4 mat-dialog-title class="confirm-dialog-title mb-3">
    {{ 'account.mobile-verification.popup-title' | translate }}
  </h4>
  <mat-dialog-content>
    <div [formGroup]="form">
      <div class="row">
        <div
          class="order-2 mb-3 col-sm-6 order-sm-1 mb-sm-0 pt-5 d-flex align-items-start justify-content-center flex-column"
        >
          <app-tar-input-id
            formControlName="code"
            label="account.mobile-verification.input-label"
            [minIdLength]="6"
            [maxIdLength]="6"
            [errors]="errors.verificationCode"
          >
          </app-tar-input-id>
          <button
            type="button"
            class="resend-link my-3"
            (click)="sendVerificationCode()"
          >
            {{ 'account.mobile-verification.resend' | translate }}
          </button>
        </div>
        <div
          class="order-1 mb-3 col-sm-6 order-sm-2 mb-sm-5 blob-container pt-3 d-flex justify-content-end flex-column"
        >
          <img
            src="/assets/img/lender/tbot.png"
            class="bot-image"
            width="76"
            height="80"
          />
          <p class="mobile-description m-0 pb-4">
            {{ 'account.mobile-verification.mobile-text' | translate
            }}<strong class="px-1">{{ phone.value }}</strong>
          </p>
        </div>
      </div>
    </div>
  </mat-dialog-content>
  <mat-dialog-actions class="d-flex justify-content-end">
    <button class="btn btn-tar-primary" (click)="verifyMobileNumber()">
      {{ 'account.mobile-verification.confirm-btn' | translate }}
    </button>
    <button
      matDialogClose
      class="btn btn-tar-basic-primary"
      (click)="phoneConfirmationDialogState(false)"
    >
      {{ 'account.mobile-verification.cancel-btn' | translate }}
    </button>
  </mat-dialog-actions>
</ng-template>
