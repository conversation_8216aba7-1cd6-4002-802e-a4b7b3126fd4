<div class="loan-row">
    <a [routerLink]="routerSidebar ? ('../' + pages[0] + boxItem.routeLink) : boxItem.routeLink">
        <div class="loan-box" [ngClass]="{'has-no-data': (!boxItem.completed && !routerSidebar) || activePage}">
            <div class="ico-holder" [ngClass]="{'checked': boxItem.completed}">
                <img [src]="boxItem.boxImage" alt="#">
            </div>
            <div class="loan-text">
                <h4>{{ boxItem.boxTitle }}</h4>
                <span *ngIf="!boxItem.completed" class="no-data">חסרים נתונים.</span>
                <span class="loan-desc" *ngIf="boxItem.boxText">{{ boxItem.boxText }}</span>
            </div>
        </div>
    </a>
</div>