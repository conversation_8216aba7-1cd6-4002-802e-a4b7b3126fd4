:host ::ng-deep .card {
  border: none;

  .card-body {
    -webkit-box-shadow: 1px -1px 3px 1px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 1px -1px 3px 1px rgba(0, 0, 0, 0.1);
    box-shadow: 1px -1px 3px 1px rgba(0, 0, 0, 0.1);
  }
}

:host ::ng-deep .td-hold {
  padding: 5px 20px 0px 10px !important;
  box-shadow: 0 0 0 0 !important;
  background: none !important;
  font-weight: normal !important;
}

:host ::ng-deep .p-datatable > .p-datatable-wrapper table th {
  padding: 0px 4px !important;
  font-weight: 300 !important;
}

:host ::ng-deep .p-datatable > .p-datatable-wrapper table th:nth-child(2) {
  width: 205px !important;
}

:host ::ng-deep .p-datatable > .p-datatable-wrapper table th:nth-child(n + 3),
:host ::ng-deep .p-datatable > .p-datatable-wrapper table th:first-child {
  width: 100px !important;
}

:host ::ng-deep .table-inner {
  position: relative;
  padding: 100px 0 20px !important;
}

:host div[role='main'] {
  background-color: #f5f5f5;
  margin-right: 0;
  margin-left: -1px;
  border-left: solid 3px #00bac5;
  border-right: none;

  .rtl & {
    margin-right: -2px;
    margin-left: 0;
    border-right: solid 3px #00bac5;
    border-left: none;
  }
}

:host ::ng-deep th > span {
  border-bottom: 0 !important;
  text-align: left !important;

  .rtl & {
    text-align: right !important;
  }
}

:host ::ng-deep p-sorticon {
  visibility: hidden;
}

:host ::ng-deep .p-datatable-thead {
  background-color: rgba(56, 185, 197, 0.07);

  tr {
    padding: 0 10px !important;
  }
}

:host ::ng-deep .p-datatable-wrapper table td > .td-weight {
  font-weight: 300 !important;
  padding: 5px 20px 0px 10px !important;
  box-shadow: 0 0 0 0 !important;
}

:host ::ng-deep .p-datatable-wrapper table th span {
  padding-right: 15px !important;
  margin-left: -5px;
}

:host ::ng-deep .dashboard-table > .p-datatable .pages-info {
  padding: 0 30px 0 10px !important;
}

:host ::ng-deep p-table .p-paginator a .pi-caret-left:before {
  padding-left: 15px !important;

  .rtl & {
    padding-right: 10px !important;
  }
}

:host ::ng-deep p-table .p-paginator a .pi-caret-right:before {
  padding-left: 5px !important;

  .rtl & {
    padding-right: 0 !important;
  }
}

:host ::ng-deep .p-paginator {
  &-element {
    margin-inline-start: 230px !important;

    .rtl & {
      margin-inline-start: 196px !important;
    }
  }
}
