import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { BusinessOwner } from '~tarya/modules/business/model/business-owner';

@Component({
  selector: 'business-owner-box',
  templateUrl: './business-owner-box.component.html',
  styleUrls: ['./business-owner-box.component.scss'],
})
export class BusinessOwnerBoxComponent implements OnInit {
  @Input('owner')
  owner: BusinessOwner;

  @Output() toggleEditData = new EventEmitter();

  @Output() toggleDeleteData = new EventEmitter();

  constructor() {}

  ngOnInit() {}

  editData() {
    this.toggleEditData.emit(this.owner.id);
  }

  deleteData(event: Event) {
    event.stopPropagation();
    this.toggleDeleteData.emit(this.owner.id);
  }
}
