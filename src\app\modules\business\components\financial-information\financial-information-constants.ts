import { ValidatorFn, Validators } from '@angular/forms';

export class FinancialInformationItem {
  hidden?: boolean;
  label: string;
  tooltip?: string;
  validator?: ValidatorFn;
  mask?: string;
}

export const PAST_YEAR: FinancialInformationItem[] = [
  {
    hidden: true,
    label: 'id',
  },
  {
    hidden: true,
    label: 'date',
  },
  {
    label: 'revenue',
    tooltip: 'הכנסות העסק לשנה הרלוונטית',
  },
  {
    label: 'operatingProfit',
    tooltip: 'רווח לאחר עלות מכר והוצאות הנהלה וכלליות',
  },
  {
    label: 'currentPropertyValue',
    tooltip: 'סך הנכסים השוטפים מהמאזן',
  },
  {
    label: 'recurringLiablilities',
    tooltip: 'סך ההתחייבויות השוטפות מהמאזן',
  },
  {
    label: 'totalDebt',
    tooltip: 'סך החוב לבנקים- טווח קצר+טווח ארוך, פחות סך המזומנים במאזן',
  },
  {
    label: 'equity',
    tooltip: 'הון עצמי כפי שמופיע במאזן החברה בתוספת הלוואות הבעלים לחברה',
  },
  {
    label: 'totalBalance',
    tooltip: 'סך המאזן לאותה שנה',
  },
];

export const CURRENT_YEAR: FinancialInformationItem[] = [
  {
    hidden: true,
    label: 'id',
  },
  {
    hidden: true,
    label: 'date',
  },
  {
    label: 'revenue',
    tooltip: 'הכנסות העסק לשנה הרלוונטית',
  },
  {
    label: 'operatingProfit',
    tooltip: 'רווח לאחר עלות מכר והוצאות הנהלה וכלליות',
  },
  {
    label: 'totalDebt',
    tooltip: 'סך החוב לבנקים- טווח קצר+טווח ארוך, פחות סך המזומנים במאזן',
  },
];

export const FORECAST: FinancialInformationItem[] = [
  {
    hidden: true,
    label: 'id',
  },
  {
    hidden: true,
    label: 'date',
  },
  {
    label: 'revenue',
    tooltip: 'הכנסות העסק לשנה הרלוונטית',
  },
  {
    label: 'cashFlow',
    tooltip: 'cashFlow',
  },
  {
    label: 'grossProfit',
    tooltip: 'grossProfit',
  },
  {
    label: 'annualTotalRepayments',
    tooltip: 'annualTotalRepayments',
  },
  {
    label: 'operatingProfit',
    tooltip: 'רווח לאחר עלות מכר והוצאות הנהלה וכלליות',
  },
  {
    label: 'netProfit',
    tooltip: 'netProfit',
  },
];
