import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  CanActivate,
  Router,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router';
import { Observable } from 'rxjs';
import { map, mergeMap } from 'rxjs/operators';
import { BorrowersService } from '../services/borrowers.service';

@Injectable({
  providedIn: 'root',
})
export class CanActivateBorrowersPageGuard implements CanActivate {
  constructor(
    private router: Router,
    private borrowersService: BorrowersService
  ) {}
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ):
    | Observable<boolean | UrlTree>
    | Promise<boolean | UrlTree>
    | boolean
    | UrlTree {
    return this.borrowersService.getAvailablePages().pipe(
      mergeMap((availablePages) => {
        return this.borrowersService.getPaymentMethods().pipe(
          map(({ creditCards }) => {
            if (availablePages.enableCDMainPage) {
              return true;
            }
            const expiredOrFailedCreditCards = creditCards.filter(
              (creditCard) => creditCard.failed || creditCard.expired
            );
            if (
              expiredOrFailedCreditCards.length > 0 &&
              availablePages.enableCDCreditCardNumberUpdatingPage
            ) {
              const expiredCCLastFourDigits =
                expiredOrFailedCreditCards[0].creditCardLastDigits;
              const firstLoanId = expiredOrFailedCreditCards[0].loans[0].id;
              return this.router.createUrlTree([
                `/app/borrowers/credit-card-update/${expiredCCLastFourDigits}/${firstLoanId}`,
              ]);
            }
            return this.router.createUrlTree([
              `/app/borrowers/under-construction`,
            ]);
          })
        );
      })
    );
  }
}
