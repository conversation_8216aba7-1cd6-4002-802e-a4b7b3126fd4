import { Component, OnInit } from '@angular/core';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { NEW_LOAN_STEPS } from '../../layouts/new-loan-request/new-loan-steps';
import { Router } from '@angular/router';
import { BusinessService } from '../../service/business.service';
import { LoanRequestService } from '../../service/loan-request.service';
import { Business } from '../../model/business';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';

@Component({
  selector: 'app-new-loan-business-details',
  templateUrl: './new-loan-business-details.component.html',
  styleUrls: ['./new-loan-business-details.component.scss'],
})
export class NewLoanBusinessDetailsComponent implements OnInit {
  businesses: Business[];
  newLoanBusinessDetailsForm: UntypedFormGroup;
  businessData: SelectItem[];
  business: Business;
  checked = true;

  constructor(
    private fb: UntypedFormBuilder,
    private router: Router,
    private businessService: BusinessService,
    private loanRequestService: LoanRequestService,
    private translate: TranslateService
  ) {}

  ngOnInit() {
    this.businessService.getAllBusinessesBe().subscribe((res) => {
      this.businessData = [];
      this.businessData.push({
        label: this.translate.instant('business-select-label'),
        value: null,
      });
      res.forEach((item) => {
        this.businessData.push({ label: item.name, value: item.id });
      });
      this.businesses = res;
    });

    this.createForm();
    this.newLoanBusinessDetailsForm
      .get('businessState')
      .valueChanges.subscribe((val) => {
        this.setRequiredForBusinessSelect(val);
      });
    this.setRequiredForBusinessSelect(
      this.newLoanBusinessDetailsForm.get('businessState').value
    );
  }

  createForm() {
    this.newLoanBusinessDetailsForm = this.fb.group({
      businessState: ['exist', [Validators.required]],
      businessSelect: [null, []],
    });
  }

  setRequiredForBusinessSelect(state: string) {
    if (state === 'exist') {
      this.newLoanBusinessDetailsForm
        .get('businessSelect')
        .setValidators([Validators.required]);
      this.newLoanBusinessDetailsForm
        .get('businessSelect')
        .updateValueAndValidity({ emitEvent: false, onlySelf: true });
    } else {
      this.newLoanBusinessDetailsForm.get('businessSelect').setValidators([]);
      this.newLoanBusinessDetailsForm
        .get('businessSelect')
        .updateValueAndValidity({ emitEvent: false, onlySelf: true });
    }
  }

  submit() {
    if (
      this.newLoanBusinessDetailsForm.get('businessState').value === 'exist'
    ) {
      const loanRequest = this.loanRequestService.getCurrentLoanRequest();
      this.businesses.forEach((item) => {
        if (
          this.newLoanBusinessDetailsForm.get('businessSelect').value ===
          item.id
        ) {
          loanRequest.business = item;
        }
      });
      this.router.navigate(['/loans/new/' + NEW_LOAN_STEPS[1]]);
    } else {
      this.router.navigate(['/loans/new/' + NEW_LOAN_STEPS[0]]);
    }
  }
}
