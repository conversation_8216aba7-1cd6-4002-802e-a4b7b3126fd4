import {
  AfterViewChecked,
  AfterViewInit,
  Component,
  OnInit,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { LoginService } from '../../../core/services/login.service';
import { CookieService } from 'ngx-cookie';
import { RecaptchaComponent } from 'ng-recaptcha';
import { AppValidators } from '../../../shared/validators/validators';
import { first } from 'rxjs/operators';
import { Captcha } from '~tarya/modules/core/models/captcha';
import { LoginRequest } from '~tarya/modules/core/models/login-request';
import { SmsLoginRequest } from '~tarya/modules/core/models/sms-login-request';
import { SmsLoginResponse } from '~tarya/modules/core/models/sms-login-response';
import 'fingerprintjs';
@Component({
  selector: 'app-login-page',
  templateUrl: './login-page.component.html',
  styleUrls: ['./login-page.component.scss'],
})
export class LoginPageComponent
  implements OnInit, AfterViewChecked, AfterViewInit
{
  @ViewChild('captchaRef')
  captchaElement: RecaptchaComponent;

  selectedValue: string;

  captcha: Captcha;

  loginRequest = {} as LoginRequest;
  smsLoginRequest = {} as SmsLoginRequest;
  rememberLogin: boolean;

  loginForm: UntypedFormGroup;
  smsLoginForm: UntypedFormGroup;
  loginFormMessage: string;

  sentSMS: boolean;
  smsLoginFormMessage: string;
  smsLoginFormMessageParameter: any;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private fb: UntypedFormBuilder,
    private loginService: LoginService,
    private cookieService: CookieService,
    private renderer: Renderer2
  ) {
    this.selectedValue = 'mainForm';
    this.rememberLogin = false;
    this.sentSMS = false;
  }

  ngOnInit() {
    this.isCaptchaRequired();
    this.loginRequest.username = this.cookieService.get('username');
    if (this.loginRequest.username && this.loginRequest.username.length > 0) {
      this.rememberLogin = true;
    }

    this.createLoginForm();
    this.createSmsLoginForm();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      for (const key in this.loginForm.controls) {
        if (this.loginForm.controls[key]) {
          this.loginForm.controls[key].markAsPristine();
        }
      }
    }, 50);

    setTimeout(() => {
      for (const key in this.smsLoginForm.controls) {
        if (this.smsLoginForm.controls[key]) {
          this.smsLoginForm.controls[key].markAsPristine();
        }
      }
    }, 50);
  }

  createLoginForm() {
    this.loginForm = this.fb.group({
      login: [this.loginRequest.username, Validators.required],
      password: [null, Validators.required],
      rememberLogin: [this.rememberLogin],
    });
  }

  createSmsLoginForm() {
    this.smsLoginForm = this.fb.group({
      identity: [
        this.loginRequest.username,
        [Validators.required, AppValidators.tzValidator],
      ],
      smsCode: [null, Validators.required],
    });
    this.smsLoginForm.disable();
    this.smsLoginForm.valueChanges.subscribe(() =>
      this.setSmsLoginFormMessage(null, null)
    );
  }

  isCaptchaRequired(): void {
    this.loginService
      .isCaptchaRequired()
      .subscribe((captcha) => (this.captcha = captcha));
  }

  onFormChange(event: any) {
    if (event === 'smsForm') {
      this.loginForm.reset();
      this.smsLoginForm.enable();
      this.loginForm.disable();
    } else {
      this.smsLoginForm.reset();
      this.smsLoginForm.disable();
      this.loginForm.enable();
    }
  }

  postPasswordLoginForm() {
    this.loginRequest.username = this.loginForm.value.login;
    this.loginRequest.password = this.loginForm.value.password;
    if (this.captcha.required) {
      this.captchaElement.resolved.pipe(first()).subscribe(
        (captchaResponse: string) => {
          if (captchaResponse != null) {
            this.loginRequest.captcha = captchaResponse;
            this.postLogin();
          }
        },
        () => {},
        () => {
          this.captchaElement.reset();
        }
      );
      this.captchaElement.execute();
    } else {
      this.postLogin();
    }
  }

  postLogin(): void {
    this.loginFormMessage = 'login.status.VERIFYING';
    const success = (res: any) => {
      this.loginFormMessage = 'login.status.SUCCESS';
      if (this.rememberLogin) {
        this.cookieService.put('username', this.loginRequest.username);
      }
      this.loginService.navigateToPageByUserRole(res);
    };
    const fail = (res: any) => {
      this.loginFormMessage = null;
      if (res.status === 401) {
        this.loginForm.get('password').reset();
        this.captcha.required = false;
        this.isCaptchaRequired();
        this.loginForm.setErrors({ WRONG_PASSWORD_OR_EMAIL: true });
      }
    };
    this.loginRequest.fingerprint = new Fingerprint().get();
    this.loginService.login(this.loginRequest).subscribe(success, fail);
  }

  postSmsLoginForm() {
    if (this.captcha.required) {
      this.captchaElement.resolved.pipe(first()).subscribe(
        (captchaResponse: string) => {
          if (captchaResponse != null) {
            this.smsLoginRequest.captcha = captchaResponse;
            this.postSmsLogin();
          }
        },
        () => {},
        () => {
          this.captchaElement.reset();
        }
      );
      this.captchaElement.execute();
    } else {
      this.postSmsLogin();
    }
  }

  postSmsLogin(): void {
    this.smsLoginRequest.fingerprint = new Fingerprint().get();
    const success = (res: SmsLoginResponse) => {
      if (res.responseObject !== null) {
        this.loginFormMessage = 'login.status.SUCCESS';
        this.loginService.navigateToPageByUserRole(res.responseObject);
      } else {
        if (res.success) {
          if (this.sentSMS) {
            this.setSmsLoginFormMessage('login.NEW_CODE_HAS_BEEN_SENT', null);
          } else {
            this.setSmsLoginFormMessage('login.SMS_SEND_NOTIFICATION', {
              phoneNumber: res.cellPhone,
            });
          }
        } else {
          // TODO Hint to set error message after form changed - to be refactored
          setTimeout(() => {
            this.setSmsLoginFormMessage('login.SMS_CAN_BE_RESENT_AFTER', {
              resendTime: res.timeToNextResend,
            });
          }, 500);
        }
        this.sentSMS = true;
      }
      this.isCaptchaRequired();
    };

    const fail = () => {
      if (this.sentSMS) {
        this.smsLoginForm.get('smsCode').setErrors({
          SMS_CODE_IS_WRONG: true,
        });
      } else {
        this.smsLoginForm.get('identity').setErrors({ ID_NOT_FOUND: true });
      }
      this.captcha.required = false;
      this.isCaptchaRequired();
    };

    this.smsLoginRequest.id = this.smsLoginForm.value.identity;
    if (this.sentSMS && this.smsLoginForm.value.smsCode !== null) {
      this.smsLoginRequest.smsCode = this.smsLoginForm.value.smsCode;
    }

    this.loginService.smsLogin(this.smsLoginRequest).subscribe(success, fail);
  }

  setFocusOnSmsCode() {
    const onElement = this.renderer.selectRootElement('input[name=smsCode]');
    onElement.focus();
  }

  ngAfterViewChecked() {
    if (this.sentSMS) {
      this.setFocusOnSmsCode();
    }
  }

  setSmsLoginFormMessage(message: string, parameter: any) {
    this.smsLoginFormMessage = message;
    this.smsLoginFormMessageParameter = parameter;
  }

  changeId($event: any) {
    $event.preventDefault();
    this.smsLoginForm.get('smsCode').setValue(null);
    this.sentSMS = false;
  }

  resendSms($event: any) {
    $event.preventDefault();
    this.smsLoginForm.get('smsCode').setValue(null);
    this.postSmsLoginForm();
  }
}
