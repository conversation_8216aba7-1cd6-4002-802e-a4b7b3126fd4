<header aria-label="header">
  <div class="header-logo">
    <div class="container">
      <div class="d-flex justify-content-end align-items-center">
        <a class="logo" href="{{homePageUrl}}">
          <img src="/assets/img/header-logo.svg" alt="header logo" />
        </a>
      </div>
    </div>
  </div>
  <div class="top-row">
    <div class="container">
      <div class="row align-items-center flex-row">
        <div class="col-lg-6 mobile-info">
          <div class="nav d-flex flex-row flex-nowrap">
            <span
              role="group"
              class="p-0"
              aria-labelledby="hiyou header-username"
            >
              <span
                id="hiyou"
                class="hiyou d-md-inline-block"
                *ngIf="!loginState"
              >
                {{trans("welcome")}}
              </span>
              <span
                id="header-username"
                *ngIf="userName"
                (click)="goToHomePage()"
              >
                {{userName}} ({{trans("user")}} {{userId}})
              </span>
            </span>
            <nav role="navigation" aria-label="top">
              <ul>
                <li>
                  <span
                    *ngIf="!isHomePage()"
                    (click)="goToIndexPage()"
                    (keydown.enter)="goToIndexPage()"
                    tabindex="0"
                    role="button"
                  >
                    {{trans("Home")}}
                  </span>
                </li>
                <li>
                  <span
                    *ngIf="!isLoginOrRecoverPages() && loginState"
                    [routerLink]="['/' + loginPath]"
                    tabindex="0"
                    role="button"
                  >
                    {{trans("Login")}}
                  </span>
                </li>
                <li>
                  <span
                    (click)="logout()"
                    (keydown.enter)="logout()"
                    *ngIf="!loginState"
                    tabindex="0"
                    role="button"
                  >
                    {{trans("Logout")}}
                  </span>
                </li>
              </ul>
            </nav>
          </div>
        </div>
        <div
          class="col-lg-6 mobile-contact justify-content-end d-flex align-items-center flex-row"
        >
          <div
            class="align-self-center contact-us"
            [innerHtml]="trans('Contact us')"
          ></div>
          <div
            class="align-self-center flags"
            role="group"
            [attr.aria-label]="trans('select-language')"
          >
            <span
              class="en"
              (click)="switchToEnglish()"
              (keydown.enter)="switchToEnglish()"
              tabindex="0"
              role="button"
              [attr.aria-current]="lang === 'en'? 'true':'false'"
              [attr.aria-label]="trans('english')"
            ></span>
            <span
              class="he"
              (click)="switchToHebrew()"
              (keydown.enter)="switchToHebrew()"
              tabindex="0"
              role="button"
              [attr.aria-current]="lang === 'he'? 'true':'false'"
              [attr.aria-label]="trans('hebrew')"
            ></span>
          </div>
        </div>
      </div>
    </div>
    <div class="p-2" *ngIf="!loginState">
      <button class="btn">{{trans("dashboard")}}</button>
    </div>
  </div>
  <div class="top-menu">
    <div class="container">
      <div
        class="top-menu-item d-flex align-items-center"
        *ngIf="isTransactions"
      >
        <top-menu *ngIf="!isLoginOrRecoverPages()" [lang]="lang"></top-menu>
      </div>
    </div>
  </div>
</header>
