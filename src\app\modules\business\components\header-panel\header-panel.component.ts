import { Component, Input, OnInit } from '@angular/core';

import { LoginService } from '../../../core/services/login.service';
import { HOME_PAGE } from '../../layouts/homepage/homepage-routing.module';
import { DomainService } from '../../../../layouts/app-component/domain.service';
import { LanguageSwitchService } from '../../../language/language-switch.service';
import { User } from '~tarya/modules/core/models/user';
import { Base } from '~tarya/modules/table-generator/shared/base';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'header-panel',
  templateUrl: './header-panel.component.html',
  styleUrls: ['./header-panel.component.scss'],
})
export class HeaderPanelComponent extends Base implements OnInit {
  @Input() accountFirstName: User;

  homePageLink = HOME_PAGE;
  homeUrl: string = window.location.origin;

  testMode: boolean;

  constructor(
    private languageSwitchService: LanguageSwitchService,
    private loginService: LoginService,
    private domainService: DomainService
  ) {
    super();
  }

  ngOnInit() {
    this.domainService
      .getDomainData()
      .pipe(takeUntil(this.destroy$))
      .subscribe((domainData) => {
        this.testMode = domainData.testMode;
      });
  }

  switchToEnglish() {
    this.languageSwitchService.changeLanguage('en');
  }

  switchToHebrew() {
    this.languageSwitchService.changeLanguage('he');
  }

  logout() {
    this.loginService
      .logout()
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        window.location.href = this.homeUrl;
      });
  }
}
