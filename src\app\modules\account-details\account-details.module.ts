import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import {
  TranslateLoader,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { QuestionnaireModule } from '../questionnaire/questionnaire.module';
import { AppSharedModule } from '../shared/app.shared.module';
import { AccountDetailsRoutingModule } from './account-details-routing.module';
import { AccountDetailsConfirmMobileDialogComponent } from './components/account-details-confirm-mobile-dialog/account-details-confirm-mobile-dialog.component';
import { AccountDetailsLayoutComponent } from './components/account-details-layout/account-details-layout.component';
import { AccountDetailsComponent } from './components/account-details/account-details.component';
import { PasswordUpdateComponent } from './components/password-update/password-update.component';


export function HttpLoaderFactory(http: HttpClient) {
  return new MultiTranslateHttpLoader(http, [
    { prefix: '/i18n/account-details/', suffix: '.json' },
    { prefix: '/i18n/common/', suffix: '.json' },
  ]);
}

@NgModule({
  declarations: [
    AccountDetailsComponent,
    PasswordUpdateComponent,
    AccountDetailsLayoutComponent,
    AccountDetailsConfirmMobileDialogComponent,
  ],
  imports: [
    CommonModule,
    AccountDetailsRoutingModule,
    ReactiveFormsModule,
    AppSharedModule,
    QuestionnaireModule,
    TranslateModule.forChild({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient],
      },
      isolate: false,
      extend: true,
    }),
  ],
})
export class AccountDetailsModule {
  constructor(private translateService: TranslateService) {
    const currentLang = this.translateService.currentLang;
    this.translateService.currentLang = '';
    this.translateService.use(currentLang);
  }
}
