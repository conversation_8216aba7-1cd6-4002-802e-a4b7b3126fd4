@import 'styles/vendors/bootstrap/share';
@import 'styles/variables';

:host ::ng-deep .white-box-wrap {
  padding: 30px;

  @include media-breakpoint-up(md) {
    padding: 45px;
  }

  .text-add {
    padding: 5px;
    border-bottom: 1px solid #b2b2b2;

    p {
      margin: 0;
    }
  }

  .form-control {
    border-radius: 0;
    border: none;
    border-bottom: 1px solid #b2b2b2;
  }
}

.building-loan-wrap {
  color: #3d3d3d;
  font-size: 15px;
}

.loan-desc-list {
  margin-bottom: 20px;

  ul {
    padding: 0;
  }

  li {
    margin-bottom: 10px;
  }

  strong {
    color: #000;
    font-size: 16px;
    display: inline-block;
    vertical-align: top;
    padding: 0 4px;
  }

  span {
    display: inline-block;
    vertical-align: top;
  }
}

.loan-desc-text {
  margin-bottom: 20px;

  h6 {
    color: #000;
    font-size: 16px;
    margin: 0 0 5px;
    font-weight: bold;
  }

  p {
    margin: 0;
  }
}

.btn-holder {
  padding-top: 30px;
}
