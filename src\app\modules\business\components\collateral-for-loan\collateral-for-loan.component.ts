import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, TemplateRef } from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import {
  AbstractControl,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { CollateralLoanInfo } from '../../model/collateral-loan-info.class';
import { LoanRequestService } from '../../service/loan-request.service';
import { TypeEnum } from '../../model/type-enum';
import { ActivatedRoute, Data, Router } from '@angular/router';
import { LoanRequest } from '../../model/loan-request';
import { combineLatest, Observable, Subscription } from 'rxjs';
import { Person } from '../../model/person';
import { AppValidators } from '../../../shared/validators/validators';
import { IdResponse } from '../../model/id-response';
import { LOAN_PAGES } from '../../layouts/loan/loan-pages';
import { AppConstants } from '../../../shared/utils/app-constans';
import { LanguageService } from '../../../language/language.service';
import { map } from 'rxjs/operators';

export class GuarantorInfoData {
  name: string;
  email: string;
}

@Component({
  selector: 'collateral-for-loan',
  templateUrl: './collateral-for-loan.component.html',
  styleUrls: ['./collateral-for-loan.component.scss'],
})
export class CollateralForLoanComponent implements OnInit, OnDestroy {
  modalRef: BsModalRef;

  collateralLoanForm: UntypedFormGroup;
  guarantorForm: UntypedFormGroup;
  collateralLoanInfo: CollateralLoanInfo = {} as CollateralLoanInfo;
  guarantorInfoData: GuarantorInfoData;

  cellPhoneMask = AppConstants.CELL_PHONE_MASK;

  collateralOptions: TypeEnum[] = [];
  additionalCommitmentsOptions: TypeEnum[] = [];
  currentLang: string;
  otherCollateralId: number;
  otherCommitmentId: number;
  amortizationState: number;

  additionalCommitmentsState: boolean;

  loanRequest: LoanRequest;

  loanRequestObservable: Observable<Data>;
  optionsObservable: Observable<Data>;
  showGuarantorForm = false;
  guarantorsAsArray: Person[];
  guarantorLocked: boolean;

  formLocked: boolean;
  private _subscription: Subscription = new Subscription();

  constructor(
    private modalService: BsModalService,
    private fb: UntypedFormBuilder,
    private languageService: LanguageService,
    private loanRequestService: LoanRequestService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit() {
    this.currentLang = this.languageService.currentLang;

    this.loanRequestObservable = this.route.parent.parent.data;
    this.optionsObservable = this.route.data;

    const loanRequestResult$ = combineLatest([
      this.loanRequestObservable,
      this.optionsObservable,
    ]).pipe(map((results) => ({ loan: results[0], collateral: results[1] })));
    this._subscription.add(
      loanRequestResult$.subscribe(({ loan, collateral }) => {
        this.loanRequest = loan.loanRequest;
        this.formLocked = this.loanRequestService.getFormLockedStatus(
          this.loanRequest
        );
        this.setCollateralOptions(collateral.collateralOptions);
        this.setAdditionalCommitmentsOptions(
          collateral.additionalCommitmentsOptions
        );

        this.collateralLoanInfo.guarantors = new Map<number, Person>();
        this.loanRequest.guarantors.forEach((person: Person) =>
          this.collateralLoanInfo.guarantors.set(person.id, person)
        );
        this.collateralLoanInfo.otherCollateral = '';
        this.collateralLoanInfo.additionalLiabilitiesSwitch = false;
        this.collateralLoanInfo.hasGuarantors =
          this.collateralLoanInfo.guarantors.size !== 0;
        this.showGuarantorForm = !this.collateralLoanInfo.hasGuarantors;
        this.updateGuarantorsAsArray();
      })
    );

    this.createCollateralLoanForm();
    this.createGuarantorForm();
    this.amortizationState = this.loanRequest.amortizationPeriod;
    this.guarantorInfoData = new GuarantorInfoData();
    this.additionalCommitmentsState =
      this.collateralLoanInfo.additionalLiabilitiesSwitch;
  }

  ngOnDestroy() {
    this._subscription.unsubscribe();
  }

  private setCollateralOptions(collateralOptions: TypeEnum[]) {
    this.collateralLoanInfo.collaterals = [];
    this.loanRequest.collaterals.forEach((collateral) =>
      this.collateralLoanInfo.collaterals.push(collateral.id)
    );

    this.collateralLoanInfo.otherCollateral =
      this.loanRequest.collateralFreeText;
    collateralOptions.sort((a, b) => a.id - b.id);
    this.collateralOptions = collateralOptions;
    this.otherCollateralId = collateralOptions.filter(
      (collateral) => collateral.name === 'other'
    )[0].id;
  }

  private setAdditionalCommitmentsOptions(
    additionalCommitmentOptions: TypeEnum[]
  ) {
    this.collateralLoanInfo.additionalCommitments = [];
    this.loanRequest.additionalCommitments.forEach((commitment) =>
      this.collateralLoanInfo.additionalCommitments.push(commitment.id)
    );
    this.collateralLoanInfo.additionalLiabilitiesSwitch =
      this.collateralLoanInfo.additionalCommitments.length !== 0;
    this.collateralLoanInfo.otherCommitments =
      this.loanRequest.additionalCommitmentFreeText;

    additionalCommitmentOptions.sort((a, b) => a.id - b.id);
    this.additionalCommitmentsOptions = additionalCommitmentOptions;
    this.otherCommitmentId = additionalCommitmentOptions.filter(
      (commitment) => commitment.name === 'other'
    )[0].id;
  }

  createCollateralLoanForm() {
    this.collateralLoanForm = this.fb.group({
      collaterals: [this.collateralLoanInfo.collaterals],
      collateralFreeText: [
        {
          value: this.collateralLoanInfo.otherCollateral,
          disabled: !this.collateralsContainOtherCollateral(
            this.collateralLoanInfo.collaterals,
            this.otherCollateralId
          ),
        },
        this.collateralsContainOtherCollateral(
          this.collateralLoanInfo.collaterals,
          this.otherCollateralId
        )
          ? Validators.required
          : null,
      ],
      additionalLiabilitiesSwitch: [
        this.collateralLoanInfo.additionalLiabilitiesSwitch,
      ],
      additionalCommitments: [this.collateralLoanInfo.additionalCommitments],
      additionalCommitmentFreeText: [
        {
          value: this.collateralLoanInfo.otherCommitments,
          disabled: !this.collateralsContainOtherCollateral(
            this.collateralLoanInfo.additionalCommitments,
            this.otherCommitmentId
          ),
        },
        this.collateralsContainOtherCollateral(
          this.collateralLoanInfo.additionalCommitments,
          this.otherCommitmentId
        )
          ? Validators.required
          : null,
      ],
      hasGuarantors: [
        {
          value: this.collateralLoanInfo.hasGuarantors,
          disabled: this.collateralLoanInfo.hasGuarantors,
        },
      ],
    });

    if (this.formLocked) {
      this.collateralLoanForm.disable();
    } else {
      this.collateralLoanForm
        .get('collaterals')
        .valueChanges.subscribe((res: number[]) => {
          if (
            this.collateralsContainOtherCollateral(res, this.otherCollateralId)
          ) {
            this.enableControl(
              this.collateralLoanForm.get('collateralFreeText')
            );
          } else {
            this.disableControl(
              this.collateralLoanForm.get('collateralFreeText')
            );
          }
        });

      this.collateralLoanForm
        .get('additionalCommitments')
        .valueChanges.subscribe((res: number[]) => {
          if (
            this.collateralsContainOtherCollateral(res, this.otherCommitmentId)
          ) {
            this.enableControl(
              this.collateralLoanForm.get('additionalCommitmentFreeText')
            );
          } else {
            this.disableControl(
              this.collateralLoanForm.get('additionalCommitmentFreeText')
            );
          }
        });
    }
  }

  createGuarantorForm() {
    this.guarantorForm = this.fb.group({
      id: [],
      firstName: [null, Validators.required],
      lastName: [null, Validators.required],
      identity: [null, [Validators.required, AppValidators.tzValidator]],
      cellPhone: [
        null,
        [Validators.required, AppValidators.getValidator('cellPhone')],
      ],
      personalEmail: [
        null,
        [Validators.required, AppValidators.getValidator('email')],
      ],
    });

    // if (!this.formLocked) {
    //     this.guarantorForm.disable();
    // }
  }

  saveOrCreateGuarantor() {
    if (this.guarantorForm.value.id) {
      this.loanRequestService
        .updateGuarantor(this.loanRequest.id, this.guarantorForm.value)
        .subscribe(() => {
          this.updateGuarantorsListAfterEditing(this.guarantorForm.value.id);
        });
    } else {
      this.loanRequestService
        .createGuarantor(this.loanRequest.id, this.guarantorForm.value)
        .subscribe((res: IdResponse) => {
          this.updateGuarantorsListAfterEditing(res.id);
        });
    }
    this.collateralLoanForm.get('hasGuarantors').disable();
  }

  private updateGuarantorsListAfterEditing(id: number) {
    this.guarantorForm.get('id').setValue(id);
    this.collateralLoanInfo.guarantors.set(id, this.guarantorForm.value);
    this.guarantorForm.reset();
    this.updateGuarantorsAsArray();
    this.showGuarantorForm = false;
  }

  toggleUpdateGuarantor(guarantorId: number) {
    if (this.formLocked) {
      this.guarantorForm.disable();
    }

    this.showGuarantorForm = true;
    const guarantorToEdit = this.collateralLoanInfo.guarantors.get(guarantorId);
    this.guarantorForm.get('id').setValue(guarantorToEdit.id);
    this.guarantorForm.get('firstName').setValue(guarantorToEdit.firstName);
    this.guarantorForm.get('lastName').setValue(guarantorToEdit.lastName);
    this.guarantorForm.get('identity').setValue(guarantorToEdit.identity);
    this.guarantorForm.get('cellPhone').setValue(guarantorToEdit.cellPhone);
    this.guarantorForm
      .get('personalEmail')
      .setValue(guarantorToEdit.personalEmail);
    this.showGuarantorForm = true;
  }

  deleteGuarantor(guarantorId: number) {
    if (!this.formLocked) {
      this.guarantorForm.reset();
      this.showGuarantorForm = false;
      this.loanRequestService
        .deleteGuarantor(this.loanRequest.id, guarantorId)
        .subscribe(() => {
          this.collateralLoanInfo.guarantors.delete(guarantorId);
          this.updateGuarantorsAsArray();
          if (this.collateralLoanInfo.guarantors.size === 0) {
            this.showGuarantorForm = true;
            this.collateralLoanForm.get('hasGuarantors').enable();
          }
        });
    }
  }

  openBzModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(
      template,
      Object.assign({}, { class: 'bz-modal' })
    );
  }

  clearAdditionalCommitments(event: HTMLInputElement) {
    this.additionalCommitmentsState = !this.additionalCommitmentsState;

    if (!event.checked) {
      this.disableControl(
        this.collateralLoanForm.get('additionalCommitments'),
        []
      );
    } else {
      this.enableControl(this.collateralLoanForm.get('additionalCommitments'));
    }
  }

  toggleAddGuarantor() {
    if (!this.formLocked) {
      this.guarantorForm.reset();
      this.showGuarantorForm = true;
    }
  }

  cancelEditingGuarantor() {
    this.guarantorForm.reset();
    this.showGuarantorForm = false;
  }

  updateCollaterals(event: Event) {
    if (this.formLocked) {
      this.router.navigate([LOAN_PAGES[4]], {
        relativeTo: this.route.parent.parent,
      });
      return;
    }

    const collateralsLoanRequest = new LoanRequest();
    collateralsLoanRequest.id = this.loanRequest.id;
    collateralsLoanRequest.collaterals = [];
    this.collateralLoanForm.value.collaterals.forEach((typeEnumId: number) => {
      const collateralTypeEnum = {} as TypeEnum;
      collateralTypeEnum.id = typeEnumId;
      collateralsLoanRequest.collaterals.push(collateralTypeEnum);
    });
    if (this.collateralLoanForm.value.collateralFreeText) {
      collateralsLoanRequest.collateralFreeText =
        this.collateralLoanForm.value.collateralFreeText;
    }
    collateralsLoanRequest.additionalCommitments = [];
    if (this.additionalCommitmentsState) {
      this.collateralLoanForm.value.additionalCommitments.forEach(
        (typeEnumId: number) => {
          const additionalCommitmentTypeEnum = {} as TypeEnum;
          additionalCommitmentTypeEnum.id = typeEnumId;
          collateralsLoanRequest.additionalCommitments.push(
            additionalCommitmentTypeEnum
          );
        }
      );
    }
    if (this.collateralLoanForm.value.additionalCommitmentFreeText) {
      collateralsLoanRequest.additionalCommitmentFreeText =
        this.collateralLoanForm.value.additionalCommitmentFreeText;
    }
    this.loanRequestService
      .updateLoanRequest(collateralsLoanRequest)
      .subscribe();

    this.router.navigate([LOAN_PAGES[4]], {
      relativeTo: this.route.parent.parent,
    });
  }

  private updateGuarantorsAsArray() {
    this.guarantorsAsArray = Array.from(
      this.collateralLoanInfo.guarantors.values()
    );
  }

  private collateralsContainOtherCollateral(
    collaterals: number[],
    otherCollateralId: number
  ) {
    return collaterals.indexOf(otherCollateralId) !== -1;
  }

  private enableControl(control: AbstractControl) {
    control.enable();
    control.markAsPristine();
    control.markAsUntouched();
    control.setValidators(Validators.required);
  }

  private disableControl(control: AbstractControl, emptyValue = null) {
    control.disable();
    control.setValue(emptyValue);
    control.clearValidators();
  }
}
