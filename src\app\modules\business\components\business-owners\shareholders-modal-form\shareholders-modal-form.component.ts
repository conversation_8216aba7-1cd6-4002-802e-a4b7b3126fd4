import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AbstractControl, FormControl, UntypedFormGroup } from '@angular/forms';
import { AutoCompleteService } from '../../../../shared/services/autocomplete-service';
import { AppConstants } from '../../../../shared/utils/app-constans';
import { ErrorHandling } from '../../../../shared/models/error-handling';
import { ApiErrorsResponse } from '../../../../shared/models/api-errors-response';
import { UtilityService } from '~tarya/modules/business/service/utility.service';
import { DropdownOption } from '~tarya/modules/shared/models/option';
import { takeUntil } from 'rxjs/operators';
import { LanguageSwitchService } from '~tarya-language/language-switch.service';
import { Base } from '~tarya-table-generator/shared/base';

@Component({
  selector: 'shareholders-modal-form',
  templateUrl: './shareholders-modal-form.component.html',
  styleUrls: ['./shareholders-modal-form.component.scss'],
})
export class ShareholdersModalFormComponent extends Base implements OnInit {
  @Input('businessOwnersForm')
  businessOwnersForm: UntypedFormGroup;

  @Input('backEndValidationErrors')
  set backEndValidationErrors(backEndValidationErrors: ApiErrorsResponse) {
    ErrorHandling.formSetErrors(
      backEndValidationErrors,
      this.businessOwnersForm
    );
  }

  calendarLanguage;
  lang: string;
  currentDate: Date = new Date();
  currentYear: number;
  minYear: number;

  hiddenId = true;

  filteredStreetsSingle: DropdownOption[];
  filteredCitiesSingle: DropdownOption[];
  registrationNumberMask = AppConstants.TZ_AND_REGISTRATION_NUMBER_MASK;
  cellPhoneMask = AppConstants.CELL_PHONE_MASK;

  maleGenderOption = { value: 'MALE', label: 'זכר' };
  femaleGenderOption = { value: 'FEMALE', label: 'נקבה' };

  @Output() toggleCancelData = new EventEmitter();
  @Output() toggleSaveData = new EventEmitter();

  constructor(
    private utilityService: UtilityService,
    private languageService: LanguageSwitchService
  ) {
    super();
  }

  ngOnInit() {
    this.lang = this.languageService.currentLanguage;
    this.currentYear = this.currentDate.getFullYear();
    this.minYear = this.currentYear - 120;
    this.languageService.onLanguageChange
      .pipe(takeUntil(this.destroy$))
      .subscribe((lang) => {
        this.lang = lang;
        this.setCalendarLanguage();
      });
    this.setCalendarLanguage();
  }

  setCalendarLanguage() {
    const isEnglish = this.lang === 'en';
    this.calendarLanguage = {
      firstDayOfWeek: 0,
      dayNames: isEnglish
        ? AppConstants.CALENDAR.en.dayNames
        : AppConstants.CALENDAR.he.dayNames,
      dayNamesShort: isEnglish
        ? AppConstants.CALENDAR.en.dayNamesShort
        : AppConstants.CALENDAR.he.dayNamesShort,
      dayNamesMin: isEnglish
        ? AppConstants.CALENDAR.en.dayNamesMin
        : AppConstants.CALENDAR.he.dayNamesMin,
      monthNames: isEnglish
        ? AppConstants.CALENDAR.en.monthNames
        : AppConstants.CALENDAR.he.monthNames,
      monthNamesShort: isEnglish
        ? AppConstants.CALENDAR.en.monthNamesShort
        : AppConstants.CALENDAR.he.monthNamesShort,
      today: isEnglish
        ? AppConstants.CALENDAR.en.today
        : AppConstants.CALENDAR.he.today,
      clear: isEnglish
        ? AppConstants.CALENDAR.en.clear
        : AppConstants.CALENDAR.he.clear,
    };
  }

  ngAfterViewInit() {
    setTimeout(() => {
      for (const key in this.businessOwnersForm.controls) {
        if (this.businessOwnersForm.controls[key]) {
          this.businessOwnersForm.controls[key].markAsPristine();
        }
      }
    });
  }

  saveData(id: number) {
    this.toggleSaveData.emit(this.businessOwnersForm.value.id);
  }

  cancelData() {
    this.toggleCancelData.emit();
  }

  async filterCitiesSingle(event) {
    this.filteredCitiesSingle = await this.utilityService.filterCities(
      event.query
    );
  }

  async filterStreetSingle(event) {
    const city =
      this.businessOwnersForm.get('person.address.city').value.name ||
      this.businessOwnersForm.get('person.address.city').value;

    this.filteredStreetsSingle = await this.utilityService.filterStreets(
      city,
      event.query
    );
  }

  setCorrectPercent(control: AbstractControl) {
    if (control.value > 100) {
      control.setValue(100);
    } else if (control.value < 0) {
      control.setValue(0);
    }
  }
}
