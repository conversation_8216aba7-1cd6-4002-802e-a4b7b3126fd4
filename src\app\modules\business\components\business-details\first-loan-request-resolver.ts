import {
  ActivatedRouteSnapshot,
  Resolve,
  RouterStateSnapshot,
} from '@angular/router';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { LoanRequest } from '../../model/loan-request';
import { LoanRequestService } from '../../service/loan-request.service';

@Injectable()
export class FirstLoanRequestResolver implements Resolve<LoanRequest> {
  constructor(private loanRequestService: LoanRequestService) {}

  resolve(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<LoanRequest> | Promise<LoanRequest> | LoanRequest {
    return this.loanRequestService.getFirstLoanRequestBe();
  }
}
