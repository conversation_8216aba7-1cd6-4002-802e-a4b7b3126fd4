<div class="white-box-wrap">
  <div class="building-loan-wrap col-12 col-md-7 no-padding">
    <form action="#" [formGroup]="requestDetailsForm">
      <div class="heading-secondary">
        <h3>{{ 'request-details.building-a-loan' | translate }}</h3>
        <h5>{{ 'request-details.request-details' | translate }}</h5>
      </div>
      <div class="form-group">
        <input formControlName="name" type="text" class="form-control" />
      </div>
      <div class="loan-desc-list">
        <ul class="list-unstyled">
          <li>
            <span>{{ 'request-details.amount' | translate }}</span>
            <strong
              >{{
                loanApproved
                  ? loanRequest.loan.amount
                  : (loanRequest.amount | number: '1.0-2')
              }}
              ₪
            </strong>
          </li>
          <li>
            <span>{{ 'request-details.period' | translate }}</span>
            <strong>
              {{ loanApproved ? loanRequest.loan.period : loanRequest.period }}
              {{ 'request-details.months' | translate }}</strong
            >
          </li>
          <li *ngIf="loanRequest.orgCode || loanRequest.orgCode != ''">
            <span>{{ 'request-details.organization-code' | translate }}</span>
            <strong> {{ loanRequest.orgCode }}</strong>
          </li>
        </ul>
      </div>
      <div class="loan-desc-text">
        <h6>{{ 'request-details.the-purpose-of-the-loan' | translate }}</h6>
        <p>{{ loanRequest.purpose[currentLang] }}</p>
      </div>
      <div class="loan-desc-text">
        <h6>
          {{
            'request-details.the-purpose-of-the-loan-is-extended' | translate
          }}
        </h6>
        <p>{{ loanRequest.purposeFreeText }}</p>
      </div>
      <div class="btn-holder">
        <button
          type="button"
          class="btn btn-primary"
          [disabled]="!requestDetailsForm.valid && !formLocked"
          (click)="submitForm($event)"
        >
          {{ 'request-details.continue' | translate }}
        </button>
      </div>
    </form>
  </div>
</div>
