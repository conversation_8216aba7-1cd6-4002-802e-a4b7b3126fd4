import {
  ActivatedRouteSnapshot,
  Resolve,
  RouterStateSnapshot,
} from '@angular/router';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { LoanRequestService } from '../../service/loan-request.service';
import { RequiredDocumentResponse } from '../../model/required-documents-response';

@Injectable()
export class RequiredDocumentsResolver
  implements Resolve<RequiredDocumentResponse> {
  constructor(private loanRequestService: LoanRequestService) {}

  resolve(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ):
    | Observable<RequiredDocumentResponse>
    | Promise<RequiredDocumentResponse>
    | RequiredDocumentResponse {
    return this.loanRequestService.getRequiredDocuments(
      route.parent.parent.data.loanRequest.id,
      route.data.documents.category
    );
  }
}
