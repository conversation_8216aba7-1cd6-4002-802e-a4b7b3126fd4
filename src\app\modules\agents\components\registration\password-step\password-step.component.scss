@import 'styles/pages/tarya-lender-data-variables';

.turquase {
  color: $color-turquoise;
}

.btn-primary,
.btn-primary:hover,
.btn-primary:active,
.btn-primary:visited {
  margin-top: 50px;
  background-color: $color-turquoise !important;
}

:host .tarya-mor {
  background: none !important;
}

:host ::ng-deep tar-input-text > div.tar-input-text {
  width: 255px;
  margin: auto;
}

:host ::ng-deep tar-input-text > div.tar-input-text > span > label {
  left: 0 !important;
}

.rules {
  text-align: right;
  padding-right: 145px;
}

.rules-title {
  margin-top: 55px;
}

ul {
  list-style: none;
}

ul li::before {
  color: $color-turquoise;
  content: '\2022';
  font-weight: bold;
  display: inline-block;
  width: 1em;
}
:host ::ng-deep {
  h6 {
    font-size: 16;
    color: #3b3b3b;
    font-weight: normal;
  }
}
:host ::ng-deep .text-center tar-input-text {
  .form-control {
    width: 255px;
  }
}
ul li {
  font-size: 16px;
  line-height: 30px;
  color: #3b3b3b;
  font-weight: 300;
}
:host ::ng-deep tar-input-text {
  .form-control {
    border-bottom: 1px solid #979797;
  }
}
:host ::ng-deep control-validation-errors {
  .validation-errors-wrapper {
    padding: 0;
    font-size: 16px;
    line-height: 1.25;
    width: 255px;
    text-align: right;
    margin: auto;
  }
}
