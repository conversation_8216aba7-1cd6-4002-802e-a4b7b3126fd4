import { Compo<PERSON>, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import {
  UntypedFormGroup,
  UntypedFormBuilder,
  Validators,
} from '@angular/forms';
import { Subscription } from 'rxjs';
import { Router } from '@angular/router';
import { AppValidators } from '~tarya-shared/validators/validators';
import { matchingPasswords } from '~tarya-shared/validators/matching-passwords';
import { AgentsService } from '~tarya-agents/services/agents.service';
import { AgentsTemporaryService } from '~tarya-agents/services/agents.temporary.service';
import { RegistrationDetail } from '~tarya-agents/model/registrationDetails';
import { DomainService } from '../../../../../layouts/app-component/domain.service';
import { DomainDataDto } from '../../../../../layouts/app-component/domain-data';

@Component({
  selector: 'app-password-step',
  templateUrl: './password-step.component.html',
  styleUrls: ['./password-step.component.scss'],
})
export class PasswordStepComponent implements OnInit, OnDestroy {
  logInForm: UntypedFormGroup;
  newAgent: RegistrationDetail;

  subscription: Subscription;
  minPasswordLength: number;
  maxPasswordLength: number;

  constructor(
    protected formBuilder: UntypedFormBuilder,
    private router: Router,
    private agentService: AgentsService,
    private formService: AgentsTemporaryService,
    private domainService: DomainService
  ) {}

  ngOnInit() {
    this.domainService.getDomainData().subscribe((response: DomainDataDto) => {
      this.maxPasswordLength = response.maxPasswordLength;
      this.minPasswordLength = response.minPasswordLength;
      this.createForm();
    });
    this.subscription = this.formService
      .getNewAgentData()
      .subscribe((data) => (this.newAgent = data));
  }

  createForm(): void {
    this.logInForm = this.formBuilder.group(
      {
        password: [
          '',
          [
            Validators.required,
            AppValidators.getValidator('containsSpace'),
            AppValidators.getValidator('containsForbiddenChars'),
            AppValidators.getValidator('containsNoLetters'),
            AppValidators.getValidator('containsHebrewLetters'),
            AppValidators.getValidator('tooShort', this.minPasswordLength),
            AppValidators.getValidator('containConsecutiveCharacters'),
            AppValidators.getValidator('containsNoNumbersSymbols'),
            AppValidators.getValidator('containsDot'),
            AppValidators.getValidator('tooLong', this.maxPasswordLength),
          ],
        ],
        confirmPassword: ['', [Validators.required]],
      },
      { validator: matchingPasswords('password', 'confirmPassword') }
    );
  }

  onSubmit(): void {
    this.agentService
      .passwordValidation(this.logInForm.value)
      .subscribe((data: Response) => {
        if (data.status === 200) {
          this.newAgent.password = this.logInForm.value.password;
          this.agentService
            .completeRegistration(this.newAgent)
            .subscribe(() => {
              localStorage.clear();
              this.router.navigate([
                'app/agents/registration',
                'successful-registration',
              ]);
            });
        }
      });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
