<div class="white-box-wrap">
  <div class="collateral-loan-wrap col-lg-10">
    <form action="#" [formGroup]="this.collateralLoanForm">
      <div class="heading-secondary">
        <h3>בניית הלוואה</h3>
        <h5>בטחונות להלוואה</h5>
      </div>
      <div class="loan-type-section">
        <div class="loan-type-box">
          <span>סוג ההלוואה</span>
          <strong class="type">
            <span *ngIf="amortizationState === 0">רגילה שפיצר</span>
            <span *ngIf="amortizationState > 0">כמעט בלון</span>
          </strong>
        </div>
        <div class="heading-wrap">
          <strong class="heading"
            >בטחונות הניתנים לשעבוד ראשון<span
              class="bz-info-tooltip"
              (click)="openBzModal(loanTypeModal)"
              ><i class="icon-info" aria-hidden="true"></i></span
          ></strong>
        </div>
        <p>
          טריא מאפשרת לכולם להיות מלווים, גם לתקופות קצרות, ולכן עסק שלפעמים
          יושב על מזומן יכול את הכסף הזה באופן זמני לשים בטריא, ללא שום מגבלה
          ולהרוויח בזמן הזה כסף שמצמצם את הריבית שהוא משלם.
        </p>
        <div class="checkbox-list">
          <div *ngFor="let option of collateralOptions" class="checkbox-row">
            <p-checkbox
              [formControl]="collateralLoanForm.controls['collaterals']"
              name="collaterals"
              class="bz-checkbox"
              [value]="option.id"
              label="{{ option[currentLang] }}"
            >
            </p-checkbox>
            <span *ngIf="option.name === 'other'" class="check-label">
              <input
                type="text"
                class="input-code"
                formControlName="collateralFreeText"
              />
              <control-validation-errors
                [control]="collateralLoanForm.controls['collateralFreeText']"
                translateTag="error"
              >
              </control-validation-errors>
            </span>
          </div>
        </div>
      </div>
      <div class="content-row">
        <strong class="heading"
          >יש לכם משהו ששווה כבטוחה להלוואה ושלא חשבנו עליו?</strong
        >
        <p>
          אנחנו רוצים לאפשר לעסקים לקחת את ההלוואה הטובה ביותר שהם יכולים לבנות
          לעצמם. לכן, בטריא יש מגוון רחב מאוד של בטחונות וחשיבה יצירתית.
          <br />יש לכם אופציה להוסיף התחייבויות נוספות או ערבים נוספים לפי
          שיקולכם או דברו איתנו למציאת פתרונות משותפים.
        </p>
      </div>
      <div class="additional-liabilities">
        <div class="content">
          <div class="switch-hold">
            <strong class="label-switch">התחייבויות נוספות</strong>
            <p-inputSwitch
              onLabel="On"
              offLabel="Off"
              formControlName="additionalLiabilitiesSwitch"
              class="bz-switch"
              (onChange)="clearAdditionalCommitments($event)"
            >
            </p-inputSwitch>
          </div>
          <div
            class="add-block"
            *ngIf="collateralLoanForm.value.additionalLiabilitiesSwitch"
          >
            <div class="form-group">
              <div
                class="checkbox-row"
                *ngFor="let option of additionalCommitmentsOptions"
              >
                <p-checkbox
                  class="bz-checkbox"
                  [formControl]="
                    collateralLoanForm.controls['additionalCommitments']
                  "
                  [value]="option.id"
                  [label]="option[currentLang]"
                >
                </p-checkbox>
                <span *ngIf="option.name === 'other'" class="check-label">
                  <input
                    type="text"
                    class="input-code"
                    formControlName="additionalCommitmentFreeText"
                  />
                  <control-validation-errors
                    [control]="
                      collateralLoanForm.controls[
                        'additionalCommitmentFreeText'
                      ]
                    "
                    translateTag="error"
                  >
                  </control-validation-errors>
                </span>
              </div>
              <control-validation-errors
                [control]="collateralLoanForm.controls['additionalCommitments']"
                translateTag="error"
              >
              </control-validation-errors>
            </div>
          </div>
        </div>
      </div>
      <div class="add-guarantor">
        <strong class="heading">ערבים נוספים</strong>
        <div class="radio-row">
          <div class="radio-box">
            <p-radioButton
              class="bz-tarya-radio"
              name="groupname"
              [value]="false"
              label="לא"
              formControlName="hasGuarantors"
            ></p-radioButton>
          </div>
          <div class="radio-box">
            <p-radioButton
              class="bz-tarya-radio"
              name="groupname"
              [value]="true"
              label="כן"
              formControlName="hasGuarantors"
            ></p-radioButton>
          </div>
        </div>
        <div
          class="add-block add-guarantor-form"
          *ngIf="
            collateralLoanForm.value.hasGuarantors ||
            collateralLoanForm.value.hasGuarantors === undefined
          "
        >
          <div
            class="add-guarantor-form-wrap"
            [formGroup]="guarantorForm"
            [hidden]="!showGuarantorForm"
          >
            <div class="form-group-wrap">
              <input type="hidden" formControlName="id" />
              <div class="form-group">
                <div class="label-holder">
                  <label>שם פרטי</label>
                </div>
                <div class="input-holder">
                  <input
                    type="text"
                    class="form-control"
                    formControlName="firstName"
                  />
                  <control-validation-errors
                    [control]="guarantorForm.controls['firstName']"
                    translateTag="error"
                  >
                  </control-validation-errors>
                </div>
              </div>
              <div class="form-group">
                <div class="label-holder">
                  <label>שם משפחה</label>
                </div>
                <div class="input-holder">
                  <input
                    type="text"
                    class="form-control"
                    formControlName="lastName"
                  />
                  <control-validation-errors
                    [control]="guarantorForm.controls['lastName']"
                    translateTag="error"
                  >
                  </control-validation-errors>
                </div>
              </div>
              <div class="form-group">
                <div class="label-holder">
                  <label>ת.ז</label>
                </div>
                <div class="input-holder">
                  <input
                    type="text"
                    class="form-control"
                    formControlName="identity"
                  />
                  <control-validation-errors
                    [control]="guarantorForm.controls['identity']"
                    translateTag="error"
                  >
                  </control-validation-errors>
                </div>
              </div>
            </div>
            <div class="form-group-wrap">
              <div class="form-group">
                <div class="label-holder">
                  <label>מספר טלפון נייד</label>
                </div>
                <div class="input-holder">
                  <input
                    type="text"
                    [textMask]="{ mask: cellPhoneMask }"
                    class="form-control"
                    formControlName="cellPhone"
                  />
                  <control-validation-errors
                    [control]="guarantorForm.controls['cellPhone']"
                    translateTag="error"
                  >
                  </control-validation-errors>
                </div>
              </div>
              <div class="form-group">
                <div class="label-holder">
                  <label>אימייל</label>
                </div>
                <div class="input-holder">
                  <input
                    type="text"
                    class="form-control"
                    formControlName="personalEmail"
                  />
                  <control-validation-errors
                    [control]="guarantorForm.controls['personalEmail']"
                    translateTag="error"
                  >
                  </control-validation-errors>
                </div>
              </div>
            </div>
            <div class="form-group-wrap">
              <div class="form-group"></div>
              <div class="form-group">
                <div class="btn-group-wrap">
                  <div class="btn-col">
                    <button
                      type="button"
                      class="btn btn-default"
                      *ngIf="collateralLoanInfo.guarantors.size !== 0"
                      (click)="cancelEditingGuarantor()"
                    >
                      ביטול
                    </button>
                  </div>
                  <div class="btn-col">
                    <button
                      type="button"
                      class="btn btn-primary"
                      [disabled]="!guarantorForm.valid"
                      (click)="saveOrCreateGuarantor()"
                    >
                      שמור
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="detail-info-contact-wrap"
            *ngIf="collateralLoanInfo.guarantors.size !== 0"
          >
            <detail-info-card
              *ngFor="let guarantor of guarantorsAsArray"
              [lineId]="guarantor.id"
              [email]="guarantor.personalEmail"
              [formLocked]="formLocked"
              [name]="
                (guarantor.firstName || '') + ' ' + (guarantor.lastName || '')
              "
              (toggleEditData)="toggleUpdateGuarantor($event)"
              (toggleDeleteData)="deleteGuarantor($event)"
            >
            </detail-info-card>
            <div class="add-btn-wrap">
              <span
                class="add-btn"
                [ngClass]="{ disabled: formLocked }"
                (click)="toggleAddGuarantor()"
              ></span>
            </div>
          </div>
        </div>
      </div>
      <div class="btn-holder">
        <button
          type="button"
          class="btn btn-primary"
          [disabled]="!collateralLoanForm.valid && !formLocked"
          (click)="updateCollaterals($event)"
        >
          המשך
        </button>
      </div>
    </form>
  </div>
</div>

<ng-template #loanTypeModal>
  <div class="loan-type-modal">
    <div class="modal-body">
      <span class="close-btn" (click)="modalRef.hide()"></span>
      <div class="col-lg-10">
        <div class="loan-type-row">
          <h6>מניות העסק</h6>
          <p>
            שיעבוד על מניות החברה. רלוונטי לחברה בע"מ שניתן לרשום שיעבוד על
            מניותיה ברשם המשכונות או ברשם החברות (כאשר הבעלים הוא חברה בעצמו).
          </p>
        </div>
        <div class="loan-type-row">
          <h6>רכב</h6>
          <p>
            שיעבוד מדרגה ראשונה על רכבים בני פחות מחמש שנים ובעלי שווי הגבוה
            מ-50,000 לפי מחירון לוי יצחק.
          </p>
        </div>
        <div class="loan-type-row">
          <h6>נדל"ן</h6>
          <p>שיעבוד מדרגה ראשונה או שנייה על סוגי מקרקעין שונים.</p>
        </div>
        <div class="loan-type-row">
          <h6>הסבת המחאת זכות/חוזים</h6>
          <p>
            חוזים חתומים לביצוע או צ'קים שניתנו בגין שירות או מוצר. בסכומים
            שונים ולתקופות שונות ובתנאי שאין מגבלה על שיעבודם.
          </p>
        </div>
        <div class="loan-type-row">
          <h6>ציוד/ מכונות</h6>
          <p>
            ציוד ומכונות בשווי נוכחי העולה על 80,000 ש"ח לכל פריט הניתנים למשכון
            ושיעבוד.
          </p>
        </div>
        <div class="loan-type-row">
          <h6>פיקדון בטריא</h6>
          <p>
            במקום להשאיר כספים בפיקדון בבנק כנגד הלוואות שונות ולקבל עליהם 0%
            תשואה שנתית טריא מאפשרת לכם ללוות כספים כנגד פיקדון בטריא שמרוויח
            בממוצע יותר מ-5% תשואה שנתית!
          </p>
        </div>
        <div class="loan-type-row">
          <h6>מלאי</h6>
          <p>מלאי העסק השוטף.</p>
        </div>
        <div class="loan-type-row">
          <h6>צ'קים הניתנים להסבה</h6>
          <p>
            הפקדת צ'קים למשמורת בלבד כבטוחה וקבלתם חזרה לטובת הפקדה על ידי העסק
            העצמו. על הצ'קים להיות מסוג "לפקודת" ללא למוטב בלבד או "שלמו ל" ללא
            שם.
          </p>
        </div>
        <div class="loan-type-row"></div>
      </div>
    </div>
  </div>
</ng-template>
