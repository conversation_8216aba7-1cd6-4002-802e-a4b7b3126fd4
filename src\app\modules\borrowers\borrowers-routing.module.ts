import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { BorrowersCreditCardErrorPageComponent } from './components/borrowers-credit-card-error-page/borrowers-credit-card-error-page.component';
import { CreditCardSuccessPageComponent } from './components/borrowers-credit-card-success-page/credit-card-success-page.component';
import { BorrowersCreditCardUpdateComponent } from './components/borrowers-credit-card-update/borrowers-credit-card-update.component';
import { BorrowersDocumentsComponent } from './components/borrowers-documents/borrowers-documents.component';
import { BorrowersLayoutComponent } from './components/borrowers-layout/borrowers-layout.component';
import { BorrowersLoanDetailsComponent } from './components/borrowers-loan-details/borrowers-loan-detials.component';
import { BorrowersLoansComponent } from './components/borrowers-loans/borrowers-loans.component';
import { PaymentMethodsComponent } from './components/borrowers-payment-methods/payment-methods.component';
import { UnderConstructionComponent } from './components/under-construction/under-construction.component';
import { BorrowersCreditCardUpdateGuard } from './guards/borrowers-credit-card-update.guard';
import { BorrowersLoanDetailsGuard } from './guards/borrowers-loan-details.guard';
import { CanActivateBorrowersPageGuard } from './guards/can-activate-borrowers-page.guard';
import { BorrowersPaymentMethodsGuard } from './guards/borrowers-payment-methods.guard';
import { ExpiredCreditCardsResolver } from './services/borrowers-expired-credit-cards.resolver';
import { BorrowersLoanResolver } from './services/borrowers-loan.resolver';
import { BorrowersLoansResolver } from './services/borrowers-loans.resolver';
import { OrganizationWithLoansResolver } from './services/organization-with-loans.resolver';
import { PaymentMethodsResolver } from './services/payment-methods.resolver';
import { DebtPaymentMethodComponent } from './components/debt-payment-method/debt-payment-method.component';
import { DebtPaymentComponent } from './components/debt-payment/debt-payment.component';
import { DebtPaymentAllLoansComponent } from './components/debt-payment-all-loans/debt-payment-all-loans.component';
import { DebtPaymentAddCreditCardComponent } from './components/debt-payment-add-credit-card/debt-payment-add-credit-card.component';
import { DebtPaymentSuccessfulPaymentComponent } from './components/debt-payment-successful-payment/debt-payment-successful-payment.component';
import { DebtPaymentGuard } from './guards/debt-payment.guard';
import { DebtPaymentSuccessComponent } from './components/debt-payment-success/debt-payment-success.component';
import { DebtPaymentErrorComponent } from './components/debt-payment-error/debt-payment-error.component';

const routes: Routes = [
  {
    path: '',
    component: BorrowersLayoutComponent,
    resolve: {
      loansList: BorrowersLoansResolver,
    },
    children: [
      {
        path: '',
        component: BorrowersLoansComponent,
        canActivate: [CanActivateBorrowersPageGuard],
        resolve: {
          expiredCreditCards: ExpiredCreditCardsResolver,
          loansList: BorrowersLoansResolver,
        },
      },
      {
        path: 'loans/:id',
        runGuardsAndResolvers: 'always',
        canActivate: [BorrowersLoanDetailsGuard],
        component: BorrowersLoanDetailsComponent,
        resolve: {
          organization: OrganizationWithLoansResolver,
          loansList: BorrowersLoansResolver,
          loan: BorrowersLoanResolver,
        },
      },
      {
        path: 'credit-card-update/:lastDigits/:id',
        component: BorrowersCreditCardUpdateComponent,
        canActivate: [BorrowersCreditCardUpdateGuard],
        resolve: {
          paymentMethods: PaymentMethodsResolver,
        },
      },
      {
        path: 'payment-methods',
        canActivate: [BorrowersPaymentMethodsGuard],
        component: PaymentMethodsComponent,
      },
      {
        path: 'documents',
        component: BorrowersDocumentsComponent,
        resolve: {
          loansList: BorrowersLoansResolver,
        },
      },
      {
        path: 'debt-payment',
        component: DebtPaymentComponent,
        canActivate: [DebtPaymentGuard],
        resolve: {
          paymentMethods: PaymentMethodsResolver,
          loansList: BorrowersLoansResolver,
        },
        children: [
          {
            path: '',
            component: DebtPaymentAllLoansComponent,
            canActivate: [DebtPaymentGuard],
            resolve: {
              loansList: BorrowersLoansResolver,
            },
          },
          {
            path: 'add-credit-card',
            component: DebtPaymentAddCreditCardComponent,
            canActivate: [DebtPaymentGuard],
            resolve: {
              loansInfo: BorrowersLoansResolver,
            },
          },
          {
            path: 'payment-method',
            component: DebtPaymentMethodComponent,
            canActivate: [DebtPaymentGuard],
            resolve: {
              paymentMethods: PaymentMethodsResolver,
              loansInfo: BorrowersLoansResolver,
            },
          },
          {
            path: 'successful-payment',
            component: DebtPaymentSuccessfulPaymentComponent,
            resolve: {
              paymentMethods: PaymentMethodsResolver,
            },
          },
        ],
      },
    ],
  },
  {
    path: 'under-construction',
    component: UnderConstructionComponent,
  },
  {
    path: 'payment-success',
    component: CreditCardSuccessPageComponent,
  },
  {
    path: 'payment-error',
    component: BorrowersCreditCardErrorPageComponent,
  },
  {
    path: 'debt-payment-success',
    component: DebtPaymentSuccessComponent,
  },
  {
    path: 'debt-payment-error',
    component: DebtPaymentErrorComponent,
  },
  {
    path: '**',
    component: BorrowersLayoutComponent,
    resolve: {
      loansList: BorrowersLoansResolver,
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BorrowersRoutingModule {}
