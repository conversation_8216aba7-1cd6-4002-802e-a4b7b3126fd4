import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  CanActivate,
  Router,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router';
import { map, Observable, switchMap } from 'rxjs';
import { AvailablePages } from '../models/available-pages';
import { BorrowersService } from '../services/borrowers.service';

@Injectable({
  providedIn: 'root',
})
export class DebtPaymentGuard implements CanActivate {
  private lateLoanStatuses = [90, 91, 92, 93, 94, 95, 6];
  constructor(
    private router: Router,
    private borrowersService: BorrowersService
  ) {}
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ):
    | Observable<boolean | UrlTree>
    | Promise<boolean | UrlTree>
    | boolean
    | UrlTree {
    return this.borrowersService.getAvailablePages().pipe(
      switchMap((availablePages: AvailablePages) => {
        return this.borrowersService.getActiveLoansInfo().pipe(
          map(({ loans }) => {
            const redirectPath = this.router.createUrlTree([
              'app',
              'borrowers',
            ]);
            if (availablePages.enableCDDebtRepaymentPage) {
              const lateLoanNoPendingPayment = loans.some(
                ({ debtPaymentInfo, statusCode }) =>
                  this.lateLoanStatuses.includes(parseInt(statusCode)) &&
                  !debtPaymentInfo?.isPaymentPending
              );
              return lateLoanNoPendingPayment ? true : redirectPath;
            }
            return redirectPath;
          })
        );
      })
    );
  }
}
