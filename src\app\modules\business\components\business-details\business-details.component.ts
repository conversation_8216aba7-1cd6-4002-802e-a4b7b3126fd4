import { Component, OnInit } from '@angular/core';
import { Business } from '../../model/business';
import { ActivatedRoute, Router } from '@angular/router';
import { RegistrationService } from '../../service/registration.service';
import { ApiErrorsResponse } from '../../../shared/models/api-errors-response';
import * as HttpStatus from 'http-status-codes';
import { CreateBusinessResponse } from '../../model/create-business-response.class';
import { LoanRequest } from '../../model/loan-request';
import { LOAN_PAGES, LOAN_ROUTE } from '../../layouts/loan/loan-pages';
import { BusinessService } from '../../service/business.service';
import { TypeEnum } from '../../model/type-enum';
import { Address } from '../../model/address';
import { AppUtils } from '../../../shared/utils/app-utils';
import { LanguageService } from '../../../language/language.service';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-business-details',
  templateUrl: './business-details.component.html',
  styleUrls: ['./business-details.component.scss'],
})
export class BusinessDetailsComponent implements OnInit {
  uploadBusinessLogoResource: string;

  registrationMode: boolean;
  business = {} as Business;
  businessTypes: TypeEnum[];
  businessFields: TypeEnum[];
  currentLang: string;
  uploadedImages: string[] = [];
  apiErrorResponse: ApiErrorsResponse;

  private loanRequest: LoanRequest;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private registrationService: RegistrationService,
    private businessService: BusinessService,
    private languageService: LanguageService
  ) {}

  ngOnInit() {
    this.currentLang = this.languageService.currentLang;

    this.registrationMode = this.route.snapshot.data.registrationMode;
    this.loanRequest = this.route.snapshot.data.loanRequest;
    if (this.registrationMode) {
      this.business.type = {} as TypeEnum;
      this.business.address = {} as Address;
      this.business.businessSic = {} as TypeEnum;
      this.business.businessField = {} as TypeEnum;
      this.business.account = this.loanRequest.account;
    } else this.business = this.loanRequest.business;

    this.businessTypes = this.route.snapshot.data.businessCategories;
    this.businessFields = this.route.snapshot.data.businessFields;
    this.uploadBusinessLogoResource = this.businessService.getUploadBusinessLogoResource(
      this.business.id
    );
    if (this.business.logoExists) {
      setTimeout(
        () =>
          (this.uploadedImages = [
            this.businessService.getBusinessLogoResource(this.business.id) +
              '?' +
              AppUtils.makeid(),
          ]),
        1000
      );
    }
  }

  saveBusiness(business: Business) {
    let success: (res?: CreateBusinessResponse) => void;
    if (this.registrationMode) {
      success = (res: CreateBusinessResponse) => {
        this.registrationService
          .finalizeBusinessCreation(this.loanRequest.id, res)
          .subscribe(() => {
            this.router.navigate([
              '/' + LOAN_ROUTE + '/' + this.loanRequest.id,
            ]);
          });
      };
    } else {
      success = () =>
        this.router.navigate([LOAN_PAGES[8]], {
          relativeTo: this.route.parent.parent,
        });
    }

    const fail = (res: HttpErrorResponse) => {
      if (res.status === HttpStatus.UNPROCESSABLE_ENTITY) {
        this.apiErrorResponse = JSON.parse(res.error);
      }
    };

    if (this.registrationMode) {
      this.registrationService
        .createBusiness(business)
        .subscribe(success, fail);
    } else {
      this.businessService.saveBusiness(business).subscribe(success, fail);
    }
  }
}
