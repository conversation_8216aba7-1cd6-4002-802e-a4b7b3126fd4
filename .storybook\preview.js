import { HttpClient } from '@angular/common/http';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { setCompodocJson } from '@storybook/addon-docs/angular';
import { forceReRender } from '@storybook/angular';
import docJson from '../documentation.json';
setCompodocJson(docJson);

export const parameters = {
  actions: { argTypesRegex: '^on[A-Z].*' },
};

export const globalTypes = {
  locale: {
    name: 'Locale',
    description: 'Internationalization locale',
    defaultValue: 'en',
    toolbar: {
      icon: 'globe',
      items: [
        { value: 'en', right: 'en', title: 'English' },
        { value: 'he', right: 'he', title: 'Hebrew' },
      ],
    },
  },
};

function HttpLoaderFactory(http) {
  return new TranslateHttpLoader(http, './i18n/lender-data/', '.json');
}
const withLocaleProvider = (storyFunc, { globals: { locale } }) => {
  const story = storyFunc();

  return story;
};

export const decorators = [withLocaleProvider];
