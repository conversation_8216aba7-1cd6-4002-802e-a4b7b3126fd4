#!/bin/sh

LC_ALL=C

local_branch="$(git rev-parse --abbrev-ref HEAD)"

# (master|staging|dev) are added because of automatic merges
valid_branch_regex="^(feature|bugfix|hotfix|release)\/[a-zA-Z0-9._-]+$|(master|staging|dev|qa2)"

message="Branch names in this project should include one of these prefixes - feature, bugfix, hotfix or release. Your commit will be rejected. You should rename your branch to a valid name and try again."

if [[ ! $local_branch =~ $valid_branch_regex ]]
then
    echo "$message"
    exit 1
fi

exit 0