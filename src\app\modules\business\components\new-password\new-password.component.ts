import { Component, OnInit } from '@angular/core';
import { DomainDataDto } from '../../../../layouts/app-component/domain-data';
import { DomainService } from '../../../../layouts/app-component/domain.service';

@Component({
  selector: 'app-new-password',
  templateUrl: './new-password.component.html',
  styleUrls: ['./new-password.component.scss'],
})
export class NewPasswordComponent implements OnInit {
  maxPasswordLength: number;
  minPasswordLength: number;

  constructor(private domainService: DomainService) {}

  ngOnInit() {
    this.domainService.getDomainData().subscribe((response: DomainDataDto) => {
      this.maxPasswordLength = response.maxPasswordLength;
      this.minPasswordLength = response.minPasswordLength;
    });
  }
}
