<ng-container *ngIf="availablePages$ | async as availablePages">
  <tar-header
    [isFluid]="false"
    [showNotifications]="availablePages.enableCDMainPage"
    [isCustomerBorrower]="true"
  >
  </tar-header>
</ng-container>
<div class="under-construction py-5 my-2 text-center">
  <div class="under-construction-image bg-white">
    <img
      src="assets/images/borrowers/under-construction.svg"
      alt="under-construction-sign"
      width="600"
    />
  </div>
</div>
