import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FileUploadInfo } from '../general-files-info.class';
import { CookieService } from 'ngx-cookie';
import { FileUploadStatus } from './file-upload-status';

@Component({
  selector: 'document-uploader',
  templateUrl: './document-uploader.component.html',
  styleUrls: ['./document-uploader.component.scss'],
})
export class DocumentUploaderComponent {
  @Input('uploadFileInfo')
  fileUploadInfo: FileUploadInfo;

  @Output() toggleUploadData = new EventEmitter();

  selectedValue: any;

  maxSize = 5000000;
  btnLabel = ' ';

  constructor(private cookieService: CookieService) {}

  onUpload() {
    this.fileUploadInfo.fileStatus =
      FileUploadStatus.FILE_SUCCESSFULLY_UPLOADED;
    this.fileUploadInfo.requiredDocumentStatus = true;
    this.toggleUploadData.emit();
  }

  onError() {
    this.fileUploadInfo.fileStatus = FileUploadStatus.FILE_UPLOAD_ERROR;
  }

  onBeforeUpload(event: { xhr: XMLHttpRequest; formData: FormData }) {
    const xsrf = this.cookieService.get('XSRF-TOKEN');
    event.formData.append('id', this.fileUploadInfo.id.toString());
    event.formData.append(
      'typeEnumModelId',
      this.fileUploadInfo.typeEnumModelId.toString()
    );
    event.formData.append('_csrf', xsrf);
  }
}
