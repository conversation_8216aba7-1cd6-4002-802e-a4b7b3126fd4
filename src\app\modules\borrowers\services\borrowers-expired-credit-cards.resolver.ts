import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { Observable } from 'rxjs';
import { ExpiredCreditCardsServiceResponse } from '../models/expire-credit-cards-service-response';
import { BorrowersCreditCardService } from './borrowers-credit-card.service';

@Injectable({
  providedIn: 'root',
})
export class ExpiredCreditCardsResolver
  implements Resolve<ExpiredCreditCardsServiceResponse> {
  constructor(private borrowersCreditCardService: BorrowersCreditCardService) {}

  resolve(): Observable<ExpiredCreditCardsServiceResponse> {
    return this.borrowersCreditCardService.getExpiredCreditCards();
  }
}
