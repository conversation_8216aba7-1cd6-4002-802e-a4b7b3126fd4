@import 'styles/vendors/bootstrap/share';
@import 'styles/variables';

.input-code {
  border: none;
  // outline: none;
  width: 127px;
  border-bottom: 1px solid #ccc;
  display: inline-block;
  vertical-align: top;
  padding: 3px 10px;
  @include media-breakpoint-up(md) {
    width: 167px;
  }
}

:host ::ng-deep .currency {
  position: absolute;
  right: 30px;
  padding: 5px 15px;
  top: 50%;
  z-index: 3;
  transform: translateY(-50%);

  .rtl & {
    right: auto;
    left: 30px;
  }
}

:host ::ng-deep .bz-spinner {
  .p-spinner-input {
    border: 1px solid #b2b2b2;
    border-radius: 2px;
    padding: 5px 50px;
    height: 40px;
    font-size: 14px;
    line-height: 28px;
  }

  .p-inputtext {
    color: #495057;
  }

  .p-spinner-button {
    width: 35px;
    border: 1px solid #b2b2b2;
    border-radius: 2px;
  }
}

:host ::ng-deep .period-control {
  .month-label {
    pointer-events: none;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    line-height: 1;
    right: 70px;
    z-index: 2;

    .rtl & {
      right: auto;
      left: 70px;
    }
  }

  .p-spinner-input {
    padding-right: 115px;

    .rtl & {
      padding-right: 55px;
      padding-left: 115px;
    }
  }
}

:host ::ng-deep .heading-wrap {
  .heading {
    display: inline-block;
    vertical-align: top;
    padding: 0 30px 0 0;
    position: relative;

    .rtl & {
      padding: 0 0 0 30px;
    }
  }
}

:host ::ng-deep .bz-info-tooltip {
  position: absolute;
  right: 0;
  top: 3px;

  .rtl & {
    right: auto;
    left: 0;
  }
}
