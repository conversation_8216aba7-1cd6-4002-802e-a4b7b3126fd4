import { Component, OnInit, TemplateRef } from '@angular/core';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { LanguageService } from '../../../language/language.service';
import { AppUtils } from '../../../shared/utils/app-utils';
import { LOAN_PAGES } from '../../layouts/loan/loan-pages';
import { LoanRepayment } from '../../model/loan-repayment';
import { LoanRequest } from '../../model/loan-request';
import { SimulatorResponse } from '../../model/simulator-response';
import { LoanRequestService } from '../../service/loan-request.service';
import { RepaymentState } from './repayment-state';

export class RepaymentBox {
  label: string;
  state: RepaymentState;
  amount: number;
  repaymentDate?: any;
  interestPayment?: any;
  principalPayment?: any;
}

export class RepaymentsPerYear {
  year: any;
  boxes: RepaymentBox[];
}

export enum LoanRepaymentStatus {
  NOT_FUNDED = 0, // "Not Funded", "הלוואה לא ממומנת"),
  OPEN = 1, // "Open", "לא נגבה תשלום"),
  PAID_REGULAR = 2, // "Paid by Borrower", "שולם"),
  PAID_EARLY_REPAYMENT = 3, // "Paid early", "תשלום ראשון בפרעון המוקדם"),
  PAID_EARLY_REPAYMENT_WHOLE_LOAN = 4, // "Paid early with the loan", "פרעון מוקדם"),
  PAID_SPECIAL_LOAN_STATUS = 5, // "Paid special", "תשלום מיוחד"),
  PAID_BY_ORG_DEPOSIT = 6, // "Paid by Org Deposit", "שולם על ידי כרית בטחון");
}

export const PAID_STATUSES = [
  LoanRepaymentStatus.PAID_REGULAR,
  LoanRepaymentStatus.PAID_EARLY_REPAYMENT,
  LoanRepaymentStatus.PAID_EARLY_REPAYMENT_WHOLE_LOAN,
  LoanRepaymentStatus.PAID_SPECIAL_LOAN_STATUS,
];

@Component({
  selector: 'discharge-table',
  templateUrl: './loan-repayments.component.html',
  styleUrls: ['./loan-repayments.component.scss'],
})
export class LoanRepaymentsComponent implements OnInit {
  repaymentsData: RepaymentsPerYear[];

  loanRepaymentsForm: UntypedFormGroup;

  loanRequest: LoanRequest;

  modalRef: BsModalRef;

  loanApproved: boolean;
  formLocked: boolean;

  constructor(
    private modalService: BsModalService,
    private fb: UntypedFormBuilder,
    private loanRequestService: LoanRequestService,
    private router: Router,
    private route: ActivatedRoute,
    private languageService: LanguageService
  ) {}

  ngOnInit() {
    this.loanRequest = this.loanRequestService.getCurrentLoanRequest();
    this.loanApproved = !!this.loanRequest.loan;
    this.formLocked = this.loanRequestService.getFormLockedStatus(
      this.loanRequest
    );

    this.createForm();
  }

  openBzModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(
      template,
      Object.assign({}, { class: 'bz-modal' })
    );
  }

  createForm() {
    const amortizationPeriod = this.loanApproved
      ? this.loanRequest.loan.amortization
      : this.loanRequest.amortizationPeriod;
    const loanPeriod = this.loanApproved
      ? this.loanRequest.loan.period
      : this.loanRequest.period;
    this.loanRepaymentsForm = this.fb.group({
      balloonLoan: [
        amortizationPeriod !== undefined && amortizationPeriod !== 0,
      ],
      loanPeriod: [loanPeriod, [Validators.required]],
      amortizationPeriod: [amortizationPeriod],
    });

    if (this.formLocked) this.loanRepaymentsForm.disable();

    if (this.loanApproved) {
      const simulatorResponse = {} as SimulatorResponse;
      simulatorResponse.repayments = this.loanRequest.loan.loanRepayments;
      simulatorResponse.amortization = this.loanRequest.loan.amortization;
      this.populatePayments(simulatorResponse);
    } else if (this.loanRequest.period) {
      this.simulateRepayments();
    }

    this.loanRepaymentsForm
      .get('balloonLoan')
      .valueChanges.subscribe((value: boolean) => {
        if (value) {
          this.loanRepaymentsForm
            .get('amortizationPeriod')
            .setValidators(Validators.required);
        } else {
          this.loanRepaymentsForm.get('amortizationPeriod').clearValidators();
          this.loanRepaymentsForm.get('amortizationPeriod').reset();
        }
      });
  }

  simulateRepayments() {
    if (this.loanRepaymentsForm.value.balloonLoan) {
      this.loanRequestService
        .getSimulatedLoanRepayments(
          this.loanRequestService.getCurrentLoanRequest().id,
          this.loanRepaymentsForm.value.amortizationPeriod,
          this.loanRepaymentsForm.value.loanPeriod
        )
        .subscribe((res: SimulatorResponse) => {
          this.populatePayments(res);
        });
    } else {
      this.loanRequestService
        .getSimulatedLoanRepayments(
          this.loanRequestService.getCurrentLoanRequest().id,
          this.loanRepaymentsForm.value.loanPeriod,
          0
        )
        .subscribe((res: SimulatorResponse) => {
          this.populatePayments(res);
        });
    }
  }

  private populatePayments(res: SimulatorResponse) {
    this.repaymentsData = [];
    let repaymentsPerYear = new RepaymentsPerYear();
    repaymentsPerYear.boxes = [];

    for (const repayment of res.repayments) {
      repayment.repaymentDateAsDate = new Date(repayment.repaymentDate);
      repaymentsPerYear.year = repayment.repaymentDateAsDate
        .getFullYear()
        .toString();

      const repaymentBox = new RepaymentBox();
      repaymentBox.amount =
        repayment.principalPayment + repayment.interestPayment;
      repaymentBox.state = this.identifyRepaymentState(
        repayment,
        res.amortization
      );
      repaymentBox.label = (
        repayment.repaymentDateAsDate.getMonth() + 1
      ).toString();
      repaymentBox.interestPayment = repayment.interestPayment;
      repaymentBox.principalPayment = repayment.principalPayment;
      repaymentBox.repaymentDate = AppUtils.DateTo_DDMMYYYY(
        repayment.repaymentDateAsDate
      );

      repaymentsPerYear.boxes.push(repaymentBox);

      if (repayment.repaymentDateAsDate.getMonth() === 11) {
        this.repaymentsData.push(repaymentsPerYear);
        repaymentsPerYear = new RepaymentsPerYear();
        repaymentsPerYear.boxes = [];
      }
    }
    if (
      repaymentsPerYear.boxes.length > 0 &&
      repaymentsPerYear.boxes.length < 12
    ) {
      this.repaymentsData.push(repaymentsPerYear);
    }
  }

  submit() {
    if (this.formLocked) {
      this.router.navigate([LOAN_PAGES[3]], {
        relativeTo: this.route.parent.parent,
      });
      return;
    }

    this.loanRequest.period = this.loanRepaymentsForm.value.loanPeriod;
    this.loanRequest.amortizationPeriod =
      this.loanRepaymentsForm.value.amortizationPeriod;
    const loanRequestToSave = new LoanRequest();
    loanRequestToSave.id = this.loanRequest.id;
    loanRequestToSave.period = this.loanRequest.period;
    loanRequestToSave.amortizationPeriod = this.loanRequest.amortizationPeriod
      ? this.loanRequest.amortizationPeriod
      : 0;
    this.loanRequestService.updateLoanRequest(loanRequestToSave).subscribe(() =>
      this.router.navigate([LOAN_PAGES[3]], {
        relativeTo: this.route.parent.parent,
      })
    );
  }

  private identifyRepaymentState(
    repayment: LoanRepayment,
    amortization: number
  ): RepaymentState {
    if (repayment.repaymentStatus === undefined) {
      if (repayment.paymentNumber === amortization) return RepaymentState.LAST;
      return RepaymentState.FUTURE;
    } else {
      if (PAID_STATUSES.includes(repayment.repaymentStatus))
        return RepaymentState.COMPLETED;
      if (
        (repayment.repaymentStatus === LoanRepaymentStatus.OPEN ||
          repayment.repaymentStatus ===
            LoanRepaymentStatus.PAID_BY_ORG_DEPOSIT) &&
        repayment.repaymentDateAsDate < new Date()
      )
        return RepaymentState.OVERDUE;
      if (repayment.paymentNumber === amortization) return RepaymentState.LAST;
      return RepaymentState.FUTURE;
    }
  }
}
