import { Component, Input, OnInit } from '@angular/core';
import { FormItem } from '../financial-information.component';
import { FinancialInformationItem } from '../financial-information-constants';
import { NumberFormatPipe } from '../../../../shared/pipes/number-formating.pipe';
import { AbstractControl } from '@angular/forms';

@Component({
  selector: 'app-financial-information-form',
  templateUrl: './financial-information-form.component.html',
  styleUrls: ['./financial-information-form.component.scss'],
})
export class FinancialInformationFormComponent implements OnInit {
  @Input('form')
  form: FormItem;

  numberOfNonNullFields: number;
  numberOfFields: number;

  constructor(private currencyPipe: NumberFormatPipe) {}

  ngOnInit() {
    this.numberOfFields = this.getNumberOfFields(this.form.formTemplate);
    this.updateNumberOfFilledFormFields(this.form.formGroup.value);
    this.form.formGroup.valueChanges.subscribe((value) => {
      this.updateNumberOfFilledFormFields(value);
    });

    for (const control of this.form.formTemplate) {
      if (!control.hidden) {
        this.transformToNumbers(this.form.formGroup.controls[control.label]);
      }
    }
  }

  updateNumberOfFilledFormFields(value) {
    this.numberOfNonNullFields = this.getNumberOfNonNullFields(value);
  }

  getNumberOfNonNullFields(object): number {
    return Object.keys(object)
      .filter(
        (key) =>
          !this.form.formTemplate.find((value) => value.label === key).hidden
      )
      .map((k) => object[k])
      .filter((value) => value !== null).length;
  }

  getNumberOfFields(array: FinancialInformationItem[]): number {
    return array.filter((v) => !v.hidden).length;
  }

  transformToNumbers(control: AbstractControl) {
    const formatted = this.currencyPipe.transform(control.value, '1.2-2');
    control.setValue(formatted);
  }

  onlyNumbers(control: AbstractControl) {
    if (control.value.match(/^-?\d*\.?\d{0,6}$/) === null) {
      control.setValue(control.value.slice(0, -1));
    } else if (!control.value) {
      control.setValue(0);
    }
  }
}
