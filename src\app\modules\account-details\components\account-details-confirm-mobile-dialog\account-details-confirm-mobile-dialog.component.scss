@import 'styles/functions/rem-calc';
@import 'styles/mixins/rtl';

$tar-blob-svg: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='185.303' height='184.648' viewBox='0 0 185.303 184.648'%3E%3Cdefs%3E%3Cstyle%3E.a%7Bfill:%23ddf2f7;%7D%3C/style%3E%3C/defs%3E%3Cpath class='a' d='M97.789,169.455a56.72,56.72,0,0,0,16.628-44.692h0c-2.92-38.527,13.3-48.829,37.96-50.694h0a36.4,36.4,0,1,0-39.1-39.339h0c-2.028,24.576-12.41,40.8-50.937,37.635h0a56.935,56.935,0,1,0-9.49,113.474,57.618,57.618,0,0,0,44.935-16.384' transform='translate(-0.661 -1.368)'/%3E%3C/svg%3E";

:host ::ng-deep {
  .tar-dialog .p-dialog {
    position: fixed !important;
    max-width: rem-calc(600px);
    width: 100% !important;
  }
}
.resend-link {
  background: none;
  border: none;
  outline: none;
  color: #1da6c5;
}
.blob-container {
  position: relative;
  display: flex;
  align-items: center;
  min-height: rem-calc(200px);

  &:before {
    position: absolute;
    width: rem-calc(186px);
    height: rem-calc(185px);
    content: '';
    background-image: url($tar-blob-svg);
    background-repeat: no-repeat;
    background-position: 0 center;
    transform: rotateY(0);
    @include rtl() {
      transform: rotateY(-180deg);
    }
  }

  .bot-image,
  .mobile-description {
    position: relative;
    z-index: 2;
  }

  .bot-image {
    position: absolute;
    top: rem-calc(-5px);
    right: rem-calc(20px);
    @include rtl() {
      right: auto;
      left: rem-calc(20px);
    }
    transform: rotateY(0);
    @include rtl() {
      transform: rotateY(-180deg);
    }
  }

  .mobile-description {
    padding-inline-start: rem-calc(20px);
    color: #1da6c5;
    font-size: rem-calc(15px);
    line-height: rem-calc(22px);

    strong {
      display: block;
      line-height: rem-calc(22px);
    }
  }
}
