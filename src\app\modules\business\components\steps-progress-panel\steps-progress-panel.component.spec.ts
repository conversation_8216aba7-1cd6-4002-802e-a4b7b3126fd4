import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { StepsProgressPanelComponent } from './steps-progress-panel.component';

describe('StepsProgressPanelComponent', () => {
  let component: StepsProgressPanelComponent;
  let fixture: ComponentFixture<StepsProgressPanelComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [StepsProgressPanelComponent],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(StepsProgressPanelComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });
});
