import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Loan } from '../models/loan';
import { ChargeCreditCardBody } from '../models/charge-credit-card-body';

@Injectable({
  providedIn: 'root',
})
export class DebtPaymentService {
  private debtAmountToPay$: BehaviorSubject<number> = new BehaviorSubject(null);
  private selectedDebtLoans$: BehaviorSubject<Loan[]> = new BehaviorSubject(
    null
  );
  private allDebtLoans$: BehaviorSubject<Loan[]> = new BehaviorSubject(null);
  private currentStep$: BehaviorSubject<number> = new BehaviorSubject(null);
  private chargeCreditCardApi =
    '/rest/api/borrowers/dashboard/credit-card/self-service/charge';

  constructor(private http: HttpClient) {}

  setSelectedDebtLoans(selectedLoans: Loan[]): void {
    this.selectedDebtLoans$.next(selectedLoans);
    sessionStorage.setItem('selectedDebtLoans', JSON.stringify(selectedLoans));
  }

  getSelectedDebtLoans(): Observable<Loan[]> {
    return this.selectedDebtLoans$ as Observable<Loan[]>;
  }

  setDebtAmountToPay(amount: number): void {
    this.debtAmountToPay$.next(amount);
  }

  getDebtAmountToPay(): Observable<number> {
    return this.debtAmountToPay$ as Observable<number>;
  }

  setAllDebtLoans(loans: Loan[]): void {
    this.allDebtLoans$.next(loans);
  }

  getAllDebtLoans(): Observable<Loan[]> {
    return this.allDebtLoans$ as Observable<Loan[]>;
  }

  setCurrentStep(step: number): void {
    this.currentStep$.next(step);
    sessionStorage.setItem('currentStep', step?.toLocaleString());
  }

  getCurrentStep(): Observable<number> {
    return this.currentStep$ as Observable<number>;
  }

  chargeCreditCard(data: ChargeCreditCardBody): Observable<any> {
    return this.http.post(this.chargeCreditCardApi, data);
  }
}
