import { Component, OnInit } from '@angular/core';
import { LOAN_PAGES } from '../../layouts/loan/loan-pages';
import { LoanRequestService } from '../../service/loan-request.service';
import { LoanRequest } from '../../model/loan-request';
import { DocumentsProgress } from '../../model/documents-progress';
import { ActivatedRoute } from '@angular/router';
import { LoanProgress } from '../../layouts/loan/loan-progress.class';
import { Subscription } from 'rxjs';
import { DocumentsService } from '../../service/documents.service';

export class LoanDashboardBoxData {
  routeLink?: any;
  boxTitle: string;
  boxText?: string;
  boxImage?: any;
  completed?: boolean;
  progress?: DocumentsProgress;
}

export class LoansDashboardData {
  title: string;
  titleIcon: string;
  boxes: LoanDashboardBoxData[];
}

@Component({
  selector: 'app-loan-dashboard',
  templateUrl: './loan-dashboard.component.html',
  styleUrls: ['./loan-dashboard.component.scss'],
})
export class LoanDashboardComponent implements OnInit {
  loansDashboardData: LoansDashboardData[];
  loanRequest: LoanRequest;

  pages = LOAN_PAGES;

  loanApproved: boolean;

  loanProgress: LoanProgress;
  loanRequestSubscription: Subscription;
  showSidebar: boolean;
  finished: boolean;

  constructor(
    private loanRequestService: LoanRequestService,
    private route: ActivatedRoute,
    private documentsService: DocumentsService
  ) {}

  ngOnInit() {
    this.showSidebar = this.route.snapshot.url.length !== 0;
    this.documentsService.currentUploaded.subscribe(
      (data) => (this.finished = data)
    );

    this.loanRequestSubscription = this.route.parent.data.subscribe(
      ({ loanRequest }) => {
        this.loanProgress = loanRequest.progress;
        this.loanRequest = loanRequest;
        this.loanApproved = this.loanRequestService.getFormLockedStatus(
          this.loanRequest
        );
      }
    );

    this.loansDashboardData = [
      {
        title: 'בניית הלוואה',
        titleIcon: 'img/biz/loan-icon-01.svg',
        boxes: [
          {
            boxTitle: 'פרטי הבקשה',
            boxText: 'ריבית שנתית אפקטיבית החל מ - 3.9%',
            completed: this.loanProgress.requestDetails,
            routeLink: LOAN_PAGES[1],
            boxImage: 'img/biz/inner-loan-01.svg',
          },
          {
            boxTitle: 'לוח סילוקין',
            boxText: 'בחירת סוג ההלוואה',
            completed: this.loanProgress.loanRepayments,
            routeLink: LOAN_PAGES[2],
            boxImage: 'img/biz/inner-loan-010.svg',
          },
          {
            boxTitle: 'בטחונות להלוואה',
            completed: this.loanProgress.loanCollaterals,
            routeLink: LOAN_PAGES[3],
            boxText: this.loanApproved ? null : 'דירוג ההלוואה הזמני הוא AA',
            boxImage: 'img/biz/inner-loan-02.svg',
          },
        ],
      },
      {
        title: 'פרטי העסק',
        titleIcon: 'img/biz/loan-icon-02.svg',
        boxes: [
          {
            routeLink: LOAN_PAGES[4],
            boxTitle: 'נתונים כלליים',
            completed: this.loanProgress.businessDetails,
            boxText: this.loanApproved ? null : 'דירוג ההלוואה הזמני הוא AA',
            boxImage: 'img/biz/inner-loan-03.svg',
          },
          {
            routeLink: LOAN_PAGES[8],
            completed: this.loanProgress.businessOwners,
            boxTitle: 'בעלי השליטה',
            boxImage: 'img/biz/inner-loan-04.svg',
          },
          {
            routeLink: LOAN_PAGES[7],
            completed: true,
            boxTitle: 'איתנות פיננסית',
            boxImage: 'img/biz/inner-loan-05.svg',
          },
        ],
      },
      {
        title: 'מסמכים',
        titleIcon: 'img/biz/loan-icon-03.svg',
        boxes: [
          {
            routeLink: LOAN_PAGES[14],
            completed: !this.finished,
            boxTitle: 'מסמכים הדרושים לתהליך',
            boxImage: 'img/biz/inner-loan-06.svg',
          },
        ],
      },
    ];
  }

  ngOnDestroy() {
    this.loanRequestSubscription.unsubscribe();
  }

  isCompleted(category, docsProgress) {
    return (
      docsProgress.get(category).numberOfRequiredDocs ===
      docsProgress.get(category).numberOfUploadedDocs
    );
  }

  getDocsProgressText(category, docsProgress) {
    return (
      docsProgress.get(category).numberOfRequiredDocs -
      docsProgress.get(category).numberOfUploadedDocs +
      ' מתוך ' +
      docsProgress.get(category).numberOfRequiredDocs +
      ' מסמכים נשארו להעלאה.'
    );
  }
}
