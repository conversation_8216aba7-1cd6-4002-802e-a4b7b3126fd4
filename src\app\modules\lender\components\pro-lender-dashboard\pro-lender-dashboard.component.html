<ng-container *ngIf="lender">
  <div style="margin: 0 5px">
    <div class="responsive-video-container">
      <!-- <iframe
        [src]="youtubeUrl"
        title="YouTube video player"
        frameborder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        referrerpolicy="strict-origin-when-cross-origin"
        allowfullscreen
      ></iframe> -->
    </div>
  </div>
  <app-investment-info-boxes
    [returns]="lender.returns"
    [lenderId]="lender.id"
    [transactions]="lender.transactions"
    [lateLoansDevaluation]="lateLoansDevaluation"
    [portfolioValue]="lender.portfolioValue"
    [totalPortfolioValue]="lender.totalPortfolioValue"
    [nextMonthExpectedInterest]="lender.nextMonthExpectedInterest"
    [totalInvestedWithdraws]="totalInvestedWithdraws"
    [accumulatedGrossReturns]="accumulatedGrossReturns"
  ></app-investment-info-boxes>
  <div class="row g-0">
    <div class="col-lg-9 order-1 order-lg-0">
      <app-transactions-deposits-withdrawals
        class="d-block h-100 p-1"
        [tableData]="transactions"
        [state]="TR.LENDER"
      >
      </app-transactions-deposits-withdrawals>
    </div>
    <div class="col-lg-3 order-0 order-lg-1">
      <div class="row h-100 g-0 flex-md-columns">
        <div class="col-6 col-lg-auto d-flex flex-lg-grow-1">
          <app-investment-agent
            class="d-block flex-grow-1 p-1"
            [agentEnabled]="investmentAgentEnabled"
            [lender]="lender"
          ></app-investment-agent>
        </div>
        <div class="col-6 col-lg-auto d-flex flex-lg-grow-1">
          <app-investment-profile
            class="d-block flex-grow-1 p-1"
            [agentEnabled]="lender.investmentProfile"
          >
          </app-investment-profile>
        </div>
      </div>
    </div>
  </div>
  <app-tar-dialog-material
    [display]="openReportsPageInfoPopUp"
    [toggle]="false"
    [dialogMessage]="'reportsPageInfoPopUp.text' | translate"
    [dialogTitle]="'reportsPageInfoPopUp.title' | translate"
    [dialogOkButtonName]="'reportsPageInfoPopUp.ok' | translate"
    (onClose)="closeReportsPageInfoPopUp()"
  >
  </app-tar-dialog-material>
</ng-container>
