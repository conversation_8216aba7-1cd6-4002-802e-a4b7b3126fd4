import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  CanActivate,
  Router,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BorrowersService } from '../services/borrowers.service';

@Injectable({
  providedIn: 'root',
})
export class BorrowersPaymentMethodsGuard implements CanActivate {
  constructor(
    private borrowersService: BorrowersService,
    private router: Router
  ) {}
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ):
    | Observable<boolean | UrlTree>
    | Promise<boolean | UrlTree>
    | boolean
    | UrlTree {
    return this.borrowersService.getAvailablePages().pipe(
      map((availablePages) => {
        return availablePages &&
          availablePages.enableCDCreditAndDebitAccountsPage
          ? true
          : this.router.createUrlTree([`/app/borrowers/under-construction`]);
      })
    );
  }
}
