<div class="register-steps-wrap">
  <div class="col-lg-11 col-xl-10 no-padding">
    <div class="loan-info-title">
      <h2>{{ 'register.general-loan-info.step-name' | translate }}</h2>
    </div>
    <div class="form-holder">
      <form class="register-steps-form" [formGroup]="generalLoanInfoForm">
        <div class="form-group-wrap">
          <div class="form-group">
            <div class="label-holder">
              <label>{{
                'register.general-loan-info.loan-name' | translate
              }}</label>
            </div>
            <div class="input-holder">
              <input
                type="text"
                class="form-control"
                placeholder="{{
                  'register.general-loan-info.loan-name-placeholder' | translate
                }}"
                formControlName="name"
              />
            </div>
            <control-validation-errors
              [control]="generalLoanInfoForm.controls['name']"
              translateTag="error"
            >
            </control-validation-errors>
          </div>
        </div>
        <div class="form-group-wrap">
          <div class="form-group">
            <div class="label-holder">
              <label>{{
                'register.general-loan-info.requested-loan-amount' | translate
              }}</label>
            </div>
            <div class="input-holder">
              <span class="currency">₪</span>
              <p-spinner
                class="bz-spinner"
                [min]="0"
                [max]="initialRegistrationParams.maxLoanAmount"
                [step]="initialRegistrationParams.stepLoanAmount"
                formControlName="amount"
                (onBlur)="setMinLoanAmount()"
              >
              </p-spinner>
            </div>
            <control-validation-errors
              [control]="generalLoanInfoForm.controls['amount']"
              translateTag="error"
            >
            </control-validation-errors>
          </div>
          <div class="form-group">
            <div class="label-holder">
              <label>{{
                'register.general-loan-info.loan-duration' | translate
              }}</label>
            </div>
            <div class="input-holder period-control">
              <div class="period-control">
                <span class="month-label">חודשים</span>
                <p-spinner
                  class="bz-spinner"
                  [min]="(minPeriod$ | async).minLoanDuration"
                  [max]="initialRegistrationParams.maxPeriod"
                  [step]="initialRegistrationParams.stepPeriod"
                  formControlName="period"
                >
                </p-spinner>
              </div>
            </div>
          </div>
        </div>
        <div class="form-group-wrap">
          <div class="form-group">
            <div class="label-holder">
              <label>{{
                'register.general-loan-info.loan-purpose' | translate
              }}</label>
            </div>
            <div class="input-holder">
              <div class="bz-select-holder">
                <p-dropdown
                  [options]="loanPurposes"
                  [style]="{ width: '100%' }"
                  formControlName="purpose"
                ></p-dropdown>
              </div>
              <control-validation-errors
                [control]="generalLoanInfoForm.controls['purpose']"
                translateTag="error"
              >
              </control-validation-errors>
            </div>
          </div>
        </div>
        <div class="form-group-wrap">
          <div class="form-group">
            <div class="label-holder">
              <label>{{
                'register.general-loan-info.loan-request-information'
                  | translate
              }}</label>
            </div>
            <div class="input-holder">
              <textarea
                class="form-control textarea"
                placeholder="{{
                  'register.general-loan-info.loan-request-information-placeholder'
                    | translate
                }}"
                formControlName="purposeFreeText"
              ></textarea>
            </div>
            <control-validation-errors
              [control]="generalLoanInfoForm.controls['purposeFreeText']"
              translateTag="error"
            >
            </control-validation-errors>
          </div>
        </div>
        <div class="code-section">
          <div class="heading-wrap">
            <strong class="heading"
              >{{ 'register.general-loan-info.organization-code' | translate }}
              <span
                class="bz-info-tooltip"
                pTooltip="{{
                  'register.general-loan-info.organization-code-tooltip'
                    | translate
                }}"
                tooltipPosition="top"
                tooltipStyleClass="bz-tooltip"
                ><i class="icon-info" aria-hidden="true"></i></span
            ></strong>
          </div>
          <div class="radio-row">
            <div class="radio-box">
              <p-radioButton
                class="bz-tarya-radio"
                name="groupname"
                formControlName="orgCodeSwitch"
                label="{{ 'register.general-loan-info.no-code' | translate }}"
                (click)="clearOrgCode()"
              >
              </p-radioButton>
            </div>
            <div
              class="radio-box"
              [ngClass]="{
                disabled: !generalLoanInfoForm.controls['orgCodeSwitch'].value
              }"
            >
              <p-radioButton
                class="bz-tarya-radio"
                name="groupname"
                [value]="true"
                formControlName="orgCodeSwitch"
                label="{{ 'register.general-loan-info.have-code' | translate }}"
              >
              </p-radioButton>
              <input type="text" class="input-code" formControlName="orgCode" />
              <control-validation-errors
                [control]="generalLoanInfoForm.controls['orgCode']"
                translateTag="error"
              >
              </control-validation-errors>
            </div>
          </div>
        </div>
        <div class="btn-holder" [ngClass]="{ 'stop-click-wrap': disabledBtn }">
          <button
            type="button"
            class="btn btn-primary"
            [ngClass]="{ 'stop-click': disabledBtn }"
            [disabled]="!generalLoanInfoForm.valid"
            (click)="submit($event)"
          >
            {{ 'register.continue' | translate }}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
