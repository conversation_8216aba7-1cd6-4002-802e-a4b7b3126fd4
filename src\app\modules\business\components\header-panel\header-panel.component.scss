@import 'styles/vendors/bootstrap/share';
@import 'styles/variables';

.header-panel {
  position: relative;
  background: #fff;
  padding: 30px 15px;
}

:host ::ng-deep .header-logo {
  position: absolute;
  top: 50%;
  width: 30px;
  transform: translateY(-50%);
  left: 0;
  .rtl & {
    left: auto;
    right: 0;
  }

  a {
    display: block;
    height: 100%;
  }

  img {
    max-width: 100%;
  }
}

:host ::ng-deep .flags {
  position: absolute;
  left: 5px;
  top: 5px;
  cursor: pointer;

  .rtl & {
    left: auto;
    right: 5px;
  }
}

:host ::ng-deep .header-inner-wrap {
  position: relative;
  max-width: 1360px;
  padding: 0 0 0 40px;
  margin: 0 auto;

  .rtl & {
    padding: 0 40px 0 0;
  }
}

:host ::ng-deep .header-inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  color: #979797;

  a {
    color: inherit;
    text-decoration: none;
  }
}

.header-settings {
  margin: 0;
  padding: 0;
  list-style: none;
  font-size: 18px;

  li {
    padding: 0 5px;
    display: inline-block;
    vertical-align: top;

    i {
      display: inline-block;
      vertical-align: middle;
    }

    .text {
      display: inline-block;
      vertical-align: middle;
      padding: 0 4px;
    }
  }

  a {
    position: relative;

    &:hover {
      cursor: pointer;
      opacity: 0.7;
    }
  }

  .notification-number {
    font-size: 8px;
    color: #fff;
    height: 12px;
    width: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    background: #ff0000;
    border-radius: 50%;
    position: absolute;
    top: -3px;
    right: -4px;
  }
}

:host ::ng-deep .header-links {
  margin: 0;
  padding: 0;
  list-style: none;
  font-size: 16px;
  @include media-breakpoint-up(md) {
    font-size: 18px;
  }

  li {
    display: inline-block;
    vertical-align: top;
    padding: 0 5px;
    color: #898989;

    + li {
      position: relative;
      color: #898989;

      &:after {
        position: absolute;
        content: '';
        top: 50%;
        height: 18px;
        width: 2px;
        transform: translateY(-50%);
        background: #898989;
        left: -2px;

        .rtl & {
          left: auto;
          right: -4px;
        }
      }
    }

    .active-link {
      color: #000000;
    }

    a {
      &:hover {
        color: #000000;
      }
    }
  }
}
