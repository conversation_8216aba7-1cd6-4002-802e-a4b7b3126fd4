<div class="register-steps-wrap">
  <div class="col-lg-10 no-padding">
    <div class="photo-row">
      <div class="photo-box">
        <i class="icon-shop" aria-hidden="true"></i>
        <image-upload
          class="bz-upload-image"
          [uploadedFiles]="uploadedImages"
          (uploadFinished)="onImageSelected($event)"
        >
        </image-upload>
      </div>
      <div class="photo-info">
        <span>{{
          'register.business-details.profile-picture' | translate
        }}</span>
      </div>
    </div>
    <div class="form-holder">
      <form
        action=""
        class="register-steps-form"
        [formGroup]="businessModelForm"
      >
        <div class="role-section">
          <strong class="heading">{{
            'register.business-details.business-type' | translate
          }}</strong>
          <div class="radio-row">
            <div
              class="radio-box"
              *ngFor="let businessType of businessTypesPRadiobutton"
            >
              <p-radioButton
                class="bz-tarya-radio"
                name="userRole"
                value="{{ businessType.id }}"
                formControlName="type"
                label="{{ businessType.description }}"
              ></p-radioButton>
            </div>
            <control-validation-errors
              [control]="businessModelForm.controls['type']"
              translateTag="error"
            >
            </control-validation-errors>
          </div>
        </div>
        <div class="form-group-wrap">
          <div class="form-group">
            <div class="label-holder">
              <label>{{
                'register.business-details.business-name' | translate
              }}</label>
            </div>
            <div class="input-holder">
              <input type="text" class="form-control" formControlName="name" />
              <control-validation-errors
                [control]="businessModelForm.controls['name']"
                translateTag="error"
              >
              </control-validation-errors>
            </div>
          </div>
          <div class="form-group">
            <div class="label-holder">
              <label>{{
                'register.business-details.business-registration-number'
                  | translate
              }}</label>
            </div>
            <div class="input-holder">
              <input
                type="text"
                class="form-control"
                formControlName="registrationNumber"
                [textMask]="{ mask: registrationNumberMask }"
              />
              <control-validation-errors
                [control]="businessModelForm.controls['registrationNumber']"
                translateTag="error"
              >
              </control-validation-errors>
            </div>
          </div>
        </div>
        <div class="form-group-wrap">
          <div class="form-group">
            <div class="label-holder">
              <label>{{
                'register.business-details.business-field' | translate
              }}</label>
            </div>
            <div class="input-holder">
              <div class="bz-autocomplete">
                <p-autoComplete
                  formControlName="businessField"
                  [suggestions]="filteredBusinessFields"
                  (completeMethod)="suggestBusinessFields($event)"
                  (onSelect)="onBusinessFieldSelect($event)"
                  field="name"
                  placeholder="{{
                    'register.business-details.hiring-a-branch' | translate
                  }}"
                  [minLength]="1"
                  [style]="{ width: '100%' }"
                  [dropdown]="true"
                  [forceSelection]="true"
                >
                </p-autoComplete>
                <control-validation-errors
                  [control]="businessModelForm.controls['businessField']"
                  translateTag="error"
                >
                </control-validation-errors>
              </div>
            </div>
          </div>
          <div class="form-group">
            <div class="label-holder">
              <label>{{
                'register.business-details.business-sic' | translate
              }}</label>
            </div>
            <div class="input-holder">
              <div class="bz-autocomplete">
                <p-autoComplete
                  formControlName="businessSic"
                  [suggestions]="filteredSics"
                  (completeMethod)="suggestSics($event)"
                  placeholder="{{
                    'register.business-details.select-a-domain' | translate
                  }}"
                  [minLength]="1"
                  [style]="{ width: '100%' }"
                  [dropdown]="true"
                  field="name"
                  [forceSelection]="true"
                >
                </p-autoComplete>
                <control-validation-errors
                  [control]="businessModelForm.controls['businessSic']"
                  translateTag="error"
                >
                </control-validation-errors>
                <!--{{ businessModelForm.controls['businessSic'].errors | json}}-->
              </div>
            </div>
          </div>
        </div>
        <div class="form-group-wrap">
          <div class="form-group">
            <div class="label-holder">
              <label>{{
                'register.business-details.establishment-year' | translate
              }}</label>
            </div>
            <div class="input-holder">
              <input
                type="text"
                class="form-control"
                formControlName="establishmentYear"
                (input)="
                  checkCurrentYear(
                    businessModelForm.controls['establishmentYear']
                  )
                "
                [textMask]="{ mask: yearMask }"
              />
              <control-validation-errors
                [control]="businessModelForm.controls['establishmentYear']"
                translateTag="error"
              >
              </control-validation-errors>
            </div>
          </div>
          <div class="form-group"></div>
        </div>
        <div class="form-group-wrap">
          <div class="form-group">
            <div class="label-holder">
              <label>{{
                'register.business-details.business-address' | translate
              }}</label>
            </div>
            <div class="input-wrap">
              <div class="input-holder">
                <div class="bz-autocomplete">
                  <p-autoComplete
                    formControlName="city"
                    [suggestions]="filteredCitiesSingle"
                    (completeMethod)="filterCitiesSingle($event)"
                    field="name"
                    [forceSelection]="true"
                    placeholder="{{ 'register.city' | translate }}"
                    [minLength]="1"
                    [style]="{ width: '100%' }"
                    [dropdown]="true"
                  ></p-autoComplete>
                </div>
                <control-validation-errors
                  [control]="businessModelForm.controls['city']"
                  translateTag="error"
                >
                </control-validation-errors>
              </div>
              <div class="input-holder">
                <div class="bz-autocomplete">
                  <p-autoComplete
                    formControlName="street"
                    [suggestions]="filteredStreetsSingle"
                    (completeMethod)="filterStreetSingle($event)"
                    field="name"
                    [forceSelection]="true"
                    placeholder="{{ 'register.street' | translate }}"
                    [minLength]="1"
                    [style]="{ width: '100%' }"
                    [dropdown]="true"
                  ></p-autoComplete>
                </div>
                <control-validation-errors
                  [control]="businessModelForm.controls['street']"
                  translateTag="error"
                >
                </control-validation-errors>
              </div>
              <div class="input-holder">
                <input
                  type="text"
                  class="form-control"
                  placeholder="{{ 'register.street-number' | translate }}"
                  formControlName="streetNumber"
                />
                <control-validation-errors
                  [control]="businessModelForm.controls['streetNumber']"
                  translateTag="error"
                >
                </control-validation-errors>
              </div>
              <div class="input-holder">
                <input
                  type="number"
                  class="form-control"
                  placeholder="{{ 'register.zip-code' | translate }}"
                  formControlName="zipcode"
                />
                <control-validation-errors
                  [control]="businessModelForm.controls['zipcode']"
                  translateTag="error"
                >
                </control-validation-errors>
              </div>
            </div>
          </div>
        </div>
        <div class="form-group-wrap">
          <div class="form-group">
            <div class="label-holder has-counter">
              <span class="descr-counter"
                >{{ 'register.business-details.left' | translate }}
                {{ leftCharacters }}
                {{ 'register.business-details.characters' | translate }}</span
              >
              <label>{{
                'register.business-details.business-description' | translate
              }}</label>
            </div>
            <div class="input-holder">
              <textarea
                class="form-control textarea"
                [maxlength]="businessDescriptionMaxLength"
                placeholder="{{
                  'register.business-details.business-description-placeholder'
                    | translate
                }}"
                formControlName="description"
                (input)="
                  updateLeftChars(businessModelForm.controls['description'])
                "
              ></textarea>
              <span class="description-text"
                >{{ 'register.business-details.up-to' | translate }}
                {{ businessDescriptionMaxLength }}
                {{ 'register.business-details.characters' | translate }}</span
              >
            </div>
            <control-validation-errors
              [control]="businessModelForm.controls['description']"
              translateTag="error"
            >
            </control-validation-errors>
          </div>
        </div>
        <div class="btn-holder" [ngClass]="{ 'stop-click-wrap': disabledBtn }">
          <button
            type="button"
            class="btn btn-primary"
            [ngClass]="{ 'stop-click': disabledBtn }"
            [disabled]="!businessModelForm.valid"
            (click)="submit($event)"
          >
            {{ 'register.continue' | translate }}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
