import { Component, OnInit } from '@angular/core';
import { BusinessOwner } from '../../model/business-owner';
import { Business } from '../../model/business';
import { LoanRequest } from '../../model/loan-request';
import { LoanRequestService } from '../../service/loan-request.service';
import { RequiredDocumentResponse } from '../../model/required-documents-response';
import { ActivatedRoute, Router } from '@angular/router';
import { LOAN_PAGES } from '../../layouts/loan/loan-pages';

export class BusinessOwnerData {
  businessOwnerId: number;
  name: string;
  businessEmailAddress: string;
  requiredDocuments: RequiredDocumentResponse;
  fileUploadResource: string;
  numberOfUploadedDocuments: number;
}

@Component({
  selector: 'app-business-owner-documents',
  templateUrl: './business-owner-documents.component.html',
  styleUrls: ['./business-owner-documents.component.scss'],
})
export class BusinessOwnerDocumentsComponent implements OnInit {
  businessOwnersRequiredDocuments: Map<number, RequiredDocumentResponse>;
  businessOwnersData: BusinessOwnerData[] = [];
  loanRequest: LoanRequest;
  fileUploadResource: string;
  documentsTypeTranslationKey: string;

  private business: Business;

  constructor(
    private loanRequestService: LoanRequestService,
    private activatedRoute: ActivatedRoute,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit() {
    this.businessOwnersRequiredDocuments = this.activatedRoute.snapshot.data.requiredDocuments;
    const documentsData = this.activatedRoute.snapshot.data.documents;
    if (documentsData.entity === 'loan-request')
      this.business = this.loanRequestService.getCurrentLoanRequest().business;
    this.documentsTypeTranslationKey = this.activatedRoute.snapshot.data.documents.translationKey;
    this.business.owners.forEach((owner: BusinessOwner) =>
      this.businessOwnersData.push({
        businessOwnerId: owner.id,
        name: owner.person.firstName + ' ' + owner.person.lastName,
        businessEmailAddress: owner.person.businessEmail,
        requiredDocuments: this.businessOwnersRequiredDocuments.get(owner.id),
        fileUploadResource: documentsData.fileUploadResource
          .replace('{businessId}', this.business.id)
          .replace('{businessOwnerId}', owner.id),
        numberOfUploadedDocuments: owner.documents.length,
      })
    );
  }

  submit() {
    this.router.navigate([LOAN_PAGES[13]], {
      relativeTo: this.route.parent.parent,
    });
  }
}
