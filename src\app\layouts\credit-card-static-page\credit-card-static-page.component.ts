import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { CreditCardStaticPageService } from '~tarya-layouts/credit-card-static-page/credit-card-static-page.service';
import { CreditCardInfo } from '~tarya/modules/shared/models/credit-card-info';

@Component({
  selector: 'app-credit-card-static-page',
  templateUrl: './credit-card-static-page.component.html',
  styleUrls: ['./credit-card-static-page.component.scss'],
})
export class CreditCardStaticPageComponent implements OnInit {
  constructor(
    private activatedRoute: ActivatedRoute,
    private staticPageService: CreditCardStaticPageService
  ) {}

  ngOnInit() {
    this.activatedRoute.queryParams.subscribe((params) => {
      const cardParams = this.mapObjKeysToFirstLowerCase(params);
      const staticPageObj: CreditCardInfo = {
        uniqueId: cardParams.uniqueID ? cardParams.uniqueID : null,
        token: cardParams.cardToken ? cardParams.cardToken : null,
        auth: cardParams.authNumber ? cardParams.authNumber : null,
        cardExp: cardParams.cardExp ? cardParams.cardExp : null,
        cardOwnerSocialId: cardParams.personalId ? cardParams.personalId : null,
        errorCode: cardParams.errorCode ? cardParams.errorCode : null,
        errorText: cardParams.errorText ? cardParams.errorText : null,
        responseMac: cardParams.responseMac ? cardParams.responseMac : null,
        txId: cardParams.txId ? cardParams.txId : null,
      };
      this.getCreditCardProcessingUrl(staticPageObj);
    });
  }

  mapObjKeysToFirstLowerCase(params): any {
    let newParams = {};
    Object.keys(params).forEach((key) => {
      newParams[key.charAt(0).toLowerCase() + key.slice(1)] = params[key];
    });
    return newParams;
  }

  getCreditCardProcessingUrl(body) {
    if (body.errorText) {
      sessionStorage.setItem('creditCardErrorMessage', body.errorText);
    } else {
      sessionStorage.removeItem('creditCardErrorMessage');
    }
    this.staticPageService.getCreditCardUrl(body).subscribe((data) => {
      window.location.href = data.url;
    });
  }
}
