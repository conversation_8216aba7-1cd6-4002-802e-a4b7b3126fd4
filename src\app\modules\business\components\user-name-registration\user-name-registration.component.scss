@import 'styles/vendors/bootstrap/share';
@import 'styles/variables';

:host ::ng-deep .radio-row {
  margin-bottom: 10px;
  @include media-breakpoint-up(md) {
    margin-bottom: 10px;
  }
}

.role-section {
  margin-bottom: 30px;

  p {
    font-size: 14px;
    max-width: 300px;
  }
}

.add-info {
  font-size: 15px;
  color: #3d3d3d;

  p {
    margin: 0 0 20px;
  }

  ul {
    padding: 0;
    margin: 0 0 20px;
  }
}

:host ::ng-deep .password-form-group {
  .password-hints-wrap {
    @include media-breakpoint-up(md) {
      padding-top: 25px;
    }
  }

  .input-holder {
    margin-bottom: 35px;
  }

  strong {
    font-weight: normal;
    display: block;
    margin-bottom: 15px;
  }

  ul {
    margin: 0 0 15px;
    padding: 0;
    list-style: none;
  }

  li {
    font-size: 13px;
    padding: 0 0 0 11px;
    margin-bottom: 4px;
    position: relative;

    .rtl & {
      padding: 0 11px 0 0;
    }

    &:after {
      content: '';
      height: 6px;
      width: 6px;
      background-color: #979797;
      position: absolute;
      top: 6px;
      left: 0;
      border-radius: 50%;

      .rtl & {
        left: auto;
        right: 0;
      }
    }
  }
}

.checkbox-row {
  margin-bottom: 10px;
  display: flex;
  align-items: center;

  .check-label {
    padding: 0 10px;
    color: #aaa;
    width: calc(100% - 20px);

    &.checked {
      color: #212529;
    }

    a {
      text-decoration: underline;
      color: #267ffa;
      display: inline-block;
      vertical-align: top;

      &:hover {
        text-decoration: none;
      }
    }
  }
}
.terms {
  cursor: pointer;
  display: flex;
}
