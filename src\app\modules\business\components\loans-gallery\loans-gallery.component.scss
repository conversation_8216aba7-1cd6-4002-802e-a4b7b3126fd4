@import 'styles/vendors/bootstrap/share';
@import 'styles/variables';

.gallery-holder {
  width: 100%;
  position: relative;
  @include media-breakpoint-up(md) {
    display: flex;
    flex-wrap: wrap;
  }
}

.loan-gallery-box {
  padding: 0 15px;
  margin-bottom: 30px;
  display: flex;
  flex-wrap: wrap;
  min-height: 335px;
  @include media-breakpoint-up(md) {
    width: 50%;
    margin-bottom: 44px;
  }
  @include media-breakpoint-up(lg) {
    width: 33.33%;
  }
}

.loans-gallery-options {
  padding: 20px 10px;
  @include media-breakpoint-up(md) {
    padding: 20px 10px 70px;
  }
}

.no-results {
  display: none;
  opacity: 0.8;

  &:only-child {
    display: block;
    width: 100%;
    text-align: center;
    padding: 40px 10px;
  }
}

.control-wrap {
  display: inline-block;
  vertical-align: middle;
  padding: 5px;
}

.loans-gallery-options {
  @include media-breakpoint-up(md) {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .btn-holder {
    padding: 5px;
  }
}

.new-loan-box {
  font-size: 14px;
  display: inline-block;
  color: #474747;
  text-decoration: none;
  transition: box-shadow 0.3s ease-in-out;
  width: 100%;
  min-height: 335px;
  padding: 15px 15px 35px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #dedede;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  @include media-breakpoint-up(md) {
    padding: 30px 30px 55px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 17px;
  }
  div {
    width: 100%;
    text-align: center;
    @include media-breakpoint-up(md) {
      width: 50%;
      text-align: right;
    }
  }
  img {
    width: 100%;
    @include media-breakpoint-up(md) {
      width: 35%;
    }
  }
}
