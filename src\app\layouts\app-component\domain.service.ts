import { Injectable, Renderer2, Inject, RendererFactory2 } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map, share } from 'rxjs/operators';
import { DomainDataDto } from '~tarya-layouts/app-component/domain-data';
import { DOCUMENT } from '@angular/common';

@Injectable({
  providedIn: 'root',
})
export class DomainService {
  private domainResource = '/rest/api/domain';
  private ofekCssPath = 'assets/css/cloud/dev.ofek.co.il.css';
  private renderer: Renderer2;

  domainData: DomainDataDto;
  observable: Observable<DomainDataDto>;

  private static handleError(error: Response | any) {
    return of(error);
  }

  constructor(
    @Inject(DOCUMENT) private document: Document,
    private http: HttpClient,
    private rendererFactory: RendererFactory2
  ) {
    this.renderer = this.rendererFactory.createRenderer(null, null);
  }

  getDomainData(): Observable<DomainDataDto> {
    if (this.domainData) {
      return of(this.domainData);
    }
    if (this.observable) {
      return this.observable;
    }
    this.observable = this.http.get<DomainDataDto>(this.domainResource).pipe(
      map((response) => {
        this.domainData = response;
        if (response.region) {
          localStorage.setItem('region', response.region);
        }
        return this.domainData;
      }),
      catchError(DomainService.handleError),
      share()
    );
    return this.observable;
  }

  loadDomainCss(communityName: string): void {
    const cloudCssStylesheetLink = this.document.getElementById(
      'cloud-css-link'
    );
    // Add domains different from Tarya
    if (communityName.toLocaleLowerCase() === 'ofek') {
      this.renderer.setAttribute(
        cloudCssStylesheetLink,
        'href',
        this.ofekCssPath
      );
    }
  }
}
