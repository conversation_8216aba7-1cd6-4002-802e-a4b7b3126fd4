.card {
  border: none;
  .card-body {
    -webkit-box-shadow: 1px -1px 3px 1px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 1px -1px 3px 1px rgba(0, 0, 0, 0.1);
    box-shadow: 1px -1px 3px 1px rgba(0, 0, 0, 0.1);
  }
}

.td-hold {
  width: 148px;
  height: 45px;
  text-align: right;
  padding-right: 15px !important;
}
:host ::ng-deep .p-datatable > .p-datatable-wrapper table th:nth-child(2),
.td-big {
  width: 280px !important;
}
:host ::ng-deep .p-datatable-loading {
  background: rgb(249, 249, 249);
}

:host ::ng-deep .dashboard-table > .p-datatable .pages-info {
  padding: 0 30px 0 10px !important;
  width: 120px !important;
  .rtl & {
    padding: 0 0 0 20px !important;
  }
}

:host ::ng-deep .p-paginator-element {
  margin-left: -20px !important;

  .rtl & {
    margin-right: -60px !important;
    margin-left: 0 !important;
  }
}

:host ::ng-deep .table-inner {
  position: relative;
  padding: 100px 0 20px !important;
}

.highlighted {
  background-color: transparent;
}
