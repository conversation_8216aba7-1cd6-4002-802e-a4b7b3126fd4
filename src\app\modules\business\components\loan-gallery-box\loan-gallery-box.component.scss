@import 'styles/vendors/bootstrap/share';
@import 'styles/variables';

.gallery-header {
  color: #575757;
  padding: 15px 0;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #d0d0d0;
}

.loan-title {
  color: #000;
  display: block;
  font-size: 18px;
}

.logo-holder {
  height: 57px;
  width: 57px;
  border-radius: 50%;
  border: 1px solid #979797;
  position: relative;
  background: #f3f3f3;
  color: #cdcdcd;

  .img {
    border-radius: 50%;
    display: block;
    width: 100%;
    height: auto;
  }

  .img-wrap {
    position: absolute;
    background-position: 50% 50%;
    background-size: cover;
    top: 0;
    left: 0;
    height: 100%;
    z-index: 3;
    width: 100%;
    display: block;
    border-radius: 50%;
  }

  .placeholder {
    line-height: 1;
    z-index: 2;
    position: absolute;
    top: 50%;
    left: 50%;
    font-size: 28px;
    transform: translate(-50%, -50%);
  }
}

.header-info {
  > span {
    display: block;
  }
}

.gallery-text {
  padding: 0 10px;
  width: calc(100% - 57px);
  @include media-breakpoint-up(md) {
    padding: 0 20px;
  }
}

.gallery-inner {
  position: relative;
  font-size: 14px;
  display: block;
  color: #474747;
  text-decoration: none;
  transition: box-shadow 0.3s ease-in-out;
  width: 100%;
  min-height: 335px;
  padding: 15px 15px 35px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #dedede;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  @include media-breakpoint-up(md) {
    padding: 30px 30px 55px;
  }

  &:hover {
    box-shadow: 0 3px 5px 0 rgba(0, 0, 0, 0.46);
    cursor: pointer;
  }
}

.gallery-body {
  padding: 15px 0 0;

  ul {
    padding: 0;
    margin: 0 0 20px;
    font-size: 16px;

    li {
      margin-bottom: 6px;

      strong {
        display: inline-block;
        vertical-align: top;
        font-weight: 900;
        color: #000;
        padding: 0 4px;
      }

      span {
        display: inline-block;
        vertical-align: top;
        color: #575757;
      }
    }
  }

  p {
    margin: 0;

    + p {
      margin: 0 0 20px;
    }

    span {
      display: inline-block;
      vertical-align: top;
    }
  }

  .body-inner {
    max-width: 260px;
  }

  .link-span {
    color: #2981fa;
  }

  .link-span-red {
    color: #ff0000;
  }

  .loan-message-description {
    overflow: hidden;
  }
}

:host ::ng-deep .bz-progress-box {
  position: absolute;
  bottom: 30px;
  padding: 0 15px;
  max-width: 260px;
  left: 0;
  width: 100%;
  z-index: 2;
  @include media-breakpoint-up(md) {
    padding: 0 30px;
  }

  &.progress-full {
    .bz-progressbar {
      .p-progressbar {
        .p-progressbar-value {
          background: #00bec7;
        }
      }
    }
  }

  .rtl & {
    left: auto;
    right: 0;
  }
}
