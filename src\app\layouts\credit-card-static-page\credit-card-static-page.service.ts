import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CreditCardStaticPageService {
  private readonly apiCreditCardStaticProcessing: any =
    'api/credit-card/static-processing';

  constructor(private http: HttpClient) {}

  getCreditCardUrl(body: any): Observable<any> {
    return this.http.post<any>(this.apiCreditCardStaticProcessing, body);
  }
}
