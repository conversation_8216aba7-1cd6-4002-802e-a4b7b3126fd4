<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="ubuntu_lightregular" horiz-adv-x="1155" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="466" />
<glyph unicode="&#xfb01;" horiz-adv-x="1255" d="M176 0v1174q0 207 95 311t294 104q86 0 140.5 -13t70.5 -24l-26 -114q-23 10 -67 21t-112 11q-141 0 -201.5 -71.5t-60.5 -229.5v-110h426v-113h-426v-946h-133zM913 1378q0 47 29 75t70 28t69.5 -28t28.5 -75t-28.5 -74.5t-69.5 -27.5t-70 27.5t-29 74.5zM946 0v1059 h133v-1059h-133z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1292" d="M176 0v1174q0 207 95 311t294 104q86 0 140.5 -13t70.5 -24l-26 -114q-23 10 -67 21t-112 11q-141 0 -201.5 -71.5t-60.5 -229.5v-110h426v-113h-426v-946h-133zM944 254v1311l133 24v-1331q0 -49 9.5 -78.5t27.5 -47t48 -26t71 -14.5l-19 -110q-68 2 -118 17t-83.5 47 t-51 82t-17.5 126z" />
<glyph unicode="&#xfb03;" horiz-adv-x="2025" d="M176 0v1174q0 207 95 311t294 104q86 0 140.5 -13t70.5 -24l-26 -114q-23 10 -67 21t-112 11q-141 0 -201.5 -71.5t-60.5 -229.5v-110h426v-113h-426v-946h-133zM946 0v1174q0 207 95 311t294 104q86 0 140.5 -13t70.5 -24l-26 -114q-23 10 -67 21t-112 11 q-141 0 -201.5 -71.5t-60.5 -229.5v-110h426v-113h-426v-946h-133zM1683 1378q0 47 29 75t70 28t69.5 -28t28.5 -75t-28.5 -74.5t-69.5 -27.5t-70 27.5t-29 74.5zM1716 0v1059h133v-1059h-133z" />
<glyph unicode="&#xfb04;" horiz-adv-x="2062" d="M176 0v1174q0 207 95 311t294 104q86 0 140.5 -13t70.5 -24l-26 -114q-23 10 -67 21t-112 11q-141 0 -201.5 -71.5t-60.5 -229.5v-110h426v-113h-426v-946h-133zM946 0v1174q0 207 95 311t294 104q86 0 140.5 -13t70.5 -24l-26 -114q-23 10 -67 21t-112 11 q-141 0 -201.5 -71.5t-60.5 -229.5v-110h426v-113h-426v-946h-133zM1714 254v1311l133 24v-1331q0 -49 9.5 -78.5t27.5 -47t48 -26t71 -14.5l-19 -110q-68 2 -118 17t-83.5 47t-51 82t-17.5 126z" />
<glyph unicode="&#xd;" horiz-adv-x="466" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode=" "  horiz-adv-x="466" />
<glyph unicode="&#x09;" horiz-adv-x="466" />
<glyph unicode="&#xa0;" horiz-adv-x="466" />
<glyph unicode="!" horiz-adv-x="561" d="M168 88q0 47 29.5 80t83.5 33q53 0 82.5 -33t29.5 -80t-29.5 -80t-82.5 -33t-83 33t-30 80zM209 1024v395h143v-395q0 -88 -3 -165t-9 -148.5t-13.5 -139t-15.5 -135.5h-61q-18 137 -29.5 274.5t-11.5 313.5z" />
<glyph unicode="&#x22;" horiz-adv-x="802" d="M172 1481v75h147v-77q0 -102 -11 -208t-29 -210h-68q-16 104 -27.5 209.5t-11.5 210.5zM483 1481v75h147v-77q0 -102 -11 -208t-29 -210h-68q-16 104 -27.5 209.5t-11.5 210.5z" />
<glyph unicode="#" horiz-adv-x="1337" d="M96 387v111h217l80 424h-297v108h320l73 389h125l-73 -389h379l73 389h125l-74 -389h197v-108h-219l-80 -424h299v-111h-319l-76 -387h-125l76 387h-379l-76 -387h-125l76 387h-197zM438 498h379l82 424h-379z" />
<glyph unicode="$" d="M154 125l39 115q27 -14 59.5 -28.5t75.5 -27t99 -19.5t132 -7q80 0 136.5 16t92 44t51 67t15.5 82q0 68 -25.5 112.5t-71.5 78.5t-111.5 59.5t-143.5 54.5q-55 20 -112.5 45.5t-103.5 63.5t-75 92.5t-29 130.5q0 152 83 234.5t257 107.5v243h121v-237q96 -2 169 -19.5 t122 -38.5l-35 -112q-35 14 -102.5 33.5t-182.5 19.5q-291 0 -290 -211q0 -57 20.5 -95t59 -66.5t94 -51.5t123.5 -47q74 -29 142 -58.5t120.5 -72.5t83 -104.5t30.5 -149.5q0 -143 -94 -231.5t-260 -100.5v-270h-121v264q-139 2 -231 29.5t-137 54.5z" />
<glyph unicode="%" horiz-adv-x="1701" d="M121 1083q0 90 24.5 159t67.5 115t98.5 69.5t116.5 23.5t116.5 -23.5t98.5 -69.5t67.5 -115t24.5 -159t-24.5 -158.5t-67.5 -114.5t-98.5 -69.5t-116.5 -23.5t-116.5 23.5t-98.5 69.5t-67.5 114.5t-24.5 158.5zM242 1083q0 -117 47 -187.5t139 -70.5t139 70.5t47 187.5 t-47 187.5t-139 70.5t-139 -70.5t-47 -187.5zM379 0l809 1419h135l-809 -1419h-135zM967 336q0 90 24.5 158.5t67.5 114.5t98 69.5t117 23.5q61 0 116.5 -23.5t98.5 -69.5t67.5 -114.5t24.5 -158.5t-24.5 -158.5t-67.5 -115t-98.5 -70t-116.5 -23.5t-116.5 23.5t-98.5 70 t-67.5 115t-24.5 158.5zM1087 336q0 -117 47.5 -187.5t139.5 -70.5t139 70.5t47 187.5t-47 187.5t-139 70.5t-139.5 -70.5t-47.5 -187.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1327" d="M102 367q0 82 26 146.5t69 114.5t100 89t119 67q-90 84 -126 164t-36 162q0 80 25.5 143.5t71.5 107.5t109.5 66.5t137.5 22.5q76 0 137.5 -23.5t102.5 -63.5t63.5 -95.5t22.5 -118.5q0 -76 -27 -136.5t-70 -107.5t-99 -84t-114 -67l381 -389q43 72 70 156.5t37 170.5 l115 -14q-10 -100 -45 -202.5t-97 -194.5q76 -78 136.5 -148t95.5 -133h-150q-29 47 -68.5 93t-87.5 93q-86 -92 -199.5 -148t-254.5 -56q-125 0 -210 35.5t-137.5 92t-75 124t-22.5 133.5zM246 367q0 -47 16.5 -95.5t51 -86.5t92 -62.5t141.5 -24.5q119 0 212 45t163 123 l-430 441q-41 -20 -85.5 -47t-80 -67t-58 -95.5t-22.5 -130.5zM389 1124q0 -72 32 -142.5t122 -152.5q111 53 182.5 125t71.5 193q0 80 -54.5 134t-140.5 54q-104 0 -158.5 -56t-54.5 -155z" />
<glyph unicode="'" horiz-adv-x="491" d="M172 1481v75h147v-77q0 -102 -11 -208t-29 -210h-68q-16 104 -27.5 209.5t-11.5 210.5z" />
<glyph unicode="(" horiz-adv-x="634" d="M170 612q0 297 97.5 547t252.5 445l96 -66q-162 -201 -236.5 -425t-74.5 -501q0 -276 75 -500.5t236 -424.5l-96 -66q-156 195 -253 444.5t-97 546.5z" />
<glyph unicode=")" horiz-adv-x="634" d="M18 -313q162 201 237 425t75 500t-75 500.5t-237 425.5l97 66q156 -195 253 -445t97 -547t-97.5 -546.5t-252.5 -444.5z" />
<glyph unicode="*" horiz-adv-x="966" d="M80 1073l43 133q49 -16 90 -32.5t80 -35t78 -38t82 -39.5q-8 49 -15.5 92t-12.5 85t-8 86t-3 95h139q0 -51 -3 -95t-8 -86t-12.5 -85t-15.5 -92q43 20 82 39.5t78 38t80 35t90 32.5l43 -133q-96 -31 -177.5 -45.5t-177.5 -28.5q70 -70 126.5 -127t117.5 -139l-114 -84 q-29 41 -52.5 79t-44 76t-40 78t-42.5 83q-23 -43 -42 -83t-39.5 -78t-44 -76t-52.5 -79l-115 84q31 41 60 75t57.5 64.5t60 61.5t66.5 65q-96 14 -177 28.5t-177 45.5z" />
<glyph unicode="+" d="M115 535v114h403v445h119v-445h403v-114h-403v-447h-119v447h-403z" />
<glyph unicode="," horiz-adv-x="503" d="M100 -281q47 90 66.5 189.5t19.5 206.5q0 25 -1 41t-3 39h150q2 -18 2 -33v-25q0 -127 -36 -243.5t-99 -219.5z" />
<glyph unicode="-" horiz-adv-x="577" d="M57 528v129h463v-129h-463z" />
<glyph unicode="." horiz-adv-x="503" d="M139 88q0 47 30 80t83 33t83 -33t30 -80t-30 -80t-83 -33t-83 33t-30 80z" />
<glyph unicode="/" horiz-adv-x="735" d="M-47 -379l698 1983h131l-694 -1983h-135z" />
<glyph unicode="0" d="M121 709q0 360 118.5 550.5t338.5 190.5q217 0 336.5 -190.5t119.5 -550.5q0 -358 -119.5 -549t-336.5 -191t-337 191t-120 549zM266 709q0 -123 15.5 -235t51.5 -196.5t95 -136t150 -51.5q88 0 147 51.5t96 136t52.5 196.5t15.5 235t-15.5 234.5t-52.5 197.5t-96 137 t-147 51q-90 0 -149.5 -51t-95.5 -137t-51.5 -198t-15.5 -234z" />
<glyph unicode="1" d="M219 1128q123 43 219.5 110t190.5 181h94v-1419h-133v1217q-23 -25 -58.5 -51.5t-80.5 -52.5t-94.5 -49.5t-98.5 -37.5z" />
<glyph unicode="2" d="M141 1288q12 14 44 42t80 54.5t111.5 46t141.5 19.5q207 0 311.5 -101.5t104.5 -281.5q0 -80 -28.5 -150.5t-77 -136t-110 -127t-124.5 -123.5q-53 -51 -103.5 -103t-90.5 -104.5t-63.5 -102.5t-23.5 -99v-4h670v-117h-817q0 4 -1 15.5t-1 33.5q0 96 34.5 179t89 157 t121 137.5t128.5 122.5q51 49 97 95t81 95.5t55.5 104.5t20.5 121q0 143 -77 205.5t-194 62.5q-66 0 -117 -16.5t-89 -38t-64.5 -44t-38.5 -34.5z" />
<glyph unicode="3" d="M137 45l35 119q12 -8 41 -20.5t69 -23.5t90 -19.5t109 -8.5q195 0 279 81t84 218q0 90 -34 149.5t-92.5 95.5t-135 51t-162.5 15h-21v111h47q55 0 113.5 11.5t107 42t80 81.5t31.5 131q0 68 -22.5 116t-60.5 76.5t-88 42t-105 13.5q-111 0 -172.5 -30.5t-100.5 -57.5 l-57 104q20 14 52 33t75 35t95 27.5t114 11.5q106 0 183 -26.5t128 -75t76 -115t25 -143.5q0 -125 -67 -204t-171 -114q57 -14 111.5 -44t97.5 -76t69.5 -111.5t26.5 -153.5q0 -90 -31.5 -166t-94 -132t-159 -88t-225.5 -32q-68 0 -124 9.5t-99 21.5t-72.5 24.5t-44.5 20.5z " />
<glyph unicode="4" d="M102 385v82q37 98 103.5 221t150.5 252t182.5 253t198.5 226h140v-925h176v-109h-176v-385h-132v385h-643zM240 494h505v776q-72 -74 -145.5 -170t-140 -199.5t-123.5 -208t-96 -198.5z" />
<glyph unicode="5" d="M147 45l35 119q12 -8 42 -20.5t70 -23.5t90 -19.5t105 -8.5q102 0 171 23.5t110 64.5t58.5 95.5t17.5 117.5q0 82 -26.5 147.5t-94.5 110.5t-182.5 68.5t-290.5 23.5q12 104 20.5 191.5t14.5 168.5t9 157.5t5 158.5h639v-116h-522q-2 -39 -6 -100.5t-8.5 -126t-10.5 -124 t-10 -94.5q158 -4 272.5 -39t188.5 -94t109.5 -143t35.5 -187q0 -90 -29.5 -167.5t-91 -135t-156 -90.5t-223.5 -33q-66 0 -122 9.5t-100 21.5t-74.5 24.5t-45.5 20.5z" />
<glyph unicode="6" d="M158 563q0 416 199.5 631t580.5 231l8 -112q-125 -8 -230.5 -38t-187.5 -92.5t-137 -160.5t-76 -242q55 29 133 50.5t166 21.5q115 0 195 -36t129 -95t71.5 -137t22.5 -164q0 -80 -24.5 -160t-76.5 -144.5t-131 -105.5t-188 -41q-223 0 -338.5 156.5t-115.5 437.5z M301 561q0 -96 13.5 -182t49 -151.5t95 -103.5t151.5 -38q76 0 129.5 27.5t86 72.5t48 104.5t15.5 125.5q0 164 -73 241.5t-214 77.5q-94 0 -164.5 -21.5t-132.5 -49.5q-4 -35 -4 -54.5v-48.5z" />
<glyph unicode="7" d="M141 1296v123h863v-119q-70 -84 -153 -224t-156.5 -315t-128 -371.5t-66.5 -389.5h-142q12 170 62.5 360.5t123 368.5t157.5 327.5t165 239.5h-725z" />
<glyph unicode="8" d="M135 356q0 72 20.5 130.5t54.5 104.5t78 82t93 62q-125 61 -170 145.5t-45 182.5q0 88 30.5 159.5t85 122t130 78t166.5 27.5q106 0 184 -32t128 -83t73.5 -116.5t23.5 -135.5q0 -68 -20.5 -124t-53 -102t-73.5 -81t-80 -59q143 -61 201.5 -153.5t58.5 -194.5 q0 -88 -32 -162t-89 -126t-139 -82t-182 -30t-181.5 29t-139.5 81t-90 122.5t-32 154.5zM276 356q0 -57 18.5 -106t56.5 -86t94.5 -58.5t132.5 -21.5t132 21.5t94 58.5t56.5 86t18.5 106q0 88 -34 143.5t-88.5 90.5t-123 55.5t-137.5 42.5q-104 -49 -162 -132t-58 -200z M305 1077q0 -57 13.5 -104t50.5 -85t102.5 -70t171.5 -60q94 53 149.5 131t55.5 194q0 47 -15.5 92.5t-47 81t-83 57t-124.5 21.5q-68 0 -119 -20.5t-85 -56t-51.5 -83t-17.5 -98.5z" />
<glyph unicode="9" d="M129 1001q0 78 24.5 158t77 144.5t131 105.5t187.5 41q223 0 339 -156.5t116 -437.5q0 -428 -205 -644t-598 -216l-4 110q135 0 247.5 31t195.5 95.5t135 166t71 244.5q-55 -29 -133 -50.5t-166 -21.5q-115 0 -195 36t-129 95.5t-71.5 137.5t-22.5 161zM272 1006 q0 -162 73 -240t214 -78q94 0 165 21.5t132 50.5q2 25 3 47t1 51q0 96 -13 182t-49 151.5t-95.5 103.5t-151.5 38q-76 0 -129 -27.5t-86 -72.5t-48.5 -104.5t-15.5 -122.5z" />
<glyph unicode=":" horiz-adv-x="503" d="M139 88q0 47 30 80t83 33t83 -33t30 -80t-30 -80t-83 -33t-83 33t-30 80zM139 946q0 47 30 80t83 33t83 -33t30 -80t-30 -80t-83 -33t-83 33t-30 80z" />
<glyph unicode=";" horiz-adv-x="503" d="M100 -281q47 90 66.5 189.5t19.5 206.5q0 25 -1 41t-3 39h150q2 -18 2 -33v-25q0 -127 -36 -243.5t-99 -219.5zM139 946q0 47 30 80t83 33t83 -33t30 -80t-30 -80t-83 -33t-83 33t-30 80z" />
<glyph unicode="&#x3c;" d="M113 530v119l886 377l41 -113l-772 -323l772 -324l-41 -112z" />
<glyph unicode="=" d="M115 319v115h925v-115h-925zM115 750v114h925v-114h-925z" />
<glyph unicode="&#x3e;" d="M113 266l772 324l-772 323l41 113l886 -377v-119l-886 -376z" />
<glyph unicode="?" horiz-adv-x="778" d="M41 1380q43 25 124 47.5t179 22.5q109 0 181.5 -27.5t115.5 -71.5t61.5 -99.5t18.5 -108.5q0 -74 -26.5 -131.5t-67.5 -106.5t-87 -94t-87 -93t-68 -103.5t-27 -122.5v-34.5t2 -21.5h-106q-4 20 -6 44t-2 48q0 70 24.5 124.5t61.5 101.5t80 89t79.5 86t61.5 94t25 114 q0 57 -20.5 95t-54.5 60.5t-79 30.5t-94 8q-137 0 -250 -61zM205 88q0 47 29.5 80t82.5 33t83 -33t30 -80t-29.5 -80t-83.5 -33q-53 0 -82.5 33t-29.5 80z" />
<glyph unicode="@" horiz-adv-x="1925" d="M147 575q0 219 66 383t179.5 272.5t266 162t324.5 53.5q164 0 308.5 -55.5t253 -158t171 -247.5t62.5 -326q0 -133 -29 -234t-79 -169t-117.5 -102.5t-145.5 -34.5q-66 0 -105.5 22.5t-58.5 48.5q-37 -25 -100.5 -49t-138.5 -24q-94 0 -173 27.5t-137.5 84t-91.5 142.5 t-33 202q0 96 30 180.5t86 147t139 98t190 35.5q98 0 177 -17.5t124 -35.5v-600q0 -80 27.5 -115t72.5 -35q70 0 114 44t69.5 111t36 141.5t10.5 136.5q0 133 -45.5 253.5t-130.5 212t-207.5 145.5t-278.5 54q-158 0 -287 -50t-221 -146.5t-143 -236.5t-51 -321 q0 -190 56 -333.5t154.5 -239.5t232.5 -144t290 -48q119 0 187.5 11t90.5 19l15 -112q-35 -10 -117 -22.5t-176 -12.5q-178 0 -336 52t-275.5 161.5t-186.5 275.5t-69 393zM707 575q0 -90 22.5 -155.5t61 -108.5t92 -63.5t112.5 -20.5q70 0 113 11.5t90 40.5q-8 20 -11 51.5 t-3 54.5v520q-16 4 -53 11.5t-103 7.5q-78 0 -138.5 -26t-101 -72t-61 -110.5t-20.5 -140.5z" />
<glyph unicode="A" horiz-adv-x="1312" d="M20 0q82 221 154 412.5t140.5 362.5t136 329t143.5 315h123q76 -158 143.5 -315.5t136 -328.5t140 -362.5t155.5 -412.5h-153q-39 104 -74 200.5t-68 188.5h-690q-35 -92 -69.5 -188.5t-71.5 -200.5h-146zM350 508h604q-78 211 -151.5 393.5t-151.5 358.5 q-78 -176 -150.5 -358.5t-150.5 -393.5z" />
<glyph unicode="B" horiz-adv-x="1290" d="M184 27v1366q37 10 82 17t93.5 12t94.5 7.5t85 2.5q115 0 218 -18.5t181 -61.5t124 -114t46 -175q0 -115 -58.5 -196t-156.5 -111q61 -14 112.5 -41t90.5 -69t60.5 -102.5t21.5 -144.5q0 -201 -160 -306t-494 -105q-35 0 -81 3t-94 8t-91 12t-74 16zM324 121 q31 -4 92 -9.5t125 -5.5q94 0 183 12.5t156.5 45.5t108.5 90t41 147q0 82 -28.5 136.5t-82 86t-130 44t-171.5 12.5h-294v-559zM324 799h270q76 0 143.5 11t117.5 41t79 80t29 128q0 70 -31 118t-87.5 78.5t-134 44t-171.5 13.5q-76 0 -128.5 -4t-86.5 -11v-499z" />
<glyph unicode="C" horiz-adv-x="1247" d="M127 711q0 178 54.5 316t144.5 232.5t208.5 142.5t247.5 48q80 0 145.5 -10t115 -25.5t84 -31t53.5 -27.5l-45 -121q-51 31 -130 60.5t-196 29.5q-133 0 -232.5 -45t-167 -127t-101.5 -194.5t-34 -249.5q0 -141 34 -255t99.5 -194t161 -123t217.5 -43q135 0 226.5 25.5 t138.5 52.5l39 -119q-14 -10 -49 -23.5t-88.5 -27.5t-123 -23.5t-155.5 -9.5q-137 0 -255 49.5t-205 143.5t-137 232t-50 317z" />
<glyph unicode="D" horiz-adv-x="1437" d="M184 25v1368q72 20 166 29.5t180 9.5q172 0 315.5 -45.5t247 -135.5t161 -225t57.5 -315q0 -182 -57.5 -317.5t-161 -225.5t-247 -135t-315.5 -45q-86 0 -180 9t-166 28zM324 123q12 -2 62 -8t161 -6q154 0 268.5 42t192 119.5t116.5 189t39 251.5q0 139 -39 249.5 t-116.5 188.5t-192.5 120t-268 42q-111 0 -161 -6.5t-62 -8.5v-1173z" />
<glyph unicode="E" horiz-adv-x="1136" d="M184 0v1419h820v-121h-680v-493h606v-119h-606v-565h733v-121h-873z" />
<glyph unicode="F" horiz-adv-x="1064" d="M184 0v1419h809v-121h-669v-499h594v-121h-594v-678h-140z" />
<glyph unicode="G" horiz-adv-x="1347" d="M127 711q0 178 53 316t144.5 231.5t212 142.5t258.5 49q158 0 260 -34t149 -64l-47 -119q-57 41 -155.5 66.5t-213.5 25.5q-106 0 -200 -39t-164 -116.5t-110 -192.5t-40 -266q0 -141 33 -255t99.5 -195t165 -124t227.5 -43q104 0 174.5 11.5t95.5 21.5v545h139v-633 q-39 -16 -152.5 -42t-279.5 -26q-139 0 -258 49.5t-206 142.5t-136 231t-49 317z" />
<glyph unicode="H" horiz-adv-x="1417" d="M184 0v1419h140v-616h770v616h139v-1419h-139v682h-770v-682h-140z" />
<glyph unicode="I" horiz-adv-x="507" d="M184 0v1419h140v-1419h-140z" />
<glyph unicode="J" horiz-adv-x="997" d="M16 90l66 113q18 -16 46 -36t64.5 -36t81.5 -26.5t99 -10.5q158 0 232.5 83t74.5 284v958h139v-973q0 -102 -20.5 -189t-70.5 -151.5t-133 -100.5t-208 -36q-74 0 -135.5 13.5t-108.5 32t-78.5 39t-48.5 36.5z" />
<glyph unicode="K" horiz-adv-x="1226" d="M184 0v1419h140v-661q78 70 173 161t188 183t175 176t131 141h168q-59 -63 -143 -150t-178.5 -179.5t-187.5 -181.5t-167 -156q59 -37 128 -91.5t139.5 -119t139 -135t129 -142t109 -139.5t78.5 -125h-155q-70 106 -157 210.5t-183.5 197t-195.5 168t-191 126.5v-702 h-140z" />
<glyph unicode="L" horiz-adv-x="1028" d="M184 0v1419h140v-1298h667v-121h-807z" />
<glyph unicode="M" horiz-adv-x="1771" d="M150 0q18 387 41.5 741.5t58.5 677.5h123q61 -102 130.5 -236t138.5 -278.5t133.5 -285t113.5 -252.5q49 113 113.5 253t133 284.5t138 278.5t131.5 236h117q35 -324 58.5 -678t41.5 -741h-141q-12 330 -27.5 630t-42.5 556q-16 -29 -51 -98.5t-80 -161.5t-94 -197.5 t-96.5 -205t-85 -183.5t-58.5 -135h-125q-20 51 -58 135t-85 183.5t-96.5 205t-94.5 197.5t-79.5 161.5t-51.5 98.5q-27 -256 -42 -556t-27 -630h-137z" />
<glyph unicode="N" horiz-adv-x="1452" d="M184 0v1419h111q104 -117 224 -269.5t234.5 -311t214 -310t162.5 -264.5v1155h138v-1419h-117q-39 72 -99.5 168t-132 205.5t-151.5 225.5t-159 225.5t-153.5 205.5t-133.5 166v-1196h-138z" />
<glyph unicode="O" horiz-adv-x="1585" d="M127 711q0 182 53 320t144.5 231.5t212 140.5t256.5 47q135 0 255.5 -47t212 -140.5t144.5 -231.5t53 -320t-53 -321.5t-144.5 -233t-212 -140.5t-255.5 -47t-256 47t-212.5 140.5t-144.5 232.5t-53 322zM274 711q0 -143 37 -257t104.5 -194t163 -123t214.5 -43t214 43 t162.5 123t104.5 194t37 257t-37 256.5t-104.5 193.5t-163 122t-213.5 42q-119 0 -214.5 -42t-163 -122t-104.5 -193.5t-37 -256.5z" />
<glyph unicode="P" horiz-adv-x="1212" d="M184 0v1393q74 20 173.5 29.5t185.5 9.5q301 0 448.5 -115t147.5 -326q0 -121 -43 -206t-123 -137t-195.5 -75.5t-261.5 -23.5h-192v-549h-140zM324 670h176q113 0 204 13t155.5 49t99 99.5t34.5 161.5q0 94 -37.5 155.5t-100 97.5t-142.5 50.5t-164 14.5 q-78 0 -132.5 -5.5t-92.5 -9.5v-626z" />
<glyph unicode="Q" horiz-adv-x="1585" d="M127 711q0 182 53 320t144.5 231.5t212 140.5t256.5 47q135 0 255.5 -47t212 -140.5t144.5 -231.5t53 -320q0 -164 -43 -291t-116.5 -219t-174 -147.5t-215.5 -73.5q8 -57 52.5 -98.5t106.5 -70t138 -46t148 -27.5l-29 -117q-78 14 -168 35.5t-171 60.5t-140.5 99.5 t-77.5 152.5q-131 4 -248 53.5t-204 142.5t-138 230t-51 316zM274 711q0 -143 37 -257t104.5 -194t163 -123t214.5 -43t214 43t162.5 123t104.5 194t37 257t-37 256.5t-104.5 193.5t-163 122t-213.5 42q-119 0 -214.5 -42t-163 -122t-104.5 -193.5t-37 -256.5z" />
<glyph unicode="R" horiz-adv-x="1265" d="M184 0v1393q74 20 173.5 29.5t183.5 9.5q293 0 441 -111t148 -328q0 -160 -83.5 -252t-221.5 -133q33 -41 85.5 -110.5t107.5 -152.5t108.5 -173t90.5 -172h-150q-43 82 -93 164t-102.5 158.5t-101.5 142t-86 115.5q-66 -6 -131 -7h-229v-573h-140zM324 692h192 q96 0 182 9.5t149.5 41t100.5 91t37 161.5q0 94 -37 155.5t-96 97.5t-136 49.5t-159 13.5q-78 0 -136.5 -5.5t-96.5 -9.5v-604z" />
<glyph unicode="S" horiz-adv-x="1058" d="M70 66l45 120q20 -12 55 -28.5t82 -31.5t106 -25.5t131 -10.5q166 0 258.5 60.5t92.5 195.5q0 78 -30 130t-79 88t-112.5 61.5t-133.5 54.5q-88 35 -157.5 71.5t-118.5 83t-74.5 105.5t-25.5 139q0 182 120.5 276.5t335.5 94.5q57 0 112.5 -8t103.5 -21.5t88 -30 t67 -34.5l-49 -117q-59 41 -144.5 65.5t-183.5 24.5q-68 0 -124 -13t-97 -43t-64.5 -75t-23.5 -108q0 -66 24.5 -111t68.5 -78t103.5 -59.5t128.5 -55.5q84 -35 159 -69.5t130.5 -81.5t88 -114.5t32.5 -166.5q0 -193 -134 -289t-364 -96q-82 0 -149.5 10.5t-120.5 25.5 t-90 31.5t-57 29.5z" />
<glyph unicode="T" horiz-adv-x="1122" d="M37 1298v121h1048v-121h-454v-1298h-139v1298h-455z" />
<glyph unicode="U" horiz-adv-x="1392" d="M178 526v893h139v-874q0 -117 28 -202t77 -140.5t118.5 -82t155.5 -26.5t156 26.5t119 82t76.5 140.5t27.5 202v874h139v-893q0 -117 -27.5 -218t-89 -177t-161 -119t-240.5 -43t-240.5 43t-161 119t-89 177.5t-27.5 217.5z" />
<glyph unicode="V" horiz-adv-x="1277" d="M20 1419h156q61 -182 118.5 -345t113 -315.5t113 -299t122.5 -295.5q63 147 121.5 293.5t115 299t113 316.5t117.5 346h147q-94 -268 -168.5 -471t-139 -366.5t-123 -302t-121.5 -279.5h-130q-63 141 -122.5 279.5t-124 302t-139 366.5t-169.5 471z" />
<glyph unicode="W" horiz-adv-x="1886" d="M74 1419h143q31 -180 63.5 -353t68.5 -335t74 -306t75 -267q88 227 188 492t199 595h121q98 -330 197.5 -597t193.5 -490q72 242 142.5 569.5t135.5 691.5h137q-37 -219 -74.5 -410.5t-78.5 -364.5t-86 -331.5t-98 -312.5h-150q-49 117 -97 233.5t-95.5 240.5t-94.5 259 t-96 287q-49 -152 -97 -287t-95.5 -259t-95.5 -240.5t-97 -233.5h-147q-55 154 -101.5 312.5t-86.5 331.5t-75.5 364.5t-72.5 410.5z" />
<glyph unicode="X" horiz-adv-x="1245" d="M55 0q94 186 221 374.5t259 375.5l-457 669h160l383 -571l383 571h155l-457 -665q133 -188 260.5 -378t221.5 -376h-152q-31 61 -79 141t-104 168t-116.5 176t-116.5 166q-55 -78 -115.5 -166t-116.5 -176t-104.5 -168t-78.5 -141h-146z" />
<glyph unicode="Y" horiz-adv-x="1165" d="M20 1419h162q86 -180 189.5 -365.5t216.5 -353.5q109 168 213 353.5t192 365.5h152q-109 -195 -229.5 -404.5t-262.5 -426.5v-588h-139v584q-145 223 -267 431t-227 404z" />
<glyph unicode="Z" horiz-adv-x="1146" d="M78 0v104q45 88 107.5 193.5t134 217.5t149.5 224.5t153.5 216t144.5 191.5t126 151h-776v121h927v-127q-49 -57 -113.5 -139t-138 -179t-150.5 -207t-151.5 -221.5t-141 -220t-120.5 -204.5h836v-121h-987z" />
<glyph unicode="[" horiz-adv-x="634" d="M205 -379v1983h413v-103h-286v-1777h286v-103h-413z" />
<glyph unicode="\" horiz-adv-x="735" d="M-47 1604h131l698 -1983h-135z" />
<glyph unicode="]" horiz-adv-x="634" d="M16 -276h287v1777h-287v103h414v-1983h-414v103z" />
<glyph unicode="^" d="M127 729l385 690h131l385 -690l-100 -53l-350 631l-351 -631z" />
<glyph unicode="_" horiz-adv-x="1003" d="M-10 -264h1024v-115h-1024v115z" />
<glyph unicode="`" horiz-adv-x="770" d="M123 1479l94 104l324 -311l-68 -78z" />
<glyph unicode="a" horiz-adv-x="1042" d="M98 309q0 86 33 147.5t92.5 101.5t141 58.5t178.5 18.5q29 0 60.5 -3t61 -8.5t51 -10.5t29.5 -9v66q0 55 -8 107t-35.5 94t-78 68t-131.5 26q-117 0 -174.5 -16.5t-84.5 -26.5l-18 116q35 16 108 30.5t175 14.5q104 0 177 -29.5t118 -81.5t65.5 -124t20.5 -156v-667 q-20 -6 -59.5 -13.5t-90.5 -15.5t-113.5 -13.5t-128.5 -5.5q-84 0 -154.5 18.5t-122.5 58.5t-82 102.5t-30 152.5zM236 309q0 -59 19 -100t55 -65.5t86.5 -36t113.5 -11.5q78 0 136.5 5t98.5 14v372q-23 10 -71.5 21.5t-134.5 11.5q-49 0 -102.5 -7t-98.5 -30.5t-73.5 -64.5 t-28.5 -109z" />
<glyph unicode="b" horiz-adv-x="1193" d="M176 33v1532l133 24v-594q35 27 112 56.5t181 29.5q115 0 204 -41t150.5 -114.5t93 -175t31.5 -222.5q0 -129 -37.5 -231t-106.5 -174t-164 -110t-212 -38q-141 0 -233 18.5t-152 39.5zM309 125q31 -10 91.5 -19.5t158.5 -9.5q172 0 276.5 111.5t104.5 320.5 q0 88 -18.5 167t-59.5 137.5t-107.5 93.5t-164.5 35q-47 0 -90 -9.5t-80 -24t-64.5 -30.5t-46.5 -31v-741z" />
<glyph unicode="c" horiz-adv-x="942" d="M113 526q0 125 33.5 227.5t97 176.5t153.5 113.5t203 39.5q86 0 161 -14t116 -37l-35 -114q-41 20 -92.5 32.5t-137.5 12.5q-176 0 -267 -114t-91 -323q0 -94 20.5 -173t66.5 -135t118.5 -88t177.5 -32q86 0 144.5 19.5t84.5 34.5l29 -115q-37 -20 -116 -40t-165 -20 q-123 0 -217 39t-156.5 112t-95 174t-32.5 224z" />
<glyph unicode="d" horiz-adv-x="1193" d="M113 528q0 121 31.5 222.5t93 175t150.5 114.5t204 41q104 0 181 -29.5t112 -56.5v570l133 24v-1556q-59 -20 -151.5 -39t-233.5 -19q-117 0 -212 38t-164 110t-106.5 174t-37.5 231zM254 528q0 -209 104.5 -320.5t276.5 -111.5q98 0 158.5 9.5t91.5 19.5v741 q-18 14 -46 30.5t-65 31t-80 24t-90 9.5q-98 0 -164.5 -35t-107.5 -93.5t-59.5 -137.5t-18.5 -167z" />
<glyph unicode="e" horiz-adv-x="1122" d="M113 530q0 139 40 243t104.5 173.5t147 103t170.5 33.5q195 0 310.5 -127t115.5 -391q0 -16 -1 -32.5t-3 -30.5h-743q6 -195 96 -299.5t287 -104.5q109 0 172 20.5t92 35.5l25 -115q-29 -16 -108 -39t-185 -23q-139 0 -237.5 41t-162 115t-92 175t-28.5 222zM258 614h606 q-4 166 -77.5 258.5t-208.5 92.5q-72 0 -128.5 -29t-98.5 -77t-65.5 -111.5t-27.5 -133.5z" />
<glyph unicode="f" horiz-adv-x="770" d="M176 0v1174q0 207 95 311t294 104q86 0 140.5 -13t70.5 -24l-26 -114q-23 10 -67 21t-112 11q-141 0 -201.5 -71.5t-60.5 -229.5v-110h426v-113h-426v-946h-133z" />
<glyph unicode="g" horiz-adv-x="1173" d="M113 553q0 113 34.5 209t99 166.5t158 110.5t209.5 40q147 0 236.5 -19.5t146.5 -37.5v-967q0 -238 -122.5 -339t-370.5 -101q-102 0 -185 15.5t-143 35.5l27 121q49 -23 134 -39t171 -16q188 0 273 72.5t85 248.5v70q-12 -10 -36.5 -23.5t-60.5 -28t-82 -23.5t-103 -9 q-92 0 -178.5 28.5t-151 90t-103 159t-38.5 236.5zM254 551q0 -104 28.5 -179t74.5 -122t105.5 -68.5t121.5 -21.5q90 0 164.5 26.5t115.5 61.5v682q-35 10 -90 19t-158 9q-86 0 -152.5 -29.5t-113.5 -83.5t-71.5 -129t-24.5 -165z" />
<glyph unicode="h" horiz-adv-x="1165" d="M176 0v1565l133 24v-561q66 25 133.5 38t135.5 13q123 0 204.5 -34.5t131 -99t68.5 -155t19 -198.5v-592h-133v549q0 113 -15 190.5t-51 126.5t-95.5 70.5t-149.5 21.5q-35 0 -75 -6t-74.5 -13t-62.5 -15.5t-36 -12.5v-911h-133z" />
<glyph unicode="i" horiz-adv-x="485" d="M143 1378q0 47 29 75t70 28t69.5 -28t28.5 -75t-28.5 -74.5t-69.5 -27.5t-70 27.5t-29 74.5zM176 0v1059h133v-1059h-133z" />
<glyph unicode="j" horiz-adv-x="485" d="M-129 -362l18 108q14 -4 39 -8t47 -4q121 0 161 67.5t40 202.5v1055h133v-1049q0 -201 -76 -297t-256 -96q-23 0 -56.5 6t-49.5 15zM143 1378q0 47 29 75t70 28t69.5 -28t28.5 -75t-28.5 -74.5t-69.5 -27.5t-70 27.5t-29 74.5z" />
<glyph unicode="k" horiz-adv-x="1013" d="M176 0v1565l133 24v-1007q51 49 114.5 112.5t128 129t122 127t98.5 108.5h160q-47 -49 -106.5 -110.5t-124 -128t-128 -130.5t-114.5 -115q63 -43 136 -110.5t143.5 -146t133 -161.5t105.5 -157h-158q-49 82 -111.5 161t-131 148.5t-137 127t-130.5 95.5v-532h-133z" />
<glyph unicode="l" horiz-adv-x="522" d="M174 254v1311l133 24v-1331q0 -49 9.5 -78.5t27.5 -47t48 -26t71 -14.5l-19 -110q-68 2 -118 17t-83.5 47t-51 82t-17.5 126z" />
<glyph unicode="m" horiz-adv-x="1767" d="M176 0v1022q59 16 154.5 36.5t247.5 20.5q201 0 290 -116q10 8 40 27.5t75 40t105.5 34.5t134.5 14q121 0 195.5 -35.5t115.5 -100t55.5 -155t14.5 -196.5v-592h-134v549q0 104 -9 181t-36.5 127t-79 75.5t-133.5 25.5q-61 0 -111 -11t-87 -27.5t-60.5 -33t-33.5 -24.5 q18 -53 27 -119.5t9 -140.5v-602h-133v549q0 104 -9 181t-37.5 127t-81 75.5t-138.5 25.5q-96 0 -162.5 -10t-85.5 -18v-930h-133z" />
<glyph unicode="n" horiz-adv-x="1165" d="M176 0v1022q59 16 159.5 36.5t246.5 20.5q121 0 201.5 -34.5t129 -99t68.5 -155t20 -198.5v-592h-133v549q0 113 -15 190.5t-51 126.5t-94.5 70.5t-146.5 21.5q-94 0 -162.5 -10t-89.5 -18v-930h-133z" />
<glyph unicode="o" horiz-adv-x="1191" d="M113 528q0 125 35.5 227.5t100 175.5t152.5 112.5t195 39.5q106 0 194.5 -39.5t153 -112.5t100 -175.5t35.5 -227.5t-35.5 -227t-100 -174t-153 -112t-194.5 -40t-194.5 40t-153 112t-100 174t-35.5 227zM254 528q0 -199 92 -315.5t250 -116.5t250 116.5t92 315.5 t-92 317t-250 118t-250 -118t-92 -317z" />
<glyph unicode="p" horiz-adv-x="1193" d="M176 -379v1401q59 20 151.5 38.5t233.5 18.5q117 0 212 -37.5t164 -109.5t106.5 -174.5t37.5 -231.5q0 -121 -31.5 -222t-93 -174t-150.5 -114t-204 -41q-104 0 -181 30t-112 56v-440h-133zM309 190q18 -14 46 -30.5t65 -30.5t80 -23.5t90 -9.5q98 0 164.5 34t107.5 92.5 t59.5 137t18.5 166.5q0 209 -104.5 320.5t-276.5 111.5q-98 0 -158.5 -9t-91.5 -19v-740z" />
<glyph unicode="q" horiz-adv-x="1193" d="M113 526q0 129 37.5 231.5t106.5 174.5t164 109.5t212 37.5q141 0 233 -18.5t152 -38.5v-1401h-133v440q-35 -27 -112 -56.5t-181 -29.5q-115 0 -204 41t-150.5 114t-93 174.5t-31.5 221.5zM254 526q0 -88 18.5 -166.5t59.5 -137t107.5 -92.5t164.5 -34q47 0 90 9.5 t80 23.5t64.5 30.5t46.5 30.5v740q-31 10 -91.5 19t-158.5 9q-172 0 -276.5 -111.5t-104.5 -320.5z" />
<glyph unicode="r" horiz-adv-x="765" d="M176 0v1010q51 23 141.5 46t219.5 23q66 0 124 -10t80 -20l-26 -115q-16 8 -66.5 17.5t-132.5 9.5q-86 0 -138 -12.5t-69 -20.5v-928h-133z" />
<glyph unicode="s" horiz-adv-x="876" d="M82 35l35 121q6 -4 26.5 -13.5t55 -20.5t83 -19.5t111.5 -8.5q115 0 189.5 38t74.5 128q0 43 -13 74t-44 55.5t-81 47t-124 52.5q-59 25 -112.5 50.5t-94.5 59.5t-65.5 81t-24.5 115q0 129 97.5 208.5t267.5 79.5q109 0 179.5 -18t98.5 -33l-30 -119q-25 12 -83.5 33 t-170.5 21q-45 0 -85 -9.5t-71 -29t-49.5 -49t-18.5 -72.5t16.5 -74t48.5 -54.5t78 -44t105 -44.5q61 -25 117.5 -50.5t99.5 -61.5t70 -86t27 -126q0 -143 -107.5 -217t-294.5 -74q-139 0 -213 24t-98 36z" />
<glyph unicode="t" horiz-adv-x="792" d="M168 395v975l133 25v-336h422v-113h-422v-563q0 -90 15.5 -146.5t44 -87t69.5 -41t90 -10.5q84 0 135.5 19.5t79.5 36.5l33 -111q-29 -18 -100.5 -42t-155.5 -24q-98 0 -164.5 26t-106.5 78t-56.5 130t-16.5 184z" />
<glyph unicode="u" horiz-adv-x="1165" d="M164 465v594h133v-551q0 -113 16.5 -190.5t53 -126t95 -70t142.5 -21.5q94 0 164 10.5t88 18.5v930h133v-1022q-59 -16 -159.5 -36.5t-245.5 -20.5q-119 0 -199 34.5t-129 98t-70.5 153.5t-21.5 199z" />
<glyph unicode="v" horiz-adv-x="983" d="M41 1059h141q25 -109 61 -233t77.5 -245.5t87 -235.5t88.5 -202q43 88 88 202t87 235.5t77.5 245.5t60.5 233h133q-37 -145 -82 -286.5t-95 -277t-104.5 -260t-105.5 -235.5h-127q-53 111 -106.5 235.5t-103.5 260t-95 276.5t-82 287z" />
<glyph unicode="w" horiz-adv-x="1587" d="M47 1059h141q23 -109 57 -235t72.5 -249.5t78.5 -234.5t75 -188q39 102 79 218.5t74.5 235.5t65.5 234.5t53 218.5h115q18 -102 48 -218t66 -236t75.5 -236.5t80.5 -218.5q35 78 74 189.5t78 235t72.5 249.5t56.5 235h131q-74 -311 -161 -569.5t-191 -489.5h-125 q-31 72 -64.5 161t-69.5 190.5t-70 213t-64 228.5q-31 -117 -65 -228.5t-69.5 -213t-69.5 -190.5t-65 -161h-127q-104 231 -191 489.5t-161 569.5z" />
<glyph unicode="x" horiz-adv-x="1021" d="M57 0q84 158 176.5 294t188.5 267l-344 498h155l275 -406l278 406h146l-338 -490q43 -57 92 -124.5t98.5 -140.5t95.5 -150.5t85 -153.5h-144q-25 45 -59.5 103.5t-76.5 122t-88 129t-89 124.5q-45 -59 -90 -124.5t-87 -129t-77 -122t-59 -103.5h-138z" />
<glyph unicode="y" horiz-adv-x="970" d="M2 -354l27 110q8 -6 46 -17t79 -11q57 0 103 12t83 42t66.5 76.5t58.5 118.5q-68 125 -130.5 260.5t-116.5 273.5t-99 276.5t-78 271.5h141q23 -98 57.5 -212t78.5 -234.5t97.5 -247.5t114.5 -250q45 127 80 239.5t65 224t57.5 228.5t58.5 252h133q-66 -297 -150 -577.5 t-182 -514.5q-39 -92 -80 -158.5t-91 -109.5t-116.5 -63.5t-157.5 -20.5q-51 0 -93 12.5t-52 18.5z" />
<glyph unicode="z" horiz-adv-x="931" d="M78 0v96q53 100 132 221t163 239t163.5 221.5t135.5 168.5h-563v113h725v-109q-47 -53 -123 -147t-162 -210t-170 -242t-148 -238h621v-113h-774z" />
<glyph unicode="{" horiz-adv-x="653" d="M88 541v100q51 0 88 18.5t61.5 47t36 64.5t11.5 73v397q0 82 15 146.5t53 107.5t103.5 65.5t164.5 22.5h16v-102q-84 0 -129 -16.5t-66.5 -56.5t-25.5 -104.5t-4 -160.5v-219q0 -76 -9.5 -130.5t-28 -93t-45 -64.5t-59.5 -44q33 -18 59.5 -44t45 -65t28 -94t9.5 -131 v-219q0 -96 4 -160.5t25.5 -104.5t66.5 -56.5t129 -16.5v-102h-16q-98 0 -164 22.5t-104 65.5t-53 106.5t-15 147.5v397q0 37 -11.5 72.5t-36 64.5t-61.5 47.5t-88 18.5z" />
<glyph unicode="|" horiz-adv-x="536" d="M205 -379v1983h127v-1983h-127z" />
<glyph unicode="}" horiz-adv-x="653" d="M16 -299q84 0 129 16.5t66.5 56.5t26 104.5t4.5 160.5v219q0 76 9 131t27.5 94t45 65t59.5 44q-33 18 -59.5 44t-45 64.5t-27.5 93t-9 130.5v219q0 96 -4.5 160.5t-26 104.5t-66.5 56.5t-129 16.5v102h17q98 0 163.5 -22.5t103.5 -65.5t53.5 -107.5t15.5 -146.5v-397 q0 -37 11 -73t35.5 -64.5t61.5 -47t88 -18.5v-100q-51 0 -88 -18.5t-61.5 -47.5t-35.5 -64.5t-11 -72.5v-397q0 -84 -15.5 -147.5t-53.5 -106.5t-103.5 -65.5t-163.5 -22.5h-17v102z" />
<glyph unicode="~" d="M102 498q8 37 25.5 81t47.5 81.5t77 63.5t117 26t125 -29t105 -63.5t98 -63.5t102 -29q39 0 65.5 19.5t44 47t26.5 55.5t13 44l105 -24q-10 -37 -26.5 -81t-46.5 -82t-77 -63.5t-117 -25.5t-125 28.5t-105 63.5t-98 63.5t-102 28.5q-39 0 -65.5 -19.5t-44 -47 t-26.5 -55.5t-13 -44z" />
<glyph unicode="&#xa1;" horiz-adv-x="561" d="M168 946q0 47 29.5 80t83.5 33q53 0 82.5 -33t29.5 -80t-29.5 -79.5t-82.5 -32.5t-83 32.5t-30 79.5zM209 10q0 176 11 313.5t30 274.5h61q8 -68 15.5 -135.5t13.5 -139t9 -148.5t3 -165v-389h-143v389z" />
<glyph unicode="&#xa2;" d="M219 594q0 104 27.5 195.5t80 161t127 115.5t169.5 60v293h120v-289q63 0 122 -13t116 -36l-35 -112q-43 18 -101.5 32.5t-121.5 14.5q-178 0 -270.5 -105.5t-92.5 -316.5q0 -90 21.5 -166t68 -131t119 -86t174.5 -31q86 0 145.5 17.5t86.5 29.5l24 -110 q-12 -8 -39.5 -17.5t-62.5 -16.5t-75 -12t-79 -8v-286h-120v293q-98 14 -174 59t-126.5 113.5t-77 157.5t-26.5 194z" />
<glyph unicode="&#xa3;" d="M162 633v112h196v197q0 152 31 250t87 154.5t133 78t169 21.5q86 0 150.5 -17.5t111.5 -39.5l-36 -121q-100 53 -242 53q-63 0 -113.5 -16.5t-84.5 -58.5t-51 -111.5t-17 -173.5v-216h411v-112h-411v-19q0 -121 -7.5 -246.5t-27.5 -250.5h620v-117h-776q23 141 38 289.5 t15 318.5v25h-196z" />
<glyph unicode="&#xa4;" d="M115 319l151 156q-35 47 -53 107.5t-18 128.5t18 128t53 107l-151 156l82 84l155 -160q47 35 104.5 53.5t121.5 18.5q63 0 120.5 -18.5t104.5 -53.5l155 160l82 -84l-151 -156q35 -47 53.5 -107.5t18.5 -127.5q0 -68 -18.5 -128.5t-53.5 -107.5l151 -156l-82 -83 l-155 159q-47 -35 -104.5 -53t-120.5 -18t-121 18t-105 53l-155 -159zM313 711q0 -61 20.5 -112.5t56.5 -88.5t84 -57.5t104 -20.5q55 0 103 20.5t84 57.5t56.5 88.5t20.5 112.5t-20.5 112t-56.5 88t-84 57.5t-103 20.5t-103.5 -20.5t-84.5 -57.5t-56.5 -88t-20.5 -112z" />
<glyph unicode="&#xa5;" d="M63 1419h142q80 -170 176 -337t201 -322q104 156 199.5 322.5t174.5 336.5h136q-88 -178 -193.5 -355t-228.5 -368h309v-108h-334v-244h334v-108h-334v-236h-133v236h-334v108h334v244h-334v108h305q-123 190 -227 367.5t-193 355.5z" />
<glyph unicode="&#xa6;" horiz-adv-x="536" d="M205 -379v770h127v-770h-127zM205 834v770h127v-770h-127z" />
<glyph unicode="&#xa7;" horiz-adv-x="987" d="M96 633q0 57 19.5 105t48.5 85t64.5 63.5t66.5 45.5q-61 45 -91 97t-30 128q0 135 91 214t270 79q123 0 194.5 -19.5t101.5 -31.5l-32 -109q-35 16 -99.5 36t-167.5 20q-100 0 -165.5 -40t-65.5 -126q0 -84 54.5 -131.5t181.5 -85.5q68 -23 131 -47.5t114.5 -62.5 t82 -95.5t30.5 -143.5q0 -51 -16.5 -96t-44 -84t-63.5 -68.5t-73 -50.5q68 -41 106 -102t38 -137q0 -147 -109.5 -223t-294.5 -76q-123 0 -199.5 19.5t-123.5 41.5l32 105q53 -23 119 -42.5t187 -19.5q53 0 100 10.5t83 32t56.5 54t20.5 79.5q0 43 -14.5 76t-46 59.5t-82 49 t-119.5 47.5q-72 25 -136.5 50.5t-112.5 63t-77 93t-29 137.5zM223 649q0 -59 23.5 -98t64.5 -67.5t96.5 -50t118.5 -42.5q20 -6 43 -14t37 -15q78 39 120 104.5t42 137.5q0 57 -26.5 98t-69.5 70t-97.5 49.5t-109.5 38.5q-23 8 -44.5 15.5t-31.5 11.5q-72 -37 -119 -94.5 t-47 -143.5z" />
<glyph unicode="&#xa8;" horiz-adv-x="770" d="M115 1378q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5t-26.5 71.5zM467 1378q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5t-26.5 71.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1691" d="M152 711q0 174 57 312t152.5 232.5t221.5 144.5t263 50t263 -50t221 -144.5t152.5 -232.5t57.5 -312q0 -176 -57.5 -313.5t-152.5 -232.5t-221 -145.5t-263 -50.5t-263 50.5t-221.5 145.5t-152.5 232.5t-57 313.5zM272 711q0 -141 43 -258t119 -200t181.5 -129t230.5 -46 t230.5 46t181 129t118.5 200t43 258t-43 257.5t-118.5 199.5t-181 128t-230.5 45t-230.5 -45t-181.5 -128t-119 -199.5t-43 -257.5zM479 713q0 98 32 175t85 131t122.5 81.5t145.5 27.5q100 0 163 -27.5t85 -43.5l-39 -92q-31 16 -79 35.5t-130 19.5q-113 0 -187.5 -80 t-74.5 -227q0 -152 73 -233t197 -81q88 0 138.5 19.5t72.5 34.5l35 -99q-27 -16 -94.5 -39.5t-167.5 -23.5q-80 0 -148.5 28.5t-119.5 82t-80 132t-29 179.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="790" d="M94 907q0 61 25.5 105.5t69.5 72t102.5 41t124.5 13.5q23 0 59.5 -4.5t77.5 -12.5v19q0 37 -4 72.5t-20.5 64.5t-50.5 47t-91 18q-53 0 -103.5 -5t-90.5 -19l-15 94q35 10 89 19.5t120 9.5q76 0 129 -19.5t86 -55.5t47.5 -86t14.5 -110v-462q-25 -6 -59 -11.5t-69.5 -9.5 t-71.5 -6t-63 -2q-137 0 -222 54t-85 173zM207 915q0 -66 40.5 -102.5t151.5 -36.5q45 0 88 3t66 7v246q-27 4 -61.5 8t-71.5 4q-43 0 -82 -6t-68.5 -20t-46 -40t-16.5 -63z" />
<glyph unicode="&#xab;" horiz-adv-x="925" d="M74 582l311 432l84 -53l-238 -379l238 -381l-84 -54zM436 582l311 432l84 -53l-238 -379l238 -381l-84 -54z" />
<glyph unicode="&#xac;" d="M115 664v114h925v-680h-118v566h-807z" />
<glyph unicode="&#xad;" horiz-adv-x="577" d="M57 528v129h463v-129h-463z" />
<glyph unicode="&#xae;" horiz-adv-x="1691" d="M152 711q0 174 57 312t152.5 232.5t221.5 144.5t263 50t263 -50t221 -144.5t152.5 -232.5t57.5 -312q0 -176 -57.5 -313.5t-152.5 -232.5t-221 -145.5t-263 -50.5t-263 50.5t-221.5 145.5t-152.5 232.5t-57 313.5zM272 711q0 -141 43 -258t119 -200t181.5 -129t230.5 -46 t230.5 46t181 129t118.5 200t43 258t-43 257.5t-118.5 199.5t-181 128t-230.5 45t-230.5 -45t-181.5 -128t-119 -199.5t-43 -257.5zM565 305v793q47 10 105.5 16t109.5 6q174 0 265.5 -63.5t91.5 -192.5q0 -74 -41 -132t-121 -87q16 -18 44 -56t58.5 -85t61 -100.5 t53.5 -98.5h-125q-53 96 -110.5 183t-100.5 139q-14 -2 -26.5 -2h-22.5h-129v-320h-113zM678 717h102q51 0 94.5 6t75 23.5t49 46t17.5 73.5q0 43 -19.5 73t-51.5 48.5t-73 26.5t-86 8q-31 0 -58.5 -1t-49.5 -3v-301z" />
<glyph unicode="&#xaf;" horiz-adv-x="770" d="M115 1329v113h542v-113h-542z" />
<glyph unicode="&#xb0;" horiz-adv-x="651" d="M59 1315q0 63 21.5 113.5t58.5 84t85 52t102 18.5q53 0 101 -18.5t85 -52t58.5 -84t21.5 -113.5t-21.5 -113.5t-58.5 -84.5t-85 -52t-101 -18t-101.5 18t-85.5 52t-58.5 84t-21.5 114zM158 1315q0 -82 48 -129t120 -47t120 47t48 129t-48.5 129t-119.5 47q-72 0 -120 -47 t-48 -129z" />
<glyph unicode="&#xb1;" d="M115 0v115h925v-115h-925zM115 707v114h403v445h119v-445h403v-114h-403v-445h-119v445h-403z" />
<glyph unicode="&#xb2;" horiz-adv-x="735" d="M88 1350q35 37 97.5 68.5t154.5 31.5q115 0 178.5 -55t63.5 -166q0 -43 -15.5 -80t-40 -68.5t-56.5 -60.5t-67 -55q-53 -43 -86.5 -76t-53 -57.5t-27 -45t-7.5 -38.5h392v-99h-508q-2 10 -2 20.5v20.5q0 61 20.5 109.5t52 88.5t70.5 73.5t76 68.5q29 25 53.5 46.5 t42.5 45t29.5 50t11.5 61.5q0 68 -41 93.5t-96 25.5q-35 0 -64.5 -9.5t-54.5 -22.5t-42 -26.5t-26 -21.5z" />
<glyph unicode="&#xb3;" horiz-adv-x="735" d="M92 676l27 96q29 -14 73 -27.5t107 -13.5q117 0 158 40t41 99q0 43 -20.5 72t-53.5 47.5t-77 25.5t-89 7h-27v92h31q37 0 73 5t62.5 20.5t43 40t16.5 65.5q0 59 -38 84t-102 25q-66 0 -110.5 -19.5t-71.5 -38.5l-41 86q23 18 82.5 43t145.5 25q133 0 189 -54.5t56 -140.5 q0 -127 -125 -178q76 -23 123 -74t47 -137q0 -47 -17 -89t-54 -74.5t-95.5 -51t-140.5 -18.5q-78 0 -129 13t-84 30z" />
<glyph unicode="&#xb4;" horiz-adv-x="770" d="M229 1272l324 311l94 -104l-350 -285z" />
<glyph unicode="&#xb5;" horiz-adv-x="1177" d="M176 -379v1438h133v-551q0 -113 16.5 -190.5t53.5 -126t95 -70t142 -21.5q94 0 164 10.5t88 18.5v930h133v-1022q-59 -16 -159.5 -36.5t-245.5 -20.5q-117 0 -188.5 30.5t-112.5 83.5q8 -66 9 -132t1 -124v-217h-129z" />
<glyph unicode="&#xb6;" horiz-adv-x="1261" d="M94 983q0 113 48 197t134 140t206 84t263 28q66 0 152 -5.5t158 -21.5v-1784h-119v1686q-29 4 -77 7t-87 3q-16 0 -34.5 -1t-28.5 -3v-1692h-119v920q-113 8 -205 38.5t-156.5 85t-99.5 133.5t-35 185z" />
<glyph unicode="&#xb7;" horiz-adv-x="503" d="M139 602q0 47 30 80t83 33t83 -33t30 -80t-30 -80t-83 -33t-83 33t-30 80z" />
<glyph unicode="&#xb8;" horiz-adv-x="770" d="M201 -358l16 88q23 -10 51.5 -14.5t69.5 -4.5q84 0 84 60q0 35 -28.5 53t-90.5 43q6 20 18.5 47t24.5 51.5t23.5 45t17.5 30.5h94q-14 -23 -31.5 -56.5t-27.5 -58.5q57 -25 91 -60.5t34 -94.5q0 -25 -8.5 -52.5t-31 -50t-61 -37t-100.5 -14.5q-41 0 -84 7t-61 18z" />
<glyph unicode="&#xb9;" horiz-adv-x="735" d="M125 1270q72 23 143.5 62.5t122.5 99.5h76v-783h-111v623q-23 -16 -52.5 -30.5t-58 -27t-54 -20.5t-39.5 -10z" />
<glyph unicode="&#xba;" horiz-adv-x="894" d="M94 1061q0 86 26.5 156.5t74 121t112 78t139.5 27.5q76 0 141.5 -27.5t113 -78t74 -121t26.5 -156.5t-26.5 -156.5t-74 -121t-113 -78t-141.5 -27.5t-140 27.5t-111.5 78t-74 121t-26.5 156.5zM215 1061q0 -129 63.5 -206t167.5 -77q106 0 170 77t64 206t-63.5 205.5 t-170.5 76.5q-104 0 -167.5 -76.5t-63.5 -205.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="925" d="M94 201l238 381l-238 379l84 53l311 -432l-311 -435zM456 201l238 381l-238 379l84 53l311 -432l-311 -435z" />
<glyph unicode="&#xbc;" horiz-adv-x="1789" d="M125 1270q72 23 143.5 62.5t122.5 99.5h76v-783h-111v623q-23 -16 -52.5 -30.5t-58 -27t-54 -20.5t-39.5 -10zM421 0l809 1419h136l-809 -1419h-136zM1123 199v67q20 41 62 109t96.5 143.5t117 147t121.5 119.5h96v-496h105v-90h-105v-197h-108v197h-385zM1239 289h269 v367q-35 -35 -74 -82t-75 -97.5t-67.5 -100.5t-52.5 -87z" />
<glyph unicode="&#xbd;" horiz-adv-x="1789" d="M125 1270q72 23 143.5 62.5t122.5 99.5h76v-783h-111v623q-23 -16 -52.5 -30.5t-58 -27t-54 -20.5t-39.5 -10zM378 0l809 1419h136l-809 -1419h-136zM1143 703q35 37 97.5 68.5t154.5 31.5q115 0 178.5 -55t63.5 -166q0 -43 -15.5 -80t-40 -68.5t-56.5 -60.5t-67 -55 q-53 -43 -86.5 -76t-53 -57.5t-27 -45t-7.5 -38.5h392v-99h-508q-2 10 -2 20.5v20.5q0 61 20.5 109.5t52 88.5t70.5 73.5t76 68.5q29 25 53.5 46.5t42.5 45t29.5 50t11.5 61.5q0 68 -41 93.5t-96 25.5q-35 0 -64.5 -9.5t-54.5 -22.5t-42 -26.5t-26 -21.5z" />
<glyph unicode="&#xbe;" horiz-adv-x="1789" d="M92 676l27 96q29 -14 73 -27.5t107 -13.5q117 0 158 40t41 99q0 43 -20.5 72t-53.5 47.5t-77 25.5t-89 7h-27v92h31q37 0 73 5t62.5 20.5t43 40t16.5 65.5q0 59 -38 84t-102 25q-66 0 -110.5 -19.5t-71.5 -38.5l-41 86q23 18 82.5 43t145.5 25q133 0 189 -54.5t56 -140.5 q0 -127 -125 -178q76 -23 123 -74t47 -137q0 -47 -17 -89t-54 -74.5t-95.5 -51t-140.5 -18.5q-78 0 -129 13t-84 30zM452 0l809 1419h136l-809 -1419h-136zM1123 199v67q20 41 62 109t96.5 143.5t117 147t121.5 119.5h96v-496h105v-90h-105v-197h-108v197h-385zM1239 289 h269v367q-35 -35 -74 -82t-75 -97.5t-67.5 -100.5t-52.5 -87z" />
<glyph unicode="&#xbf;" horiz-adv-x="778" d="M61 -92q0 70 26 127t64.5 106t85 96.5t85 96.5t64.5 105.5t26 125.5v17.5t-2 15.5h104q8 -35 8 -76q0 -66 -23.5 -120t-59.5 -101t-76.5 -92t-76.5 -90t-59.5 -93.5t-23.5 -105.5q0 -53 20.5 -88t54 -55.5t79.5 -29.5t98 -9q135 0 250 61l38 -110q-43 -25 -123.5 -47.5 t-179.5 -22.5q-111 0 -183.5 27.5t-116.5 69.5t-61.5 93.5t-17.5 98.5zM336 946q0 47 29.5 80t83.5 33q53 0 82.5 -33t29.5 -80t-29.5 -79.5t-82.5 -32.5t-83 32.5t-30 79.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="1312" d="M20 0q82 221 154 412.5t140.5 362.5t136 329t143.5 315h123q76 -158 143.5 -315.5t136 -328.5t140 -362.5t155.5 -412.5h-153q-39 104 -74 200.5t-68 188.5h-690q-35 -92 -69.5 -188.5t-71.5 -200.5h-146zM350 508h604q-78 211 -151.5 393.5t-151.5 358.5 q-78 -176 -150.5 -358.5t-150.5 -393.5zM373 1798l94 104l324 -311l-68 -78z" />
<glyph unicode="&#xc1;" horiz-adv-x="1312" d="M20 0q82 221 154 412.5t140.5 362.5t136 329t143.5 315h123q76 -158 143.5 -315.5t136 -328.5t140 -362.5t155.5 -412.5h-153q-39 104 -74 200.5t-68 188.5h-690q-35 -92 -69.5 -188.5t-71.5 -200.5h-146zM350 508h604q-78 211 -151.5 393.5t-151.5 358.5 q-78 -176 -150.5 -358.5t-150.5 -393.5zM520 1591l324 311l94 -104l-350 -285z" />
<glyph unicode="&#xc2;" horiz-adv-x="1312" d="M20 0q82 221 154 412.5t140.5 362.5t136 329t143.5 315h123q76 -158 143.5 -315.5t136 -328.5t140 -362.5t155.5 -412.5h-153q-39 104 -74 200.5t-68 188.5h-690q-35 -92 -69.5 -188.5t-71.5 -200.5h-146zM350 508h604q-78 211 -151.5 393.5t-151.5 358.5 q-78 -176 -150.5 -358.5t-150.5 -393.5zM385 1579l270 301l270 -301l-57 -64l-213 197l-213 -197z" />
<glyph unicode="&#xc3;" horiz-adv-x="1312" d="M20 0q82 221 154 412.5t140.5 362.5t136 329t143.5 315h123q76 -158 143.5 -315.5t136 -328.5t140 -362.5t155.5 -412.5h-153q-39 104 -74 200.5t-68 188.5h-690q-35 -92 -69.5 -188.5t-71.5 -200.5h-146zM331 1632q10 25 27.5 53.5t41.5 53t55.5 41t72.5 16.5 q45 0 82 -14.5t72 -30.5q39 -20 63.5 -27.5t44.5 -7.5q43 0 67.5 28.5t45.5 63.5l78 -37q-10 -25 -27.5 -53.5t-41.5 -53t-55.5 -41t-72.5 -16.5q-45 0 -82 14.5t-72 30.5q-39 20 -63.5 27.5t-44.5 7.5q-43 0 -67.5 -28.5t-45.5 -63.5zM350 508h604q-78 211 -151.5 393.5 t-151.5 358.5q-78 -176 -150.5 -358.5t-150.5 -393.5z" />
<glyph unicode="&#xc4;" horiz-adv-x="1312" d="M20 0q82 221 154 412.5t140.5 362.5t136 329t143.5 315h123q76 -158 143.5 -315.5t136 -328.5t140 -362.5t155.5 -412.5h-153q-39 104 -74 200.5t-68 188.5h-690q-35 -92 -69.5 -188.5t-71.5 -200.5h-146zM350 508h604q-78 211 -151.5 393.5t-151.5 358.5 q-78 -176 -150.5 -358.5t-150.5 -393.5zM383 1697q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5t-26.5 71.5zM735 1697q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5t-26.5 71.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1312" d="M20 0q76 209 143.5 390t132 344t128 313.5t133.5 298.5q-45 25 -71.5 69.5t-26.5 104.5q0 88 58 141t138 53q39 0 75 -14t62.5 -40t42 -61.5t15.5 -78.5q0 -59 -27.5 -104.5t-70.5 -69.5q70 -147 133 -298t127.5 -314t133 -344t146.5 -390h-153q-39 104 -74 200.5 t-68 188.5h-690q-35 -92 -69.5 -188.5t-71.5 -200.5h-146zM350 508h604q-78 211 -151.5 393.5t-151.5 358.5q-78 -176 -150.5 -358.5t-150.5 -393.5zM539 1520q0 -59 33.5 -90t82.5 -31t83 31t34 90q0 57 -33.5 88.5t-83.5 31.5q-49 0 -82.5 -31.5t-33.5 -88.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1873" d="M12 0q111 219 220.5 411.5t218 365.5t217 332t219.5 310h854v-121h-651v-493h577v-119h-577v-565h704v-121h-844v389h-588q-53 -98 -105 -194.5t-101 -194.5h-144zM430 508h520v797q-141 -190 -270 -388t-250 -409z" />
<glyph unicode="&#xc7;" horiz-adv-x="1247" d="M127 711q0 178 54.5 316t144.5 232.5t208.5 142.5t247.5 48q80 0 145.5 -10t115 -25.5t84 -31t53.5 -27.5l-45 -121q-51 31 -130 60.5t-196 29.5q-133 0 -232.5 -45t-167 -127t-101.5 -194.5t-34 -249.5q0 -141 34 -255t99.5 -194t161 -123t217.5 -43q135 0 226.5 25.5 t138.5 52.5l39 -119q-14 -10 -49 -23.5t-88.5 -27.5t-123 -23.5t-155.5 -9.5h-10q-6 -10 -10.5 -21.5t-8.5 -21.5q57 -25 91 -60.5t34 -94.5q0 -25 -8 -52.5t-30.5 -50t-61.5 -37t-100 -14.5q-41 0 -84 7t-62 18l17 88q23 -10 51.5 -14.5t69.5 -4.5q84 0 83 60 q0 35 -28.5 53t-89.5 43q8 23 20 52.5t27 57.5q-119 16 -219.5 71.5t-173 149t-113.5 221.5t-41 292z" />
<glyph unicode="&#xc8;" horiz-adv-x="1136" d="M184 0v1419h820v-121h-680v-493h606v-119h-606v-565h733v-121h-873zM311 1798l94 104l324 -311l-68 -78z" />
<glyph unicode="&#xc9;" horiz-adv-x="1136" d="M184 0v1419h820v-121h-680v-493h606v-119h-606v-565h733v-121h-873zM458 1591l324 311l94 -104l-350 -285z" />
<glyph unicode="&#xca;" horiz-adv-x="1136" d="M184 0v1419h820v-121h-680v-493h606v-119h-606v-565h733v-121h-873zM324 1579l270 301l270 -301l-57 -64l-213 197l-213 -197z" />
<glyph unicode="&#xcb;" horiz-adv-x="1136" d="M184 0v1419h820v-121h-680v-493h606v-119h-606v-565h733v-121h-873zM324 1697q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5t-26.5 71.5zM676 1697q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5 t-26.5 71.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="507" d="M-27 1798l94 104l324 -311l-68 -78zM184 0v1419h140v-1419h-140z" />
<glyph unicode="&#xcd;" horiz-adv-x="507" d="M120 1591l324 311l94 -104l-350 -285zM184 0v1419h140v-1419h-140z" />
<glyph unicode="&#xce;" horiz-adv-x="507" d="M-14 1579l270 301l270 -301l-57 -64l-213 197l-213 -197zM184 0v1419h140v-1419h-140z" />
<glyph unicode="&#xcf;" horiz-adv-x="507" d="M-14 1697q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5t-26.5 71.5zM184 0v1419h140v-1419h-140zM338 1697q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5t-26.5 71.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1466" d="M49 686v113h164v594q72 20 166 29.5t180 9.5q172 0 315.5 -45.5t247 -135.5t160.5 -225t57 -315q0 -182 -57 -317.5t-160.5 -225.5t-247 -135t-315.5 -45q-86 0 -180 9t-166 28v661h-164zM352 123q12 -2 62.5 -8t160.5 -6q154 0 268.5 42t192.5 119.5t117 189t39 251.5 q0 139 -39 249.5t-117 188.5t-192.5 120t-268.5 42q-111 0 -160.5 -6.5t-62.5 -8.5v-497h271v-113h-271v-563z" />
<glyph unicode="&#xd1;" horiz-adv-x="1452" d="M184 0v1419h111q104 -117 224 -269.5t234.5 -311t214 -310t162.5 -264.5v1155h138v-1419h-117q-39 72 -99.5 168t-132 205.5t-151.5 225.5t-159 225.5t-153.5 205.5t-133.5 166v-1196h-138zM401 1632q10 25 27.5 53.5t41.5 53t55.5 41t72.5 16.5q45 0 82 -14.5t72 -30.5 q39 -20 63.5 -27.5t44.5 -7.5q43 0 67.5 28.5t45.5 63.5l78 -37q-10 -25 -27.5 -53.5t-41.5 -53t-55.5 -41t-72.5 -16.5q-45 0 -82 14.5t-72 30.5q-39 20 -63.5 27.5t-44.5 7.5q-43 0 -67.5 -28.5t-45.5 -63.5z" />
<glyph unicode="&#xd2;" horiz-adv-x="1585" d="M127 711q0 182 53 320t144.5 231.5t212 140.5t256.5 47q135 0 255.5 -47t212 -140.5t144.5 -231.5t53 -320t-53 -321.5t-144.5 -233t-212 -140.5t-255.5 -47t-256 47t-212.5 140.5t-144.5 232.5t-53 322zM274 711q0 -143 37 -257t104.5 -194t163 -123t214.5 -43t214 43 t162.5 123t104.5 194t37 257t-37 256.5t-104.5 193.5t-163 122t-213.5 42q-119 0 -214.5 -42t-163 -122t-104.5 -193.5t-37 -256.5zM510 1798l94 104l324 -311l-68 -78z" />
<glyph unicode="&#xd3;" horiz-adv-x="1585" d="M127 711q0 182 53 320t144.5 231.5t212 140.5t256.5 47q135 0 255.5 -47t212 -140.5t144.5 -231.5t53 -320t-53 -321.5t-144.5 -233t-212 -140.5t-255.5 -47t-256 47t-212.5 140.5t-144.5 232.5t-53 322zM274 711q0 -143 37 -257t104.5 -194t163 -123t214.5 -43t214 43 t162.5 123t104.5 194t37 257t-37 256.5t-104.5 193.5t-163 122t-213.5 42q-119 0 -214.5 -42t-163 -122t-104.5 -193.5t-37 -256.5zM657 1591l324 311l94 -104l-350 -285z" />
<glyph unicode="&#xd4;" horiz-adv-x="1585" d="M127 711q0 182 53 320t144.5 231.5t212 140.5t256.5 47q135 0 255.5 -47t212 -140.5t144.5 -231.5t53 -320t-53 -321.5t-144.5 -233t-212 -140.5t-255.5 -47t-256 47t-212.5 140.5t-144.5 232.5t-53 322zM274 711q0 -143 37 -257t104.5 -194t163 -123t214.5 -43t214 43 t162.5 123t104.5 194t37 257t-37 256.5t-104.5 193.5t-163 122t-213.5 42q-119 0 -214.5 -42t-163 -122t-104.5 -193.5t-37 -256.5zM523 1579l270 301l270 -301l-57 -64l-213 197l-213 -197z" />
<glyph unicode="&#xd5;" horiz-adv-x="1585" d="M127 711q0 182 53 320t144.5 231.5t212 140.5t256.5 47q135 0 255.5 -47t212 -140.5t144.5 -231.5t53 -320t-53 -321.5t-144.5 -233t-212 -140.5t-255.5 -47t-256 47t-212.5 140.5t-144.5 232.5t-53 322zM274 711q0 -143 37 -257t104.5 -194t163 -123t214.5 -43t214 43 t162.5 123t104.5 194t37 257t-37 256.5t-104.5 193.5t-163 122t-213.5 42q-119 0 -214.5 -42t-163 -122t-104.5 -193.5t-37 -256.5zM469 1632q10 25 27.5 53.5t41.5 53t55.5 41t72.5 16.5q45 0 82 -14.5t72 -30.5q39 -20 63.5 -27.5t44.5 -7.5q43 0 67.5 28.5t45.5 63.5 l78 -37q-10 -25 -27.5 -53.5t-41.5 -53t-55.5 -41t-72.5 -16.5q-45 0 -82 14.5t-72 30.5q-39 20 -63.5 27.5t-44.5 7.5q-43 0 -67.5 -28.5t-45.5 -63.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="1585" d="M127 711q0 182 53 320t144.5 231.5t212 140.5t256.5 47q135 0 255.5 -47t212 -140.5t144.5 -231.5t53 -320t-53 -321.5t-144.5 -233t-212 -140.5t-255.5 -47t-256 47t-212.5 140.5t-144.5 232.5t-53 322zM274 711q0 -143 37 -257t104.5 -194t163 -123t214.5 -43t214 43 t162.5 123t104.5 194t37 257t-37 256.5t-104.5 193.5t-163 122t-213.5 42q-119 0 -214.5 -42t-163 -122t-104.5 -193.5t-37 -256.5zM523 1697q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5t-26.5 71.5zM875 1697q0 45 26.5 72t67.5 27 t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5t-26.5 71.5z" />
<glyph unicode="&#xd7;" d="M184 289l316 313l-316 313l80 80l314 -315l313 315l80 -80l-316 -313l316 -313l-80 -80l-313 315l-314 -315z" />
<glyph unicode="&#xd8;" horiz-adv-x="1585" d="M127 711q0 182 53 320t144.5 231.5t212 140.5t256.5 47q111 0 212 -32t187 -97l131 170l98 -74l-139 -180q82 -92 129 -223t47 -303q0 -182 -53 -321.5t-144.5 -233t-212 -140.5t-255.5 -47q-111 0 -210.5 31t-185.5 94l-125 -164l-98 74l133 172q-86 92 -133 226.5 t-47 308.5zM274 711q0 -131 31 -238.5t88 -185.5l719 930q-133 109 -319 108q-119 0 -214.5 -42t-163 -122t-104.5 -193.5t-37 -256.5zM477 197q127 -102 316 -103q119 0 214 43t162.5 123t104.5 194t37 257q0 129 -30 233.5t-85 181.5z" />
<glyph unicode="&#xd9;" horiz-adv-x="1392" d="M178 526v893h139v-874q0 -117 28 -202t77 -140.5t118.5 -82t155.5 -26.5t156 26.5t119 82t76.5 140.5t27.5 202v874h139v-893q0 -117 -27.5 -218t-89 -177t-161 -119t-240.5 -43t-240.5 43t-161 119t-89 177.5t-27.5 217.5zM414 1798l94 104l324 -311l-68 -78z" />
<glyph unicode="&#xda;" horiz-adv-x="1392" d="M178 526v893h139v-874q0 -117 28 -202t77 -140.5t118.5 -82t155.5 -26.5t156 26.5t119 82t76.5 140.5t27.5 202v874h139v-893q0 -117 -27.5 -218t-89 -177t-161 -119t-240.5 -43t-240.5 43t-161 119t-89 177.5t-27.5 217.5zM561 1591l324 311l94 -104l-350 -285z" />
<glyph unicode="&#xdb;" horiz-adv-x="1392" d="M178 526v893h139v-874q0 -117 28 -202t77 -140.5t118.5 -82t155.5 -26.5t156 26.5t119 82t76.5 140.5t27.5 202v874h139v-893q0 -117 -27.5 -218t-89 -177t-161 -119t-240.5 -43t-240.5 43t-161 119t-89 177.5t-27.5 217.5zM426 1579l270 301l270 -301l-57 -64l-213 197 l-213 -197z" />
<glyph unicode="&#xdc;" horiz-adv-x="1392" d="M178 526v893h139v-874q0 -117 28 -202t77 -140.5t118.5 -82t155.5 -26.5t156 26.5t119 82t76.5 140.5t27.5 202v874h139v-893q0 -117 -27.5 -218t-89 -177t-161 -119t-240.5 -43t-240.5 43t-161 119t-89 177.5t-27.5 217.5zM426 1697q0 45 26.5 72t67.5 27t67.5 -27 t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5t-26.5 71.5zM778 1697q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5t-26.5 71.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1165" d="M20 1419h162q86 -180 189.5 -365.5t216.5 -353.5q109 168 213 353.5t192 365.5h152q-109 -195 -229.5 -404.5t-262.5 -426.5v-588h-139v584q-145 223 -267 431t-227 404zM448 1591l324 311l94 -104l-350 -285z" />
<glyph unicode="&#xde;" horiz-adv-x="1214" d="M184 0v1419h140v-252q35 4 99 8.5t116 4.5q303 0 451.5 -116t148.5 -325q0 -121 -43 -205.5t-123 -137t-195.5 -76t-261.5 -23.5h-192v-297h-140zM324 418h176q113 0 204 13t155.5 49t99 99.5t34.5 161.5q0 94 -37.5 155.5t-100 97.5t-143.5 50.5t-167 14.5 q-74 0 -130.5 -4t-90.5 -8v-629z" />
<glyph unicode="&#xdf;" horiz-adv-x="1220" d="M176 0v1155q0 98 28.5 179t83 137.5t130.5 87t170 30.5q201 0 293 -89t92 -232q0 -53 -17.5 -97.5t-43 -79t-54.5 -63.5l-49 -49q-29 -29 -48.5 -50.5t-31.5 -40t-17 -36.5t-5 -41q0 -29 13 -51.5t34.5 -38.5t48 -29.5t53.5 -25.5q61 -29 115.5 -60t94.5 -71.5t62.5 -98 t22.5 -139.5q0 -154 -104.5 -238t-290.5 -84q-100 0 -172 21.5t-92 32.5l28 123q16 -10 79 -35t181 -25q111 0 172.5 55.5t61.5 149.5q0 45 -11.5 79t-38 62.5t-67.5 54t-98 56.5q-68 35 -112 61.5t-69.5 54t-36 58.5t-10.5 76q0 70 40 123t94 108q31 33 56 58.5t42.5 52 t27 55.5t9.5 66q0 92 -63.5 150t-172.5 58q-158 0 -226.5 -88t-68.5 -235v-1147h-133z" />
<glyph unicode="&#xe0;" horiz-adv-x="1042" d="M98 309q0 86 33 147.5t92.5 101.5t141 58.5t178.5 18.5q29 0 60.5 -3t61 -8.5t51 -10.5t29.5 -9v66q0 55 -8 107t-35.5 94t-78 68t-131.5 26q-117 0 -174.5 -16.5t-84.5 -26.5l-18 116q35 16 108 30.5t175 14.5q104 0 177 -29.5t118 -81.5t65.5 -124t20.5 -156v-667 q-20 -6 -59.5 -13.5t-90.5 -15.5t-113.5 -13.5t-128.5 -5.5q-84 0 -154.5 18.5t-122.5 58.5t-82 102.5t-30 152.5zM236 309q0 -59 19 -100t55 -65.5t86.5 -36t113.5 -11.5q78 0 136.5 5t98.5 14v372q-23 10 -71.5 21.5t-134.5 11.5q-49 0 -102.5 -7t-98.5 -30.5t-73.5 -64.5 t-28.5 -109zM250 1479l94 104l324 -311l-68 -78z" />
<glyph unicode="&#xe1;" horiz-adv-x="1042" d="M98 309q0 86 33 147.5t92.5 101.5t141 58.5t178.5 18.5q29 0 60.5 -3t61 -8.5t51 -10.5t29.5 -9v66q0 55 -8 107t-35.5 94t-78 68t-131.5 26q-117 0 -174.5 -16.5t-84.5 -26.5l-18 116q35 16 108 30.5t175 14.5q104 0 177 -29.5t118 -81.5t65.5 -124t20.5 -156v-667 q-20 -6 -59.5 -13.5t-90.5 -15.5t-113.5 -13.5t-128.5 -5.5q-84 0 -154.5 18.5t-122.5 58.5t-82 102.5t-30 152.5zM236 309q0 -59 19 -100t55 -65.5t86.5 -36t113.5 -11.5q78 0 136.5 5t98.5 14v372q-23 10 -71.5 21.5t-134.5 11.5q-49 0 -102.5 -7t-98.5 -30.5t-73.5 -64.5 t-28.5 -109zM397 1272l324 311l94 -104l-350 -285z" />
<glyph unicode="&#xe2;" horiz-adv-x="1042" d="M98 309q0 86 33 147.5t92.5 101.5t141 58.5t178.5 18.5q29 0 60.5 -3t61 -8.5t51 -10.5t29.5 -9v66q0 55 -8 107t-35.5 94t-78 68t-131.5 26q-117 0 -174.5 -16.5t-84.5 -26.5l-18 116q35 16 108 30.5t175 14.5q104 0 177 -29.5t118 -81.5t65.5 -124t20.5 -156v-667 q-20 -6 -59.5 -13.5t-90.5 -15.5t-113.5 -13.5t-128.5 -5.5q-84 0 -154.5 18.5t-122.5 58.5t-82 102.5t-30 152.5zM236 309q0 -59 19 -100t55 -65.5t86.5 -36t113.5 -11.5q78 0 136.5 5t98.5 14v372q-23 10 -71.5 21.5t-134.5 11.5q-49 0 -102.5 -7t-98.5 -30.5t-73.5 -64.5 t-28.5 -109zM262 1260l270 301l270 -301l-57 -64l-213 197l-213 -197z" />
<glyph unicode="&#xe3;" horiz-adv-x="1042" d="M98 309q0 86 33 147.5t92.5 101.5t141 58.5t178.5 18.5q29 0 60.5 -3t61 -8.5t51 -10.5t29.5 -9v66q0 55 -8 107t-35.5 94t-78 68t-131.5 26q-117 0 -174.5 -16.5t-84.5 -26.5l-18 116q35 16 108 30.5t175 14.5q104 0 177 -29.5t118 -81.5t65.5 -124t20.5 -156v-667 q-20 -6 -59.5 -13.5t-90.5 -15.5t-113.5 -13.5t-128.5 -5.5q-84 0 -154.5 18.5t-122.5 58.5t-82 102.5t-30 152.5zM208 1313q10 25 27.5 53.5t41.5 53t55.5 41t72.5 16.5q45 0 82 -14.5t72 -30.5q39 -20 63.5 -27.5t44.5 -7.5q43 0 67.5 28.5t45.5 63.5l78 -37 q-10 -25 -27.5 -53.5t-41.5 -53t-55.5 -41t-72.5 -16.5q-45 0 -82 14.5t-72 30.5q-39 20 -63.5 27.5t-44.5 7.5q-43 0 -67.5 -28.5t-45.5 -63.5zM236 309q0 -59 19 -100t55 -65.5t86.5 -36t113.5 -11.5q78 0 136.5 5t98.5 14v372q-23 10 -71.5 21.5t-134.5 11.5 q-49 0 -102.5 -7t-98.5 -30.5t-73.5 -64.5t-28.5 -109z" />
<glyph unicode="&#xe4;" horiz-adv-x="1042" d="M98 309q0 86 33 147.5t92.5 101.5t141 58.5t178.5 18.5q29 0 60.5 -3t61 -8.5t51 -10.5t29.5 -9v66q0 55 -8 107t-35.5 94t-78 68t-131.5 26q-117 0 -174.5 -16.5t-84.5 -26.5l-18 116q35 16 108 30.5t175 14.5q104 0 177 -29.5t118 -81.5t65.5 -124t20.5 -156v-667 q-20 -6 -59.5 -13.5t-90.5 -15.5t-113.5 -13.5t-128.5 -5.5q-84 0 -154.5 18.5t-122.5 58.5t-82 102.5t-30 152.5zM236 309q0 -59 19 -100t55 -65.5t86.5 -36t113.5 -11.5q78 0 136.5 5t98.5 14v372q-23 10 -71.5 21.5t-134.5 11.5q-49 0 -102.5 -7t-98.5 -30.5t-73.5 -64.5 t-28.5 -109zM262 1378q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5t-26.5 71.5zM614 1378q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5t-26.5 71.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1042" d="M98 309q0 86 33 147.5t92.5 101.5t141 58.5t178.5 18.5q29 0 60.5 -3t61 -8.5t51 -10.5t29.5 -9v66q0 55 -8 107t-35.5 94t-78 68t-131.5 26q-117 0 -174.5 -16.5t-84.5 -26.5l-18 116q35 16 108 30.5t175 14.5q104 0 177 -29.5t118 -81.5t65.5 -124t20.5 -156v-667 q-20 -6 -59.5 -13.5t-90.5 -15.5t-113.5 -13.5t-128.5 -5.5q-84 0 -154.5 18.5t-122.5 58.5t-82 102.5t-30 152.5zM236 309q0 -59 19 -100t55 -65.5t86.5 -36t113.5 -11.5q78 0 136.5 5t98.5 14v372q-23 10 -71.5 21.5t-134.5 11.5q-49 0 -102.5 -7t-98.5 -30.5t-73.5 -64.5 t-28.5 -109zM338 1384q0 88 58.5 141.5t138.5 53.5q39 0 75 -14.5t62.5 -40t42 -61.5t15.5 -79q0 -45 -15.5 -80.5t-42 -61t-62.5 -40t-75 -14.5q-80 0 -138.5 53t-58.5 143zM418 1384q0 -59 34 -89.5t83 -30.5t83 30.5t34 89.5q0 57 -34 89t-83 32t-83 -31.5t-34 -89.5z " />
<glyph unicode="&#xe6;" horiz-adv-x="1748" d="M98 309q0 86 33 147.5t92.5 101.5t141 58.5t178.5 18.5q29 0 60.5 -3t61 -8.5t51 -10.5t29.5 -9v66q0 55 -8 107t-35.5 94t-78 68t-131.5 26q-111 0 -172.5 -15.5t-88.5 -25.5l-16 114q35 16 108.5 30.5t174.5 14.5q145 0 228 -54t118 -146q68 100 162 150t196 50 q195 0 310.5 -127t115.5 -391q0 -16 -1 -32.5t-3 -30.5h-743q6 -195 96 -299.5t287 -104.5q109 0 172 20.5t92 35.5l24 -115q-29 -16 -107.5 -39t-184.5 -23q-117 0 -207 29t-158 94q-23 -18 -56.5 -39.5t-80.5 -40t-106.5 -31t-135.5 -12.5q-90 0 -167 18.5t-132 58.5 t-87 102.5t-32 152.5zM236 309q0 -59 20 -100t58 -65.5t89.5 -36t112.5 -11.5q127 0 201 34t102 60q-33 53 -51 126t-20 171q-23 10 -73 21.5t-136 11.5q-49 0 -102.5 -7t-98.5 -30.5t-73.5 -64.5t-28.5 -109zM885 614h606q-4 166 -78 258.5t-209 92.5q-72 0 -128 -29 t-98 -77t-65.5 -111.5t-27.5 -133.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="942" d="M113 526q0 125 33.5 227.5t97 176.5t153.5 113.5t203 39.5q86 0 161 -14t116 -37l-35 -114q-41 20 -92.5 32.5t-137.5 12.5q-176 0 -267 -114t-91 -323q0 -94 20.5 -173t66.5 -135t118.5 -88t177.5 -32q86 0 144.5 19.5t84.5 34.5l29 -115q-35 -20 -110.5 -38.5 t-159.5 -21.5q-6 -14 -12.5 -27.5t-10.5 -23.5q57 -25 91 -60.5t34 -94.5q0 -25 -8 -52.5t-30.5 -50t-61.5 -37t-101 -14.5q-41 0 -84 7t-61 18l16 88q23 -10 51.5 -14.5t69.5 -4.5q84 0 84 60q0 35 -28.5 53t-90.5 43q8 25 21.5 56.5t30.5 60.5q-207 25 -314.5 170 t-107.5 372z" />
<glyph unicode="&#xe8;" horiz-adv-x="1122" d="M113 530q0 139 40 243t104.5 173.5t147 103t170.5 33.5q195 0 310.5 -127t115.5 -391q0 -16 -1 -32.5t-3 -30.5h-743q6 -195 96 -299.5t287 -104.5q109 0 172 20.5t92 35.5l25 -115q-29 -16 -108 -39t-185 -23q-139 0 -237.5 41t-162 115t-92 175t-28.5 222zM258 614h606 q-4 166 -77.5 258.5t-208.5 92.5q-72 0 -128.5 -29t-98.5 -77t-65.5 -111.5t-27.5 -133.5zM291 1479l94 104l324 -311l-68 -78z" />
<glyph unicode="&#xe9;" horiz-adv-x="1122" d="M113 530q0 139 40 243t104.5 173.5t147 103t170.5 33.5q195 0 310.5 -127t115.5 -391q0 -16 -1 -32.5t-3 -30.5h-743q6 -195 96 -299.5t287 -104.5q109 0 172 20.5t92 35.5l25 -115q-29 -16 -108 -39t-185 -23q-139 0 -237.5 41t-162 115t-92 175t-28.5 222zM258 614h606 q-4 166 -77.5 258.5t-208.5 92.5q-72 0 -128.5 -29t-98.5 -77t-65.5 -111.5t-27.5 -133.5zM438 1272l324 311l94 -104l-350 -285z" />
<glyph unicode="&#xea;" horiz-adv-x="1122" d="M113 530q0 139 40 243t104.5 173.5t147 103t170.5 33.5q195 0 310.5 -127t115.5 -391q0 -16 -1 -32.5t-3 -30.5h-743q6 -195 96 -299.5t287 -104.5q109 0 172 20.5t92 35.5l25 -115q-29 -16 -108 -39t-185 -23q-139 0 -237.5 41t-162 115t-92 175t-28.5 222zM258 614h606 q-4 166 -77.5 258.5t-208.5 92.5q-72 0 -128.5 -29t-98.5 -77t-65.5 -111.5t-27.5 -133.5zM303 1260l270 301l270 -301l-57 -64l-213 197l-213 -197z" />
<glyph unicode="&#xeb;" horiz-adv-x="1122" d="M113 530q0 139 40 243t104.5 173.5t147 103t170.5 33.5q195 0 310.5 -127t115.5 -391q0 -16 -1 -32.5t-3 -30.5h-743q6 -195 96 -299.5t287 -104.5q109 0 172 20.5t92 35.5l25 -115q-29 -16 -108 -39t-185 -23q-139 0 -237.5 41t-162 115t-92 175t-28.5 222zM258 614h606 q-4 166 -77.5 258.5t-208.5 92.5q-72 0 -128.5 -29t-98.5 -77t-65.5 -111.5t-27.5 -133.5zM303 1378q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5t-26.5 71.5zM655 1378q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5 t-67.5 26.5t-26.5 71.5z" />
<glyph unicode="&#xec;" horiz-adv-x="485" d="M-39 1479l94 104l324 -311l-68 -78zM176 0v1059h133v-1059h-133z" />
<glyph unicode="&#xed;" horiz-adv-x="485" d="M108 1272l324 311l94 -104l-350 -285zM176 0v1059h133v-1059h-133z" />
<glyph unicode="&#xee;" horiz-adv-x="485" d="M-26 1260l270 301l270 -301l-57 -64l-213 197l-213 -197zM176 0v1059h133v-1059h-133z" />
<glyph unicode="&#xef;" horiz-adv-x="485" d="M-26 1378q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5t-26.5 71.5zM176 0v1059h133v-1059h-133zM326 1378q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5t-26.5 71.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1183" d="M113 502q0 119 34.5 211t97 155.5t149.5 96t192 32.5q61 0 111.5 -11t90 -27.5t69.5 -35t46 -32.5q-10 86 -45 180t-94 184l-268 -94l-35 92l241 84q-84 100 -200 172l88 80q137 -84 235 -207l246 86l35 -92l-219 -78q84 -129 129 -284.5t45 -337.5q0 -123 -19.5 -248 t-74 -225.5t-150.5 -164t-250 -63.5q-106 0 -190 42t-142.5 114t-90 167t-31.5 204zM252 502q0 -82 20.5 -156t60.5 -129t98 -88t136 -33q111 0 179.5 50.5t107.5 132t53.5 186t14.5 213.5v35.5t-2 36.5q-72 72 -152 100.5t-166 28.5q-92 0 -158.5 -30t-108.5 -80 t-62.5 -118.5t-20.5 -148.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="1165" d="M176 0v1022q59 16 159.5 36.5t246.5 20.5q121 0 201.5 -34.5t129 -99t68.5 -155t20 -198.5v-592h-133v549q0 113 -15 190.5t-51 126.5t-94.5 70.5t-146.5 21.5q-94 0 -162.5 -10t-89.5 -18v-930h-133zM264 1313q10 25 27.5 53.5t41.5 53t55.5 41t72.5 16.5q45 0 82 -14.5 t72 -30.5q39 -20 63.5 -27.5t44.5 -7.5q43 0 67.5 28.5t45.5 63.5l78 -37q-10 -25 -27.5 -53.5t-41.5 -53t-55.5 -41t-72.5 -16.5q-45 0 -82 14.5t-72 30.5q-39 20 -63.5 27.5t-44.5 7.5q-43 0 -67.5 -28.5t-45.5 -63.5z" />
<glyph unicode="&#xf2;" horiz-adv-x="1191" d="M113 528q0 125 35.5 227.5t100 175.5t152.5 112.5t195 39.5q106 0 194.5 -39.5t153 -112.5t100 -175.5t35.5 -227.5t-35.5 -227t-100 -174t-153 -112t-194.5 -40t-194.5 40t-153 112t-100 174t-35.5 227zM254 528q0 -199 92 -315.5t250 -116.5t250 116.5t92 315.5 t-92 317t-250 118t-250 -118t-92 -317zM313 1479l94 104l324 -311l-68 -78z" />
<glyph unicode="&#xf3;" horiz-adv-x="1191" d="M113 528q0 125 35.5 227.5t100 175.5t152.5 112.5t195 39.5q106 0 194.5 -39.5t153 -112.5t100 -175.5t35.5 -227.5t-35.5 -227t-100 -174t-153 -112t-194.5 -40t-194.5 40t-153 112t-100 174t-35.5 227zM254 528q0 -199 92 -315.5t250 -116.5t250 116.5t92 315.5 t-92 317t-250 118t-250 -118t-92 -317zM460 1272l324 311l94 -104l-350 -285z" />
<glyph unicode="&#xf4;" horiz-adv-x="1191" d="M113 528q0 125 35.5 227.5t100 175.5t152.5 112.5t195 39.5q106 0 194.5 -39.5t153 -112.5t100 -175.5t35.5 -227.5t-35.5 -227t-100 -174t-153 -112t-194.5 -40t-194.5 40t-153 112t-100 174t-35.5 227zM254 528q0 -199 92 -315.5t250 -116.5t250 116.5t92 315.5 t-92 317t-250 118t-250 -118t-92 -317zM326 1260l270 301l270 -301l-57 -64l-213 197l-213 -197z" />
<glyph unicode="&#xf5;" horiz-adv-x="1191" d="M113 528q0 125 35.5 227.5t100 175.5t152.5 112.5t195 39.5q106 0 194.5 -39.5t153 -112.5t100 -175.5t35.5 -227.5t-35.5 -227t-100 -174t-153 -112t-194.5 -40t-194.5 40t-153 112t-100 174t-35.5 227zM254 528q0 -199 92 -315.5t250 -116.5t250 116.5t92 315.5 t-92 317t-250 118t-250 -118t-92 -317zM272 1313q10 25 27.5 53.5t41.5 53t55.5 41t72.5 16.5q45 0 82 -14.5t72 -30.5q39 -20 63.5 -27.5t44.5 -7.5q43 0 67.5 28.5t45.5 63.5l78 -37q-10 -25 -27.5 -53.5t-41.5 -53t-55.5 -41t-72.5 -16.5q-45 0 -82 14.5t-72 30.5 q-39 20 -63.5 27.5t-44.5 7.5q-43 0 -67.5 -28.5t-45.5 -63.5z" />
<glyph unicode="&#xf6;" horiz-adv-x="1191" d="M113 528q0 125 35.5 227.5t100 175.5t152.5 112.5t195 39.5q106 0 194.5 -39.5t153 -112.5t100 -175.5t35.5 -227.5t-35.5 -227t-100 -174t-153 -112t-194.5 -40t-194.5 40t-153 112t-100 174t-35.5 227zM254 528q0 -199 92 -315.5t250 -116.5t250 116.5t92 315.5 t-92 317t-250 118t-250 -118t-92 -317zM326 1378q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5t-26.5 71.5zM678 1378q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5t-26.5 71.5z" />
<glyph unicode="&#xf7;" d="M115 535v114h925v-114h-925zM479 205q0 45 28 73.5t68 28.5q43 0 71 -28.5t28 -73.5q0 -43 -28 -72t-71 -29q-41 0 -68.5 29t-27.5 72zM479 977q0 43 28 71.5t68 28.5q43 0 71 -28.5t28 -71.5q0 -45 -28 -74t-71 -29q-41 0 -68.5 29t-27.5 74z" />
<glyph unicode="&#xf8;" horiz-adv-x="1191" d="M113 528q0 125 35.5 227.5t100 175.5t152.5 112.5t195 39.5q166 0 289 -98l104 137l86 -63l-114 -150q57 -72 87.5 -168t30.5 -213q0 -125 -35.5 -227t-100 -174t-153 -112t-194.5 -40q-164 0 -285 93l-104 -138l-88 66l114 147q-59 72 -89.5 169.5t-30.5 215.5zM254 528 q0 -164 63 -274l490 631q-41 37 -94.5 57.5t-116.5 20.5q-158 0 -250 -118t-92 -317zM389 168q84 -72 207 -72q158 0 250 116.5t92 315.5q0 160 -64 271z" />
<glyph unicode="&#xf9;" horiz-adv-x="1165" d="M164 465v594h133v-551q0 -113 16.5 -190.5t53 -126t95 -70t142.5 -21.5q94 0 164 10.5t88 18.5v930h133v-1022q-59 -16 -159.5 -36.5t-245.5 -20.5q-119 0 -199 34.5t-129 98t-70.5 153.5t-21.5 199zM293 1479l94 104l324 -311l-68 -78z" />
<glyph unicode="&#xfa;" horiz-adv-x="1165" d="M164 465v594h133v-551q0 -113 16.5 -190.5t53 -126t95 -70t142.5 -21.5q94 0 164 10.5t88 18.5v930h133v-1022q-59 -16 -159.5 -36.5t-245.5 -20.5q-119 0 -199 34.5t-129 98t-70.5 153.5t-21.5 199zM440 1272l324 311l94 -104l-350 -285z" />
<glyph unicode="&#xfb;" horiz-adv-x="1165" d="M164 465v594h133v-551q0 -113 16.5 -190.5t53 -126t95 -70t142.5 -21.5q94 0 164 10.5t88 18.5v930h133v-1022q-59 -16 -159.5 -36.5t-245.5 -20.5q-119 0 -199 34.5t-129 98t-70.5 153.5t-21.5 199zM305 1260l270 301l270 -301l-57 -64l-213 197l-213 -197z" />
<glyph unicode="&#xfc;" horiz-adv-x="1165" d="M164 465v594h133v-551q0 -113 16.5 -190.5t53 -126t95 -70t142.5 -21.5q94 0 164 10.5t88 18.5v930h133v-1022q-59 -16 -159.5 -36.5t-245.5 -20.5q-119 0 -199 34.5t-129 98t-70.5 153.5t-21.5 199zM305 1378q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5 t-67.5 -26.5t-67.5 26.5t-26.5 71.5zM657 1378q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5t-26.5 71.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="970" d="M2 -354l27 110q8 -6 46 -17t79 -11q57 0 103 12t83 42t66.5 76.5t58.5 118.5q-68 125 -130.5 260.5t-116.5 273.5t-99 276.5t-78 271.5h141q23 -98 57.5 -212t78.5 -234.5t97.5 -247.5t114.5 -250q45 127 80 239.5t65 224t57.5 228.5t58.5 252h133q-66 -297 -150 -577.5 t-182 -514.5q-39 -92 -80 -158.5t-91 -109.5t-116.5 -63.5t-157.5 -20.5q-51 0 -93 12.5t-52 18.5zM372 1272l324 311l94 -104l-350 -285z" />
<glyph unicode="&#xfe;" horiz-adv-x="1193" d="M176 -379v1944l133 24v-561q131 51 260 51q115 0 209 -39t162 -110.5t104.5 -174t36.5 -229.5q0 -121 -31.5 -222t-93 -174t-150.5 -114t-204 -41q-104 0 -181 30t-112 56v-440h-133zM309 190q18 -14 46 -30.5t65 -30.5t80 -23.5t90 -9.5q98 0 164.5 34t107.5 92.5 t59.5 137t18.5 166.5q0 207 -105.5 319.5t-277.5 112.5q-86 0 -155.5 -18t-92.5 -29v-721z" />
<glyph unicode="&#xff;" horiz-adv-x="970" d="M2 -354l27 110q8 -6 46 -17t79 -11q57 0 103 12t83 42t66.5 76.5t58.5 118.5q-68 125 -130.5 260.5t-116.5 273.5t-99 276.5t-78 271.5h141q23 -98 57.5 -212t78.5 -234.5t97.5 -247.5t114.5 -250q45 127 80 239.5t65 224t57.5 228.5t58.5 252h133q-66 -297 -150 -577.5 t-182 -514.5q-39 -92 -80 -158.5t-91 -109.5t-116.5 -63.5t-157.5 -20.5q-51 0 -93 12.5t-52 18.5zM238 1378q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5t-26.5 71.5zM590 1378q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5 t-67.5 -26.5t-67.5 26.5t-26.5 71.5z" />
<glyph unicode="&#x152;" horiz-adv-x="2027" d="M133 711q0 180 56.5 315t159 225t242.5 135.5t310 45.5q49 0 101.5 -3.5t101.5 -9.5h790v-121h-651v-493h578v-119h-578v-565h705v-121h-842q-102 -12 -205 -12q-170 0 -310 45t-242.5 135t-159 225t-56.5 318zM281 711q0 -139 37.5 -251t113.5 -189.5t189.5 -119.5 t263.5 -42q111 0 159 6t60 8v1173q-12 2 -60.5 8.5t-158.5 6.5q-150 0 -263.5 -42t-189.5 -120t-113.5 -188.5t-37.5 -249.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1933" d="M113 528q0 125 35.5 227.5t100 175.5t153 112.5t194.5 39.5q143 0 244.5 -70.5t158.5 -195.5q59 133 162 199.5t225 66.5q195 0 310.5 -127t115.5 -391q0 -16 -1 -32.5t-3 -30.5h-743q6 -195 96 -299.5t287 -104.5q109 0 172 20.5t92 35.5l25 -115q-29 -16 -108 -39 t-185 -23q-184 0 -288.5 68t-158.5 186q-59 -121 -159.5 -188.5t-241.5 -67.5q-106 0 -194.5 40t-153 112t-100 174t-35.5 227zM254 528q0 -199 92 -315.5t250 -116.5t250 116.5t92 315.5t-92 317t-250 118t-250 -118t-92 -317zM1069 614h606q-4 166 -77.5 258.5 t-208.5 92.5q-72 0 -128.5 -29t-98.5 -77t-65.5 -111.5t-27.5 -133.5z" />
<glyph unicode="&#x178;" horiz-adv-x="1165" d="M20 1419h162q86 -180 189.5 -365.5t216.5 -353.5q109 168 213 353.5t192 365.5h152q-109 -195 -229.5 -404.5t-262.5 -426.5v-588h-139v584q-145 223 -267 431t-227 404zM314 1697q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5 t-26.5 71.5zM666 1697q0 45 26.5 72t67.5 27t67.5 -27t26.5 -72t-26.5 -71.5t-67.5 -26.5t-67.5 26.5t-26.5 71.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="770" d="M115 1260l270 301l270 -301l-57 -64l-213 197l-213 -197z" />
<glyph unicode="&#x2dc;" horiz-adv-x="770" d="M61 1313q10 25 27.5 53.5t41.5 53t55.5 41t72.5 16.5q45 0 82 -14.5t72 -30.5q39 -20 63.5 -27.5t44.5 -7.5q43 0 67.5 28.5t45.5 63.5l78 -37q-10 -25 -27.5 -53.5t-41.5 -53t-55.5 -41t-72.5 -16.5q-45 0 -82 14.5t-72 30.5q-39 20 -63.5 27.5t-44.5 7.5 q-43 0 -67.5 -28.5t-45.5 -63.5z" />
<glyph unicode="&#x2000;" horiz-adv-x="951" />
<glyph unicode="&#x2001;" horiz-adv-x="1902" />
<glyph unicode="&#x2002;" horiz-adv-x="951" />
<glyph unicode="&#x2003;" horiz-adv-x="1902" />
<glyph unicode="&#x2004;" horiz-adv-x="634" />
<glyph unicode="&#x2005;" horiz-adv-x="475" />
<glyph unicode="&#x2006;" horiz-adv-x="317" />
<glyph unicode="&#x2007;" horiz-adv-x="317" />
<glyph unicode="&#x2008;" horiz-adv-x="237" />
<glyph unicode="&#x2009;" horiz-adv-x="380" />
<glyph unicode="&#x200a;" horiz-adv-x="105" />
<glyph unicode="&#x2010;" horiz-adv-x="577" d="M57 528v129h463v-129h-463z" />
<glyph unicode="&#x2011;" horiz-adv-x="577" d="M57 528v129h463v-129h-463z" />
<glyph unicode="&#x2012;" horiz-adv-x="577" d="M57 528v129h463v-129h-463z" />
<glyph unicode="&#x2013;" horiz-adv-x="1015" d="M-4 535v116h1024v-116h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2039" d="M-4 535v116h2048v-116h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="471" d="M152 1090v20q0 117 34.5 222.5t104.5 209.5l98 -45q-51 -98 -70.5 -180t-19.5 -154v-39t2 -34h-149z" />
<glyph unicode="&#x2019;" horiz-adv-x="471" d="M82 1110q51 98 70.5 180t19.5 154v39t-2 35h149v-21q0 -117 -34.5 -222.5t-104.5 -209.5z" />
<glyph unicode="&#x201a;" horiz-adv-x="471" d="M82 -213q51 98 70.5 180t19.5 154v39t-2 35h149v-21q0 -117 -34.5 -222.5t-104.5 -209.5z" />
<glyph unicode="&#x201c;" horiz-adv-x="802" d="M152 1090v20q0 117 34.5 222.5t104.5 209.5l98 -45q-51 -98 -70.5 -180t-19.5 -154v-39t2 -34h-149zM484 1090v20q0 117 34.5 222.5t104.5 209.5l98 -45q-51 -98 -70.5 -180t-19.5 -154v-39t2 -34h-149z" />
<glyph unicode="&#x201d;" horiz-adv-x="802" d="M82 1110q51 98 70.5 180t19.5 154v39t-2 35h149v-21q0 -117 -34.5 -222.5t-104.5 -209.5zM414 1110q51 98 70.5 180t19.5 154v39t-2 35h149v-21q0 -117 -34.5 -222.5t-104.5 -209.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="802" d="M82 -213q51 98 70.5 180t19.5 154v39t-2 35h149v-21q0 -117 -34.5 -222.5t-104.5 -209.5zM414 -213q51 98 70.5 180t19.5 154v39t-2 35h149v-21q0 -117 -34.5 -222.5t-104.5 -209.5z" />
<glyph unicode="&#x2022;" horiz-adv-x="737" d="M123 723q0 49 16.5 93t48 78t77.5 54.5t104 20.5q57 0 102 -20.5t77 -54.5t49 -78t17 -93t-17 -94t-49 -79t-77 -53.5t-102 -19.5t-103.5 19.5t-78 53.5t-48 79t-16.5 94z" />
<glyph unicode="&#x2026;" horiz-adv-x="2039" d="M225 88q0 47 30 80t83 33t83 -33t30 -80t-30 -80t-83 -33t-83 33t-30 80zM907 88q0 47 30 80t83 33t83 -33t30 -80t-30 -80t-83 -33t-83 33t-30 80zM1589 88q0 47 30 80t83 33t83 -33t30 -80t-30 -80t-83 -33t-83 33t-30 80z" />
<glyph unicode="&#x202f;" horiz-adv-x="380" />
<glyph unicode="&#x2039;" horiz-adv-x="563" d="M74 582l311 432l84 -53l-238 -379l238 -381l-84 -54z" />
<glyph unicode="&#x203a;" horiz-adv-x="563" d="M94 201l238 381l-238 379l84 53l311 -432l-311 -435z" />
<glyph unicode="&#x205f;" horiz-adv-x="475" />
<glyph unicode="&#x20ac;" d="M92 485v109h187q-2 29 -2.5 57.5t-0.5 57.5q0 63 7 129h-191v108h203q18 100 55 190.5t100.5 159t153.5 108.5t215 40q45 0 81 -3t65.5 -8.5t57.5 -13.5t56 -18l-32 -119q-49 20 -111 31.5t-117 11.5q-92 0 -157.5 -30.5t-109.5 -83t-70.5 -121t-39.5 -144.5h490 l-21 -108h-483q-2 -33 -3 -66t-1 -63v-57.5t2 -57.5h436l-22 -109h-404q12 -78 37 -148.5t69 -123.5t113.5 -86t169.5 -33q92 0 157 17.5t101 38.5l31 -115q-14 -8 -43 -18.5t-67.5 -19.5t-86 -16.5t-96.5 -7.5q-133 0 -226 41t-155.5 111t-98.5 163t-50 197h-199z" />
<glyph unicode="&#x2122;" horiz-adv-x="1460" d="M41 1337v82h528v-82h-219v-581h-90v581h-219zM625 756q2 102 9 200.5t14 185.5t16.5 157.5t17.5 119.5h76q18 -29 48 -81t63.5 -113.5t69.5 -128t65 -123.5q29 57 64.5 123.5t69 128t63.5 114t48 80.5h72q8 -49 17.5 -119.5t16.5 -157.5t13 -185.5t10 -200.5h-92 q-6 147 -15 274t-22 219q-25 -43 -57.5 -100t-63 -114.5t-56 -107.5t-40.5 -79h-65q-14 29 -40 79t-56.5 107.5t-63.5 114.5t-57 100q-12 -92 -21.5 -219t-15.5 -274h-88z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1054" d="M0 0v1055h1055v-1055h-1055z" />
<hkern u1="K" u2="&#xef;" k="-43" />
<hkern u1="T" u2="&#xef;" k="-63" />
<hkern u1="T" u2="&#xee;" k="-43" />
<hkern u1="V" u2="&#xef;" k="-66" />
<hkern u1="V" u2="&#xec;" k="-23" />
<hkern u1="W" u2="&#xef;" k="-37" />
<hkern u1="Y" u2="&#xef;" k="-84" />
<hkern u1="Z" u2="&#xef;" k="-20" />
<hkern u1="f" u2="&#xef;" k="-82" />
<hkern u1="f" u2="&#xec;" k="-61" />
<hkern u1="&#xdd;" u2="&#xef;" k="-84" />
<hkern u1="&#x178;" u2="&#xef;" k="-84" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quotedbl,quotesingle" 	k="33" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="parenright" 	k="27" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="asterisk" 	k="18" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="question" 	k="35" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="bracketright" 	k="39" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="braceright" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteright,quotedblright" 	k="27" />
<hkern g1="b" 	g2="quotedbl,quotesingle" 	k="53" />
<hkern g1="b" 	g2="parenright" 	k="33" />
<hkern g1="b" 	g2="question" 	k="35" />
<hkern g1="b" 	g2="bracketright" 	k="39" />
<hkern g1="b" 	g2="braceright" 	k="20" />
<hkern g1="b" 	g2="quoteright,quotedblright" 	k="45" />
<hkern g1="b" 	g2="slash" 	k="23" />
<hkern g1="b" 	g2="quoteleft,quotedblleft" 	k="45" />
<hkern g1="b" 	g2="x" 	k="18" />
<hkern g1="b" 	g2="z" 	k="18" />
<hkern g1="c,ccedilla" 	g2="bracketright" 	k="33" />
<hkern g1="c,ccedilla" 	g2="x" 	k="-33" />
<hkern g1="c,ccedilla" 	g2="hyphen,endash,emdash" 	k="43" />
<hkern g1="c,ccedilla" 	g2="braceleft" 	k="20" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="37" />
<hkern g1="c,ccedilla" 	g2="v" 	k="-23" />
<hkern g1="c,ccedilla" 	g2="w" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="y,yacute,ydieresis" 	k="-23" />
<hkern g1="c,ccedilla" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="parenright" 	k="35" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="question" 	k="39" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="bracketright" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="braceright" 	k="23" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="27" />
<hkern g1="f" 	g2="parenright" 	k="-80" />
<hkern g1="f" 	g2="asterisk" 	k="-23" />
<hkern g1="f" 	g2="question" 	k="-49" />
<hkern g1="f" 	g2="bracketright" 	k="-80" />
<hkern g1="f" 	g2="braceright" 	k="-80" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="f" 	g2="slash" 	k="63" />
<hkern g1="f" 	g2="x" 	k="-31" />
<hkern g1="f" 	g2="hyphen,endash,emdash" 	k="49" />
<hkern g1="f" 	g2="guillemotleft,guilsinglleft" 	k="16" />
<hkern g1="f" 	g2="v" 	k="-39" />
<hkern g1="f" 	g2="w" 	k="-37" />
<hkern g1="f" 	g2="y,yacute,ydieresis" 	k="-39" />
<hkern g1="f" 	g2="comma,period,ellipsis" 	k="84" />
<hkern g1="f" 	g2="eth" 	k="18" />
<hkern g1="f" 	g2="quotesinglbase,quotedblbase" 	k="86" />
<hkern g1="f" 	g2="guillemotright,guilsinglright" 	k="-18" />
<hkern g1="h" 	g2="quotedbl,quotesingle" 	k="53" />
<hkern g1="h" 	g2="parenright" 	k="27" />
<hkern g1="h" 	g2="asterisk" 	k="20" />
<hkern g1="h" 	g2="question" 	k="33" />
<hkern g1="h" 	g2="bracketright" 	k="37" />
<hkern g1="h" 	g2="braceright" 	k="20" />
<hkern g1="h" 	g2="quoteright,quotedblright" 	k="39" />
<hkern g1="h" 	g2="quoteleft,quotedblleft" 	k="39" />
<hkern g1="h" 	g2="y,yacute,ydieresis" 	k="18" />
<hkern g1="j" 	g2="j" 	k="-18" />
<hkern g1="k" 	g2="bracketright" 	k="29" />
<hkern g1="k" 	g2="slash" 	k="-23" />
<hkern g1="k" 	g2="x" 	k="-39" />
<hkern g1="k" 	g2="hyphen,endash,emdash" 	k="33" />
<hkern g1="k" 	g2="braceleft" 	k="16" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="47" />
<hkern g1="k" 	g2="eth" 	k="33" />
<hkern g1="k" 	g2="c,ccedilla" 	k="31" />
<hkern g1="k" 	g2="d" 	k="31" />
<hkern g1="k" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="31" />
<hkern g1="k" 	g2="g" 	k="37" />
<hkern g1="k" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="k" 	g2="q" 	k="31" />
<hkern g1="k" 	g2="at" 	k="18" />
<hkern g1="m" 	g2="quotedbl,quotesingle" 	k="35" />
<hkern g1="m" 	g2="parenright" 	k="27" />
<hkern g1="m" 	g2="asterisk" 	k="20" />
<hkern g1="m" 	g2="question" 	k="33" />
<hkern g1="m" 	g2="bracketright" 	k="37" />
<hkern g1="m" 	g2="braceright" 	k="20" />
<hkern g1="m" 	g2="quoteright,quotedblright" 	k="29" />
<hkern g1="m" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="n,ntilde" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="n,ntilde" 	g2="parenright" 	k="29" />
<hkern g1="n,ntilde" 	g2="asterisk" 	k="23" />
<hkern g1="n,ntilde" 	g2="question" 	k="35" />
<hkern g1="n,ntilde" 	g2="bracketright" 	k="39" />
<hkern g1="n,ntilde" 	g2="braceright" 	k="20" />
<hkern g1="n,ntilde" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="n,ntilde" 	g2="quoteleft,quotedblleft" 	k="23" />
<hkern g1="n,ntilde" 	g2="y,yacute,ydieresis" 	k="14" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotedbl,quotesingle" 	k="43" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="parenright" 	k="33" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="asterisk" 	k="18" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="question" 	k="39" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="bracketright" 	k="39" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="braceright" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteright,quotedblright" 	k="33" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="slash" 	k="25" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteleft,quotedblleft" 	k="23" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="x" 	k="18" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="z" 	k="18" />
<hkern g1="p" 	g2="quotedbl,quotesingle" 	k="43" />
<hkern g1="p" 	g2="parenright" 	k="33" />
<hkern g1="p" 	g2="asterisk" 	k="18" />
<hkern g1="p" 	g2="question" 	k="35" />
<hkern g1="p" 	g2="bracketright" 	k="39" />
<hkern g1="p" 	g2="braceright" 	k="20" />
<hkern g1="p" 	g2="quoteright,quotedblright" 	k="33" />
<hkern g1="p" 	g2="slash" 	k="23" />
<hkern g1="p" 	g2="quoteleft,quotedblleft" 	k="23" />
<hkern g1="p" 	g2="x" 	k="18" />
<hkern g1="p" 	g2="z" 	k="14" />
<hkern g1="q" 	g2="j" 	k="-61" />
<hkern g1="r" 	g2="asterisk" 	k="-25" />
<hkern g1="r" 	g2="question" 	k="66" />
<hkern g1="r" 	g2="bracketright" 	k="27" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-29" />
<hkern g1="r" 	g2="slash" 	k="72" />
<hkern g1="r" 	g2="x" 	k="-33" />
<hkern g1="r" 	g2="hyphen,endash,emdash" 	k="43" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="r" 	g2="v" 	k="-43" />
<hkern g1="r" 	g2="w" 	k="-39" />
<hkern g1="r" 	g2="y,yacute,ydieresis" 	k="-43" />
<hkern g1="r" 	g2="comma,period,ellipsis" 	k="90" />
<hkern g1="r" 	g2="eth" 	k="16" />
<hkern g1="r" 	g2="quotesinglbase,quotedblbase" 	k="84" />
<hkern g1="s" 	g2="quotedbl,quotesingle" 	k="27" />
<hkern g1="s" 	g2="parenright" 	k="33" />
<hkern g1="s" 	g2="question" 	k="20" />
<hkern g1="s" 	g2="bracketright" 	k="45" />
<hkern g1="s" 	g2="braceright" 	k="23" />
<hkern g1="t" 	g2="bracketright" 	k="33" />
<hkern g1="t" 	g2="x" 	k="-35" />
<hkern g1="t" 	g2="hyphen,endash,emdash" 	k="57" />
<hkern g1="t" 	g2="braceleft" 	k="20" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="35" />
<hkern g1="t" 	g2="comma,period,ellipsis" 	k="-23" />
<hkern g1="v" 	g2="asterisk" 	k="-18" />
<hkern g1="v" 	g2="question" 	k="57" />
<hkern g1="v" 	g2="bracketright" 	k="31" />
<hkern g1="v" 	g2="quoteright,quotedblright" 	k="-25" />
<hkern g1="v" 	g2="slash" 	k="43" />
<hkern g1="v" 	g2="x" 	k="-29" />
<hkern g1="v" 	g2="v" 	k="-37" />
<hkern g1="v" 	g2="w" 	k="-35" />
<hkern g1="v" 	g2="y,yacute,ydieresis" 	k="-37" />
<hkern g1="v" 	g2="comma,period,ellipsis" 	k="49" />
<hkern g1="v" 	g2="eth" 	k="18" />
<hkern g1="v" 	g2="quotesinglbase,quotedblbase" 	k="51" />
<hkern g1="w" 	g2="asterisk" 	k="-18" />
<hkern g1="w" 	g2="question" 	k="49" />
<hkern g1="w" 	g2="bracketright" 	k="31" />
<hkern g1="w" 	g2="quoteright,quotedblright" 	k="-25" />
<hkern g1="w" 	g2="slash" 	k="35" />
<hkern g1="w" 	g2="x" 	k="-29" />
<hkern g1="w" 	g2="v" 	k="-37" />
<hkern g1="w" 	g2="w" 	k="-35" />
<hkern g1="w" 	g2="y,yacute,ydieresis" 	k="-37" />
<hkern g1="w" 	g2="comma,period,ellipsis" 	k="43" />
<hkern g1="w" 	g2="quotesinglbase,quotedblbase" 	k="39" />
<hkern g1="x" 	g2="bracketright" 	k="33" />
<hkern g1="x" 	g2="x" 	k="-35" />
<hkern g1="x" 	g2="braceleft" 	k="20" />
<hkern g1="x" 	g2="guillemotleft,guilsinglleft" 	k="37" />
<hkern g1="x" 	g2="eth" 	k="27" />
<hkern g1="x" 	g2="c,ccedilla" 	k="20" />
<hkern g1="x" 	g2="d" 	k="20" />
<hkern g1="x" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="20" />
<hkern g1="x" 	g2="g" 	k="20" />
<hkern g1="x" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="x" 	g2="q" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="question" 	k="43" />
<hkern g1="y,yacute,ydieresis" 	g2="bracketright" 	k="33" />
<hkern g1="y,yacute,ydieresis" 	g2="quoteright,quotedblright" 	k="-23" />
<hkern g1="y,yacute,ydieresis" 	g2="slash" 	k="37" />
<hkern g1="y,yacute,ydieresis" 	g2="x" 	k="-27" />
<hkern g1="y,yacute,ydieresis" 	g2="v" 	k="-35" />
<hkern g1="y,yacute,ydieresis" 	g2="w" 	k="-33" />
<hkern g1="y,yacute,ydieresis" 	g2="y,yacute,ydieresis" 	k="-35" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,ellipsis" 	k="23" />
<hkern g1="y,yacute,ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="39" />
<hkern g1="z" 	g2="bracketright" 	k="47" />
<hkern g1="z" 	g2="braceright" 	k="20" />
<hkern g1="z" 	g2="braceleft" 	k="35" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="45" />
<hkern g1="z" 	g2="eth" 	k="14" />
<hkern g1="z" 	g2="c,ccedilla" 	k="16" />
<hkern g1="z" 	g2="d" 	k="18" />
<hkern g1="z" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="16" />
<hkern g1="z" 	g2="g" 	k="23" />
<hkern g1="z" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="z" 	g2="q" 	k="14" />
<hkern g1="germandbls" 	g2="quotedbl,quotesingle" 	k="59" />
<hkern g1="germandbls" 	g2="parenright" 	k="33" />
<hkern g1="germandbls" 	g2="asterisk" 	k="45" />
<hkern g1="germandbls" 	g2="question" 	k="18" />
<hkern g1="germandbls" 	g2="bracketright" 	k="20" />
<hkern g1="germandbls" 	g2="braceright" 	k="20" />
<hkern g1="germandbls" 	g2="quoteright,quotedblright" 	k="39" />
<hkern g1="germandbls" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="germandbls" 	g2="v" 	k="35" />
<hkern g1="germandbls" 	g2="w" 	k="29" />
<hkern g1="germandbls" 	g2="y,yacute,ydieresis" 	k="35" />
<hkern g1="eth" 	g2="question" 	k="-31" />
<hkern g1="eth" 	g2="bracketright" 	k="-27" />
<hkern g1="eth" 	g2="braceright" 	k="-27" />
<hkern g1="eth" 	g2="slash" 	k="20" />
<hkern g1="thorn" 	g2="quotedbl,quotesingle" 	k="59" />
<hkern g1="thorn" 	g2="parenright" 	k="33" />
<hkern g1="thorn" 	g2="asterisk" 	k="18" />
<hkern g1="thorn" 	g2="question" 	k="35" />
<hkern g1="thorn" 	g2="bracketright" 	k="39" />
<hkern g1="thorn" 	g2="braceright" 	k="20" />
<hkern g1="thorn" 	g2="quoteright,quotedblright" 	k="51" />
<hkern g1="thorn" 	g2="slash" 	k="23" />
<hkern g1="thorn" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="thorn" 	g2="x" 	k="18" />
<hkern g1="thorn" 	g2="z" 	k="14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle" 	k="135" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk" 	k="104" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="bracketright" 	k="33" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="115" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="slash" 	k="-35" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="121" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="x" 	k="-53" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="z" 	k="-31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="braceleft" 	k="18" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guillemotleft,guilsinglleft" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="comma,period,ellipsis" 	k="-53" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotesinglbase,quotedblbase" 	k="-51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,Ccedilla" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="G" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Q" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="106" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="102" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="W" 	k="14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="137" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="parenleft" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="s" 	k="-27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-68" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="J" 	k="-59" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="S" 	k="-33" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="X" 	k="-51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Z" 	k="-31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="AE" 	k="-70" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="colon,semicolon" 	k="-35" />
<hkern g1="B" 	g2="quotedbl,quotesingle" 	k="14" />
<hkern g1="B" 	g2="parenright" 	k="29" />
<hkern g1="B" 	g2="asterisk" 	k="14" />
<hkern g1="B" 	g2="question" 	k="18" />
<hkern g1="B" 	g2="bracketright" 	k="47" />
<hkern g1="B" 	g2="braceright" 	k="23" />
<hkern g1="B" 	g2="slash" 	k="20" />
<hkern g1="B" 	g2="V" 	k="16" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="29" />
<hkern g1="B" 	g2="parenleft" 	k="14" />
<hkern g1="B" 	g2="X" 	k="20" />
<hkern g1="B" 	g2="colon,semicolon" 	k="14" />
<hkern g1="C,Ccedilla" 	g2="question" 	k="-31" />
<hkern g1="C,Ccedilla" 	g2="quoteright,quotedblright" 	k="-27" />
<hkern g1="C,Ccedilla" 	g2="x" 	k="-29" />
<hkern g1="C,Ccedilla" 	g2="hyphen,endash,emdash" 	k="39" />
<hkern g1="C,Ccedilla" 	g2="braceleft" 	k="35" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="70" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="comma,period,ellipsis" 	k="-45" />
<hkern g1="C,Ccedilla" 	g2="quotesinglbase,quotedblbase" 	k="-37" />
<hkern g1="C,Ccedilla" 	g2="at" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="C,Ccedilla" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="G" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="Q" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="-35" />
<hkern g1="C,Ccedilla" 	g2="V" 	k="-35" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="-35" />
<hkern g1="C,Ccedilla" 	g2="parenleft" 	k="25" />
<hkern g1="C,Ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-45" />
<hkern g1="C,Ccedilla" 	g2="J" 	k="-47" />
<hkern g1="C,Ccedilla" 	g2="S" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="X" 	k="-27" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="AE" 	k="-45" />
<hkern g1="C,Ccedilla" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="18" />
<hkern g1="D,Eth" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="D,Eth" 	g2="parenright" 	k="45" />
<hkern g1="D,Eth" 	g2="question" 	k="57" />
<hkern g1="D,Eth" 	g2="bracketright" 	k="63" />
<hkern g1="D,Eth" 	g2="braceright" 	k="37" />
<hkern g1="D,Eth" 	g2="slash" 	k="66" />
<hkern g1="D,Eth" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="D,Eth" 	g2="comma,period,ellipsis" 	k="45" />
<hkern g1="D,Eth" 	g2="quotesinglbase,quotedblbase" 	k="47" />
<hkern g1="D,Eth" 	g2="at" 	k="14" />
<hkern g1="D,Eth" 	g2="T" 	k="33" />
<hkern g1="D,Eth" 	g2="V" 	k="18" />
<hkern g1="D,Eth" 	g2="W" 	k="18" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="D,Eth" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="16" />
<hkern g1="D,Eth" 	g2="J" 	k="29" />
<hkern g1="D,Eth" 	g2="X" 	k="27" />
<hkern g1="D,Eth" 	g2="AE" 	k="63" />
<hkern g1="D,Eth" 	g2="bracketleft" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="asterisk" 	k="68" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="bracketright" 	k="66" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="braceright" 	k="23" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="quoteleft,quotedblleft" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="x" 	k="-18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="braceleft" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guillemotleft,guilsinglleft" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v" 	k="29" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="29" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="y,yacute,ydieresis" 	k="29" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="comma,period,ellipsis" 	k="-23" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="d" 	k="18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="g" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="C,Ccedilla" 	k="35" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="G" 	k="35" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="35" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Q" 	k="35" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="parenleft" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-35" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="J" 	k="-35" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="AE" 	k="-37" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="bracketleft" 	k="18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="23" />
<hkern g1="F" 	g2="asterisk" 	k="41" />
<hkern g1="F" 	g2="question" 	k="-27" />
<hkern g1="F" 	g2="bracketright" 	k="61" />
<hkern g1="F" 	g2="slash" 	k="98" />
<hkern g1="F" 	g2="x" 	k="51" />
<hkern g1="F" 	g2="z" 	k="31" />
<hkern g1="F" 	g2="comma,period,ellipsis" 	k="109" />
<hkern g1="F" 	g2="quotesinglbase,quotedblbase" 	k="186" />
<hkern g1="F" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="F" 	g2="at" 	k="14" />
<hkern g1="F" 	g2="T" 	k="-29" />
<hkern g1="F" 	g2="V" 	k="-37" />
<hkern g1="F" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="F" 	g2="parenleft" 	k="27" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="72" />
<hkern g1="F" 	g2="J" 	k="139" />
<hkern g1="F" 	g2="AE" 	k="131" />
<hkern g1="F" 	g2="colon,semicolon" 	k="14" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="29" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="82" />
<hkern g1="F" 	g2="m" 	k="35" />
<hkern g1="F" 	g2="n,ntilde" 	k="35" />
<hkern g1="F" 	g2="p" 	k="35" />
<hkern g1="F" 	g2="r" 	k="35" />
<hkern g1="G" 	g2="asterisk" 	k="29" />
<hkern g1="G" 	g2="quoteright,quotedblright" 	k="23" />
<hkern g1="G" 	g2="quoteleft,quotedblleft" 	k="29" />
<hkern g1="G" 	g2="v" 	k="14" />
<hkern g1="G" 	g2="y,yacute,ydieresis" 	k="14" />
<hkern g1="G" 	g2="comma,period,ellipsis" 	k="-37" />
<hkern g1="G" 	g2="quotesinglbase,quotedblbase" 	k="-29" />
<hkern g1="J" 	g2="bracketright" 	k="47" />
<hkern g1="J" 	g2="braceright" 	k="16" />
<hkern g1="J" 	g2="quoteright,quotedblright" 	k="-29" />
<hkern g1="J" 	g2="slash" 	k="51" />
<hkern g1="J" 	g2="z" 	k="14" />
<hkern g1="J" 	g2="quotesinglbase,quotedblbase" 	k="20" />
<hkern g1="J" 	g2="Z" 	k="18" />
<hkern g1="J" 	g2="AE" 	k="29" />
<hkern g1="J" 	g2="colon,semicolon" 	k="29" />
<hkern g1="K" 	g2="asterisk" 	k="45" />
<hkern g1="K" 	g2="question" 	k="-23" />
<hkern g1="K" 	g2="bracketright" 	k="31" />
<hkern g1="K" 	g2="slash" 	k="-37" />
<hkern g1="K" 	g2="x" 	k="-55" />
<hkern g1="K" 	g2="z" 	k="-33" />
<hkern g1="K" 	g2="hyphen,endash,emdash" 	k="74" />
<hkern g1="K" 	g2="braceleft" 	k="16" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="100" />
<hkern g1="K" 	g2="w" 	k="76" />
<hkern g1="K" 	g2="comma,period,ellipsis" 	k="-33" />
<hkern g1="K" 	g2="eth" 	k="29" />
<hkern g1="K" 	g2="quotesinglbase,quotedblbase" 	k="-31" />
<hkern g1="K" 	g2="c,ccedilla" 	k="20" />
<hkern g1="K" 	g2="d" 	k="20" />
<hkern g1="K" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="20" />
<hkern g1="K" 	g2="g" 	k="20" />
<hkern g1="K" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="K" 	g2="q" 	k="20" />
<hkern g1="K" 	g2="at" 	k="16" />
<hkern g1="K" 	g2="C,Ccedilla" 	k="49" />
<hkern g1="K" 	g2="G" 	k="49" />
<hkern g1="K" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="49" />
<hkern g1="K" 	g2="Q" 	k="49" />
<hkern g1="K" 	g2="T" 	k="-35" />
<hkern g1="K" 	g2="V" 	k="-41" />
<hkern g1="K" 	g2="W" 	k="-20" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="-45" />
<hkern g1="K" 	g2="parenleft" 	k="14" />
<hkern g1="K" 	g2="s" 	k="-29" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-70" />
<hkern g1="K" 	g2="J" 	k="-55" />
<hkern g1="K" 	g2="X" 	k="-53" />
<hkern g1="K" 	g2="Z" 	k="-43" />
<hkern g1="K" 	g2="AE" 	k="-72" />
<hkern g1="K" 	g2="colon,semicolon" 	k="-35" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="231" />
<hkern g1="L" 	g2="asterisk" 	k="258" />
<hkern g1="L" 	g2="bracketright" 	k="43" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="215" />
<hkern g1="L" 	g2="slash" 	k="-25" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="219" />
<hkern g1="L" 	g2="x" 	k="-43" />
<hkern g1="L" 	g2="z" 	k="-20" />
<hkern g1="L" 	g2="hyphen,endash,emdash" 	k="117" />
<hkern g1="L" 	g2="braceleft" 	k="27" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="57" />
<hkern g1="L" 	g2="v" 	k="59" />
<hkern g1="L" 	g2="w" 	k="53" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="18" />
<hkern g1="L" 	g2="comma,period,ellipsis" 	k="-47" />
<hkern g1="L" 	g2="quotesinglbase,quotedblbase" 	k="-29" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="-23" />
<hkern g1="L" 	g2="C,Ccedilla" 	k="70" />
<hkern g1="L" 	g2="G" 	k="70" />
<hkern g1="L" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="70" />
<hkern g1="L" 	g2="Q" 	k="70" />
<hkern g1="L" 	g2="T" 	k="209" />
<hkern g1="L" 	g2="V" 	k="180" />
<hkern g1="L" 	g2="W" 	k="70" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="209" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-57" />
<hkern g1="L" 	g2="J" 	k="-57" />
<hkern g1="L" 	g2="S" 	k="-29" />
<hkern g1="L" 	g2="X" 	k="-41" />
<hkern g1="L" 	g2="Z" 	k="-29" />
<hkern g1="L" 	g2="AE" 	k="-59" />
<hkern g1="L" 	g2="colon,semicolon" 	k="-31" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="14" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="25" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="parenright" 	k="35" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="question" 	k="47" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="bracketright" 	k="63" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="braceright" 	k="37" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="63" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="39" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,ellipsis" 	k="45" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotesinglbase,quotedblbase" 	k="39" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="at" 	k="14" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="33" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="18" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="16" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="29" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="27" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="59" />
<hkern g1="P" 	g2="parenright" 	k="27" />
<hkern g1="P" 	g2="asterisk" 	k="-39" />
<hkern g1="P" 	g2="bracketright" 	k="61" />
<hkern g1="P" 	g2="braceright" 	k="27" />
<hkern g1="P" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="P" 	g2="slash" 	k="102" />
<hkern g1="P" 	g2="quoteleft,quotedblleft" 	k="-31" />
<hkern g1="P" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="P" 	g2="v" 	k="-20" />
<hkern g1="P" 	g2="w" 	k="-18" />
<hkern g1="P" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="P" 	g2="comma,period,ellipsis" 	k="160" />
<hkern g1="P" 	g2="quotesinglbase,quotedblbase" 	k="209" />
<hkern g1="P" 	g2="c,ccedilla" 	k="20" />
<hkern g1="P" 	g2="d" 	k="20" />
<hkern g1="P" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="43" />
<hkern g1="P" 	g2="g" 	k="20" />
<hkern g1="P" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="P" 	g2="q" 	k="14" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="80" />
<hkern g1="P" 	g2="J" 	k="170" />
<hkern g1="P" 	g2="X" 	k="14" />
<hkern g1="P" 	g2="AE" 	k="147" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="P" 	g2="exclam" 	k="14" />
<hkern g1="Q" 	g2="quotedbl,quotesingle" 	k="25" />
<hkern g1="Q" 	g2="question" 	k="47" />
<hkern g1="Q" 	g2="quoteleft,quotedblleft" 	k="39" />
<hkern g1="Q" 	g2="comma,period,ellipsis" 	k="45" />
<hkern g1="Q" 	g2="quotesinglbase,quotedblbase" 	k="18" />
<hkern g1="Q" 	g2="j" 	k="-31" />
<hkern g1="Q" 	g2="at" 	k="14" />
<hkern g1="Q" 	g2="T" 	k="33" />
<hkern g1="Q" 	g2="V" 	k="18" />
<hkern g1="Q" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="Q" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="16" />
<hkern g1="Q" 	g2="J" 	k="29" />
<hkern g1="Q" 	g2="X" 	k="27" />
<hkern g1="Q" 	g2="AE" 	k="59" />
<hkern g1="R" 	g2="bracketright" 	k="45" />
<hkern g1="R" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="R" 	g2="slash" 	k="-25" />
<hkern g1="R" 	g2="x" 	k="-43" />
<hkern g1="R" 	g2="z" 	k="-18" />
<hkern g1="R" 	g2="braceleft" 	k="18" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="18" />
<hkern g1="R" 	g2="comma,period,ellipsis" 	k="-29" />
<hkern g1="R" 	g2="quotesinglbase,quotedblbase" 	k="-29" />
<hkern g1="R" 	g2="C,Ccedilla" 	k="14" />
<hkern g1="R" 	g2="G" 	k="14" />
<hkern g1="R" 	g2="Q" 	k="14" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="16" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-57" />
<hkern g1="R" 	g2="J" 	k="-45" />
<hkern g1="R" 	g2="X" 	k="-39" />
<hkern g1="R" 	g2="Z" 	k="-29" />
<hkern g1="R" 	g2="AE" 	k="-59" />
<hkern g1="R" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="S" 	g2="asterisk" 	k="23" />
<hkern g1="S" 	g2="quoteleft,quotedblleft" 	k="25" />
<hkern g1="S" 	g2="v" 	k="25" />
<hkern g1="S" 	g2="y,yacute,ydieresis" 	k="27" />
<hkern g1="S" 	g2="comma,period,ellipsis" 	k="-33" />
<hkern g1="S" 	g2="quotesinglbase,quotedblbase" 	k="-31" />
<hkern g1="T" 	g2="parenright" 	k="-25" />
<hkern g1="T" 	g2="asterisk" 	k="51" />
<hkern g1="T" 	g2="question" 	k="-45" />
<hkern g1="T" 	g2="bracketright" 	k="43" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="-29" />
<hkern g1="T" 	g2="slash" 	k="152" />
<hkern g1="T" 	g2="x" 	k="14" />
<hkern g1="T" 	g2="z" 	k="39" />
<hkern g1="T" 	g2="hyphen,endash,emdash" 	k="78" />
<hkern g1="T" 	g2="braceleft" 	k="27" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="150" />
<hkern g1="T" 	g2="v" 	k="23" />
<hkern g1="T" 	g2="w" 	k="141" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="23" />
<hkern g1="T" 	g2="comma,period,ellipsis" 	k="129" />
<hkern g1="T" 	g2="eth" 	k="137" />
<hkern g1="T" 	g2="quotesinglbase,quotedblbase" 	k="113" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="131" />
<hkern g1="T" 	g2="c,ccedilla" 	k="90" />
<hkern g1="T" 	g2="d" 	k="90" />
<hkern g1="T" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="90" />
<hkern g1="T" 	g2="g" 	k="90" />
<hkern g1="T" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="90" />
<hkern g1="T" 	g2="q" 	k="90" />
<hkern g1="T" 	g2="at" 	k="82" />
<hkern g1="T" 	g2="C,Ccedilla" 	k="33" />
<hkern g1="T" 	g2="G" 	k="33" />
<hkern g1="T" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="33" />
<hkern g1="T" 	g2="Q" 	k="33" />
<hkern g1="T" 	g2="T" 	k="-49" />
<hkern g1="T" 	g2="V" 	k="-55" />
<hkern g1="T" 	g2="W" 	k="-35" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-59" />
<hkern g1="T" 	g2="parenleft" 	k="33" />
<hkern g1="T" 	g2="s" 	k="70" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="106" />
<hkern g1="T" 	g2="J" 	k="160" />
<hkern g1="T" 	g2="X" 	k="-31" />
<hkern g1="T" 	g2="AE" 	k="123" />
<hkern g1="T" 	g2="colon,semicolon" 	k="147" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="47" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="70" />
<hkern g1="T" 	g2="m" 	k="49" />
<hkern g1="T" 	g2="n,ntilde" 	k="59" />
<hkern g1="T" 	g2="p" 	k="59" />
<hkern g1="T" 	g2="r" 	k="59" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="bracketright" 	k="53" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="braceright" 	k="23" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="quoteright,quotedblright" 	k="-31" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="slash" 	k="61" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z" 	k="14" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,ellipsis" 	k="37" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="quotesinglbase,quotedblbase" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="47" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="colon,semicolon" 	k="31" />
<hkern g1="V" 	g2="quotedbl,quotesingle" 	k="-29" />
<hkern g1="V" 	g2="parenright" 	k="-33" />
<hkern g1="V" 	g2="question" 	k="-49" />
<hkern g1="V" 	g2="bracketright" 	k="35" />
<hkern g1="V" 	g2="quoteright,quotedblright" 	k="-49" />
<hkern g1="V" 	g2="slash" 	k="113" />
<hkern g1="V" 	g2="quoteleft,quotedblleft" 	k="-39" />
<hkern g1="V" 	g2="braceleft" 	k="27" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="66" />
<hkern g1="V" 	g2="comma,period,ellipsis" 	k="129" />
<hkern g1="V" 	g2="eth" 	k="82" />
<hkern g1="V" 	g2="quotesinglbase,quotedblbase" 	k="127" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="14" />
<hkern g1="V" 	g2="c,ccedilla" 	k="70" />
<hkern g1="V" 	g2="d" 	k="70" />
<hkern g1="V" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="70" />
<hkern g1="V" 	g2="g" 	k="70" />
<hkern g1="V" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="70" />
<hkern g1="V" 	g2="q" 	k="70" />
<hkern g1="V" 	g2="at" 	k="55" />
<hkern g1="V" 	g2="C,Ccedilla" 	k="18" />
<hkern g1="V" 	g2="G" 	k="18" />
<hkern g1="V" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="V" 	g2="Q" 	k="18" />
<hkern g1="V" 	g2="T" 	k="-57" />
<hkern g1="V" 	g2="V" 	k="-63" />
<hkern g1="V" 	g2="W" 	k="-43" />
<hkern g1="V" 	g2="Y,Yacute,Ydieresis" 	k="-68" />
<hkern g1="V" 	g2="parenleft" 	k="45" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="102" />
<hkern g1="V" 	g2="J" 	k="170" />
<hkern g1="V" 	g2="X" 	k="-39" />
<hkern g1="V" 	g2="AE" 	k="143" />
<hkern g1="V" 	g2="colon,semicolon" 	k="18" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="27" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="29" />
<hkern g1="V" 	g2="m" 	k="39" />
<hkern g1="V" 	g2="n,ntilde" 	k="39" />
<hkern g1="V" 	g2="p" 	k="39" />
<hkern g1="V" 	g2="r" 	k="39" />
<hkern g1="W" 	g2="quotedbl,quotesingle" 	k="-27" />
<hkern g1="W" 	g2="question" 	k="-33" />
<hkern g1="W" 	g2="bracketright" 	k="53" />
<hkern g1="W" 	g2="quoteright,quotedblright" 	k="-51" />
<hkern g1="W" 	g2="slash" 	k="74" />
<hkern g1="W" 	g2="quoteleft,quotedblleft" 	k="-37" />
<hkern g1="W" 	g2="braceleft" 	k="18" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="18" />
<hkern g1="W" 	g2="comma,period,ellipsis" 	k="53" />
<hkern g1="W" 	g2="eth" 	k="31" />
<hkern g1="W" 	g2="quotesinglbase,quotedblbase" 	k="55" />
<hkern g1="W" 	g2="c,ccedilla" 	k="23" />
<hkern g1="W" 	g2="d" 	k="23" />
<hkern g1="W" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="23" />
<hkern g1="W" 	g2="g" 	k="31" />
<hkern g1="W" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="23" />
<hkern g1="W" 	g2="q" 	k="23" />
<hkern g1="W" 	g2="at" 	k="16" />
<hkern g1="W" 	g2="T" 	k="-39" />
<hkern g1="W" 	g2="V" 	k="-45" />
<hkern g1="W" 	g2="W" 	k="-25" />
<hkern g1="W" 	g2="Y,Yacute,Ydieresis" 	k="-49" />
<hkern g1="W" 	g2="s" 	k="18" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="14" />
<hkern g1="W" 	g2="J" 	k="98" />
<hkern g1="W" 	g2="X" 	k="-20" />
<hkern g1="W" 	g2="AE" 	k="78" />
<hkern g1="W" 	g2="colon,semicolon" 	k="14" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="37" />
<hkern g1="W" 	g2="m" 	k="20" />
<hkern g1="W" 	g2="n,ntilde" 	k="20" />
<hkern g1="W" 	g2="p" 	k="20" />
<hkern g1="W" 	g2="r" 	k="20" />
<hkern g1="X" 	g2="asterisk" 	k="31" />
<hkern g1="X" 	g2="bracketright" 	k="47" />
<hkern g1="X" 	g2="slash" 	k="-20" />
<hkern g1="X" 	g2="x" 	k="-39" />
<hkern g1="X" 	g2="hyphen,endash,emdash" 	k="35" />
<hkern g1="X" 	g2="braceleft" 	k="31" />
<hkern g1="X" 	g2="guillemotleft,guilsinglleft" 	k="74" />
<hkern g1="X" 	g2="v" 	k="14" />
<hkern g1="X" 	g2="w" 	k="53" />
<hkern g1="X" 	g2="comma,period,ellipsis" 	k="-27" />
<hkern g1="X" 	g2="eth" 	k="37" />
<hkern g1="X" 	g2="quotesinglbase,quotedblbase" 	k="-20" />
<hkern g1="X" 	g2="c,ccedilla" 	k="14" />
<hkern g1="X" 	g2="d" 	k="14" />
<hkern g1="X" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="14" />
<hkern g1="X" 	g2="g" 	k="14" />
<hkern g1="X" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="X" 	g2="q" 	k="14" />
<hkern g1="X" 	g2="at" 	k="31" />
<hkern g1="X" 	g2="C,Ccedilla" 	k="27" />
<hkern g1="X" 	g2="G" 	k="27" />
<hkern g1="X" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="27" />
<hkern g1="X" 	g2="Q" 	k="27" />
<hkern g1="X" 	g2="T" 	k="-27" />
<hkern g1="X" 	g2="V" 	k="-35" />
<hkern g1="X" 	g2="Y,Yacute,Ydieresis" 	k="-39" />
<hkern g1="X" 	g2="parenleft" 	k="29" />
<hkern g1="X" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-55" />
<hkern g1="X" 	g2="J" 	k="-41" />
<hkern g1="X" 	g2="X" 	k="-37" />
<hkern g1="X" 	g2="Z" 	k="-27" />
<hkern g1="X" 	g2="AE" 	k="-57" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="parenright" 	k="-37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="question" 	k="-51" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="bracketright" 	k="31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-35" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteleft,quotedblleft" 	k="-23" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="35" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,endash,emdash" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="braceleft" 	k="23" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="131" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,ellipsis" 	k="109" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="eth" 	k="135" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="135" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="43" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,ccedilla" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="q" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="at" 	k="94" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,Ccedilla" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="G" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Q" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="V" 	k="-68" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="W" 	k="-47" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="-72" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="parenleft" 	k="63" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="18" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="137" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="209" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="X" 	k="-43" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Z" 	k="-23" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="139" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="55" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="47" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m" 	k="59" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="n,ntilde" 	k="59" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="p" 	k="59" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="r" 	k="59" />
<hkern g1="Z" 	g2="bracketright" 	k="66" />
<hkern g1="Z" 	g2="quoteright,quotedblright" 	k="-33" />
<hkern g1="Z" 	g2="quoteleft,quotedblleft" 	k="-27" />
<hkern g1="Z" 	g2="x" 	k="-18" />
<hkern g1="Z" 	g2="hyphen,endash,emdash" 	k="98" />
<hkern g1="Z" 	g2="braceleft" 	k="49" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="80" />
<hkern g1="Z" 	g2="v" 	k="25" />
<hkern g1="Z" 	g2="w" 	k="27" />
<hkern g1="Z" 	g2="y,yacute,ydieresis" 	k="25" />
<hkern g1="Z" 	g2="comma,period,ellipsis" 	k="-23" />
<hkern g1="Z" 	g2="d" 	k="18" />
<hkern g1="Z" 	g2="g" 	k="25" />
<hkern g1="Z" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="Z" 	g2="C,Ccedilla" 	k="51" />
<hkern g1="Z" 	g2="G" 	k="51" />
<hkern g1="Z" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="Z" 	g2="Q" 	k="51" />
<hkern g1="Z" 	g2="V" 	k="-20" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="-25" />
<hkern g1="Z" 	g2="parenleft" 	k="20" />
<hkern g1="Z" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-35" />
<hkern g1="Z" 	g2="J" 	k="-35" />
<hkern g1="Z" 	g2="AE" 	k="-37" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="Z" 	g2="bracketleft" 	k="18" />
<hkern g1="Thorn" 	g2="quotedbl,quotesingle" 	k="45" />
<hkern g1="Thorn" 	g2="parenright" 	k="57" />
<hkern g1="Thorn" 	g2="question" 	k="47" />
<hkern g1="Thorn" 	g2="bracketright" 	k="63" />
<hkern g1="Thorn" 	g2="braceright" 	k="33" />
<hkern g1="Thorn" 	g2="quoteright,quotedblright" 	k="27" />
<hkern g1="Thorn" 	g2="slash" 	k="61" />
<hkern g1="Thorn" 	g2="quoteleft,quotedblleft" 	k="29" />
<hkern g1="Thorn" 	g2="comma,period,ellipsis" 	k="98" />
<hkern g1="Thorn" 	g2="quotesinglbase,quotedblbase" 	k="94" />
<hkern g1="Thorn" 	g2="T" 	k="68" />
<hkern g1="Thorn" 	g2="V" 	k="20" />
<hkern g1="Thorn" 	g2="Y,Yacute,Ydieresis" 	k="63" />
<hkern g1="Thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="33" />
<hkern g1="Thorn" 	g2="J" 	k="92" />
<hkern g1="Thorn" 	g2="X" 	k="66" />
<hkern g1="Thorn" 	g2="Z" 	k="74" />
<hkern g1="Thorn" 	g2="AE" 	k="78" />
<hkern g1="parenleft" 	g2="C,Ccedilla" 	k="39" />
<hkern g1="parenleft" 	g2="G" 	k="39" />
<hkern g1="parenleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="39" />
<hkern g1="parenleft" 	g2="Q" 	k="39" />
<hkern g1="parenleft" 	g2="S" 	k="14" />
<hkern g1="parenleft" 	g2="T" 	k="-23" />
<hkern g1="parenleft" 	g2="V" 	k="-31" />
<hkern g1="parenleft" 	g2="Y,Yacute,Ydieresis" 	k="-35" />
<hkern g1="parenleft" 	g2="parenright" 	k="-82" />
<hkern g1="parenright" 	g2="T" 	k="35" />
<hkern g1="parenright" 	g2="V" 	k="47" />
<hkern g1="parenright" 	g2="Y,Yacute,Ydieresis" 	k="66" />
<hkern g1="parenright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="18" />
<hkern g1="parenright" 	g2="W" 	k="14" />
<hkern g1="parenright" 	g2="X" 	k="35" />
<hkern g1="parenright" 	g2="Z" 	k="23" />
<hkern g1="parenright" 	g2="AE" 	k="18" />
<hkern g1="bracketleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="66" />
<hkern g1="bracketleft" 	g2="Q" 	k="66" />
<hkern g1="bracketleft" 	g2="S" 	k="31" />
<hkern g1="bracketleft" 	g2="T" 	k="45" />
<hkern g1="bracketleft" 	g2="V" 	k="37" />
<hkern g1="bracketleft" 	g2="Y,Yacute,Ydieresis" 	k="33" />
<hkern g1="bracketleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="35" />
<hkern g1="bracketleft" 	g2="W" 	k="53" />
<hkern g1="bracketleft" 	g2="X" 	k="51" />
<hkern g1="bracketleft" 	g2="Z" 	k="61" />
<hkern g1="bracketleft" 	g2="AE" 	k="33" />
<hkern g1="bracketleft" 	g2="C,Ccedilla" 	k="66" />
<hkern g1="bracketleft" 	g2="G" 	k="66" />
<hkern g1="bracketleft" 	g2="J" 	k="35" />
<hkern g1="bracketleft" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="55" />
<hkern g1="bracketleft" 	g2="bracketright" 	k="-164" />
<hkern g1="braceleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="39" />
<hkern g1="braceleft" 	g2="Q" 	k="39" />
<hkern g1="braceleft" 	g2="S" 	k="20" />
<hkern g1="braceleft" 	g2="Z" 	k="14" />
<hkern g1="braceleft" 	g2="AE" 	k="14" />
<hkern g1="braceleft" 	g2="C,Ccedilla" 	k="31" />
<hkern g1="braceleft" 	g2="G" 	k="39" />
<hkern g1="braceleft" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="25" />
<hkern g1="braceleft" 	g2="braceright" 	k="-82" />
<hkern g1="braceright" 	g2="S" 	k="14" />
<hkern g1="braceright" 	g2="T" 	k="29" />
<hkern g1="braceright" 	g2="V" 	k="29" />
<hkern g1="braceright" 	g2="Y,Yacute,Ydieresis" 	k="27" />
<hkern g1="braceright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="braceright" 	g2="W" 	k="16" />
<hkern g1="braceright" 	g2="X" 	k="37" />
<hkern g1="braceright" 	g2="Z" 	k="47" />
<hkern g1="braceright" 	g2="AE" 	k="18" />
<hkern g1="braceright" 	g2="J" 	k="20" />
<hkern g1="braceright" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="14" />
<hkern g1="asterisk" 	g2="S" 	k="-25" />
<hkern g1="asterisk" 	g2="T" 	k="51" />
<hkern g1="asterisk" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="106" />
<hkern g1="asterisk" 	g2="X" 	k="29" />
<hkern g1="asterisk" 	g2="Z" 	k="88" />
<hkern g1="asterisk" 	g2="AE" 	k="174" />
<hkern g1="asterisk" 	g2="J" 	k="223" />
<hkern g1="quotedbl,quotesingle" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="29" />
<hkern g1="quotedbl,quotesingle" 	g2="Q" 	k="29" />
<hkern g1="quotedbl,quotesingle" 	g2="V" 	k="-27" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="137" />
<hkern g1="quotedbl,quotesingle" 	g2="W" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="AE" 	k="248" />
<hkern g1="quotedbl,quotesingle" 	g2="C,Ccedilla" 	k="29" />
<hkern g1="quotedbl,quotesingle" 	g2="G" 	k="29" />
<hkern g1="quotedbl,quotesingle" 	g2="J" 	k="225" />
<hkern g1="quoteleft,quotedblleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="Q" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="S" 	k="-27" />
<hkern g1="quoteleft,quotedblleft" 	g2="T" 	k="-43" />
<hkern g1="quoteleft,quotedblleft" 	g2="V" 	k="-39" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-27" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="129" />
<hkern g1="quoteleft,quotedblleft" 	g2="W" 	k="-35" />
<hkern g1="quoteleft,quotedblleft" 	g2="X" 	k="-25" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="244" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,Ccedilla" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="G" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="209" />
<hkern g1="quoteright,quotedblright" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="quoteright,quotedblright" 	g2="Q" 	k="41" />
<hkern g1="quoteright,quotedblright" 	g2="T" 	k="-23" />
<hkern g1="quoteright,quotedblright" 	g2="V" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="-31" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="145" />
<hkern g1="quoteright,quotedblright" 	g2="W" 	k="-33" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="260" />
<hkern g1="quoteright,quotedblright" 	g2="C,Ccedilla" 	k="41" />
<hkern g1="quoteright,quotedblright" 	g2="G" 	k="41" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="225" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="35" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Q" 	k="41" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="S" 	k="-33" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="113" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="V" 	k="127" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="133" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-53" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="47" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="X" 	k="-25" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Z" 	k="-43" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="AE" 	k="-43" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,Ccedilla" 	k="29" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="G" 	k="29" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="J" 	k="-57" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="16" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="133" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="45" />
<hkern g1="guillemotright,guilsinglright" 	g2="S" 	k="20" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="152" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="68" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="135" />
<hkern g1="guillemotright,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="18" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="20" />
<hkern g1="guillemotright,guilsinglright" 	g2="X" 	k="82" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="66" />
<hkern g1="guillemotright,guilsinglright" 	g2="AE" 	k="68" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="63" />
<hkern g1="hyphen,endash,emdash" 	g2="T" 	k="80" />
<hkern g1="hyphen,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="80" />
<hkern g1="hyphen,endash,emdash" 	g2="X" 	k="41" />
<hkern g1="hyphen,endash,emdash" 	g2="J" 	k="35" />
<hkern g1="comma,period,ellipsis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="45" />
<hkern g1="comma,period,ellipsis" 	g2="Q" 	k="45" />
<hkern g1="comma,period,ellipsis" 	g2="S" 	k="-29" />
<hkern g1="comma,period,ellipsis" 	g2="T" 	k="119" />
<hkern g1="comma,period,ellipsis" 	g2="V" 	k="133" />
<hkern g1="comma,period,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="139" />
<hkern g1="comma,period,ellipsis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-51" />
<hkern g1="comma,period,ellipsis" 	g2="W" 	k="59" />
<hkern g1="comma,period,ellipsis" 	g2="X" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="Z" 	k="-39" />
<hkern g1="comma,period,ellipsis" 	g2="AE" 	k="-39" />
<hkern g1="comma,period,ellipsis" 	g2="C,Ccedilla" 	k="37" />
<hkern g1="comma,period,ellipsis" 	g2="G" 	k="37" />
<hkern g1="comma,period,ellipsis" 	g2="J" 	k="-57" />
<hkern g1="comma,period,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="25" />
<hkern g1="colon,semicolon" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="colon,semicolon" 	g2="Q" 	k="14" />
<hkern g1="colon,semicolon" 	g2="T" 	k="150" />
<hkern g1="colon,semicolon" 	g2="V" 	k="20" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="59" />
<hkern g1="colon,semicolon" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-35" />
<hkern g1="colon,semicolon" 	g2="AE" 	k="-37" />
<hkern g1="colon,semicolon" 	g2="C,Ccedilla" 	k="14" />
<hkern g1="colon,semicolon" 	g2="G" 	k="14" />
<hkern g1="colon,semicolon" 	g2="J" 	k="-39" />
<hkern g1="colon,semicolon" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="33" />
<hkern g1="slash" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="slash" 	g2="Q" 	k="31" />
<hkern g1="slash" 	g2="T" 	k="-59" />
<hkern g1="slash" 	g2="V" 	k="-66" />
<hkern g1="slash" 	g2="Y,Yacute,Ydieresis" 	k="-70" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="80" />
<hkern g1="slash" 	g2="W" 	k="-45" />
<hkern g1="slash" 	g2="X" 	k="-41" />
<hkern g1="slash" 	g2="Z" 	k="-18" />
<hkern g1="slash" 	g2="AE" 	k="133" />
<hkern g1="slash" 	g2="C,Ccedilla" 	k="33" />
<hkern g1="slash" 	g2="G" 	k="33" />
<hkern g1="slash" 	g2="J" 	k="121" />
<hkern g1="exclamdown" 	g2="T" 	k="145" />
<hkern g1="exclamdown" 	g2="V" 	k="37" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="74" />
<hkern g1="exclamdown" 	g2="W" 	k="20" />
<hkern g1="exclamdown" 	g2="Z" 	k="14" />
<hkern g1="exclamdown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="14" />
<hkern g1="questiondown" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="53" />
<hkern g1="questiondown" 	g2="Q" 	k="53" />
<hkern g1="questiondown" 	g2="S" 	k="41" />
<hkern g1="questiondown" 	g2="T" 	k="201" />
<hkern g1="questiondown" 	g2="V" 	k="74" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="111" />
<hkern g1="questiondown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="72" />
<hkern g1="questiondown" 	g2="W" 	k="57" />
<hkern g1="questiondown" 	g2="X" 	k="117" />
<hkern g1="questiondown" 	g2="Z" 	k="115" />
<hkern g1="questiondown" 	g2="AE" 	k="121" />
<hkern g1="questiondown" 	g2="C,Ccedilla" 	k="53" />
<hkern g1="questiondown" 	g2="G" 	k="53" />
<hkern g1="questiondown" 	g2="J" 	k="102" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="68" />
<hkern g1="at" 	g2="S" 	k="14" />
<hkern g1="at" 	g2="T" 	k="68" />
<hkern g1="at" 	g2="V" 	k="51" />
<hkern g1="at" 	g2="Y,Yacute,Ydieresis" 	k="88" />
<hkern g1="at" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="at" 	g2="W" 	k="25" />
<hkern g1="at" 	g2="X" 	k="57" />
<hkern g1="at" 	g2="Z" 	k="47" />
<hkern g1="at" 	g2="AE" 	k="55" />
<hkern g1="at" 	g2="J" 	k="31" />
</font>
</defs></svg> 