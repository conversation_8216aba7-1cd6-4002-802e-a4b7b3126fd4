<div class="credit-card-update">
  <div class="fz-28 fw-bold px-3 px-sm-0">
    {{ 'credit-card-update.title' | translate }}
  </div>
  <ng-container
    *ngIf="
      this.selectedLoan &&
      this.selectedLoan.creditCardStatus !== creditCardStatuses.Updated
    "
  >
    <div class="d-flex py-2">
      <div class="credit-card-update-image">
        <img
          class="w-100 d-block"
          src="assets/images/borrowers/credit-card.svg"
          alt="credit-card-image"
        />
      </div>
      <div class="px-3">
        <div class="fz-16 mb-1">
          {{
            'credit-card-update.subtitle.incorrect-updated-credit-card'
              | translate: selected<PERSON>oan
          }}
        </div>
        <div>
          <span class="fw-bold" dir="ltr">
            {{ creditCardMask }} {{ currentCreditCard.creditCardLastDigits }}
          </span>
          <span
            *ngIf="currentCreditCard.expired || currentCreditCard.failed"
            class="color-1 px-2"
            [ngClass]="{
              'color-1': currentCreditCard.expired,
              'color-2': currentCreditCard.failed
            }"
          >
            <i class="fas fa-exclamation-triangle"></i>
            <span *ngIf="currentCreditCard.expired">
              {{ 'payment-methods.expired' | translate }}
            </span>
            <span *ngIf="currentCreditCard.failed">
              {{ 'payment-methods.failed' | translate }}
            </span>
          </span>
        </div>
      </div>
    </div>
  </ng-container>
  <div class="text-uppercase fz-16 mb-1 px-3 px-sm-0">
    {{ 'credit-card-update.subtitle.text' | translate }}
  </div>
  <!-- Disabled until borrowers is on production -->
  <!-- <div *ngIf="selectedLoanIndex > 0" class="text-uppercase fz-18 mb-1">
    {{ subtitleText | translate: loanNumbers }}
  </div> -->
  <div class="iframe-credit-card-container pt-4">
    <ng-container *ngIf="iframeLink">
      <iframe
        title="Credit card"
        class="iframe-credit-card border-0"
        [src]="iframeLink"
        width="100%"
        height="400"
        sandbox="allow-scripts allow-popups allow-forms allow-same-origin"
        allowfullscreen
      >
      </iframe>
    </ng-container>
  </div>
  <div class="d-flex justify-content-end">
    <!-- Button for continue / cancel is disabled until borrowers is on production -->
    <!-- <button class="btn btn-tar-outline-primary" routerLink="/app/borrowers">
      {{
        updateFailed
          ? ('credit-card-update.cancel' | translate)
          : ('credit-card-update.continue' | translate)
      }}
    </button> -->
    <button
      *ngIf="btnTryDifferentCardEnabled"
      class="btn btn-tar-outline-primary"
      (click)="tryDifferentCard()"
    >
      {{ 'credit-card-update.try-different-card' | translate }}
    </button>
  </div>
</div>
