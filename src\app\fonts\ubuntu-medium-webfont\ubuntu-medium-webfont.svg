<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="ubuntu_mediumregular" horiz-adv-x="1163" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="477" />
<glyph unicode="&#xfb01;" horiz-adv-x="1392" d="M158 0v1147q0 209 108.5 325.5t331.5 116.5q78 0 132 -12t85 -25l-39 -200q-29 10 -68.5 18t-86.5 8q-123 0 -169 -65.5t-46 -169.5v-72h393v-207h-393v-864h-248zM956 1384q0 72 45.5 113t108.5 41q61 0 106.5 -41t45.5 -113q0 -70 -45.5 -110.5t-106.5 -40.5 q-63 0 -108.5 41t-45.5 110zM985 0v1071h248v-1071h-248z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1439" d="M158 0v1147q0 209 108.5 325.5t331.5 116.5q78 0 132 -12t85 -25l-39 -200q-29 10 -68.5 18t-86.5 8q-123 0 -169 -65.5t-46 -169.5v-72h393v-207h-393v-864h-248zM985 315v1233l248 41v-1227q0 -45 7 -75.5t25.5 -52t50.5 -33t81 -17.5l-35 -204q-111 2 -183.5 24.5 t-115.5 64.5t-60.5 104.5t-17.5 141.5z" />
<glyph unicode="&#xfb03;" horiz-adv-x="2220" d="M158 0v1147q0 207 108.5 324.5t327.5 117.5q86 0 151.5 -15t98.5 -28l-47 -205q-35 16 -81 26.5t-100 10.5q-59 0 -100 -17.5t-65.5 -48t-34.5 -74.5t-10 -95v-72h389v-207h-389v-864h-248zM985 0v1147q0 209 108.5 325.5t331.5 116.5q78 0 132 -12t85 -25l-39 -200 q-29 10 -68.5 18t-86.5 8q-123 0 -169 -65.5t-46 -169.5v-72h393v-207h-393v-864h-248zM1783 1384q0 72 45.5 113t108.5 41q61 0 106.5 -41t45.5 -113q0 -70 -45.5 -110.5t-106.5 -40.5q-63 0 -108.5 41t-45.5 110zM1812 0v1071h248v-1071h-248z" />
<glyph unicode="&#xfb04;" horiz-adv-x="2355" d="M158 0v1147q0 207 108.5 324.5t327.5 117.5q86 0 151.5 -15t98.5 -28l-47 -205q-35 16 -81 26.5t-100 10.5q-59 0 -100 -17.5t-65.5 -48t-34.5 -74.5t-10 -95v-72h389v-207h-389v-864h-248zM985 0v1147q0 209 108.5 325.5t331.5 116.5q78 0 132 -12t85 -25l-39 -200 q-29 10 -68.5 18t-86.5 8q-123 0 -169 -65.5t-46 -169.5v-72h393v-207h-393v-864h-248zM1812 315v1233l248 41v-1227q0 -45 7 -75.5t25.5 -52t50.5 -33t81 -17.5l-35 -204q-111 2 -183.5 24.5t-115.5 64.5t-60.5 104.5t-17.5 141.5z" />
<glyph unicode="&#xd;" horiz-adv-x="477" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode=" "  horiz-adv-x="477" />
<glyph unicode="&#x09;" horiz-adv-x="477" />
<glyph unicode="&#xa0;" horiz-adv-x="477" />
<glyph unicode="!" horiz-adv-x="626" d="M150 135q0 72 48 118t115 46q70 0 117 -46t47 -118t-47 -117t-117 -45q-68 0 -115.5 45t-47.5 117zM182 1038v381h264v-381q0 -170 -12 -311t-31 -285h-176q-20 143 -32.5 285.5t-12.5 310.5z" />
<glyph unicode="&#x22;" horiz-adv-x="935" d="M141 1432v124h250v-126q0 -98 -14.5 -212t-32.5 -229h-158q-20 115 -32.5 227.5t-12.5 215.5zM544 1432v124h250v-126q0 -98 -14.5 -212t-32.5 -229h-158q-20 115 -32.5 227.5t-12.5 215.5z" />
<glyph unicode="#" horiz-adv-x="1374" d="M94 360v195h201l59 309h-260v193h297l70 362h219l-70 -362h285l70 362h219l-70 -362h166v-193h-203l-59 -309h262v-195h-299l-70 -360h-219l70 360h-285l-69 -360h-220l70 360h-164zM514 555h285l59 309h-285z" />
<glyph unicode="$" d="M104 121l70 194q66 -33 159 -59.5t212 -26.5q141 0 195.5 42t54.5 112q0 47 -22.5 82t-63.5 61.5t-97.5 49t-124.5 47.5q-63 23 -125.5 51t-112.5 70t-81 101.5t-31 145.5q0 141 84 240.5t258 128.5v229h213v-221q98 -8 176 -27.5t123 -37.5l-51 -203q-59 23 -140 43 t-186 20q-111 0 -166 -40t-55 -109q0 -41 16.5 -68.5t49.5 -49.5t80 -41t106 -40q84 -33 159 -68.5t130 -84t87 -114t32 -155.5q0 -135 -84 -233.5t-277 -126.5v-256h-213v250q-147 10 -238 39.5t-137 54.5z" />
<glyph unicode="%" horiz-adv-x="1837" d="M96 1063q0 188 95.5 287.5t248.5 99.5q154 0 249 -99.5t95 -287.5q0 -186 -95 -286.5t-249 -100.5q-76 0 -139 25.5t-108.5 74.5t-71 122t-25.5 165zM293 1063q0 -109 39 -163t108 -54q70 0 110 54t40 163t-40 163t-110 54t-108.5 -54.5t-38.5 -162.5zM399 0l801 1419 h236l-801 -1419h-236zM1053 354q0 188 95 287.5t249 99.5t249 -99.5t95 -287.5q0 -186 -95.5 -286.5t-248.5 -100.5q-76 0 -139.5 25.5t-108.5 75t-70.5 122t-25.5 164.5zM1249 354q0 -109 39 -163t109 -54t109.5 54t39.5 163t-40 163t-109 54q-70 0 -109 -54t-39 -163z" />
<glyph unicode="&#x26;" horiz-adv-x="1400" d="M88 377q0 78 23.5 141.5t63.5 114.5t92 90t110 70q-61 66 -99 139.5t-38 154.5q0 172 105.5 268.5t277.5 96.5q88 0 156.5 -24.5t113.5 -67.5t68.5 -101.5t23.5 -123.5q0 -66 -21.5 -123.5t-58.5 -105.5t-85 -88t-103 -70l280 -283q29 53 47.5 119.5t28.5 130.5l207 -27 q-10 -82 -43 -181t-86 -190q68 -76 130 -156.5t103 -160.5h-258q-20 35 -52 76t-66 76q-84 -76 -192.5 -123t-246.5 -47q-137 0 -229 36.5t-148.5 94t-80 127t-23.5 137.5zM326 393q0 -35 13 -71.5t43 -68.5t78 -52.5t115 -20.5q92 0 167 29t128 82l-360 366 q-31 -16 -63.5 -38.5t-59.5 -54.5t-44 -74t-17 -97zM459 1102q0 -59 32.5 -117.5t90.5 -112.5q39 20 72.5 45t59 56t41 67.5t15.5 83.5q0 59 -39 101.5t-113 42.5q-78 0 -118.5 -47t-40.5 -119z" />
<glyph unicode="'" horiz-adv-x="532" d="M141 1432v124h250v-126q0 -98 -14.5 -212t-32.5 -229h-158q-20 115 -32.5 227.5t-12.5 215.5z" />
<glyph unicode="(" horiz-adv-x="694" d="M154 612q0 287 96 540t262 452l174 -115q-135 -184 -211 -404.5t-76 -472.5t76 -473t211 -403l-174 -115q-166 199 -262 452t-96 539z" />
<glyph unicode=")" horiz-adv-x="694" d="M8 -264q135 182 211 403t76 473t-76 472.5t-211 404.5l174 115q166 -199 262.5 -452t96.5 -540t-96.5 -539.5t-262.5 -451.5z" />
<glyph unicode="*" horiz-adv-x="987" d="M70 1030l69 213l31 -10q70 -23 139.5 -66t128.5 -82l-37.5 150t-19.5 149v35h225v-35q0 -72 -19.5 -149.5t-37.5 -149.5q59 39 130 82t138 66l31 10l70 -213l-33 -10q-66 -20 -149 -27.5t-154 -9.5q57 -45 119.5 -97t105.5 -112l20 -29l-182 -129l-20 27q-41 59 -72 135 t-59 142q-27 -66 -59 -142t-73 -135l-18 -27l-184 129l22 29q41 59 103.5 111.5t122.5 97.5q-72 2 -155 7t-151 30z" />
<glyph unicode="+" d="M106 481v209h365v402h221v-402h365v-209h-365v-401h-221v401h-365z" />
<glyph unicode="," horiz-adv-x="548" d="M72 -276q49 119 66.5 232.5t17.5 199.5q0 25 -1 61.5t-3 54.5h262q4 -35 4 -51v-33q0 -127 -40 -262t-112 -256z" />
<glyph unicode="-" horiz-adv-x="645" d="M47 471v227h551v-227h-551z" />
<glyph unicode="." horiz-adv-x="544" d="M109 135q0 72 47 118t116 46q68 0 116 -46t48 -118t-48 -117t-116 -45q-70 0 -116.5 45t-46.5 117z" />
<glyph unicode="/" horiz-adv-x="851" d="M-55 -379l692 1983h250l-688 -1983h-254z" />
<glyph unicode="0" d="M88 711q0 178 34 316t98.5 232.5t155.5 143.5t206 49q231 0 362 -191.5t131 -549.5t-131 -550t-362 -192t-362.5 191.5t-131.5 550.5zM344 711q0 -111 11.5 -207t39 -167t73.5 -112t114 -41t112.5 41t72.5 112t40 167t12 207t-12 207t-40 166.5t-73 111.5t-112 41 q-68 0 -114 -41t-73.5 -111.5t-39 -167t-11.5 -206.5z" />
<glyph unicode="1" d="M184 1124q119 47 239 121t212 174h174v-1419h-248v1087q-55 -43 -139 -83.5t-162 -67.5z" />
<glyph unicode="2" d="M92 1270q86 82 202 132t249 50q227 0 334.5 -107.5t107.5 -293.5q0 -74 -30.5 -144.5t-79 -137.5t-109.5 -129t-123 -122q-33 -31 -74 -74t-77.5 -86t-63.5 -82.5t-29 -66.5h631v-209h-903q-2 16 -2 39.5v36.5q0 98 31.5 181t83 154.5t115 135t126.5 125.5q49 47 94.5 92 t79 89t54 90t20.5 95q0 106 -61.5 151.5t-149.5 45.5q-57 0 -106.5 -16.5t-87 -39t-66.5 -46t-43 -37.5z" />
<glyph unicode="3" d="M98 45l49 211q43 -20 125 -46t203 -26q162 0 233.5 62.5t71.5 167.5q0 68 -27.5 112.5t-75.5 72.5t-110.5 39t-132.5 11h-84v201h103q47 0 95 9t87 32.5t63.5 62.5t24.5 101q0 49 -18.5 84t-48 57.5t-68.5 32.5t-82 10q-92 0 -164 -27.5t-127 -62.5l-90 184q29 18 70 39 t91 39.5t107.5 29.5t120.5 11q119 0 205 -28.5t142.5 -81t84 -123t27.5 -154.5q0 -98 -54.5 -176t-140.5 -119q111 -39 183.5 -129t72.5 -231q0 -94 -32.5 -175t-100 -139.5t-171.5 -92.5t-247 -34q-55 0 -113.5 7.5t-110.5 18.5t-95 24.5t-66 25.5z" />
<glyph unicode="4" d="M68 340v176q41 90 109.5 207t152.5 239.5t178 241.5t188 215h236v-876h160v-203h-160v-340h-242v340h-622zM311 543h379v571q-49 -59 -101 -126.5t-101.5 -141.5t-94.5 -151t-82 -152z" />
<glyph unicode="5" d="M117 43l47 209q43 -20 124 -44t197 -24q80 0 137.5 17.5t93.5 48.5t52 71t16 85q0 68 -26.5 121t-93 89.5t-178 55t-281.5 18.5q23 197 35 374t20 355h725v-209h-514q-2 -35 -5 -79t-6 -89t-7 -86t-9 -69q303 -16 447.5 -138t144.5 -333q0 -96 -32.5 -178t-100 -141.5 t-170 -93.5t-239.5 -34q-55 0 -112.5 8.5t-110 19.5t-93.5 23.5t-61 22.5z" />
<glyph unicode="6" d="M106 573q0 205 60.5 364t172.5 267.5t270.5 164.5t357.5 58l14 -204q-111 -2 -207 -23.5t-172 -67t-130 -117t-83 -175.5q100 47 219 47q127 0 216 -36t144.5 -97.5t81 -142.5t25.5 -169q0 -80 -27.5 -163.5t-86 -152.5t-146.5 -113t-209 -44q-244 0 -372 161t-128 443z M358 575q0 -82 11.5 -154.5t39 -126.5t75.5 -86t120 -32q59 0 101.5 24.5t69 63.5t38.5 85t12 89q0 123 -59 187.5t-188 64.5q-59 0 -114.5 -10t-101.5 -31q-2 -16 -3 -35.5t-1 -38.5z" />
<glyph unicode="7" d="M109 1206v213h948v-188q-68 -76 -148 -207t-151.5 -296t-123.5 -353.5t-65 -374.5h-254q12 156 55.5 328t106 334.5t137 305t148.5 238.5h-653z" />
<glyph unicode="8" d="M98 365q0 61 17.5 113t48.5 96t69.5 81t81.5 66q-94 66 -140 142.5t-46 187.5q0 80 32 152.5t91 127.5t143 88t187 33q121 0 205.5 -35t139 -90t79 -122.5t24.5 -131.5q0 -113 -61.5 -200t-141.5 -138q121 -66 179.5 -151.5t58.5 -202.5q0 -88 -30.5 -164t-92 -131 t-153 -86t-209.5 -31q-139 0 -231.5 39t-148 98.5t-79 128t-23.5 130.5zM342 369q0 -31 12.5 -66t41 -63.5t73.5 -48t113 -19.5q61 0 106 16.5t74 44t43 63.5t14 73q0 57 -22.5 100t-64.5 74.5t-99.5 55.5t-126.5 42q-74 -47 -119 -113.5t-45 -158.5zM373 1065 q0 -78 58 -147.5t196 -110.5q76 47 118.5 107.5t42.5 154.5q0 27 -11 58.5t-36.5 58t-64.5 45t-94 18.5t-95.5 -17.5t-65 -45t-36.5 -59t-12 -62.5z" />
<glyph unicode="9" d="M88 979q0 80 26.5 163t84 152.5t145.5 113.5t209 44q250 0 378 -161t128 -453q0 -414 -213 -627t-653 -215l-11 207q123 0 222.5 20.5t174 65.5t125.5 118t78 179q-49 -23 -108.5 -35t-114.5 -12q-127 0 -216 33.5t-145.5 94t-83 140.5t-26.5 172zM338 987 q0 -125 60.5 -188.5t189.5 -63.5q59 0 116.5 11.5t98.5 33.5q2 23 2 39.5v28.5q0 82 -11.5 154.5t-39 127t-75.5 86t-120 31.5q-59 0 -101 -24.5t-69 -63.5t-39 -84t-12 -88z" />
<glyph unicode=":" horiz-adv-x="544" d="M109 135q0 72 47 118t116 46q68 0 116 -46t48 -118t-48 -117t-116 -45q-70 0 -116.5 45t-46.5 117zM109 901q0 72 47 118t116 46q68 0 116 -46t48 -118t-48 -117t-116 -45q-70 0 -116.5 45t-46.5 117z" />
<glyph unicode=";" horiz-adv-x="540" d="M72 -276q49 119 66.5 232.5t17.5 199.5q0 25 -1 61.5t-3 54.5h262q4 -35 4 -51v-33q0 -127 -40 -262t-112 -256zM107 901q0 72 47 118t116 46q68 0 116 -46t48 -118t-48 -117t-116 -45q-70 0 -116.5 45t-46.5 117z" />
<glyph unicode="&#x3c;" d="M106 479v209l883 373l68 -203l-686 -274l686 -275l-68 -203z" />
<glyph unicode="=" d="M106 260v209h951v-209h-951zM106 702v209h951v-209h-951z" />
<glyph unicode="&#x3e;" d="M106 309l687 275l-687 274l68 203l883 -373v-209l-883 -373z" />
<glyph unicode="?" horiz-adv-x="868" d="M33 1358q76 43 173 68.5t200 25.5q123 0 202.5 -34t126.5 -85t65.5 -112.5t18.5 -118.5q0 -70 -25.5 -125t-64.5 -102.5t-84 -89.5t-84 -86t-64.5 -93t-25.5 -110v-23.5t2 -26.5h-213q-4 20 -6 44t-2 45q0 68 22.5 121t57.5 98t74.5 84t74.5 77.5t57.5 79.5t22.5 90 q0 68 -46 112t-138 44q-72 0 -137.5 -17.5t-137.5 -56.5zM209 135q0 72 48 118t116 46q70 0 117 -46t47 -118t-47.5 -117t-116.5 -45q-68 0 -116 45t-48 117z" />
<glyph unicode="@" horiz-adv-x="1988" d="M127 563q0 223 73.5 392t196.5 281t282 168t331 56q178 0 333.5 -53t270.5 -154.5t181.5 -249t66.5 -335.5q0 -145 -32 -256t-89.5 -186.5t-135 -114.5t-171.5 -39q-61 0 -112.5 17t-86.5 54q-45 -33 -105.5 -49t-125.5 -16q-100 0 -183.5 30.5t-144 92t-93 152.5 t-32.5 212q0 100 32.5 189.5t95 155t154 103.5t207.5 38q113 0 203 -18.5t143 -39.5v-635q0 -57 22 -78.5t58 -21.5q88 0 138.5 109.5t50.5 314.5q0 127 -44 236.5t-128 189.5t-204 126t-269 46q-147 0 -271.5 -49t-213.5 -141.5t-139 -225.5t-50 -301q0 -176 51 -308 t144.5 -220t223.5 -133t289 -45q111 0 196 13t128 24l25 -173q-23 -6 -59 -14t-82 -15t-99 -12.5t-109 -5.5q-190 0 -357 51.5t-291 160t-196.5 276.5t-72.5 401zM786 565q0 -129 55.5 -213t172.5 -84q37 0 76.5 5.5t78.5 21.5q-4 23 -7 51.5t-3 54.5v451q-41 10 -106 10 q-70 0 -120 -22.5t-83 -62.5t-48.5 -94t-15.5 -118z" />
<glyph unicode="A" horiz-adv-x="1400" d="M10 0q80 227 151.5 419.5t140.5 364.5t136.5 328t139.5 307h243q72 -152 139.5 -307.5t136 -327.5t141.5 -364.5t153 -419.5h-279q-29 84 -59.5 166t-59.5 168h-598q-29 -86 -58.5 -169t-57.5 -165h-269zM461 543h467q-66 182 -127.5 340t-106.5 262 q-47 -109 -107.5 -264.5t-125.5 -337.5z" />
<glyph unicode="B" horiz-adv-x="1345" d="M170 20v1379q45 8 96 15t104.5 11.5t105.5 6.5t99 2q129 0 240 -19.5t191.5 -64.5t126 -119t45.5 -182q0 -100 -48.5 -172t-134.5 -115q129 -43 190.5 -127t61.5 -213q0 -219 -159.5 -328.5t-489.5 -109.5q-113 0 -227.5 7t-200.5 29zM424 207q43 -4 92 -6t90 -2 q80 0 148.5 10t119 35.5t80 68.5t29.5 111q0 121 -88 168t-244 47h-227v-432zM424 842h182q147 0 231.5 42t84.5 150q0 102 -87 145.5t-227 43.5q-59 0 -105 -2t-79 -7v-372z" />
<glyph unicode="C" horiz-adv-x="1306" d="M117 711q0 178 54 315t148.5 232.5t221.5 144.5t272 49q88 0 159.5 -13.5t125 -29.5t88.5 -34.5t49 -26.5l-76 -211q-49 31 -140 58.5t-198 27.5q-92 0 -172 -32t-138 -95.5t-91 -159.5t-33 -223q0 -113 25.5 -207t79 -163t135.5 -106.5t196 -37.5q137 0 223.5 27.5 t133.5 52.5l69 -211q-25 -16 -66.5 -33t-99 -32t-129 -24.5t-153.5 -9.5q-160 0 -287 49.5t-215 144.5t-135 232t-47 316z" />
<glyph unicode="D" horiz-adv-x="1486" d="M170 20v1379q98 23 208.5 29t196.5 6q178 0 326 -42t253.5 -131.5t162.5 -225.5t57 -324q0 -184 -57 -320.5t-162.5 -227.5t-254 -135t-329.5 -44q-86 0 -194.5 7t-206.5 29zM428 213q23 -2 59.5 -3t100.5 -1q260 0 388 132t128 370q0 242 -125 370.5t-387 128.5 q-115 0 -164 -6v-991z" />
<glyph unicode="E" horiz-adv-x="1200" d="M170 0v1419h911v-219h-653v-350h582v-215h-582v-416h702v-219h-960z" />
<glyph unicode="F" horiz-adv-x="1128" d="M170 0v1419h897v-219h-639v-362h567v-220h-567v-618h-258z" />
<glyph unicode="G" horiz-adv-x="1398" d="M113 711q0 178 54 315t148.5 232.5t221.5 144.5t270 49q98 0 175 -13.5t132 -29.5t91 -34.5t50 -26.5l-77 -211q-61 37 -152.5 62.5t-191.5 25.5q-219 0 -335 -136.5t-116 -377.5q0 -115 27.5 -210t82 -163t135.5 -105.5t189 -37.5q68 0 117 5t78 13v510h258v-682 q-53 -20 -180 -45t-293 -25q-154 0 -281 49.5t-216 143.5t-138 231t-49 316z" />
<glyph unicode="H" horiz-adv-x="1472" d="M170 0v1419h258v-567h616v567h259v-1419h-259v629h-616v-629h-258z" />
<glyph unicode="I" horiz-adv-x="598" d="M170 0v1419h258v-1419h-258z" />
<glyph unicode="J" horiz-adv-x="1050" d="M10 88l92 203q51 -35 126 -66.5t153 -31.5q131 0 190.5 65.5t59.5 224.5v936h260v-952q0 -104 -22.5 -194.5t-79 -158t-151.5 -106.5t-241 -39q-135 0 -234.5 38t-152.5 81z" />
<glyph unicode="K" horiz-adv-x="1333" d="M170 0v1419h258v-585q68 68 141.5 144.5t146 155t138.5 152.5t119 133h309q-68 -78 -143.5 -161.5t-155.5 -168.5t-161 -169t-158 -160q86 -66 175 -153t175 -186t164.5 -207t144.5 -214h-305q-53 82 -123 172t-146.5 174t-158.5 160t-162 131v-637h-258z" />
<glyph unicode="L" horiz-adv-x="1093" d="M170 0v1419h258v-1196h639v-223h-897z" />
<glyph unicode="M" horiz-adv-x="1810" d="M131 0q8 176 19.5 364.5t24.5 374t29.5 359.5t35.5 321h239q45 -78 100.5 -190.5t113 -236.5t113.5 -252t103 -236q47 109 102.5 236.5t113 251.5t112.5 236.5t100 190.5h234q18 -156 34.5 -331t30 -358t24.5 -368.5t19 -361.5h-254q-8 248 -19 510t-34 508 q-23 -47 -53.5 -112.5t-65 -142.5t-70.5 -159t-70 -158.5t-62.5 -143t-49.5 -114.5h-192q-20 47 -49 115t-62.5 144.5t-69.5 158.5t-71 158t-65.5 142.5t-53.5 111.5q-23 -246 -34 -508t-19 -510h-254z" />
<glyph unicode="N" horiz-adv-x="1515" d="M170 0v1419h211q82 -86 176 -203.5t189.5 -244.5t184.5 -255t159 -239v942h256v-1419h-220q-70 117 -155.5 251t-178 270t-187.5 264t-181 231v-1016h-254z" />
<glyph unicode="O" horiz-adv-x="1601" d="M113 711q0 182 55 320t150.5 232.5t220.5 141.5t264 47t264 -47t218 -141.5t148.5 -232.5t55.5 -320t-54.5 -321.5t-147.5 -233t-218 -140.5t-266 -47t-267 47t-219.5 140.5t-148.5 232.5t-55 322zM381 711q0 -117 28.5 -212.5t83 -162t132 -103t176.5 -36.5 q96 0 174 36.5t132 103t83 162t29 212.5t-29 212t-83 161.5t-132 103.5t-174 37q-98 0 -176 -37t-132.5 -104.5t-83 -161.5t-28.5 -211z" />
<glyph unicode="P" horiz-adv-x="1275" d="M170 0v1399q90 20 199.5 27.5t201.5 7.5q309 0 474 -113t165 -352q0 -125 -44 -214t-128 -145.5t-204.5 -83t-276.5 -26.5h-129v-500h-258zM428 721h125q186 0 286.5 52t100.5 198q0 70 -26.5 117t-73.5 73.5t-111.5 37.5t-138.5 11q-98 0 -162 -6v-483z" />
<glyph unicode="Q" horiz-adv-x="1601" d="M113 711q0 182 55 320t150.5 232.5t220.5 141.5t264 47t264 -47t218 -141.5t148.5 -232.5t55.5 -320q0 -154 -39 -277t-108.5 -214t-163 -148.5t-203.5 -81.5q6 -43 43 -74t93 -51.5t129 -33.5t153 -19l-58 -195q-264 20 -419.5 101t-198.5 257q-127 12 -236.5 65.5 t-191.5 146t-129 223.5t-47 301zM381 711q0 -117 28.5 -212.5t83 -162t132 -103t176.5 -36.5q96 0 174 36.5t132 103t83 162t29 212.5t-29 212t-83 161.5t-132 103.5t-174 37q-98 0 -176 -37t-132.5 -104.5t-83 -161.5t-28.5 -211z" />
<glyph unicode="R" horiz-adv-x="1310" d="M170 0v1399q94 20 200.5 27.5t190.5 7.5q307 0 470 -113t163 -344q0 -289 -285 -391q39 -47 88.5 -115t100.5 -146.5t98 -161.5t84 -163h-289q-39 74 -84 148.5t-91 145.5t-91 133t-84 112q-29 -2 -49.5 -2h-38.5h-125v-537h-258zM428 745h113q94 0 165.5 10.5t119.5 37 t73 71.5t25 115q0 66 -25 111t-71 71.5t-109 37.5t-139 11q-82 0 -152 -6v-459z" />
<glyph unicode="S" horiz-adv-x="1105" d="M61 74l76 211q55 -31 149.5 -61.5t227.5 -30.5q139 0 202.5 47t63.5 133q0 51 -21.5 88t-61.5 66.5t-97 55t-131 50.5q-74 27 -142.5 58.5t-120.5 78.5t-84 112.5t-32 157.5q0 193 133 302.5t363 109.5q133 0 236.5 -29.5t162.5 -64.5l-80 -209q-70 39 -152.5 59.5 t-170.5 20.5q-104 0 -163 -43t-59 -121q0 -47 19.5 -81t55.5 -60.5t84 -49t106 -43.5q100 -37 179 -74.5t133 -90t83 -123t29 -170.5q0 -193 -136.5 -298.5t-398.5 -105.5q-88 0 -160.5 11.5t-129 28t-97.5 33.5t-66 32z" />
<glyph unicode="T" horiz-adv-x="1185" d="M27 1196v223h1132v-223h-436v-1196h-260v1196h-436z" />
<glyph unicode="U" horiz-adv-x="1439" d="M160 532v887h260v-862q0 -96 21.5 -164.5t61.5 -111.5t94 -63.5t122 -20.5t123 20.5t95 63.5t61.5 111.5t21.5 164.5v862h260v-887q0 -123 -34 -225t-102.5 -178t-175 -118t-251.5 -42t-250 42t-173.5 118t-101 178.5t-32.5 224.5z" />
<glyph unicode="V" horiz-adv-x="1398" d="M14 1419h285q49 -143 100.5 -292.5t102.5 -294t102 -279.5t101 -248q47 113 98 247t103 279.5t103.5 295t98.5 292.5h276q-125 -362 -266 -724.5t-295 -694.5h-248q-154 332 -296 694.5t-265 724.5z" />
<glyph unicode="W" horiz-adv-x="1921" d="M51 1419h277q25 -139 52.5 -284.5t57 -285.5t60 -271.5t61.5 -241.5q39 104 79 220t79 235.5t74.5 238.5t66.5 230h221q33 -111 70 -231t77 -239.5t80 -235.5t76 -218q31 111 59.5 241t57.5 270t56.5 285.5t52.5 286.5h266q-72 -385 -160 -740t-196 -679h-246 q-160 414 -309 903q-76 -248 -154 -471t-156 -432h-245q-111 324 -198 679t-159 740z" />
<glyph unicode="X" horiz-adv-x="1345" d="M41 0q92 174 213 357.5t262 383.5l-455 678h308l313 -485l309 485h295l-448 -676q156 -211 275.5 -399t201.5 -344h-303q-59 121 -149.5 266.5t-188.5 280.5q-41 -53 -89 -127t-96.5 -150.5t-89.5 -148.5t-65 -121h-293z" />
<glyph unicode="Y" horiz-adv-x="1280" d="M6 1419h303q72 -160 158 -317t180 -311q92 154 180 311.5t160 316.5h287q-115 -217 -239 -431t-265 -431v-557h-258v553q-141 219 -266 434t-240 432z" />
<glyph unicode="Z" horiz-adv-x="1204" d="M61 0v162q41 76 95.5 165t116 183t128 190.5t132 185.5t128 170t117.5 144h-682v219h1016v-190q-68 -74 -164 -191.5t-198.5 -256t-201.5 -285t-175 -277.5h762v-219h-1074z" />
<glyph unicode="[" horiz-adv-x="708" d="M190 -379v1983h508v-197h-274v-1589h274v-197h-508z" />
<glyph unicode="\" horiz-adv-x="851" d="M-35 1604h250l692 -1983h-254z" />
<glyph unicode="]" horiz-adv-x="708" d="M10 -182h273v1589h-273v197h508v-1983h-508v197z" />
<glyph unicode="^" d="M68 715l411 704h205l410 -704l-203 -101l-309 539l-310 -539z" />
<glyph unicode="_" horiz-adv-x="1024" d="M0 -172h1024v-207h-1024v207z" />
<glyph unicode="`" horiz-adv-x="802" d="M158 1511l157 144l297 -359l-123 -110z" />
<glyph unicode="a" horiz-adv-x="1093" d="M82 324q0 92 36 155.5t97 103.5t142 57t169 17q41 0 86 -5t97 -17v41q0 43 -10.5 82t-36 68.5t-67.5 46t-106 16.5q-86 0 -157.5 -12.5t-116.5 -28.5l-31 201q47 16 137.5 32.5t192.5 16.5q123 0 207 -31t134 -86t71.5 -134t21.5 -173v-649q-57 -12 -173 -30t-261 -18 q-96 0 -176 18.5t-136.5 59.5t-88 106.5t-31.5 162.5zM330 330q0 -88 55.5 -122t149.5 -34q115 0 174 12v275q-20 6 -59.5 12t-86.5 6q-41 0 -83 -6t-75.5 -22.5t-54 -46t-20.5 -74.5z" />
<glyph unicode="b" horiz-adv-x="1220" d="M158 33v1515l248 41v-555q43 23 105 41.5t138 18.5q113 0 202 -40t148.5 -114t91 -177t31.5 -228q0 -129 -37.5 -232.5t-108.5 -177.5t-171 -113t-227 -39q-123 0 -235 18.5t-185 41.5zM406 205q27 -6 68.5 -11.5t100.5 -5.5q137 0 215 93.5t78 257.5q0 158 -62.5 250 t-197.5 92q-61 0 -115.5 -18.5t-86.5 -41.5v-616z" />
<glyph unicode="c" horiz-adv-x="966" d="M98 535q0 119 37 222t105.5 179t167 119t221.5 43q152 0 286 -56l-53 -202q-43 18 -97 30.5t-116 12.5q-145 0 -221 -91.5t-76 -256.5q0 -160 72 -253.5t242 -93.5q63 0 124.5 12.5t106.5 30.5l35 -204q-41 -20 -124 -37t-171 -17q-137 0 -238.5 42t-168 117t-99.5 178.5 t-33 224.5z" />
<glyph unicode="d" horiz-adv-x="1220" d="M98 532q0 127 32 230.5t93.5 177.5t149.5 114t202 40q78 0 137.5 -18.5t102.5 -41.5v514l248 41v-1556q-74 -23 -185.5 -41.5t-234.5 -18.5q-127 0 -227.5 39t-171 112t-108.5 176t-38 232zM352 539q0 -164 78 -257.5t215 -93.5q59 0 101 5.5t69 11.5v614 q-33 23 -87 42.5t-116 19.5q-135 0 -197.5 -92t-62.5 -250z" />
<glyph unicode="e" horiz-adv-x="1169" d="M98 530q0 141 42 248t112 177.5t160 106.5t184 36q221 0 345 -137.5t124 -409.5q0 -20 -1 -46t-3 -46h-707q10 -129 91 -200t235 -71q90 0 165 16.5t118 35.5l32 -203q-20 -10 -56 -21.5t-82 -20.5t-99 -15.5t-109 -6.5q-141 0 -245.5 42t-172 117t-100.5 176t-33 222z M356 641h461q0 51 -14.5 97t-42 80t-67.5 53.5t-95 19.5q-57 0 -100 -21.5t-73 -56.5t-46.5 -80t-22.5 -92z" />
<glyph unicode="f" horiz-adv-x="819" d="M158 0v1147q0 207 108.5 324.5t327.5 117.5q86 0 151.5 -15t98.5 -28l-47 -205q-35 16 -81 26.5t-100 10.5q-59 0 -100 -17.5t-65.5 -48t-34.5 -74.5t-10 -95v-72h389v-207h-389v-864h-248z" />
<glyph unicode="g" horiz-adv-x="1200" d="M98 563q0 117 36 215.5t104.5 168t167 108.5t223.5 39q121 0 229.5 -18.5t183.5 -39.5v-927q0 -256 -130 -375t-398 -119q-98 0 -191.5 16.5t-168.5 42.5l45 211q63 -27 144 -43t175 -16q150 0 213.5 61.5t63.5 182.5v41q-37 -18 -97.5 -37t-138.5 -19q-102 0 -187 33 t-145.5 96.5t-94.5 158.5t-34 220zM352 563q0 -158 68.5 -230.5t177.5 -72.5q59 0 111.5 16.5t85.5 38.5v555q-27 6 -66 11.5t-98 5.5q-135 0 -207 -89.5t-72 -234.5z" />
<glyph unicode="h" horiz-adv-x="1191" d="M158 0v1548l248 41v-530q41 14 95 24.5t107 10.5q129 0 214 -36t136.5 -100.5t73 -154.5t21.5 -201v-602h-248v563q0 86 -11.5 146.5t-37 98.5t-68.5 55.5t-106 17.5q-49 0 -100.5 -10.5t-75.5 -18.5v-852h-248z" />
<glyph unicode="i" horiz-adv-x="565" d="M129 1384q0 72 45 113t109 41q61 0 106 -41t45 -113q0 -70 -45 -110.5t-106 -40.5q-63 0 -108.5 41t-45.5 110zM160 0v1071h248v-1071h-248z" />
<glyph unicode="j" horiz-adv-x="563" d="M-139 -356l33 202q45 -14 108 -14q86 0 121 50t35 151v1038h248v-1042q0 -209 -100 -309.5t-286 -100.5q-27 0 -74.5 5t-84.5 20zM127 1384q0 72 45 113t109 41q61 0 106 -41t45 -113q0 -70 -45 -110.5t-106 -40.5q-63 0 -108.5 41t-45.5 110z" />
<glyph unicode="k" horiz-adv-x="1128" d="M158 0v1548l248 41v-936q47 49 100 105.5t104.5 113t97.5 108.5t78 91h293q-102 -115 -215.5 -237.5t-228.5 -239.5q61 -51 128.5 -124t131 -155t119 -163.5t92.5 -151.5h-287q-37 63 -85 133t-103.5 135.5t-112.5 124t-112 99.5v-492h-248z" />
<glyph unicode="l" horiz-adv-x="608" d="M154 315v1233l247 41v-1227q0 -45 7.5 -75.5t26 -52t50 -33t80.5 -17.5l-35 -204q-111 2 -183.5 24.5t-115.5 64.5t-60 104.5t-17 141.5z" />
<glyph unicode="m" horiz-adv-x="1771" d="M158 0v1036q72 20 187.5 39t244.5 19q111 0 181.5 -29t117.5 -76q23 16 57.5 34.5t77.5 34t91 26t97 10.5q125 0 206 -36t127 -100.5t63.5 -155.5t17.5 -200v-602h-248v563q0 168 -41 243t-157 75q-59 0 -112.5 -19.5t-80.5 -38.5q16 -51 22.5 -108.5t6.5 -122.5v-592 h-248v563q0 168 -42 243t-157 75q-41 0 -90 -6.5t-73 -10.5v-864h-248z" />
<glyph unicode="n" horiz-adv-x="1198" d="M158 0v1036q72 20 186.5 39t253.5 19q131 0 219 -36t140.5 -100.5t74 -155.5t21.5 -200v-602h-248v563q0 86 -11.5 146.5t-37 98.5t-69.5 55.5t-107 17.5q-47 0 -98.5 -6.5t-75.5 -10.5v-864h-248z" />
<glyph unicode="o" horiz-adv-x="1224" d="M98 537q0 127 38 230t106.5 177t163 114t206.5 40q113 0 208 -40t163 -114t105.5 -177t37.5 -230t-36.5 -231.5t-104 -178.5t-163 -115t-210.5 -41t-209 41t-161.5 115t-105.5 178.5t-38 231.5zM352 537q0 -162 68.5 -256.5t191.5 -94.5t191.5 94.5t68.5 256.5 q0 160 -68.5 253t-191.5 93t-191.5 -93.5t-68.5 -252.5z" />
<glyph unicode="p" horiz-adv-x="1220" d="M158 -379v1415q76 20 186.5 39t233.5 19q127 0 227 -39t171 -112t108.5 -176t37.5 -232q0 -123 -31.5 -225.5t-91 -176.5t-148.5 -115t-202 -41q-76 0 -139.5 18.5t-103.5 41.5v-416h-248zM406 252q33 -23 87 -42.5t115 -19.5q135 0 197.5 92t62.5 248q0 166 -74.5 258.5 t-240.5 92.5q-35 0 -73 -3.5t-74 -13.5v-612z" />
<glyph unicode="q" horiz-adv-x="1220" d="M98 535q0 127 38 230t108.5 176t171 113t227.5 40t237.5 -18.5t182.5 -39.5v-1415h-248v416q-43 -23 -105.5 -41.5t-138.5 -18.5q-115 0 -202.5 40t-147 114t-91.5 176.5t-32 227.5zM352 530q0 -156 62.5 -248t197.5 -92q61 0 115.5 19.5t87.5 42.5v612q-27 6 -66.5 11.5 t-103.5 5.5q-137 0 -215 -93.5t-78 -257.5z" />
<glyph unicode="r" horiz-adv-x="825" d="M158 0v1020q72 27 179 50.5t238 23.5q25 0 58 -3.5t65.5 -8.5t63.5 -12t49 -13l-43 -209q-31 10 -85 21.5t-126 11.5q-41 0 -87 -8.5t-64 -14.5v-858h-248z" />
<glyph unicode="s" horiz-adv-x="935" d="M76 35l43 207q55 -23 132 -43.5t175 -20.5t143 23.5t45 81.5q0 53 -48 88t-158 75q-68 25 -124.5 52.5t-97.5 64.5t-64.5 89.5t-23.5 127.5q0 147 108.5 232.5t295.5 85.5q94 0 180 -17.5t129 -33.5l-45 -201q-41 18 -104.5 33.5t-147.5 15.5q-76 0 -123 -25.5t-47 -78.5 q0 -27 9 -47.5t32 -38t59.5 -35t90.5 -35.5q88 -33 149 -64.5t101 -71.5t58.5 -91.5t18.5 -122.5q0 -154 -113.5 -233t-324.5 -79q-141 0 -227 23.5t-121 38.5z" />
<glyph unicode="t" horiz-adv-x="856" d="M145 426v928l248 41v-324h381v-207h-381v-436q0 -129 41 -184.5t139 -55.5q68 0 120 14.5t83 26.5l41 -196q-43 -18 -112.5 -38t-163.5 -20q-115 0 -192 31t-122 89.5t-63.5 141t-18.5 189.5z" />
<glyph unicode="u" horiz-adv-x="1193" d="M145 471v600h248v-561q0 -172 50.5 -246t174.5 -74q45 0 95.5 4.5t74.5 10.5v866h248v-1038q-72 -18 -186.5 -38t-251.5 -20q-129 0 -216 37t-139.5 102.5t-75 156.5t-22.5 200z" />
<glyph unicode="v" horiz-adv-x="1077" d="M27 1071h264q23 -90 52.5 -193.5t63 -207t69.5 -200.5t67 -175q31 78 65.5 175t68.5 200.5t64.5 207t53.5 193.5h256q-88 -313 -195.5 -586.5t-210.5 -484.5h-213q-102 211 -209.5 484.5t-195.5 586.5z" />
<glyph unicode="w" horiz-adv-x="1597" d="M31 1071h262q18 -86 42.5 -185.5t51.5 -202.5t55.5 -201.5t57.5 -180.5q31 94 60.5 194.5t56 201t50 195.5t42.5 179h190q18 -84 41 -179t48.5 -195.5t55 -201t60.5 -194.5q27 82 55.5 180.5t56 201.5t52 202.5t43.5 185.5h256q-43 -158 -88 -309.5t-90.5 -288.5 t-90.5 -258t-88 -215h-200q-53 152 -109.5 323t-103.5 351q-47 -180 -101.5 -351t-107.5 -323h-203q-41 94 -86 215t-90 258t-90 288.5t-88 309.5z" />
<glyph unicode="x" horiz-adv-x="1083" d="M27 0q31 61 73.5 131t92 142.5t101.5 145.5t103 138l-354 514h272l228 -350l227 350h260l-352 -506q111 -143 212 -289.5t167 -275.5h-269q-18 39 -47.5 89t-63.5 104.5t-71 107.5t-71 100q-72 -92 -140.5 -203.5t-113.5 -197.5h-254z" />
<glyph unicode="y" horiz-adv-x="1062" d="M-2 -344l45 199q74 -29 145 -29q96 0 150.5 46t95.5 142q-117 225 -224.5 491.5t-191.5 565.5h265q20 -86 49.5 -186.5t64.5 -203.5t74 -205.5t78 -190.5q66 182 123 388t104 398h256q-166 -610 -385 -1106q-41 -92 -84 -157.5t-96 -108.5t-118.5 -62.5t-151.5 -19.5 q-57 0 -113.5 12.5t-85.5 26.5z" />
<glyph unicode="z" horiz-adv-x="991" d="M68 0v154q45 82 112.5 178t139 193.5t142 185.5t126.5 153h-494v207h813v-174q-41 -43 -107.5 -121t-142 -173t-152.5 -198.5t-138 -197.5h553v-207h-852z" />
<glyph unicode="{" horiz-adv-x="712" d="M76 514v195q94 0 138 58t44 138v301q0 96 18.5 171t65.5 125t131 76t215 26h14v-197q-59 0 -100 -8t-65.5 -29.5t-36 -57.5t-11.5 -91v-293q0 -123 -31.5 -196t-107.5 -120q76 -47 107.5 -120.5t31.5 -194.5v-295q0 -55 11.5 -90t36 -56.5t65.5 -29.5t100 -8v-197 q-137 0 -223 24.5t-135 75t-67.5 124t-18.5 171.5v303q0 78 -44 136.5t-138 58.5z" />
<glyph unicode="|" horiz-adv-x="614" d="M190 -379v1983h234v-1983h-234z" />
<glyph unicode="}" horiz-adv-x="712" d="M10 -182q119 0 166 38t47 146v295q0 121 32 194.5t107 120.5q-76 47 -107.5 120t-31.5 196v293q0 55 -11 91t-35.5 57.5t-65.5 29.5t-101 8v197h15q131 0 215 -26t131 -76t65.5 -124.5t18.5 -171.5v-301q0 -80 44 -138t138 -58v-195q-94 0 -138 -58.5t-44 -136.5v-303 q0 -98 -18.5 -171.5t-68 -124t-135.5 -75t-223 -24.5v197z" />
<glyph unicode="~" d="M68 481q10 49 29.5 105.5t55 104.5t89 81t129.5 33q63 0 117.5 -25.5t105.5 -57.5t98 -57.5t96 -25.5q51 0 82 39t52 117l170 -47q-10 -49 -30 -105.5t-55.5 -105t-89 -81t-129.5 -32.5q-63 0 -117.5 25.5t-105.5 56.5t-98 56.5t-96 25.5t-80 -38t-53 -116z" />
<glyph unicode="&#xa1;" horiz-adv-x="626" d="M150 909q0 72 48 117t115 45q70 0 117 -45t47 -117t-47 -118t-117 -46q-68 0 -115.5 46.5t-47.5 117.5zM182 6q0 168 12.5 310.5t32.5 285.5h176q18 -143 30.5 -284.5t12.5 -311.5v-381h-264v381z" />
<glyph unicode="&#xa2;" d="M135 598q0 94 25.5 180t75 154.5t120 117t160.5 70.5v297h232v-280q109 -8 210 -50l-55 -200q-43 16 -96 28.5t-117 12.5q-145 0 -222 -86t-77 -244q0 -154 73 -241t241 -87q66 0 128 12.5t107 30.5l35 -202q-35 -16 -96.5 -30.5t-130.5 -21.5v-282h-232v297 q-193 43 -287 185t-94 339z" />
<glyph unicode="&#xa3;" d="M102 584v200h185v136q0 156 34.5 257t96 160.5t147.5 84t189 24.5q86 0 152.5 -16.5t127.5 -43.5l-61 -200q-96 45 -211 47q-53 0 -96 -13.5t-73 -47t-46.5 -92t-16.5 -146.5v-150h365v-200h-365q0 -88 -7 -187.5t-21 -187.5h563v-209h-840q61 313 62 584h-185z" />
<glyph unicode="&#xa4;" d="M72 362l149 146q-51 84 -51 203q0 117 51 200l-149 146l155 153l152 -149q88 47 203 47q113 0 200 -47l154 149l154 -153l-150 -146q51 -84 51 -200q0 -119 -51 -201l150 -148l-156 -153l-152 149q-88 -47 -200 -47q-115 0 -203 47l-152 -147zM377 711 q0 -104 59.5 -160.5t145.5 -56.5q84 0 144 56t60 161q0 102 -60 158.5t-144 56.5q-86 0 -145.5 -56.5t-59.5 -158.5z" />
<glyph unicode="&#xa5;" d="M14 1419h279q70 -152 141.5 -302t153.5 -292q78 141 147.5 293t136.5 301h277q-86 -166 -175 -335t-194 -343h244v-174h-317v-172h317v-174h-317v-221h-248v221h-318v174h318v172h-318v174h242q-98 168 -195.5 346.5t-173.5 331.5z" />
<glyph unicode="&#xa6;" horiz-adv-x="614" d="M190 -379v799h234v-799h-234zM190 805v799h234v-799h-234z" />
<glyph unicode="&#xa7;" horiz-adv-x="1042" d="M82 -143l61 196q78 -33 162 -51t174 -18q31 0 66 4t64.5 16t49 33.5t19.5 56.5q0 53 -47 86t-166 76q-78 27 -145.5 56.5t-118.5 71.5t-81 101.5t-30 145.5q0 51 14.5 94t39 79t55 65.5t61.5 54.5q-49 41 -76.5 92t-27.5 114q0 150 107.5 236t305.5 86q98 0 193.5 -20.5 t161.5 -45.5l-60 -202q-57 23 -127.5 42t-169.5 19q-78 0 -124 -25.5t-46 -76.5q0 -57 43 -89t148 -67q82 -29 150.5 -58.5t118.5 -72.5t78 -101.5t28 -140.5q0 -98 -49.5 -164.5t-114.5 -115.5q125 -90 125 -236q0 -94 -37 -155.5t-99.5 -98.5t-142.5 -52t-166 -15 q-129 0 -232.5 25.5t-164.5 54.5zM313 651q0 -84 69 -128t185 -83l62 -22q47 35 75.5 78t28.5 92q0 51 -23.5 84t-65.5 57.5t-99 44.5t-127 47q-47 -35 -76 -78t-29 -92z" />
<glyph unicode="&#xa8;" horiz-adv-x="829" d="M66 1384q0 63 40.5 101.5t96.5 38.5q55 0 97 -38t42 -102q0 -63 -42 -100t-97 -37t-96 37t-41 100zM487 1384q0 63 42 101.5t98 38.5q55 0 96 -38t41 -102q0 -63 -41 -100t-96 -37t-97.5 37t-42.5 100z" />
<glyph unicode="&#xa9;" horiz-adv-x="1667" d="M127 711q0 174 58.5 312t155.5 233.5t225 145.5t268 50q141 0 269 -50t225 -145.5t154.5 -233.5t57.5 -312q0 -176 -57.5 -313.5t-154.5 -232.5t-225 -145.5t-269 -50.5q-139 0 -267.5 50.5t-225.5 145.5t-155.5 232.5t-58.5 313.5zM307 711q0 -129 39 -235.5 t108.5 -182.5t166 -117t213.5 -41t213 41t166.5 117t109.5 182.5t39 235.5t-39 234.5t-109.5 181t-167 116.5t-212.5 41q-117 0 -213.5 -41t-166 -116.5t-108.5 -181t-39 -234.5zM479 713q0 94 29 169.5t80 127t120.5 79t151.5 27.5q102 0 166 -27.5t84 -39.5l-57 -152 q-27 16 -71 31.5t-110 15.5q-86 0 -141 -58.5t-55 -168.5q0 -49 11 -93t34.5 -76t61.5 -50.5t91 -18.5q72 0 118 15.5t77 29.5l49 -155q-25 -12 -92.5 -37t-163.5 -25q-180 0 -281.5 105.5t-101.5 300.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="823" d="M72 901q0 68 26.5 113t72.5 72.5t106.5 40t128.5 12.5q35 0 68.5 -3t66.5 -8v13q0 59 -33 102t-127 43q-53 0 -109.5 -7t-97.5 -22l-27 156q39 12 108.5 23.5t141.5 11.5q90 0 152.5 -21.5t100.5 -60.5t54.5 -94t16.5 -123v-465q-47 -10 -133 -21.5t-189 -11.5 q-147 0 -237 56.5t-90 193.5zM258 895q0 -31 15.5 -49.5t41 -27.5t57 -12t64.5 -3q35 0 62.5 2t42.5 6v180q-18 4 -48 7t-59 3q-25 0 -55.5 -3t-57 -14t-45 -32.5t-18.5 -56.5z" />
<glyph unicode="&#xab;" horiz-adv-x="1130" d="M55 582l328 471l182 -90l-217 -381l217 -383l-182 -90zM544 582l328 471l182 -90l-217 -381l217 -383l-182 -90z" />
<glyph unicode="&#xac;" d="M106 604v209h951v-717h-221v508h-730z" />
<glyph unicode="&#xad;" horiz-adv-x="645" d="M47 471v227h551v-227h-551z" />
<glyph unicode="&#xae;" horiz-adv-x="1667" d="M127 711q0 174 58.5 312t155.5 233.5t225 145.5t268 50q141 0 269 -50t225 -145.5t154.5 -233.5t57.5 -312q0 -176 -57.5 -313.5t-154.5 -232.5t-225 -145.5t-269 -50.5q-139 0 -267.5 50.5t-225.5 145.5t-155.5 232.5t-58.5 313.5zM307 711q0 -129 39 -235.5 t108.5 -182.5t166 -117t213.5 -41t213 41t166.5 117t109.5 182.5t39 235.5t-39 234.5t-109.5 181t-167 116.5t-212.5 41q-117 0 -213.5 -41t-166 -116.5t-108.5 -181t-39 -234.5zM555 332v747q63 16 124.5 22.5t108.5 6.5q170 0 257.5 -63.5t87.5 -192.5q0 -70 -34 -122 t-98 -81q41 -59 88.5 -137t92.5 -180h-185q-41 86 -82 156.5t-77 117.5h-111v-274h-172zM727 745h57q80 0 127 21.5t47 87.5q0 57 -44 80.5t-111 23.5h-35.5t-40.5 -4v-209z" />
<glyph unicode="&#xaf;" horiz-adv-x="796" d="M88 1292v178h621v-178h-621z" />
<glyph unicode="&#xb0;" horiz-adv-x="710" d="M47 1292q0 70 24.5 126.5t67.5 95t98.5 59t116.5 20.5t117.5 -20.5t99.5 -59t68 -95t25 -126.5t-25 -126t-68 -95t-99 -59.5t-118 -20.5q-61 0 -116.5 20.5t-98.5 59.5t-67.5 95.5t-24.5 125.5zM223 1292q0 -63 39 -99t92 -36t92 36t39 99t-38.5 100.5t-92.5 37.5 q-53 0 -92 -37t-39 -101z" />
<glyph unicode="&#xb1;" d="M106 0v209h951v-209h-951zM106 686v209h365v379h221v-379h365v-209h-365v-381h-221v381h-365z" />
<glyph unicode="&#xb2;" horiz-adv-x="743" d="M57 1337q43 41 120 78t169 37q152 0 219.5 -64.5t67.5 -175.5q0 -43 -15.5 -79.5t-44 -73.5t-70.5 -76t-97 -88q-31 -27 -59 -55.5t-38 -51.5h350v-157h-567q-2 39 0 71q8 98 64.5 170t144.5 146q61 53 100 96t39 86q0 41 -25.5 62.5t-72.5 21.5q-66 0 -116 -27.5 t-79 -50.5z" />
<glyph unicode="&#xb3;" horiz-adv-x="743" d="M66 666l38 153q59 -23 107.5 -34t103.5 -11q92 0 126 27.5t34 70.5q0 57 -52 82t-132 25h-62v143h68q53 0 98 20.5t45 69.5q0 80 -112 80q-53 0 -99.5 -17.5t-85.5 -35.5l-65 137q41 29 114.5 52.5t145.5 23.5q78 0 132 -17.5t88 -48t48.5 -71.5t14.5 -86 q0 -94 -101 -160q70 -29 106 -80t36 -119q0 -53 -18.5 -100t-59.5 -82t-106.5 -54.5t-160.5 -19.5q-66 0 -137 15.5t-114 36.5z" />
<glyph unicode="&#xb4;" horiz-adv-x="802" d="M190 1296l302 359l155 -146l-334 -323z" />
<glyph unicode="&#xb5;" horiz-adv-x="1210" d="M158 -360v1431h248v-573q0 -88 13 -146.5t40.5 -94.5t70.5 -51.5t103 -15.5q47 0 97 4.5t75 10.5v866h248v-1038q-74 -18 -181.5 -37t-232.5 -19q-84 0 -144.5 17.5t-101.5 48.5q6 -49 8 -103.5t2 -125.5v-174h-245z" />
<glyph unicode="&#xb6;" horiz-adv-x="1398" d="M82 977q0 223 178 340t494 117q106 0 228 -10.5t218 -32.5v-1770h-223v1600q-23 4 -59.5 6t-65.5 2h-42t-38 -2v-1606h-225v911q-229 27 -347 132.5t-118 312.5z" />
<glyph unicode="&#xb7;" horiz-adv-x="430" d="M54 602q0 72 47 118t116 46q68 0 116 -46t48 -118t-48 -117t-116 -45q-70 0 -116.5 45t-46.5 117z" />
<glyph unicode="&#xb8;" horiz-adv-x="798" d="M135 -393l29 147q20 -6 53 -14t68 -8t54 12t19 37q0 33 -28.5 48t-57.5 26l-20 6q6 20 18.5 46.5t24.5 53.5t22.5 48.5t16.5 31.5h164q-12 -23 -31 -59.5t-29 -61.5q66 -31 91.5 -69.5t25.5 -96.5q0 -72 -61.5 -123t-184.5 -51q-98 0 -174 27z" />
<glyph unicode="&#xb9;" horiz-adv-x="743" d="M96 1268q82 31 160 73.5t135 94.5h139v-805h-188v577q-41 -25 -92 -46t-103 -38z" />
<glyph unicode="&#xba;" horiz-adv-x="931" d="M82 1049q0 92 28.5 166.5t80 127t122 80t154.5 27.5t154.5 -27.5t121 -80t79 -127t28.5 -166.5t-28.5 -166t-79 -126t-121 -80t-154.5 -28t-154.5 28t-122 80t-80 125.5t-28.5 166.5zM274 1049q0 -104 50.5 -167t142.5 -63t141 63t49 167q0 106 -49 168.5t-141 62.5 t-142.5 -62.5t-50.5 -168.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="1130" d="M76 199l217 383l-217 381l182 90l328 -471l-328 -473zM565 199l217 383l-217 381l182 90l328 -471l-328 -473z" />
<glyph unicode="&#xbc;" horiz-adv-x="1798" d="M73 1268q82 31 160 73.5t135 94.5h139v-805h-188v577q-41 -25 -92 -46t-103 -38zM391 0l766 1419h229l-764 -1419h-231zM1102 184v121q35 61 79 126.5t94 131t104.5 128t109.5 116.5h168v-478h98v-145h-98v-182h-166v182h-389zM1286 329h205v283q-53 -59 -108.5 -133 t-96.5 -150z" />
<glyph unicode="&#xbd;" horiz-adv-x="1798" d="M73 1268q82 31 160 73.5t135 94.5h139v-805h-188v577q-41 -25 -92 -46t-103 -38zM340 0l766 1419h229l-764 -1419h-231zM1104 708q43 41 120 78t169 37q152 0 219.5 -64.5t67.5 -175.5q0 -43 -15.5 -79.5t-44 -73.5t-70.5 -76t-97 -88q-31 -27 -59 -55.5t-38 -51.5h350 v-157h-567q-2 39 0 71q8 98 64.5 170t144.5 146q61 53 100 96t39 86q0 41 -25.5 62.5t-72.5 21.5q-66 0 -116 -27.5t-79 -50.5z" />
<glyph unicode="&#xbe;" horiz-adv-x="1798" d="M60 666l38 153q59 -23 107.5 -34t103.5 -11q92 0 126 27.5t34 70.5q0 57 -52 82t-132 25h-62v143h68q53 0 98 20.5t45 69.5q0 80 -112 80q-53 0 -99.5 -17.5t-85.5 -35.5l-65 137q41 29 114.5 52.5t145.5 23.5q78 0 132 -17.5t88 -48t48.5 -71.5t14.5 -86 q0 -94 -101 -160q70 -29 106 -80t36 -119q0 -53 -18.5 -100t-59.5 -82t-106.5 -54.5t-160.5 -19.5q-66 0 -137 15.5t-114 36.5zM428 0l766 1419h229l-764 -1419h-231zM1102 184v121q35 61 79 126.5t94 131t104.5 128t109.5 116.5h168v-478h98v-145h-98v-182h-166v182h-389z M1286 329h205v283q-53 -59 -108.5 -133t-96.5 -150z" />
<glyph unicode="&#xbf;" horiz-adv-x="868" d="M49 -57q0 70 25.5 125t64.5 102t84 89t84 86t64.5 93t25.5 111v23.5t-2 25.5h213q4 -20 6 -44t2 -44q0 -68 -22.5 -121t-57 -98t-74.5 -84t-75 -78t-57.5 -80t-22.5 -90q0 -68 46.5 -112t138.5 -44q72 0 137 17.5t137 56.5l70 -190q-76 -43 -173.5 -69t-199.5 -26 q-123 0 -203 34t-127 85t-65.5 112.5t-18.5 119.5zM332 909q0 72 47 117t117 45q68 0 115.5 -45t47.5 -117t-48 -118t-115 -46q-70 0 -117 46t-47 118z" />
<glyph unicode="&#xc0;" horiz-adv-x="1400" d="M10 0q80 227 151.5 419.5t140.5 364.5t136.5 328t139.5 307h243q72 -152 139.5 -307.5t136 -327.5t141.5 -364.5t153 -419.5h-279q-29 84 -59.5 166t-59.5 168h-598q-29 -86 -58.5 -169t-57.5 -165h-269zM379 1843l157 144l297 -359l-123 -110zM461 543h467 q-66 182 -127.5 340t-106.5 262q-47 -109 -107.5 -264.5t-125.5 -337.5z" />
<glyph unicode="&#xc1;" horiz-adv-x="1400" d="M10 0q80 227 151.5 419.5t140.5 364.5t136.5 328t139.5 307h243q72 -152 139.5 -307.5t136 -327.5t141.5 -364.5t153 -419.5h-279q-29 84 -59.5 166t-59.5 168h-598q-29 -86 -58.5 -169t-57.5 -165h-269zM461 543h467q-66 182 -127.5 340t-106.5 262 q-47 -109 -107.5 -264.5t-125.5 -337.5zM569 1628l302 359l155 -146l-334 -323z" />
<glyph unicode="&#xc2;" horiz-adv-x="1400" d="M10 0q80 227 151.5 419.5t140.5 364.5t136.5 328t139.5 307h243q72 -152 139.5 -307.5t136 -327.5t141.5 -364.5t153 -419.5h-279q-29 84 -59.5 166t-59.5 168h-598q-29 -86 -58.5 -169t-57.5 -165h-269zM399 1630l299 287l299 -287l-96 -108l-203 166l-200 -166z M461 543h467q-66 182 -127.5 340t-106.5 262q-47 -109 -107.5 -264.5t-125.5 -337.5z" />
<glyph unicode="&#xc3;" horiz-adv-x="1400" d="M10 0q80 227 151.5 419.5t140.5 364.5t136.5 328t139.5 307h243q72 -152 139.5 -307.5t136 -327.5t141.5 -364.5t153 -419.5h-279q-29 84 -59.5 166t-59.5 168h-598q-29 -86 -58.5 -169t-57.5 -165h-269zM344 1643q10 29 29.5 62.5t49.5 63.5t66.5 50t81.5 20 q37 0 70 -12t64.5 -25.5t61.5 -25.5t58 -12q39 0 66.5 27.5t44.5 62.5l125 -74q-13 -29 -32 -62.5t-48 -63.5t-66 -50.5t-82 -20.5q-37 0 -69.5 11.5t-64 26t-61.5 25.5t-59 11q-39 0 -66.5 -26.5t-43.5 -61.5zM461 543h467q-66 182 -127.5 340t-106.5 262 q-47 -109 -107.5 -264.5t-125.5 -337.5z" />
<glyph unicode="&#xc4;" horiz-adv-x="1400" d="M10 0q80 227 151.5 419.5t140.5 364.5t136.5 328t139.5 307h243q72 -152 139.5 -307.5t136 -327.5t141.5 -364.5t153 -419.5h-279q-29 84 -59.5 166t-59.5 168h-598q-29 -86 -58.5 -169t-57.5 -165h-269zM342 1708q0 63 40.5 101.5t96.5 38.5q55 0 97 -38t42 -102 q0 -63 -42 -100t-97 -37t-96 37t-41 100zM461 543h467q-66 182 -127.5 340t-106.5 262q-47 -109 -107.5 -264.5t-125.5 -337.5zM763 1708q0 63 42 101.5t98 38.5q55 0 96 -38t41 -102q0 -63 -41 -100t-96 -37t-97.5 37t-42.5 100z" />
<glyph unicode="&#xc5;" horiz-adv-x="1400" d="M10 0q76 217 144.5 402.5t134 351.5t130 316.5t132.5 295.5q-35 29 -56.5 72t-21.5 102q0 51 18.5 93t49 70t71.5 43t86 15t86 -15t73 -43t50.5 -70t18.5 -93q0 -59 -21.5 -102t-58.5 -72q68 -145 132 -295.5t131 -316.5t135.5 -351.5t146.5 -402.5h-279 q-29 84 -59.5 166t-59.5 168h-598q-29 -86 -58.5 -169t-57.5 -165h-269zM461 543h467q-66 182 -127.5 340t-106.5 262q-47 -109 -107.5 -264.5t-125.5 -337.5zM596 1540q0 -53 30.5 -81.5t71.5 -28.5q43 0 74 28.5t31 81.5q0 51 -31 81t-74 30q-41 0 -71.5 -30t-30.5 -81z " />
<glyph unicode="&#xc6;" horiz-adv-x="1959" d="M6 0q242 444 455 791.5t411 627.5h969v-217h-610v-354h538v-211h-538v-420h659v-217h-911v350h-506q-47 -86 -96 -175t-90 -175h-281zM584 557h395v629q-43 -61 -92 -135t-100.5 -156t-102.5 -168t-100 -170z" />
<glyph unicode="&#xc7;" horiz-adv-x="1306" d="M117 711q0 178 54 315t148.5 232.5t221.5 144.5t272 49q88 0 159.5 -13.5t125 -29.5t88.5 -34.5t49 -26.5l-76 -211q-49 31 -140 58.5t-198 27.5q-92 0 -172 -32t-138 -95.5t-91 -159.5t-33 -223q0 -113 25.5 -207t79 -163t135.5 -106.5t196 -37.5q137 0 223.5 27.5 t133.5 52.5l69 -211q-45 -29 -151.5 -61t-253.5 -38q-8 -14 -13.5 -26.5t-9.5 -22.5q66 -31 91.5 -69.5t25.5 -96.5q0 -72 -61.5 -123t-184.5 -51q-98 0 -174 27l29 147q20 -6 53 -14t68 -8t54 12t19 37q0 33 -28.5 48t-57.5 26l-20 6q8 25 21.5 56.5t27.5 59.5 q-268 33 -417.5 221.5t-149.5 512.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="1200" d="M170 0v1419h911v-219h-653v-350h582v-215h-582v-416h702v-219h-960zM322 1843l157 144l297 -359l-123 -110z" />
<glyph unicode="&#xc9;" horiz-adv-x="1200" d="M170 0v1419h911v-219h-653v-350h582v-215h-582v-416h702v-219h-960zM524 1628l302 359l155 -146l-334 -323z" />
<glyph unicode="&#xca;" horiz-adv-x="1200" d="M170 0v1419h911v-219h-653v-350h582v-215h-582v-416h702v-219h-960zM327 1630l299 287l299 -287l-96 -108l-203 166l-200 -166z" />
<glyph unicode="&#xcb;" horiz-adv-x="1200" d="M170 0v1419h911v-219h-653v-350h582v-215h-582v-416h702v-219h-960zM273 1708q0 63 40.5 101.5t96.5 38.5q55 0 97 -38t42 -102q0 -63 -42 -100t-97 -37t-96 37t-41 100zM694 1708q0 63 42 101.5t98 38.5q55 0 96 -38t41 -102q0 -63 -41 -100t-96 -37t-97.5 37t-42.5 100 z" />
<glyph unicode="&#xcc;" horiz-adv-x="598" d="M-26 1843l157 144l297 -359l-123 -110zM170 0v1419h258v-1419h-258z" />
<glyph unicode="&#xcd;" horiz-adv-x="598" d="M170 0v1419h258v-1419h-258zM176 1628l302 359l155 -146l-334 -323z" />
<glyph unicode="&#xce;" horiz-adv-x="598" d="M2 1630l299 287l299 -287l-96 -108l-203 166l-200 -166zM170 0v1419h258v-1419h-258z" />
<glyph unicode="&#xcf;" horiz-adv-x="598" d="M-47 1708q0 63 40.5 101.5t96.5 38.5q55 0 97 -38t42 -102q0 -63 -42 -100t-97 -37t-96 37t-41 100zM170 0v1419h258v-1419h-258zM374 1708q0 63 42 101.5t98 38.5q55 0 96 -38t41 -102q0 -63 -41 -100t-96 -37t-97.5 37t-42.5 100z" />
<glyph unicode="&#xd0;" horiz-adv-x="1515" d="M33 641v195h166v563q98 23 208.5 29t196.5 6q178 0 325.5 -42t253 -131.5t163 -225.5t57.5 -324q0 -184 -57.5 -320.5t-163 -227.5t-254 -135t-328.5 -44q-86 0 -194.5 7t-206.5 29v621h-166zM457 213q23 -2 59.5 -3t99.5 -1q260 0 388.5 132t128.5 370q0 242 -125 370.5 t-387 128.5q-115 0 -164 -6v-368h262v-195h-262v-428z" />
<glyph unicode="&#xd1;" horiz-adv-x="1515" d="M170 0v1419h211q82 -86 176 -203.5t189.5 -244.5t184.5 -255t159 -239v942h256v-1419h-220q-70 117 -155.5 251t-178 270t-187.5 264t-181 231v-1016h-254zM401 1643q10 29 29.5 62.5t49.5 63.5t66.5 50t81.5 20q37 0 70 -12t64.5 -25.5t61.5 -25.5t58 -12 q39 0 66.5 27.5t44.5 62.5l125 -74q-13 -29 -32 -62.5t-48 -63.5t-66 -50.5t-82 -20.5q-37 0 -69.5 11.5t-64 26t-61.5 25.5t-59 11q-39 0 -66.5 -26.5t-43.5 -61.5z" />
<glyph unicode="&#xd2;" horiz-adv-x="1601" d="M113 711q0 182 55 320t150.5 232.5t220.5 141.5t264 47t264 -47t218 -141.5t148.5 -232.5t55.5 -320t-54.5 -321.5t-147.5 -233t-218 -140.5t-266 -47t-267 47t-219.5 140.5t-148.5 232.5t-55 322zM381 711q0 -117 28.5 -212.5t83 -162t132 -103t176.5 -36.5 q96 0 174 36.5t132 103t83 162t29 212.5t-29 212t-83 161.5t-132 103.5t-174 37q-98 0 -176 -37t-132.5 -104.5t-83 -161.5t-28.5 -211zM473 1843l157 144l297 -359l-123 -110z" />
<glyph unicode="&#xd3;" horiz-adv-x="1601" d="M113 711q0 182 55 320t150.5 232.5t220.5 141.5t264 47t264 -47t218 -141.5t148.5 -232.5t55.5 -320t-54.5 -321.5t-147.5 -233t-218 -140.5t-266 -47t-267 47t-219.5 140.5t-148.5 232.5t-55 322zM381 711q0 -117 28.5 -212.5t83 -162t132 -103t176.5 -36.5 q96 0 174 36.5t132 103t83 162t29 212.5t-29 212t-83 161.5t-132 103.5t-174 37q-98 0 -176 -37t-132.5 -104.5t-83 -161.5t-28.5 -211zM667 1628l302 359l155 -146l-334 -323z" />
<glyph unicode="&#xd4;" horiz-adv-x="1601" d="M113 711q0 182 55 320t150.5 232.5t220.5 141.5t264 47t264 -47t218 -141.5t148.5 -232.5t55.5 -320t-54.5 -321.5t-147.5 -233t-218 -140.5t-266 -47t-267 47t-219.5 140.5t-148.5 232.5t-55 322zM381 711q0 -117 28.5 -212.5t83 -162t132 -103t176.5 -36.5 q96 0 174 36.5t132 103t83 162t29 212.5t-29 212t-83 161.5t-132 103.5t-174 37q-98 0 -176 -37t-132.5 -104.5t-83 -161.5t-28.5 -211zM501 1630l299 287l299 -287l-96 -108l-203 166l-200 -166z" />
<glyph unicode="&#xd5;" horiz-adv-x="1601" d="M113 711q0 182 55 320t150.5 232.5t220.5 141.5t264 47t264 -47t218 -141.5t148.5 -232.5t55.5 -320t-54.5 -321.5t-147.5 -233t-218 -140.5t-266 -47t-267 47t-219.5 140.5t-148.5 232.5t-55 322zM381 711q0 -117 28.5 -212.5t83 -162t132 -103t176.5 -36.5 q96 0 174 36.5t132 103t83 162t29 212.5t-29 212t-83 161.5t-132 103.5t-174 37q-98 0 -176 -37t-132.5 -104.5t-83 -161.5t-28.5 -211zM444 1643q10 29 29.5 62.5t49.5 63.5t66.5 50t81.5 20q37 0 70 -12t64.5 -25.5t61.5 -25.5t58 -12q39 0 66.5 27.5t44.5 62.5l125 -74 q-13 -29 -32 -62.5t-48 -63.5t-66 -50.5t-82 -20.5q-37 0 -69.5 11.5t-64 26t-61.5 25.5t-59 11q-39 0 -66.5 -26.5t-43.5 -61.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="1601" d="M113 711q0 182 55 320t150.5 232.5t220.5 141.5t264 47t264 -47t218 -141.5t148.5 -232.5t55.5 -320t-54.5 -321.5t-147.5 -233t-218 -140.5t-266 -47t-267 47t-219.5 140.5t-148.5 232.5t-55 322zM381 711q0 -117 28.5 -212.5t83 -162t132 -103t176.5 -36.5 q96 0 174 36.5t132 103t83 162t29 212.5t-29 212t-83 161.5t-132 103.5t-174 37q-98 0 -176 -37t-132.5 -104.5t-83 -161.5t-28.5 -211zM445 1708q0 63 40.5 101.5t96.5 38.5q55 0 97 -38t42 -102q0 -63 -42 -100t-97 -37t-96 37t-41 100zM866 1708q0 63 42 101.5t98 38.5 q55 0 96 -38t41 -102q0 -63 -41 -100t-96 -37t-97.5 37t-42.5 100z" />
<glyph unicode="&#xd7;" d="M147 315l289 287l-289 289l146 147l289 -288l288 288l146 -147l-289 -289l289 -287l-146 -147l-288 289l-289 -289z" />
<glyph unicode="&#xd8;" horiz-adv-x="1601" d="M113 711q0 182 55 320t150.5 232.5t220.5 141.5t264 47q102 0 196.5 -25.5t176.5 -76.5l118 153l152 -112l-127 -164q78 -92 124 -221t46 -295q0 -182 -54.5 -321.5t-147.5 -233t-218 -140.5t-266 -47q-104 0 -200.5 25.5t-178.5 77.5l-119 -156l-149 113l127 164 q-80 92 -125 222t-45 296zM381 711q0 -178 65 -303l580 749q-96 68 -225 68q-98 0 -176 -37t-132.5 -104.5t-83 -161.5t-28.5 -211zM573 262q94 -66 228 -65q96 0 174 36.5t132 103t83 162t29 212.5q0 88 -17.5 163.5t-48.5 137.5z" />
<glyph unicode="&#xd9;" horiz-adv-x="1439" d="M160 532v887h260v-862q0 -96 21.5 -164.5t61.5 -111.5t94 -63.5t122 -20.5t123 20.5t95 63.5t61.5 111.5t21.5 164.5v862h260v-887q0 -123 -34 -225t-102.5 -178t-175 -118t-251.5 -42t-250 42t-173.5 118t-101 178.5t-32.5 224.5zM371 1843l157 144l297 -359l-123 -110z " />
<glyph unicode="&#xda;" horiz-adv-x="1439" d="M160 532v887h260v-862q0 -96 21.5 -164.5t61.5 -111.5t94 -63.5t122 -20.5t123 20.5t95 63.5t61.5 111.5t21.5 164.5v862h260v-887q0 -123 -34 -225t-102.5 -178t-175 -118t-251.5 -42t-250 42t-173.5 118t-101 178.5t-32.5 224.5zM606 1628l302 359l155 -146l-334 -323z " />
<glyph unicode="&#xdb;" horiz-adv-x="1439" d="M160 532v887h260v-862q0 -96 21.5 -164.5t61.5 -111.5t94 -63.5t122 -20.5t123 20.5t95 63.5t61.5 111.5t21.5 164.5v862h260v-887q0 -123 -34 -225t-102.5 -178t-175 -118t-251.5 -42t-250 42t-173.5 118t-101 178.5t-32.5 224.5zM419 1630l299 287l299 -287l-96 -108 l-203 166l-200 -166z" />
<glyph unicode="&#xdc;" horiz-adv-x="1439" d="M160 532v887h260v-862q0 -96 21.5 -164.5t61.5 -111.5t94 -63.5t122 -20.5t123 20.5t95 63.5t61.5 111.5t21.5 164.5v862h260v-887q0 -123 -34 -225t-102.5 -178t-175 -118t-251.5 -42t-250 42t-173.5 118t-101 178.5t-32.5 224.5zM371 1708q0 63 40.5 101.5t96.5 38.5 q55 0 97 -38t42 -102q0 -63 -42 -100t-97 -37t-96 37t-41 100zM792 1708q0 63 42 101.5t98 38.5q55 0 96 -38t41 -102q0 -63 -41 -100t-96 -37t-97.5 37t-42.5 100z" />
<glyph unicode="&#xdd;" horiz-adv-x="1280" d="M6 1419h303q72 -160 158 -317t180 -311q92 154 180 311.5t160 316.5h287q-115 -217 -239 -431t-265 -431v-557h-258v553q-141 219 -266 434t-240 432zM491 1628l302 359l155 -146l-334 -323z" />
<glyph unicode="&#xde;" horiz-adv-x="1286" d="M172 0v1419h258v-219q37 4 79 5t77 1q295 0 460.5 -110.5t165.5 -352.5q0 -125 -44 -214t-128 -145t-204.5 -82t-276.5 -26h-129v-276h-258zM430 494h125q92 0 165 11t122 40.5t74.5 78t25.5 121.5q0 70 -26.5 117t-75.5 74t-118 38t-151 11q-47 0 -86 -1t-55 -3v-487z " />
<glyph unicode="&#xdf;" horiz-adv-x="1298" d="M158 0v1112q0 106 28.5 193.5t86 151t144.5 98t201 34.5q115 0 198 -26.5t136.5 -71.5t78 -105.5t24.5 -130.5q0 -92 -33 -151.5t-76 -106.5q-47 -53 -82 -93t-35 -93q0 -31 15.5 -53.5t41 -41t59.5 -35.5t69 -36q45 -25 86 -53.5t71.5 -66.5t48 -87t17.5 -114 q0 -166 -96.5 -257.5t-309.5 -91.5q-70 0 -146.5 14.5t-147.5 43.5l43 205q16 -6 42.5 -16.5t59.5 -19.5t68.5 -15.5t70.5 -6.5q86 0 127 37t41 102q0 66 -50 112t-153 93q-63 29 -103 58.5t-61.5 60.5t-29.5 64.5t-8 70.5q0 82 42 141.5t93 114.5q37 41 67.5 85t30.5 98 q0 80 -48 120.5t-138 40.5q-119 0 -174.5 -69.5t-55.5 -194.5v-1114h-243z" />
<glyph unicode="&#xe0;" horiz-adv-x="1093" d="M82 324q0 92 36 155.5t97 103.5t142 57t169 17q41 0 86 -5t97 -17v41q0 43 -10.5 82t-36 68.5t-67.5 46t-106 16.5q-86 0 -157.5 -12.5t-116.5 -28.5l-31 201q47 16 137.5 32.5t192.5 16.5q123 0 207 -31t134 -86t71.5 -134t21.5 -173v-649q-57 -12 -173 -30t-261 -18 q-96 0 -176 18.5t-136.5 59.5t-88 106.5t-31.5 162.5zM250 1511l157 144l297 -359l-123 -110zM330 330q0 -88 55.5 -122t149.5 -34q115 0 174 12v275q-20 6 -59.5 12t-86.5 6q-41 0 -83 -6t-75.5 -22.5t-54 -46t-20.5 -74.5z" />
<glyph unicode="&#xe1;" horiz-adv-x="1093" d="M82 324q0 92 36 155.5t97 103.5t142 57t169 17q41 0 86 -5t97 -17v41q0 43 -10.5 82t-36 68.5t-67.5 46t-106 16.5q-86 0 -157.5 -12.5t-116.5 -28.5l-31 201q47 16 137.5 32.5t192.5 16.5q123 0 207 -31t134 -86t71.5 -134t21.5 -173v-649q-57 -12 -173 -30t-261 -18 q-96 0 -176 18.5t-136.5 59.5t-88 106.5t-31.5 162.5zM330 330q0 -88 55.5 -122t149.5 -34q115 0 174 12v275q-20 6 -59.5 12t-86.5 6q-41 0 -83 -6t-75.5 -22.5t-54 -46t-20.5 -74.5zM366 1296l302 359l155 -146l-334 -323z" />
<glyph unicode="&#xe2;" horiz-adv-x="1093" d="M82 324q0 92 36 155.5t97 103.5t142 57t169 17q41 0 86 -5t97 -17v41q0 43 -10.5 82t-36 68.5t-67.5 46t-106 16.5q-86 0 -157.5 -12.5t-116.5 -28.5l-31 201q47 16 137.5 32.5t192.5 16.5q123 0 207 -31t134 -86t71.5 -134t21.5 -173v-649q-57 -12 -173 -30t-261 -18 q-96 0 -176 18.5t-136.5 59.5t-88 106.5t-31.5 162.5zM252 1300l299 287l299 -287l-96 -108l-203 166l-200 -166zM330 330q0 -88 55.5 -122t149.5 -34q115 0 174 12v275q-20 6 -59.5 12t-86.5 6q-41 0 -83 -6t-75.5 -22.5t-54 -46t-20.5 -74.5z" />
<glyph unicode="&#xe3;" horiz-adv-x="1093" d="M82 324q0 92 36 155.5t97 103.5t142 57t169 17q41 0 86 -5t97 -17v41q0 43 -10.5 82t-36 68.5t-67.5 46t-106 16.5q-86 0 -157.5 -12.5t-116.5 -28.5l-31 201q47 16 137.5 32.5t192.5 16.5q123 0 207 -31t134 -86t71.5 -134t21.5 -173v-649q-57 -12 -173 -30t-261 -18 q-96 0 -176 18.5t-136.5 59.5t-88 106.5t-31.5 162.5zM193 1317q10 29 29.5 62.5t49.5 63.5t66.5 50t81.5 20q37 0 70 -12t64.5 -25.5t61.5 -25.5t58 -12q39 0 66.5 27.5t44.5 62.5l125 -74q-13 -29 -32 -62.5t-48 -63.5t-66 -50.5t-82 -20.5q-37 0 -69.5 11.5t-64 26 t-61.5 25.5t-59 11q-39 0 -66.5 -26.5t-43.5 -61.5zM330 330q0 -88 55.5 -122t149.5 -34q115 0 174 12v275q-20 6 -59.5 12t-86.5 6q-41 0 -83 -6t-75.5 -22.5t-54 -46t-20.5 -74.5z" />
<glyph unicode="&#xe4;" horiz-adv-x="1093" d="M82 324q0 92 36 155.5t97 103.5t142 57t169 17q41 0 86 -5t97 -17v41q0 43 -10.5 82t-36 68.5t-67.5 46t-106 16.5q-86 0 -157.5 -12.5t-116.5 -28.5l-31 201q47 16 137.5 32.5t192.5 16.5q123 0 207 -31t134 -86t71.5 -134t21.5 -173v-649q-57 -12 -173 -30t-261 -18 q-96 0 -176 18.5t-136.5 59.5t-88 106.5t-31.5 162.5zM195 1384q0 63 40.5 101.5t96.5 38.5q55 0 97 -38t42 -102q0 -63 -42 -100t-97 -37t-96 37t-41 100zM330 330q0 -88 55.5 -122t149.5 -34q115 0 174 12v275q-20 6 -59.5 12t-86.5 6q-41 0 -83 -6t-75.5 -22.5t-54 -46 t-20.5 -74.5zM616 1384q0 63 42 101.5t98 38.5q55 0 96 -38t41 -102q0 -63 -41 -100t-96 -37t-97.5 37t-42.5 100z" />
<glyph unicode="&#xe5;" horiz-adv-x="1093" d="M82 324q0 92 36 155.5t97 103.5t142 57t169 17q41 0 86 -5t97 -17v41q0 43 -10.5 82t-36 68.5t-67.5 46t-106 16.5q-86 0 -157.5 -12.5t-116.5 -28.5l-31 201q47 16 137.5 32.5t192.5 16.5q123 0 207 -31t134 -86t71.5 -134t21.5 -173v-649q-57 -12 -173 -30t-261 -18 q-96 0 -176 18.5t-136.5 59.5t-88 106.5t-31.5 162.5zM328 1409q0 51 18.5 93t49 69.5t71.5 43t86 15.5t86 -15.5t73 -43t50.5 -69.5t18.5 -93q0 -53 -18.5 -94t-50.5 -69.5t-73 -44t-86 -15.5t-86 15.5t-71.5 44t-49 69.5t-18.5 94zM330 330q0 -88 55.5 -122t149.5 -34 q115 0 174 12v275q-20 6 -59.5 12t-86.5 6q-41 0 -83 -6t-75.5 -22.5t-54 -46t-20.5 -74.5zM453 1409q0 -51 30.5 -77.5t69.5 -26.5q41 0 72 26.5t31 77.5q0 49 -31 76.5t-72 27.5q-39 0 -69.5 -27.5t-30.5 -76.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1779" d="M82 324q0 92 37 155.5t100.5 103.5t144 57t171.5 17q57 0 110 -8t76 -14v45q0 43 -11.5 82t-37 66.5t-69.5 44t-107 16.5q-86 0 -159 -12.5t-120 -28.5l-31 201q47 16 138.5 32.5t193.5 16.5q135 0 215 -41t129 -123q70 86 160 125t190 39q217 0 340 -137.5t123 -409.5 q0 -25 -2 -47.5t-2 -44.5h-706q10 -129 91 -200t232 -71q92 0 167 16.5t118 35.5l33 -203q-20 -10 -56.5 -21.5t-82.5 -20.5t-100 -15.5t-110 -6.5q-119 0 -206.5 29t-153.5 80q-80 -51 -175 -79t-204 -28q-98 0 -179 19.5t-138.5 61.5t-88 107.5t-30.5 160.5zM330 330 q0 -88 57.5 -122t159.5 -34q86 0 142 15.5t91 35.5q-29 66 -42 122t-17 116q-27 6 -67 11t-83 5t-86 -6t-77.5 -22.5t-56 -46t-21.5 -74.5zM967 641h460q0 51 -14 97t-42 80t-67.5 53.5t-95.5 19.5q-57 0 -100 -21.5t-72.5 -56.5t-46 -80t-22.5 -92z" />
<glyph unicode="&#xe7;" horiz-adv-x="966" d="M98 535q0 119 37 222t105.5 179t167 119t221.5 43q152 0 286 -56l-53 -202q-43 18 -97 30.5t-116 12.5q-145 0 -221 -91.5t-76 -256.5q0 -160 72 -253.5t242 -93.5q63 0 124.5 12.5t106.5 30.5l35 -204q-37 -18 -108.5 -32.5t-149.5 -19.5q-8 -16 -14.5 -30.5 t-10.5 -24.5q66 -31 91.5 -69.5t25.5 -96.5q0 -72 -61.5 -123t-184.5 -51q-98 0 -174 27l29 147q20 -6 53 -14t68 -8t54 12t19 37q0 33 -28.5 48t-57.5 26l-20 6q8 27 22.5 60.5t28.5 62.5q-106 18 -184 65t-129 120t-77 165t-26 201z" />
<glyph unicode="&#xe8;" horiz-adv-x="1169" d="M98 530q0 141 42 248t112 177.5t160 106.5t184 36q221 0 345 -137.5t124 -409.5q0 -20 -1 -46t-3 -46h-707q10 -129 91 -200t235 -71q90 0 165 16.5t118 35.5l32 -203q-20 -10 -56 -21.5t-82 -20.5t-99 -15.5t-109 -6.5q-141 0 -245.5 42t-172 117t-100.5 176t-33 222z M324 1511l157 144l297 -359l-123 -110zM356 641h461q0 51 -14.5 97t-42 80t-67.5 53.5t-95 19.5q-57 0 -100 -21.5t-73 -56.5t-46.5 -80t-22.5 -92z" />
<glyph unicode="&#xe9;" horiz-adv-x="1169" d="M98 530q0 141 42 248t112 177.5t160 106.5t184 36q221 0 345 -137.5t124 -409.5q0 -20 -1 -46t-3 -46h-707q10 -129 91 -200t235 -71q90 0 165 16.5t118 35.5l32 -203q-20 -10 -56 -21.5t-82 -20.5t-99 -15.5t-109 -6.5q-141 0 -245.5 42t-172 117t-100.5 176t-33 222z M356 641h461q0 51 -14.5 97t-42 80t-67.5 53.5t-95 19.5q-57 0 -100 -21.5t-73 -56.5t-46.5 -80t-22.5 -92zM403 1296l302 359l155 -146l-334 -323z" />
<glyph unicode="&#xea;" horiz-adv-x="1169" d="M98 530q0 141 42 248t112 177.5t160 106.5t184 36q221 0 345 -137.5t124 -409.5q0 -20 -1 -46t-3 -46h-707q10 -129 91 -200t235 -71q90 0 165 16.5t118 35.5l32 -203q-20 -10 -56 -21.5t-82 -20.5t-99 -15.5t-109 -6.5q-141 0 -245.5 42t-172 117t-100.5 176t-33 222z M288 1300l299 287l299 -287l-96 -108l-203 166l-200 -166zM356 641h461q0 51 -14.5 97t-42 80t-67.5 53.5t-95 19.5q-57 0 -100 -21.5t-73 -56.5t-46.5 -80t-22.5 -92z" />
<glyph unicode="&#xeb;" horiz-adv-x="1169" d="M98 530q0 141 42 248t112 177.5t160 106.5t184 36q221 0 345 -137.5t124 -409.5q0 -20 -1 -46t-3 -46h-707q10 -129 91 -200t235 -71q90 0 165 16.5t118 35.5l32 -203q-20 -10 -56 -21.5t-82 -20.5t-99 -15.5t-109 -6.5q-141 0 -245.5 42t-172 117t-100.5 176t-33 222z M246 1384q0 63 40.5 101.5t96.5 38.5q55 0 97 -38t42 -102q0 -63 -42 -100t-97 -37t-96 37t-41 100zM356 641h461q0 51 -14.5 97t-42 80t-67.5 53.5t-95 19.5q-57 0 -100 -21.5t-73 -56.5t-46.5 -80t-22.5 -92zM667 1384q0 63 42 101.5t98 38.5q55 0 96 -38t41 -102 q0 -63 -41 -100t-96 -37t-97.5 37t-42.5 100z" />
<glyph unicode="&#xec;" horiz-adv-x="565" d="M-10 1511l157 144l297 -359l-123 -110zM160 0v1071h248v-1071h-248z" />
<glyph unicode="&#xed;" horiz-adv-x="565" d="M120 1296l302 359l155 -146l-334 -323zM160 0v1071h248v-1071h-248z" />
<glyph unicode="&#xee;" horiz-adv-x="565" d="M-15 1300l299 287l299 -287l-96 -108l-203 166l-200 -166zM160 0v1071h248v-1071h-248z" />
<glyph unicode="&#xef;" horiz-adv-x="565" d="M-57 1384q0 63 40.5 101.5t96.5 38.5q55 0 97 -38t42 -102q0 -63 -42 -100t-97 -37t-96 37t-41 100zM160 0v1071h248v-1071h-248zM364 1384q0 63 42 101.5t98 38.5q55 0 96 -38t41 -102q0 -63 -41 -100t-96 -37t-97.5 37t-42.5 100z" />
<glyph unicode="&#xf0;" horiz-adv-x="1210" d="M96 494q0 113 31 205t92.5 157.5t151.5 101t207 35.5q68 0 140.5 -19.5t127.5 -51.5q-25 137 -101 256l-237 -80l-53 151l186 64q-66 72 -162 139l146 141q57 -35 118.5 -86t118.5 -121l246 84l53 -149l-200 -70q76 -135 114.5 -284.5t38.5 -315.5q0 -143 -27.5 -267 t-89 -216t-161 -144.5t-242.5 -52.5q-125 0 -218 43t-155.5 115t-93.5 166t-31 199zM346 496q0 -66 15.5 -121.5t46 -97t77.5 -65.5t109 -24q84 0 136 36t82 99.5t40 148.5t10 181v21.5t-2 27.5q-59 51 -123.5 68.5t-122.5 17.5q-74 0 -125 -22.5t-82.5 -62t-46 -93 t-14.5 -114.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="1198" d="M158 0v1036q72 20 186.5 39t253.5 19q131 0 219 -36t140.5 -100.5t74 -155.5t21.5 -200v-602h-248v563q0 86 -11.5 146.5t-37 98.5t-69.5 55.5t-107 17.5q-47 0 -98.5 -6.5t-75.5 -10.5v-864h-248zM246 1317q10 29 29.5 62.5t49.5 63.5t66.5 50t81.5 20q37 0 70 -12 t64.5 -25.5t61.5 -25.5t58 -12q39 0 66.5 27.5t44.5 62.5l125 -74q-13 -29 -32 -62.5t-48 -63.5t-66 -50.5t-82 -20.5q-37 0 -69.5 11.5t-64 26t-61.5 25.5t-59 11q-39 0 -66.5 -26.5t-43.5 -61.5z" />
<glyph unicode="&#xf2;" horiz-adv-x="1224" d="M98 537q0 127 38 230t106.5 177t163 114t206.5 40q113 0 208 -40t163 -114t105.5 -177t37.5 -230t-36.5 -231.5t-104 -178.5t-163 -115t-210.5 -41t-209 41t-161.5 115t-105.5 178.5t-38 231.5zM352 537q0 -162 68.5 -256.5t191.5 -94.5t191.5 94.5t68.5 256.5 q0 160 -68.5 253t-191.5 93t-191.5 -93.5t-68.5 -252.5zM357 1511l157 144l297 -359l-123 -110z" />
<glyph unicode="&#xf3;" horiz-adv-x="1224" d="M98 537q0 127 38 230t106.5 177t163 114t206.5 40q113 0 208 -40t163 -114t105.5 -177t37.5 -230t-36.5 -231.5t-104 -178.5t-163 -115t-210.5 -41t-209 41t-161.5 115t-105.5 178.5t-38 231.5zM352 537q0 -162 68.5 -256.5t191.5 -94.5t191.5 94.5t68.5 256.5 q0 160 -68.5 253t-191.5 93t-191.5 -93.5t-68.5 -252.5zM423 1296l302 359l155 -146l-334 -323z" />
<glyph unicode="&#xf4;" horiz-adv-x="1224" d="M98 537q0 127 38 230t106.5 177t163 114t206.5 40q113 0 208 -40t163 -114t105.5 -177t37.5 -230t-36.5 -231.5t-104 -178.5t-163 -115t-210.5 -41t-209 41t-161.5 115t-105.5 178.5t-38 231.5zM311 1300l299 287l299 -287l-96 -108l-203 166l-200 -166zM352 537 q0 -162 68.5 -256.5t191.5 -94.5t191.5 94.5t68.5 256.5q0 160 -68.5 253t-191.5 93t-191.5 -93.5t-68.5 -252.5z" />
<glyph unicode="&#xf5;" horiz-adv-x="1224" d="M98 537q0 127 38 230t106.5 177t163 114t206.5 40q113 0 208 -40t163 -114t105.5 -177t37.5 -230t-36.5 -231.5t-104 -178.5t-163 -115t-210.5 -41t-209 41t-161.5 115t-105.5 178.5t-38 231.5zM254 1317q10 29 29.5 62.5t49.5 63.5t66.5 50t81.5 20q37 0 70 -12 t64.5 -25.5t61.5 -25.5t58 -12q39 0 66.5 27.5t44.5 62.5l125 -74q-13 -29 -32 -62.5t-48 -63.5t-66 -50.5t-82 -20.5q-37 0 -69.5 11.5t-64 26t-61.5 25.5t-59 11q-39 0 -66.5 -26.5t-43.5 -61.5zM352 537q0 -162 68.5 -256.5t191.5 -94.5t191.5 94.5t68.5 256.5 q0 160 -68.5 253t-191.5 93t-191.5 -93.5t-68.5 -252.5z" />
<glyph unicode="&#xf6;" horiz-adv-x="1224" d="M98 537q0 127 38 230t106.5 177t163 114t206.5 40q113 0 208 -40t163 -114t105.5 -177t37.5 -230t-36.5 -231.5t-104 -178.5t-163 -115t-210.5 -41t-209 41t-161.5 115t-105.5 178.5t-38 231.5zM263 1384q0 63 40.5 101.5t96.5 38.5q55 0 97 -38t42 -102q0 -63 -42 -100 t-97 -37t-96 37t-41 100zM352 537q0 -162 68.5 -256.5t191.5 -94.5t191.5 94.5t68.5 256.5q0 160 -68.5 253t-191.5 93t-191.5 -93.5t-68.5 -252.5zM684 1384q0 63 42 101.5t98 38.5q55 0 96 -38t41 -102q0 -63 -41 -100t-96 -37t-97.5 37t-42.5 100z" />
<glyph unicode="&#xf7;" d="M106 498v209h951v-209h-951zM434 197q0 72 44 110.5t104 38.5q59 0 104 -38.5t45 -110.5q0 -70 -45 -109t-104 -39t-103.5 39t-44.5 109zM434 1008q0 70 44 108.5t104 38.5q59 0 104 -38.5t45 -108.5q0 -72 -45 -111t-104 -39t-103.5 39t-44.5 111z" />
<glyph unicode="&#xf8;" horiz-adv-x="1224" d="M98 537q0 127 38 230t106.5 177t163 114t206.5 40q156 0 277 -76l96 125l125 -94l-104 -135q57 -72 88.5 -168t31.5 -213q0 -127 -36.5 -231.5t-104 -178.5t-163 -115t-210.5 -41q-78 0 -147.5 20.5t-128.5 55.5l-96 -125l-127 96l104 136q-57 74 -88 170t-31 213z M344 537q0 -102 29 -183l383 496q-66 43 -144 43q-127 0 -197.5 -95t-70.5 -261zM467 219q61 -43 145 -43q129 0 199 97.5t70 263.5q0 100 -29 180z" />
<glyph unicode="&#xf9;" horiz-adv-x="1193" d="M145 471v600h248v-561q0 -172 50.5 -246t174.5 -74q45 0 95.5 4.5t74.5 10.5v866h248v-1038q-72 -18 -186.5 -38t-251.5 -20q-129 0 -216 37t-139.5 102.5t-75 156.5t-22.5 200zM303 1511l157 144l297 -359l-123 -110z" />
<glyph unicode="&#xfa;" horiz-adv-x="1193" d="M145 471v600h248v-561q0 -172 50.5 -246t174.5 -74q45 0 95.5 4.5t74.5 10.5v866h248v-1038q-72 -18 -186.5 -38t-251.5 -20q-129 0 -216 37t-139.5 102.5t-75 156.5t-22.5 200zM421 1296l302 359l155 -146l-334 -323z" />
<glyph unicode="&#xfb;" horiz-adv-x="1193" d="M145 471v600h248v-561q0 -172 50.5 -246t174.5 -74q45 0 95.5 4.5t74.5 10.5v866h248v-1038q-72 -18 -186.5 -38t-251.5 -20q-129 0 -216 37t-139.5 102.5t-75 156.5t-22.5 200zM293 1300l299 287l299 -287l-96 -108l-203 166l-200 -166z" />
<glyph unicode="&#xfc;" horiz-adv-x="1193" d="M145 471v600h248v-561q0 -172 50.5 -246t174.5 -74q45 0 95.5 4.5t74.5 10.5v866h248v-1038q-72 -18 -186.5 -38t-251.5 -20q-129 0 -216 37t-139.5 102.5t-75 156.5t-22.5 200zM242 1384q0 63 40.5 101.5t96.5 38.5q55 0 97 -38t42 -102q0 -63 -42 -100t-97 -37t-96 37 t-41 100zM663 1384q0 63 42 101.5t98 38.5q55 0 96 -38t41 -102q0 -63 -41 -100t-96 -37t-97.5 37t-42.5 100z" />
<glyph unicode="&#xfd;" horiz-adv-x="1062" d="M-2 -344l45 199q74 -29 145 -29q96 0 150.5 46t95.5 142q-117 225 -224.5 491.5t-191.5 565.5h265q20 -86 49.5 -186.5t64.5 -203.5t74 -205.5t78 -190.5q66 182 123 388t104 398h256q-166 -610 -385 -1106q-41 -92 -84 -157.5t-96 -108.5t-118.5 -62.5t-151.5 -19.5 q-57 0 -113.5 12.5t-85.5 26.5zM385 1296l302 359l155 -146l-334 -323z" />
<glyph unicode="&#xfe;" horiz-adv-x="1220" d="M158 -379v1927l248 41v-526q43 14 94 22.5t94 8.5q121 0 219 -39t168 -112t107.5 -176t37.5 -232q0 -123 -31.5 -225.5t-92 -176.5t-148.5 -115t-203 -41q-78 0 -140 18.5t-105 41.5v-416h-248zM406 252q33 -23 87 -42.5t117 -19.5q137 0 199.5 92t62.5 248 q0 66 -18 129.5t-55 112.5t-94.5 79t-133.5 30q-55 0 -99 -8.5t-66 -18.5v-602z" />
<glyph unicode="&#xff;" horiz-adv-x="1062" d="M-2 -344l45 199q74 -29 145 -29q96 0 150.5 46t95.5 142q-117 225 -224.5 491.5t-191.5 565.5h265q20 -86 49.5 -186.5t64.5 -203.5t74 -205.5t78 -190.5q66 182 123 388t104 398h256q-166 -610 -385 -1106q-41 -92 -84 -157.5t-96 -108.5t-118.5 -62.5t-151.5 -19.5 q-57 0 -113.5 12.5t-85.5 26.5zM193 1384q0 63 40.5 101.5t96.5 38.5q55 0 97 -38t42 -102q0 -63 -42 -100t-97 -37t-96 37t-41 100zM614 1384q0 63 42 101.5t98 38.5q55 0 96 -38t41 -102q0 -63 -41 -100t-96 -37t-97.5 37t-42.5 100z" />
<glyph unicode="&#x152;" horiz-adv-x="2035" d="M113 711q0 184 58 319t163.5 224t250 133.5t316.5 44.5q47 0 105.5 -3.5t107.5 -9.5h803v-217h-621v-354h547v-211h-547v-420h670v-217h-852q-49 -6 -107.5 -10t-105.5 -4q-172 0 -317.5 44t-250 134t-162.5 226t-58 321zM381 711q0 -242 134 -373t384 -131q76 0 143 6 v991q-43 4 -75.5 5t-67.5 1q-250 0 -384 -130t-134 -369z" />
<glyph unicode="&#x153;" horiz-adv-x="1931" d="M98 537q0 127 38 230t105.5 177t161 114t205.5 40q125 0 221.5 -51.5t161.5 -149.5q72 106 168 153.5t199 47.5q223 0 346 -137.5t123 -409.5q0 -25 -1 -49.5t-3 -42.5h-711q10 -129 92 -200t236 -71q92 0 166.5 16.5t117.5 35.5l33 -203q-20 -10 -56 -21.5t-82 -20.5 t-100.5 -15.5t-109.5 -6.5q-147 0 -252.5 50.5t-171.5 146.5q-68 -98 -164 -148.5t-213 -50.5q-115 0 -208 41t-160.5 115t-104.5 178.5t-37 231.5zM350 537q0 -162 68.5 -256.5t191.5 -94.5t191.5 94.5t68.5 256.5q0 160 -68.5 253t-191.5 93t-191.5 -93.5t-68.5 -252.5z M1118 641h461q0 51 -14.5 97t-42 80t-67.5 53.5t-95 19.5q-57 0 -100 -21.5t-73 -56.5t-46.5 -80t-22.5 -92z" />
<glyph unicode="&#x178;" horiz-adv-x="1280" d="M6 1419h303q72 -160 158 -317t180 -311q92 154 180 311.5t160 316.5h287q-115 -217 -239 -431t-265 -431v-557h-258v553q-141 219 -266 434t-240 432zM291 1708q0 63 40.5 101.5t96.5 38.5q55 0 97 -38t42 -102q0 -63 -42 -100t-97 -37t-96 37t-41 100zM712 1708 q0 63 42 101.5t98 38.5q55 0 96 -38t41 -102q0 -63 -41 -100t-96 -37t-97.5 37t-42.5 100z" />
<glyph unicode="&#x2c6;" horiz-adv-x="798" d="M100 1300l299 287l299 -287l-96 -108l-203 166l-200 -166z" />
<glyph unicode="&#x2dc;" horiz-adv-x="800" d="M41 1317q10 29 29.5 62.5t49.5 63.5t66.5 50t81.5 20q37 0 70 -12t64.5 -25.5t61.5 -25.5t58 -12q39 0 66.5 27.5t44.5 62.5l125 -74q-13 -29 -32 -62.5t-48 -63.5t-66 -50.5t-82 -20.5q-37 0 -69.5 11.5t-64 26t-61.5 25.5t-59 11q-39 0 -66.5 -26.5t-43.5 -61.5z" />
<glyph unicode="&#x2000;" horiz-adv-x="993" />
<glyph unicode="&#x2001;" horiz-adv-x="1987" />
<glyph unicode="&#x2002;" horiz-adv-x="993" />
<glyph unicode="&#x2003;" horiz-adv-x="1987" />
<glyph unicode="&#x2004;" horiz-adv-x="662" />
<glyph unicode="&#x2005;" horiz-adv-x="496" />
<glyph unicode="&#x2006;" horiz-adv-x="331" />
<glyph unicode="&#x2007;" horiz-adv-x="331" />
<glyph unicode="&#x2008;" horiz-adv-x="248" />
<glyph unicode="&#x2009;" horiz-adv-x="397" />
<glyph unicode="&#x200a;" horiz-adv-x="110" />
<glyph unicode="&#x2010;" horiz-adv-x="645" d="M47 471v227h551v-227h-551z" />
<glyph unicode="&#x2011;" horiz-adv-x="645" d="M47 471v227h551v-227h-551z" />
<glyph unicode="&#x2012;" horiz-adv-x="645" d="M47 471v227h551v-227h-551z" />
<glyph unicode="&#x2013;" horiz-adv-x="1024" d="M0 481v209h1024v-209h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2048" d="M0 481v209h2048v-209h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="516" d="M117 1091.5v43.5q4 123 48 235.5t107 202.5l183 -49q-41 -90 -63.5 -190.5t-22.5 -190.5v-47t4 -58h-252q-4 27 -4 53.5z" />
<glyph unicode="&#x2019;" horiz-adv-x="516" d="M61 1067q41 90 63.5 190.5t22.5 192.5q0 14 -1 45t-5 57h254q2 -25 3 -51t-1 -43q-4 -123 -48 -236.5t-107 -201.5z" />
<glyph unicode="&#x201a;" horiz-adv-x="516" d="M61 -213q41 90 63.5 190.5t22.5 190.5q0 16 -1 47t-5 57h254q2 -18 2 -37.5v-35.5q0 -129 -45 -248t-110 -213z" />
<glyph unicode="&#x201c;" horiz-adv-x="927" d="M117 1091.5v43.5q4 123 48 235.5t107 202.5l183 -49q-41 -90 -63.5 -190.5t-22.5 -190.5v-47t4 -58h-252q-4 27 -4 53.5zM529 1091.5v43.5q4 123 48 235.5t107 202.5l183 -49q-41 -90 -63.5 -190.5t-22.5 -190.5v-47t4 -58h-252q-4 27 -4 53.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="929" d="M61 1067q41 90 63.5 190.5t22.5 192.5q0 14 -1 45t-5 57h254q2 -25 3 -51t-1 -43q-4 -123 -48 -236.5t-107 -201.5zM475 1067q41 90 63.5 190.5t22.5 192.5q0 14 -1 45t-5 57h254q2 -25 3 -51t-1 -43q-4 -123 -48 -236.5t-107 -201.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="923" d="M61 -213q41 90 63.5 190.5t22.5 190.5q0 16 -1 47t-5 57h254q2 -18 2 -37.5v-35.5q0 -129 -45 -248t-110 -213zM469 -213q41 90 63.5 190.5t22.5 190.5q0 16 -1 47t-5 57h254q2 -18 2 -37.5v-35.5q0 -129 -45 -248t-110 -213z" />
<glyph unicode="&#x2022;" horiz-adv-x="757" d="M100 723q0 55 19.5 105.5t55.5 87t87 59t117 22.5q63 0 114.5 -22.5t88 -59t56 -87t19.5 -105.5t-19.5 -105.5t-56 -88t-88 -60.5t-114.5 -23q-66 0 -117 23t-87 60.5t-55.5 88t-19.5 105.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="2048" d="M248 135q0 72 47 118t116 46q68 0 116 -46t48 -118t-48 -117t-116 -45q-70 0 -116.5 45t-46.5 117zM861 135q0 72 47 118t116 46q68 0 116 -46t48 -118t-48 -117t-116 -45q-70 0 -116.5 45t-46.5 117zM1471 135q0 72 47 118t116 46q68 0 116 -46t48 -118t-48 -117 t-116 -45q-70 0 -116.5 45t-46.5 117z" />
<glyph unicode="&#x202f;" horiz-adv-x="397" />
<glyph unicode="&#x2039;" horiz-adv-x="641" d="M55 582l328 471l182 -90l-217 -381l217 -383l-182 -90z" />
<glyph unicode="&#x203a;" horiz-adv-x="641" d="M76 199l217 383l-217 381l182 90l328 -471l-328 -473z" />
<glyph unicode="&#x205f;" horiz-adv-x="496" />
<glyph unicode="&#x20ac;" d="M78 455v176h149q-2 23 -2 41v39v45t2 45h-149v174h172q51 236 195.5 353.5t369.5 117.5q90 0 152.5 -12.5t126.5 -34.5l-54 -201q-49 18 -103 29.5t-124 11.5q-139 0 -214 -70.5t-103 -193.5h465l-33 -174h-457q-2 -25 -2 -46.5v-43.5v-39t2 -41h424l-35 -176h-366 q33 -152 113.5 -212.5t201.5 -60.5q78 0 143.5 14.5t124.5 41.5l51 -199q-47 -23 -133 -44.5t-196 -21.5q-248 0 -381 127t-174 355h-166z" />
<glyph unicode="&#x2122;" horiz-adv-x="1703" d="M41 1251v168h616v-168h-213v-534h-190v534h-213zM707 717q20 260 33.5 427t29.5 275h191q37 -74 90 -178t106 -215q55 111 110.5 215.5t90.5 177.5h194q18 -109 29.5 -275.5t28.5 -426.5h-180q-4 109 -10.5 233.5t-14.5 229.5q-25 -41 -53.5 -94.5t-55 -105.5t-49 -98 t-35.5 -73h-106q-12 27 -34.5 72t-50.5 97t-56.5 105.5t-51.5 96.5q-8 -104 -14 -229.5t-10 -233.5h-182z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1075" d="M0 0v1075h1075v-1075h-1075z" />
<hkern u1="K" u2="&#xef;" k="-57" />
<hkern u1="T" u2="&#xef;" k="-76" />
<hkern u1="T" u2="&#xee;" k="-37" />
<hkern u1="V" u2="&#xef;" k="-98" />
<hkern u1="V" u2="&#xec;" k="10" />
<hkern u1="W" u2="&#xef;" k="-33" />
<hkern u1="Y" u2="&#xef;" k="-74" />
<hkern u1="Z" u2="&#xef;" k="-18" />
<hkern u1="f" u2="&#xef;" k="-94" />
<hkern u1="f" u2="&#xec;" k="-55" />
<hkern u1="&#xdd;" u2="&#xef;" k="-74" />
<hkern u1="&#x178;" u2="&#xef;" k="-74" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="v" 	k="27" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="w" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="y,yacute,ydieresis" 	k="27" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quotedbl,quotesingle" 	k="47" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="parenright" 	k="43" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="asterisk" 	k="37" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="question" 	k="49" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="bracketright" 	k="53" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="braceright" 	k="37" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteright,quotedblright" 	k="43" />
<hkern g1="b" 	g2="v" 	k="23" />
<hkern g1="b" 	g2="w" 	k="20" />
<hkern g1="b" 	g2="y,yacute,ydieresis" 	k="23" />
<hkern g1="b" 	g2="quotedbl,quotesingle" 	k="68" />
<hkern g1="b" 	g2="parenright" 	k="49" />
<hkern g1="b" 	g2="question" 	k="51" />
<hkern g1="b" 	g2="bracketright" 	k="55" />
<hkern g1="b" 	g2="braceright" 	k="37" />
<hkern g1="b" 	g2="quoteright,quotedblright" 	k="59" />
<hkern g1="b" 	g2="slash" 	k="39" />
<hkern g1="b" 	g2="quoteleft,quotedblleft" 	k="59" />
<hkern g1="b" 	g2="x" 	k="31" />
<hkern g1="b" 	g2="z" 	k="37" />
<hkern g1="c,ccedilla" 	g2="v" 	k="-18" />
<hkern g1="c,ccedilla" 	g2="w" 	k="-18" />
<hkern g1="c,ccedilla" 	g2="y,yacute,ydieresis" 	k="-18" />
<hkern g1="c,ccedilla" 	g2="bracketright" 	k="49" />
<hkern g1="c,ccedilla" 	g2="x" 	k="-29" />
<hkern g1="c,ccedilla" 	g2="hyphen,endash,emdash" 	k="57" />
<hkern g1="c,ccedilla" 	g2="braceleft" 	k="37" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="53" />
<hkern g1="c,ccedilla" 	g2="d" 	k="20" />
<hkern g1="c,ccedilla" 	g2="g" 	k="29" />
<hkern g1="c,ccedilla" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="23" />
<hkern g1="c,ccedilla" 	g2="comma,period,ellipsis" 	k="-18" />
<hkern g1="f" 	g2="v" 	k="-35" />
<hkern g1="f" 	g2="w" 	k="-33" />
<hkern g1="f" 	g2="y,yacute,ydieresis" 	k="-35" />
<hkern g1="f" 	g2="parenright" 	k="-72" />
<hkern g1="f" 	g2="asterisk" 	k="-18" />
<hkern g1="f" 	g2="question" 	k="-43" />
<hkern g1="f" 	g2="bracketright" 	k="-72" />
<hkern g1="f" 	g2="braceright" 	k="-72" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-37" />
<hkern g1="f" 	g2="slash" 	k="76" />
<hkern g1="f" 	g2="x" 	k="-27" />
<hkern g1="f" 	g2="hyphen,endash,emdash" 	k="63" />
<hkern g1="f" 	g2="guillemotleft,guilsinglleft" 	k="35" />
<hkern g1="f" 	g2="g" 	k="12" />
<hkern g1="f" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="f" 	g2="comma,period,ellipsis" 	k="94" />
<hkern g1="f" 	g2="eth" 	k="31" />
<hkern g1="f" 	g2="quotesinglbase,quotedblbase" 	k="96" />
<hkern g1="f" 	g2="guillemotright,guilsinglright" 	k="-16" />
<hkern g1="f" 	g2="c,ccedilla" 	k="20" />
<hkern g1="f" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="20" />
<hkern g1="f" 	g2="q" 	k="20" />
<hkern g1="h" 	g2="v" 	k="27" />
<hkern g1="h" 	g2="w" 	k="23" />
<hkern g1="h" 	g2="y,yacute,ydieresis" 	k="31" />
<hkern g1="h" 	g2="quotedbl,quotesingle" 	k="68" />
<hkern g1="h" 	g2="parenright" 	k="43" />
<hkern g1="h" 	g2="asterisk" 	k="37" />
<hkern g1="h" 	g2="question" 	k="49" />
<hkern g1="h" 	g2="bracketright" 	k="53" />
<hkern g1="h" 	g2="braceright" 	k="37" />
<hkern g1="h" 	g2="quoteright,quotedblright" 	k="55" />
<hkern g1="h" 	g2="quoteleft,quotedblleft" 	k="55" />
<hkern g1="j" 	g2="j" 	k="-16" />
<hkern g1="k" 	g2="bracketright" 	k="45" />
<hkern g1="k" 	g2="slash" 	k="-18" />
<hkern g1="k" 	g2="x" 	k="-35" />
<hkern g1="k" 	g2="hyphen,endash,emdash" 	k="49" />
<hkern g1="k" 	g2="braceleft" 	k="35" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="k" 	g2="d" 	k="47" />
<hkern g1="k" 	g2="g" 	k="53" />
<hkern g1="k" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="53" />
<hkern g1="k" 	g2="eth" 	k="49" />
<hkern g1="k" 	g2="c,ccedilla" 	k="47" />
<hkern g1="k" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="47" />
<hkern g1="k" 	g2="q" 	k="47" />
<hkern g1="k" 	g2="at" 	k="37" />
<hkern g1="m,n,ntilde" 	g2="v" 	k="29" />
<hkern g1="m,n,ntilde" 	g2="w" 	k="25" />
<hkern g1="m,n,ntilde" 	g2="y,yacute,ydieresis" 	k="33" />
<hkern g1="m,n,ntilde" 	g2="quotedbl,quotesingle" 	k="55" />
<hkern g1="m,n,ntilde" 	g2="parenright" 	k="45" />
<hkern g1="m,n,ntilde" 	g2="asterisk" 	k="39" />
<hkern g1="m,n,ntilde" 	g2="question" 	k="51" />
<hkern g1="m,n,ntilde" 	g2="bracketright" 	k="55" />
<hkern g1="m,n,ntilde" 	g2="braceright" 	k="37" />
<hkern g1="m,n,ntilde" 	g2="quoteright,quotedblright" 	k="47" />
<hkern g1="m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="39" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="v" 	k="25" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="w" 	k="23" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="y,yacute,ydieresis" 	k="25" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotedbl,quotesingle" 	k="57" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="parenright" 	k="49" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="asterisk" 	k="37" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="question" 	k="55" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="bracketright" 	k="55" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="braceright" 	k="37" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteright,quotedblright" 	k="49" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="slash" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteleft,quotedblleft" 	k="39" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="x" 	k="31" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="z" 	k="37" />
<hkern g1="p" 	g2="v" 	k="25" />
<hkern g1="p" 	g2="w" 	k="20" />
<hkern g1="p" 	g2="y,yacute,ydieresis" 	k="25" />
<hkern g1="p" 	g2="quotedbl,quotesingle" 	k="57" />
<hkern g1="p" 	g2="parenright" 	k="49" />
<hkern g1="p" 	g2="asterisk" 	k="37" />
<hkern g1="p" 	g2="question" 	k="51" />
<hkern g1="p" 	g2="bracketright" 	k="55" />
<hkern g1="p" 	g2="braceright" 	k="37" />
<hkern g1="p" 	g2="quoteright,quotedblright" 	k="49" />
<hkern g1="p" 	g2="slash" 	k="39" />
<hkern g1="p" 	g2="quoteleft,quotedblleft" 	k="39" />
<hkern g1="p" 	g2="x" 	k="31" />
<hkern g1="p" 	g2="z" 	k="33" />
<hkern g1="q" 	g2="j" 	k="-55" />
<hkern g1="r" 	g2="v" 	k="-37" />
<hkern g1="r" 	g2="w" 	k="-35" />
<hkern g1="r" 	g2="y,yacute,ydieresis" 	k="-37" />
<hkern g1="r" 	g2="asterisk" 	k="-20" />
<hkern g1="r" 	g2="question" 	k="78" />
<hkern g1="r" 	g2="bracketright" 	k="43" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-25" />
<hkern g1="r" 	g2="slash" 	k="84" />
<hkern g1="r" 	g2="x" 	k="-29" />
<hkern g1="r" 	g2="hyphen,endash,emdash" 	k="57" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="37" />
<hkern g1="r" 	g2="comma,period,ellipsis" 	k="100" />
<hkern g1="r" 	g2="eth" 	k="35" />
<hkern g1="r" 	g2="quotesinglbase,quotedblbase" 	k="94" />
<hkern g1="r" 	g2="q" 	k="20" />
<hkern g1="t" 	g2="bracketright" 	k="49" />
<hkern g1="t" 	g2="x" 	k="-31" />
<hkern g1="t" 	g2="hyphen,endash,emdash" 	k="72" />
<hkern g1="t" 	g2="braceleft" 	k="37" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="51" />
<hkern g1="t" 	g2="d" 	k="25" />
<hkern g1="t" 	g2="g" 	k="18" />
<hkern g1="t" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="t" 	g2="comma,period,ellipsis" 	k="-18" />
<hkern g1="t" 	g2="eth" 	k="20" />
<hkern g1="t" 	g2="c,ccedilla" 	k="23" />
<hkern g1="t" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="18" />
<hkern g1="t" 	g2="q" 	k="20" />
<hkern g1="v" 	g2="v" 	k="-33" />
<hkern g1="v" 	g2="w" 	k="-31" />
<hkern g1="v" 	g2="y,yacute,ydieresis" 	k="-33" />
<hkern g1="v" 	g2="asterisk" 	k="-16" />
<hkern g1="v" 	g2="question" 	k="72" />
<hkern g1="v" 	g2="bracketright" 	k="47" />
<hkern g1="v" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="v" 	g2="slash" 	k="57" />
<hkern g1="v" 	g2="x" 	k="-25" />
<hkern g1="v" 	g2="d" 	k="25" />
<hkern g1="v" 	g2="g" 	k="23" />
<hkern g1="v" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="v" 	g2="comma,period,ellipsis" 	k="63" />
<hkern g1="v" 	g2="eth" 	k="31" />
<hkern g1="v" 	g2="quotesinglbase,quotedblbase" 	k="66" />
<hkern g1="v" 	g2="c,ccedilla" 	k="27" />
<hkern g1="v" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="25" />
<hkern g1="v" 	g2="q" 	k="25" />
<hkern g1="w" 	g2="v" 	k="-33" />
<hkern g1="w" 	g2="w" 	k="-31" />
<hkern g1="w" 	g2="y,yacute,ydieresis" 	k="-33" />
<hkern g1="w" 	g2="asterisk" 	k="-16" />
<hkern g1="w" 	g2="question" 	k="63" />
<hkern g1="w" 	g2="bracketright" 	k="47" />
<hkern g1="w" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="w" 	g2="slash" 	k="51" />
<hkern g1="w" 	g2="x" 	k="-25" />
<hkern g1="w" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="w" 	g2="comma,period,ellipsis" 	k="57" />
<hkern g1="w" 	g2="eth" 	k="25" />
<hkern g1="w" 	g2="quotesinglbase,quotedblbase" 	k="55" />
<hkern g1="w" 	g2="c,ccedilla" 	k="23" />
<hkern g1="x" 	g2="bracketright" 	k="49" />
<hkern g1="x" 	g2="x" 	k="-31" />
<hkern g1="x" 	g2="braceleft" 	k="37" />
<hkern g1="x" 	g2="guillemotleft,guilsinglleft" 	k="53" />
<hkern g1="x" 	g2="d" 	k="37" />
<hkern g1="x" 	g2="g" 	k="37" />
<hkern g1="x" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="x" 	g2="eth" 	k="43" />
<hkern g1="x" 	g2="c,ccedilla" 	k="37" />
<hkern g1="x" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="37" />
<hkern g1="x" 	g2="q" 	k="37" />
<hkern g1="z" 	g2="bracketright" 	k="61" />
<hkern g1="z" 	g2="braceright" 	k="37" />
<hkern g1="z" 	g2="braceleft" 	k="51" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="59" />
<hkern g1="z" 	g2="d" 	k="37" />
<hkern g1="z" 	g2="g" 	k="39" />
<hkern g1="z" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="z" 	g2="eth" 	k="33" />
<hkern g1="z" 	g2="c,ccedilla" 	k="35" />
<hkern g1="z" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="35" />
<hkern g1="z" 	g2="q" 	k="33" />
<hkern g1="z" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="20" />
<hkern g1="z" 	g2="t" 	k="23" />
<hkern g1="z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="23" />
<hkern g1="germandbls" 	g2="v" 	k="51" />
<hkern g1="germandbls" 	g2="w" 	k="45" />
<hkern g1="germandbls" 	g2="y,yacute,ydieresis" 	k="51" />
<hkern g1="germandbls" 	g2="quotedbl,quotesingle" 	k="74" />
<hkern g1="germandbls" 	g2="parenright" 	k="49" />
<hkern g1="germandbls" 	g2="asterisk" 	k="59" />
<hkern g1="germandbls" 	g2="question" 	k="37" />
<hkern g1="germandbls" 	g2="bracketright" 	k="37" />
<hkern g1="germandbls" 	g2="braceright" 	k="37" />
<hkern g1="germandbls" 	g2="quoteright,quotedblright" 	k="55" />
<hkern g1="germandbls" 	g2="quoteleft,quotedblleft" 	k="55" />
<hkern g1="eth" 	g2="question" 	k="-27" />
<hkern g1="eth" 	g2="bracketright" 	k="-23" />
<hkern g1="eth" 	g2="braceright" 	k="-23" />
<hkern g1="eth" 	g2="slash" 	k="37" />
<hkern g1="eth" 	g2="comma,period,ellipsis" 	k="25" />
<hkern g1="thorn" 	g2="v" 	k="25" />
<hkern g1="thorn" 	g2="w" 	k="20" />
<hkern g1="thorn" 	g2="y,yacute,ydieresis" 	k="25" />
<hkern g1="thorn" 	g2="quotedbl,quotesingle" 	k="74" />
<hkern g1="thorn" 	g2="parenright" 	k="49" />
<hkern g1="thorn" 	g2="asterisk" 	k="37" />
<hkern g1="thorn" 	g2="question" 	k="51" />
<hkern g1="thorn" 	g2="bracketright" 	k="55" />
<hkern g1="thorn" 	g2="braceright" 	k="37" />
<hkern g1="thorn" 	g2="quoteright,quotedblright" 	k="66" />
<hkern g1="thorn" 	g2="slash" 	k="39" />
<hkern g1="thorn" 	g2="quoteleft,quotedblleft" 	k="66" />
<hkern g1="thorn" 	g2="x" 	k="31" />
<hkern g1="thorn" 	g2="z" 	k="33" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v" 	k="37" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="35" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="y,yacute,ydieresis" 	k="27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle" 	k="143" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk" 	k="115" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="bracketright" 	k="49" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="braceright" 	k="18" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="125" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="slash" 	k="-31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="129" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="x" 	k="-47" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="z" 	k="-27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="braceleft" 	k="37" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guillemotleft,guilsinglleft" 	k="35" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="d" 	k="29" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="g" 	k="29" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="comma,period,ellipsis" 	k="-47" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="eth" 	k="29" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotesinglbase,quotedblbase" 	k="-45" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="c,ccedilla" 	k="29" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="29" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="q" 	k="29" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="at" 	k="27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,Ccedilla" 	k="35" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="G" 	k="35" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="35" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Q" 	k="35" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="117" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="18" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="113" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="W" 	k="33" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="145" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="parenleft" 	k="35" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="s" 	k="-23" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-59" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="J" 	k="-53" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="S" 	k="-29" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="X" 	k="-45" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Z" 	k="-35" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="AE" 	k="-61" />
<hkern g1="B" 	g2="quotedbl,quotesingle" 	k="18" />
<hkern g1="B" 	g2="parenright" 	k="45" />
<hkern g1="B" 	g2="asterisk" 	k="18" />
<hkern g1="B" 	g2="question" 	k="37" />
<hkern g1="B" 	g2="bracketright" 	k="61" />
<hkern g1="B" 	g2="braceright" 	k="39" />
<hkern g1="B" 	g2="slash" 	k="37" />
<hkern g1="B" 	g2="z" 	k="23" />
<hkern g1="B" 	g2="comma,period,ellipsis" 	k="18" />
<hkern g1="B" 	g2="V" 	k="35" />
<hkern g1="B" 	g2="W" 	k="23" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="45" />
<hkern g1="B" 	g2="parenleft" 	k="18" />
<hkern g1="B" 	g2="X" 	k="37" />
<hkern g1="B" 	g2="AE" 	k="20" />
<hkern g1="B" 	g2="colon,semicolon" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="v" 	k="29" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="y,yacute,ydieresis" 	k="29" />
<hkern g1="C,Ccedilla" 	g2="question" 	k="-27" />
<hkern g1="C,Ccedilla" 	g2="quoteright,quotedblright" 	k="-23" />
<hkern g1="C,Ccedilla" 	g2="x" 	k="-25" />
<hkern g1="C,Ccedilla" 	g2="hyphen,endash,emdash" 	k="55" />
<hkern g1="C,Ccedilla" 	g2="braceleft" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="C,Ccedilla" 	g2="d" 	k="27" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="27" />
<hkern g1="C,Ccedilla" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="C,Ccedilla" 	g2="comma,period,ellipsis" 	k="-39" />
<hkern g1="C,Ccedilla" 	g2="eth" 	k="27" />
<hkern g1="C,Ccedilla" 	g2="quotesinglbase,quotedblbase" 	k="-33" />
<hkern g1="C,Ccedilla" 	g2="c,ccedilla" 	k="27" />
<hkern g1="C,Ccedilla" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="27" />
<hkern g1="C,Ccedilla" 	g2="q" 	k="27" />
<hkern g1="C,Ccedilla" 	g2="at" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="C,Ccedilla" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="G" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="Q" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="-31" />
<hkern g1="C,Ccedilla" 	g2="V" 	k="-31" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="-31" />
<hkern g1="C,Ccedilla" 	g2="parenleft" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-39" />
<hkern g1="C,Ccedilla" 	g2="J" 	k="-41" />
<hkern g1="C,Ccedilla" 	g2="S" 	k="-18" />
<hkern g1="C,Ccedilla" 	g2="X" 	k="-23" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="-18" />
<hkern g1="C,Ccedilla" 	g2="AE" 	k="-39" />
<hkern g1="C,Ccedilla" 	g2="bracketleft" 	k="18" />
<hkern g1="D,Eth" 	g2="quotedbl,quotesingle" 	k="47" />
<hkern g1="D,Eth" 	g2="parenright" 	k="59" />
<hkern g1="D,Eth" 	g2="question" 	k="72" />
<hkern g1="D,Eth" 	g2="bracketright" 	k="76" />
<hkern g1="D,Eth" 	g2="braceright" 	k="53" />
<hkern g1="D,Eth" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="D,Eth" 	g2="slash" 	k="78" />
<hkern g1="D,Eth" 	g2="quoteleft,quotedblleft" 	k="37" />
<hkern g1="D,Eth" 	g2="comma,period,ellipsis" 	k="59" />
<hkern g1="D,Eth" 	g2="quotesinglbase,quotedblbase" 	k="61" />
<hkern g1="D,Eth" 	g2="at" 	k="18" />
<hkern g1="D,Eth" 	g2="T" 	k="49" />
<hkern g1="D,Eth" 	g2="V" 	k="31" />
<hkern g1="D,Eth" 	g2="W" 	k="31" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="74" />
<hkern g1="D,Eth" 	g2="parenleft" 	k="20" />
<hkern g1="D,Eth" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="35" />
<hkern g1="D,Eth" 	g2="J" 	k="55" />
<hkern g1="D,Eth" 	g2="S" 	k="18" />
<hkern g1="D,Eth" 	g2="X" 	k="43" />
<hkern g1="D,Eth" 	g2="Z" 	k="27" />
<hkern g1="D,Eth" 	g2="AE" 	k="76" />
<hkern g1="D,Eth" 	g2="bracketleft" 	k="18" />
<hkern g1="D,Eth" 	g2="exclam" 	k="25" />
<hkern g1="D,Eth" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v" 	k="45" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="45" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="y,yacute,ydieresis" 	k="45" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="quotedbl,quotesingle" 	k="29" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="asterisk" 	k="23" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="bracketright" 	k="78" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="braceright" 	k="39" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="quoteleft,quotedblleft" 	k="23" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="x" 	k="-16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="braceleft" 	k="35" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="d" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="g" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="comma,period,ellipsis" 	k="-18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="eth" 	k="27" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,ccedilla" 	k="29" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="29" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="q" 	k="27" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="at" 	k="29" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="35" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="C,Ccedilla" 	k="51" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="G" 	k="51" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Q" 	k="51" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="39" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="parenleft" 	k="37" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="J" 	k="-31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="AE" 	k="-33" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="bracketleft" 	k="31" />
<hkern g1="F" 	g2="question" 	k="-23" />
<hkern g1="F" 	g2="bracketright" 	k="16" />
<hkern g1="F" 	g2="slash" 	k="121" />
<hkern g1="F" 	g2="x" 	k="66" />
<hkern g1="F" 	g2="z" 	k="47" />
<hkern g1="F" 	g2="d" 	k="25" />
<hkern g1="F" 	g2="g" 	k="25" />
<hkern g1="F" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="F" 	g2="comma,period,ellipsis" 	k="133" />
<hkern g1="F" 	g2="eth" 	k="27" />
<hkern g1="F" 	g2="quotesinglbase,quotedblbase" 	k="188" />
<hkern g1="F" 	g2="guillemotright,guilsinglright" 	k="37" />
<hkern g1="F" 	g2="c,ccedilla" 	k="25" />
<hkern g1="F" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="25" />
<hkern g1="F" 	g2="q" 	k="25" />
<hkern g1="F" 	g2="at" 	k="33" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="45" />
<hkern g1="F" 	g2="C,Ccedilla" 	k="43" />
<hkern g1="F" 	g2="G" 	k="43" />
<hkern g1="F" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="23" />
<hkern g1="F" 	g2="Q" 	k="23" />
<hkern g1="F" 	g2="T" 	k="-25" />
<hkern g1="F" 	g2="V" 	k="-33" />
<hkern g1="F" 	g2="Y,Yacute,Ydieresis" 	k="-37" />
<hkern g1="F" 	g2="parenleft" 	k="43" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="104" />
<hkern g1="F" 	g2="J" 	k="147" />
<hkern g1="F" 	g2="AE" 	k="139" />
<hkern g1="F" 	g2="colon,semicolon" 	k="33" />
<hkern g1="F" 	g2="bracketleft" 	k="27" />
<hkern g1="F" 	g2="exclam" 	k="18" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="92" />
<hkern g1="F" 	g2="m,n,ntilde" 	k="51" />
<hkern g1="F" 	g2="p" 	k="51" />
<hkern g1="F" 	g2="r" 	k="51" />
<hkern g1="G" 	g2="v" 	k="33" />
<hkern g1="G" 	g2="y,yacute,ydieresis" 	k="33" />
<hkern g1="G" 	g2="parenright" 	k="18" />
<hkern g1="G" 	g2="asterisk" 	k="45" />
<hkern g1="G" 	g2="bracketright" 	k="18" />
<hkern g1="G" 	g2="braceright" 	k="18" />
<hkern g1="J" 	g2="parenright" 	k="18" />
<hkern g1="J" 	g2="bracketright" 	k="61" />
<hkern g1="J" 	g2="braceright" 	k="35" />
<hkern g1="J" 	g2="slash" 	k="66" />
<hkern g1="J" 	g2="z" 	k="33" />
<hkern g1="J" 	g2="comma,period,ellipsis" 	k="18" />
<hkern g1="J" 	g2="quotesinglbase,quotedblbase" 	k="23" />
<hkern g1="J" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="18" />
<hkern g1="J" 	g2="J" 	k="27" />
<hkern g1="J" 	g2="Z" 	k="31" />
<hkern g1="J" 	g2="AE" 	k="45" />
<hkern g1="K" 	g2="v" 	k="31" />
<hkern g1="K" 	g2="w" 	k="88" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="25" />
<hkern g1="K" 	g2="asterisk" 	k="59" />
<hkern g1="K" 	g2="question" 	k="-18" />
<hkern g1="K" 	g2="bracketright" 	k="47" />
<hkern g1="K" 	g2="slash" 	k="-33" />
<hkern g1="K" 	g2="x" 	k="-45" />
<hkern g1="K" 	g2="z" 	k="-29" />
<hkern g1="K" 	g2="hyphen,endash,emdash" 	k="86" />
<hkern g1="K" 	g2="braceleft" 	k="35" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="111" />
<hkern g1="K" 	g2="d" 	k="47" />
<hkern g1="K" 	g2="g" 	k="43" />
<hkern g1="K" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="47" />
<hkern g1="K" 	g2="comma,period,ellipsis" 	k="-29" />
<hkern g1="K" 	g2="eth" 	k="55" />
<hkern g1="K" 	g2="quotesinglbase,quotedblbase" 	k="-27" />
<hkern g1="K" 	g2="c,ccedilla" 	k="47" />
<hkern g1="K" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="47" />
<hkern g1="K" 	g2="q" 	k="47" />
<hkern g1="K" 	g2="at" 	k="35" />
<hkern g1="K" 	g2="t" 	k="37" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="37" />
<hkern g1="K" 	g2="C,Ccedilla" 	k="63" />
<hkern g1="K" 	g2="G" 	k="63" />
<hkern g1="K" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="63" />
<hkern g1="K" 	g2="Q" 	k="63" />
<hkern g1="K" 	g2="T" 	k="-31" />
<hkern g1="K" 	g2="V" 	k="-37" />
<hkern g1="K" 	g2="W" 	k="-18" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="-39" />
<hkern g1="K" 	g2="parenleft" 	k="33" />
<hkern g1="K" 	g2="s" 	k="-25" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-61" />
<hkern g1="K" 	g2="J" 	k="-49" />
<hkern g1="K" 	g2="X" 	k="-47" />
<hkern g1="K" 	g2="Z" 	k="-37" />
<hkern g1="K" 	g2="AE" 	k="-63" />
<hkern g1="L" 	g2="v" 	k="88" />
<hkern g1="L" 	g2="w" 	k="68" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="47" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="231" />
<hkern g1="L" 	g2="asterisk" 	k="256" />
<hkern g1="L" 	g2="bracketright" 	k="57" />
<hkern g1="L" 	g2="braceright" 	k="27" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="217" />
<hkern g1="L" 	g2="slash" 	k="-20" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="221" />
<hkern g1="L" 	g2="x" 	k="-37" />
<hkern g1="L" 	g2="z" 	k="-18" />
<hkern g1="L" 	g2="hyphen,endash,emdash" 	k="127" />
<hkern g1="L" 	g2="braceleft" 	k="43" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="72" />
<hkern g1="L" 	g2="d" 	k="20" />
<hkern g1="L" 	g2="g" 	k="20" />
<hkern g1="L" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="L" 	g2="comma,period,ellipsis" 	k="-41" />
<hkern g1="L" 	g2="quotesinglbase,quotedblbase" 	k="-25" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="-18" />
<hkern g1="L" 	g2="c,ccedilla" 	k="20" />
<hkern g1="L" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="20" />
<hkern g1="L" 	g2="q" 	k="20" />
<hkern g1="L" 	g2="C,Ccedilla" 	k="82" />
<hkern g1="L" 	g2="G" 	k="82" />
<hkern g1="L" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="82" />
<hkern g1="L" 	g2="Q" 	k="82" />
<hkern g1="L" 	g2="T" 	k="211" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="33" />
<hkern g1="L" 	g2="V" 	k="184" />
<hkern g1="L" 	g2="W" 	k="82" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="211" />
<hkern g1="L" 	g2="parenleft" 	k="18" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-51" />
<hkern g1="L" 	g2="J" 	k="-51" />
<hkern g1="L" 	g2="S" 	k="-25" />
<hkern g1="L" 	g2="X" 	k="-37" />
<hkern g1="L" 	g2="Z" 	k="-25" />
<hkern g1="L" 	g2="AE" 	k="-53" />
<hkern g1="M" 	g2="Y,Yacute,Ydieresis" 	k="16" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="parenright" 	k="51" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="question" 	k="61" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="bracketright" 	k="76" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="braceright" 	k="53" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="18" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="76" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="29" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,ellipsis" 	k="59" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotesinglbase,quotedblbase" 	k="55" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="at" 	k="18" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="49" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="41" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="29" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="74" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="parenleft" 	k="20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="35" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="45" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="S" 	k="18" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="53" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="27" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="74" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="bracketleft" 	k="18" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="exclam" 	k="25" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="23" />
<hkern g1="P" 	g2="v" 	k="-18" />
<hkern g1="P" 	g2="w" 	k="-16" />
<hkern g1="P" 	g2="y,yacute,ydieresis" 	k="-18" />
<hkern g1="P" 	g2="parenright" 	k="43" />
<hkern g1="P" 	g2="bracketright" 	k="74" />
<hkern g1="P" 	g2="braceright" 	k="43" />
<hkern g1="P" 	g2="quoteright,quotedblright" 	k="-18" />
<hkern g1="P" 	g2="slash" 	k="113" />
<hkern g1="P" 	g2="guillemotleft,guilsinglleft" 	k="33" />
<hkern g1="P" 	g2="d" 	k="37" />
<hkern g1="P" 	g2="g" 	k="37" />
<hkern g1="P" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="P" 	g2="comma,period,ellipsis" 	k="176" />
<hkern g1="P" 	g2="eth" 	k="29" />
<hkern g1="P" 	g2="quotesinglbase,quotedblbase" 	k="211" />
<hkern g1="P" 	g2="c,ccedilla" 	k="37" />
<hkern g1="P" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="57" />
<hkern g1="P" 	g2="q" 	k="18" />
<hkern g1="P" 	g2="parenleft" 	k="20" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="92" />
<hkern g1="P" 	g2="J" 	k="184" />
<hkern g1="P" 	g2="X" 	k="43" />
<hkern g1="P" 	g2="AE" 	k="154" />
<hkern g1="P" 	g2="bracketleft" 	k="27" />
<hkern g1="P" 	g2="exclam" 	k="18" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="35" />
<hkern g1="Q" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="Q" 	g2="question" 	k="61" />
<hkern g1="Q" 	g2="quoteright,quotedblright" 	k="18" />
<hkern g1="Q" 	g2="quoteleft,quotedblleft" 	k="29" />
<hkern g1="Q" 	g2="comma,period,ellipsis" 	k="59" />
<hkern g1="Q" 	g2="quotesinglbase,quotedblbase" 	k="37" />
<hkern g1="Q" 	g2="j" 	k="-27" />
<hkern g1="Q" 	g2="at" 	k="18" />
<hkern g1="Q" 	g2="T" 	k="49" />
<hkern g1="Q" 	g2="V" 	k="41" />
<hkern g1="Q" 	g2="W" 	k="29" />
<hkern g1="Q" 	g2="Y,Yacute,Ydieresis" 	k="74" />
<hkern g1="Q" 	g2="parenleft" 	k="20" />
<hkern g1="Q" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="35" />
<hkern g1="Q" 	g2="J" 	k="45" />
<hkern g1="Q" 	g2="S" 	k="18" />
<hkern g1="Q" 	g2="X" 	k="53" />
<hkern g1="Q" 	g2="Z" 	k="27" />
<hkern g1="Q" 	g2="AE" 	k="74" />
<hkern g1="Q" 	g2="bracketleft" 	k="18" />
<hkern g1="Q" 	g2="exclam" 	k="25" />
<hkern g1="Q" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="25" />
<hkern g1="R" 	g2="question" 	k="23" />
<hkern g1="R" 	g2="bracketright" 	k="59" />
<hkern g1="R" 	g2="braceright" 	k="29" />
<hkern g1="R" 	g2="slash" 	k="-20" />
<hkern g1="R" 	g2="x" 	k="-37" />
<hkern g1="R" 	g2="z" 	k="-16" />
<hkern g1="R" 	g2="braceleft" 	k="31" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="37" />
<hkern g1="R" 	g2="comma,period,ellipsis" 	k="-25" />
<hkern g1="R" 	g2="eth" 	k="27" />
<hkern g1="R" 	g2="quotesinglbase,quotedblbase" 	k="-25" />
<hkern g1="R" 	g2="j" 	k="-10" />
<hkern g1="R" 	g2="at" 	k="27" />
<hkern g1="R" 	g2="C,Ccedilla" 	k="18" />
<hkern g1="R" 	g2="G" 	k="18" />
<hkern g1="R" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="R" 	g2="Q" 	k="18" />
<hkern g1="R" 	g2="V" 	k="18" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="35" />
<hkern g1="R" 	g2="parenleft" 	k="20" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-51" />
<hkern g1="R" 	g2="J" 	k="-39" />
<hkern g1="R" 	g2="X" 	k="-35" />
<hkern g1="R" 	g2="Z" 	k="-25" />
<hkern g1="R" 	g2="AE" 	k="-53" />
<hkern g1="T" 	g2="v" 	k="45" />
<hkern g1="T" 	g2="w" 	k="147" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="45" />
<hkern g1="T" 	g2="parenright" 	k="-20" />
<hkern g1="T" 	g2="question" 	k="-39" />
<hkern g1="T" 	g2="bracketright" 	k="57" />
<hkern g1="T" 	g2="slash" 	k="158" />
<hkern g1="T" 	g2="x" 	k="41" />
<hkern g1="T" 	g2="z" 	k="55" />
<hkern g1="T" 	g2="hyphen,endash,emdash" 	k="90" />
<hkern g1="T" 	g2="braceleft" 	k="43" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="156" />
<hkern g1="T" 	g2="d" 	k="113" />
<hkern g1="T" 	g2="g" 	k="113" />
<hkern g1="T" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="T" 	g2="comma,period,ellipsis" 	k="145" />
<hkern g1="T" 	g2="eth" 	k="145" />
<hkern g1="T" 	g2="quotesinglbase,quotedblbase" 	k="123" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="139" />
<hkern g1="T" 	g2="c,ccedilla" 	k="113" />
<hkern g1="T" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="113" />
<hkern g1="T" 	g2="q" 	k="113" />
<hkern g1="T" 	g2="at" 	k="92" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="68" />
<hkern g1="T" 	g2="C,Ccedilla" 	k="49" />
<hkern g1="T" 	g2="G" 	k="49" />
<hkern g1="T" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="49" />
<hkern g1="T" 	g2="Q" 	k="49" />
<hkern g1="T" 	g2="T" 	k="-43" />
<hkern g1="T" 	g2="V" 	k="-49" />
<hkern g1="T" 	g2="W" 	k="-31" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-53" />
<hkern g1="T" 	g2="parenleft" 	k="49" />
<hkern g1="T" 	g2="s" 	k="88" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="117" />
<hkern g1="T" 	g2="J" 	k="166" />
<hkern g1="T" 	g2="X" 	k="-27" />
<hkern g1="T" 	g2="AE" 	k="131" />
<hkern g1="T" 	g2="colon,semicolon" 	k="154" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="82" />
<hkern g1="T" 	g2="m,n,ntilde" 	k="74" />
<hkern g1="T" 	g2="p" 	k="74" />
<hkern g1="T" 	g2="r" 	k="74" />
<hkern g1="T" 	g2="ampersand" 	k="16" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="bracketright" 	k="68" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="braceright" 	k="39" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="slash" 	k="74" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z" 	k="33" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="braceleft" 	k="18" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,ellipsis" 	k="18" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="quotesinglbase,quotedblbase" 	k="37" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="parenleft" 	k="18" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="18" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="27" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="Z" 	k="29" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="61" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="colon,semicolon" 	k="18" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="29" />
<hkern g1="V" 	g2="bracketright" 	k="51" />
<hkern g1="V" 	g2="slash" 	k="123" />
<hkern g1="V" 	g2="hyphen,endash,emdash" 	k="18" />
<hkern g1="V" 	g2="braceleft" 	k="43" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="78" />
<hkern g1="V" 	g2="d" 	k="82" />
<hkern g1="V" 	g2="g" 	k="82" />
<hkern g1="V" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="V" 	g2="comma,period,ellipsis" 	k="137" />
<hkern g1="V" 	g2="eth" 	k="92" />
<hkern g1="V" 	g2="quotesinglbase,quotedblbase" 	k="135" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="18" />
<hkern g1="V" 	g2="c,ccedilla" 	k="82" />
<hkern g1="V" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="82" />
<hkern g1="V" 	g2="q" 	k="82" />
<hkern g1="V" 	g2="at" 	k="70" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="43" />
<hkern g1="V" 	g2="C,Ccedilla" 	k="31" />
<hkern g1="V" 	g2="G" 	k="31" />
<hkern g1="V" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="V" 	g2="Q" 	k="41" />
<hkern g1="V" 	g2="parenleft" 	k="59" />
<hkern g1="V" 	g2="s" 	k="27" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="113" />
<hkern g1="V" 	g2="J" 	k="174" />
<hkern g1="V" 	g2="AE" 	k="150" />
<hkern g1="V" 	g2="colon,semicolon" 	k="37" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="45" />
<hkern g1="V" 	g2="m,n,ntilde" 	k="55" />
<hkern g1="V" 	g2="p" 	k="55" />
<hkern g1="V" 	g2="r" 	k="55" />
<hkern g1="V" 	g2="quotedbl,quotesingle" 	k="-25" />
<hkern g1="V" 	g2="parenright" 	k="-29" />
<hkern g1="V" 	g2="question" 	k="-43" />
<hkern g1="V" 	g2="quoteleft,quotedblleft" 	k="-35" />
<hkern g1="V" 	g2="quoteright,quotedblright" 	k="-43" />
<hkern g1="V" 	g2="T" 	k="-51" />
<hkern g1="V" 	g2="V" 	k="-55" />
<hkern g1="V" 	g2="W" 	k="-37" />
<hkern g1="V" 	g2="X" 	k="-35" />
<hkern g1="V" 	g2="Y,Yacute,Ydieresis" 	k="-59" />
<hkern g1="W" 	g2="quotedbl,quotesingle" 	k="-23" />
<hkern g1="W" 	g2="question" 	k="-29" />
<hkern g1="W" 	g2="quoteleft,quotedblleft" 	k="-33" />
<hkern g1="W" 	g2="quoteright,quotedblright" 	k="-45" />
<hkern g1="W" 	g2="T" 	k="-35" />
<hkern g1="W" 	g2="V" 	k="-39" />
<hkern g1="W" 	g2="W" 	k="-20" />
<hkern g1="W" 	g2="X" 	k="-18" />
<hkern g1="W" 	g2="Y,Yacute,Ydieresis" 	k="-43" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="33" />
<hkern g1="W" 	g2="J" 	k="111" />
<hkern g1="W" 	g2="C,Ccedilla" 	k="29" />
<hkern g1="W" 	g2="G" 	k="29" />
<hkern g1="W" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="29" />
<hkern g1="W" 	g2="Q" 	k="29" />
<hkern g1="W" 	g2="AE" 	k="90" />
<hkern g1="W" 	g2="parenleft" 	k="29" />
<hkern g1="W" 	g2="comma,period,ellipsis" 	k="68" />
<hkern g1="W" 	g2="slash" 	k="86" />
<hkern g1="W" 	g2="colon,semicolon" 	k="18" />
<hkern g1="W" 	g2="at" 	k="35" />
<hkern g1="W" 	g2="bracketright" 	k="68" />
<hkern g1="W" 	g2="braceleft" 	k="31" />
<hkern g1="W" 	g2="quotesinglbase,quotedblbase" 	k="70" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="37" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="53" />
<hkern g1="W" 	g2="c,ccedilla" 	k="39" />
<hkern g1="W" 	g2="d" 	k="39" />
<hkern g1="W" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="39" />
<hkern g1="W" 	g2="g" 	k="47" />
<hkern g1="W" 	g2="m,n,ntilde" 	k="37" />
<hkern g1="W" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="39" />
<hkern g1="W" 	g2="p" 	k="37" />
<hkern g1="W" 	g2="q" 	k="39" />
<hkern g1="W" 	g2="r" 	k="37" />
<hkern g1="W" 	g2="s" 	k="31" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="35" />
<hkern g1="W" 	g2="eth" 	k="47" />
<hkern g1="X" 	g2="T" 	k="-23" />
<hkern g1="X" 	g2="V" 	k="-31" />
<hkern g1="X" 	g2="X" 	k="-33" />
<hkern g1="X" 	g2="Y,Yacute,Ydieresis" 	k="-35" />
<hkern g1="X" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-49" />
<hkern g1="X" 	g2="J" 	k="-37" />
<hkern g1="X" 	g2="C,Ccedilla" 	k="43" />
<hkern g1="X" 	g2="G" 	k="43" />
<hkern g1="X" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="53" />
<hkern g1="X" 	g2="Q" 	k="53" />
<hkern g1="X" 	g2="AE" 	k="-51" />
<hkern g1="X" 	g2="parenleft" 	k="45" />
<hkern g1="X" 	g2="comma,period,ellipsis" 	k="-23" />
<hkern g1="X" 	g2="slash" 	k="-18" />
<hkern g1="X" 	g2="at" 	k="47" />
<hkern g1="X" 	g2="bracketright" 	k="61" />
<hkern g1="X" 	g2="braceleft" 	k="47" />
<hkern g1="X" 	g2="quotesinglbase,quotedblbase" 	k="-18" />
<hkern g1="X" 	g2="guillemotleft,guilsinglleft" 	k="86" />
<hkern g1="X" 	g2="c,ccedilla" 	k="33" />
<hkern g1="X" 	g2="d" 	k="33" />
<hkern g1="X" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="33" />
<hkern g1="X" 	g2="g" 	k="33" />
<hkern g1="X" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="33" />
<hkern g1="X" 	g2="q" 	k="33" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="18" />
<hkern g1="X" 	g2="eth" 	k="53" />
<hkern g1="X" 	g2="v" 	k="18" />
<hkern g1="X" 	g2="asterisk" 	k="47" />
<hkern g1="X" 	g2="hyphen,endash,emdash" 	k="51" />
<hkern g1="X" 	g2="w" 	k="68" />
<hkern g1="X" 	g2="x" 	k="-35" />
<hkern g1="X" 	g2="Z" 	k="-23" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="parenright" 	k="-33" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="question" 	k="-45" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteleft,quotedblleft" 	k="-18" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-55" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="V" 	k="-59" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="W" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="X" 	k="-37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="-63" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="145" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="211" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,Ccedilla" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="G" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Q" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="147" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="parenleft" 	k="76" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,ellipsis" 	k="119" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="150" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="at" 	k="106" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="bracketright" 	k="47" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="braceleft" 	k="39" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="139" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,ccedilla" 	k="104" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d" 	k="104" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="104" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="104" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,ntilde" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="104" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="p" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="q" 	k="104" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="r" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="47" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="72" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="eth" 	k="154" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v" 	k="18" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,endash,emdash" 	k="90" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Z" 	k="-18" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="M" 	k="16" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="57" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="51" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="18" />
<hkern g1="Z" 	g2="V" 	k="-18" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="Z" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-31" />
<hkern g1="Z" 	g2="J" 	k="-31" />
<hkern g1="Z" 	g2="C,Ccedilla" 	k="66" />
<hkern g1="Z" 	g2="G" 	k="66" />
<hkern g1="Z" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="66" />
<hkern g1="Z" 	g2="Q" 	k="66" />
<hkern g1="Z" 	g2="AE" 	k="-33" />
<hkern g1="Z" 	g2="parenleft" 	k="37" />
<hkern g1="Z" 	g2="comma,period,ellipsis" 	k="-18" />
<hkern g1="Z" 	g2="at" 	k="29" />
<hkern g1="Z" 	g2="bracketright" 	k="78" />
<hkern g1="Z" 	g2="braceleft" 	k="63" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="92" />
<hkern g1="Z" 	g2="c,ccedilla" 	k="29" />
<hkern g1="Z" 	g2="d" 	k="31" />
<hkern g1="Z" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="29" />
<hkern g1="Z" 	g2="g" 	k="41" />
<hkern g1="Z" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="Z" 	g2="q" 	k="27" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="35" />
<hkern g1="Z" 	g2="eth" 	k="27" />
<hkern g1="Z" 	g2="v" 	k="41" />
<hkern g1="Z" 	g2="hyphen,endash,emdash" 	k="111" />
<hkern g1="Z" 	g2="w" 	k="43" />
<hkern g1="Z" 	g2="x" 	k="-16" />
<hkern g1="Z" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="Z" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="25" />
<hkern g1="Z" 	g2="braceright" 	k="27" />
<hkern g1="Z" 	g2="bracketleft" 	k="31" />
<hkern g1="Z" 	g2="t" 	k="23" />
<hkern g1="Thorn" 	g2="quotedbl,quotesingle" 	k="59" />
<hkern g1="Thorn" 	g2="parenright" 	k="72" />
<hkern g1="Thorn" 	g2="question" 	k="61" />
<hkern g1="Thorn" 	g2="quoteleft,quotedblleft" 	k="45" />
<hkern g1="Thorn" 	g2="quoteright,quotedblright" 	k="29" />
<hkern g1="Thorn" 	g2="T" 	k="80" />
<hkern g1="Thorn" 	g2="V" 	k="37" />
<hkern g1="Thorn" 	g2="X" 	k="88" />
<hkern g1="Thorn" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="Thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="49" />
<hkern g1="Thorn" 	g2="J" 	k="102" />
<hkern g1="Thorn" 	g2="AE" 	k="90" />
<hkern g1="Thorn" 	g2="comma,period,ellipsis" 	k="111" />
<hkern g1="Thorn" 	g2="slash" 	k="74" />
<hkern g1="Thorn" 	g2="bracketright" 	k="76" />
<hkern g1="Thorn" 	g2="quotesinglbase,quotedblbase" 	k="104" />
<hkern g1="Thorn" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="27" />
<hkern g1="Thorn" 	g2="Z" 	k="86" />
<hkern g1="Thorn" 	g2="braceright" 	k="49" />
<hkern g1="Thorn" 	g2="bracketleft" 	k="29" />
<hkern g1="Thorn" 	g2="exclam" 	k="25" />
<hkern g1="parenleft" 	g2="parenright" 	k="-123" />
<hkern g1="parenleft" 	g2="T" 	k="-18" />
<hkern g1="parenleft" 	g2="V" 	k="-27" />
<hkern g1="parenleft" 	g2="Y,Yacute,Ydieresis" 	k="-31" />
<hkern g1="parenleft" 	g2="C,Ccedilla" 	k="55" />
<hkern g1="parenleft" 	g2="G" 	k="55" />
<hkern g1="parenleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="55" />
<hkern g1="parenleft" 	g2="Q" 	k="55" />
<hkern g1="parenleft" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="18" />
<hkern g1="parenleft" 	g2="S" 	k="33" />
<hkern g1="parenright" 	g2="T" 	k="51" />
<hkern g1="parenright" 	g2="V" 	k="61" />
<hkern g1="parenright" 	g2="W" 	k="33" />
<hkern g1="parenright" 	g2="X" 	k="51" />
<hkern g1="parenright" 	g2="Y,Yacute,Ydieresis" 	k="78" />
<hkern g1="parenright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="37" />
<hkern g1="parenright" 	g2="J" 	k="23" />
<hkern g1="parenright" 	g2="C,Ccedilla" 	k="20" />
<hkern g1="parenright" 	g2="G" 	k="20" />
<hkern g1="parenright" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="parenright" 	g2="Q" 	k="20" />
<hkern g1="parenright" 	g2="AE" 	k="37" />
<hkern g1="parenright" 	g2="Z" 	k="39" />
<hkern g1="parenright" 	g2="S" 	k="20" />
<hkern g1="bracketleft" 	g2="T" 	k="59" />
<hkern g1="bracketleft" 	g2="V" 	k="53" />
<hkern g1="bracketleft" 	g2="W" 	k="68" />
<hkern g1="bracketleft" 	g2="X" 	k="66" />
<hkern g1="bracketleft" 	g2="Y,Yacute,Ydieresis" 	k="49" />
<hkern g1="bracketleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="bracketleft" 	g2="J" 	k="51" />
<hkern g1="bracketleft" 	g2="C,Ccedilla" 	k="78" />
<hkern g1="bracketleft" 	g2="G" 	k="78" />
<hkern g1="bracketleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="78" />
<hkern g1="bracketleft" 	g2="Q" 	k="78" />
<hkern g1="bracketleft" 	g2="AE" 	k="49" />
<hkern g1="bracketleft" 	g2="bracketright" 	k="-123" />
<hkern g1="bracketleft" 	g2="Z" 	k="74" />
<hkern g1="bracketleft" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="70" />
<hkern g1="bracketleft" 	g2="S" 	k="47" />
<hkern g1="bracketright" 	g2="W" 	k="18" />
<hkern g1="bracketright" 	g2="X" 	k="18" />
<hkern g1="bracketright" 	g2="C,Ccedilla" 	k="18" />
<hkern g1="bracketright" 	g2="G" 	k="18" />
<hkern g1="bracketright" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="bracketright" 	g2="Q" 	k="18" />
<hkern g1="bracketright" 	g2="Z" 	k="29" />
<hkern g1="braceleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="braceleft" 	g2="J" 	k="20" />
<hkern g1="braceleft" 	g2="C,Ccedilla" 	k="47" />
<hkern g1="braceleft" 	g2="G" 	k="55" />
<hkern g1="braceleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="55" />
<hkern g1="braceleft" 	g2="Q" 	k="55" />
<hkern g1="braceleft" 	g2="AE" 	k="18" />
<hkern g1="braceleft" 	g2="Z" 	k="33" />
<hkern g1="braceleft" 	g2="y,yacute,ydieresis" 	k="-25" />
<hkern g1="braceleft" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="41" />
<hkern g1="braceleft" 	g2="braceright" 	k="-123" />
<hkern g1="braceleft" 	g2="S" 	k="37" />
<hkern g1="braceright" 	g2="T" 	k="45" />
<hkern g1="braceright" 	g2="V" 	k="45" />
<hkern g1="braceright" 	g2="W" 	k="35" />
<hkern g1="braceright" 	g2="X" 	k="53" />
<hkern g1="braceright" 	g2="Y,Yacute,Ydieresis" 	k="43" />
<hkern g1="braceright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="37" />
<hkern g1="braceright" 	g2="J" 	k="37" />
<hkern g1="braceright" 	g2="AE" 	k="37" />
<hkern g1="braceright" 	g2="Z" 	k="61" />
<hkern g1="braceright" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="18" />
<hkern g1="braceright" 	g2="S" 	k="33" />
<hkern g1="asterisk" 	g2="X" 	k="45" />
<hkern g1="asterisk" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="117" />
<hkern g1="asterisk" 	g2="J" 	k="223" />
<hkern g1="asterisk" 	g2="C,Ccedilla" 	k="18" />
<hkern g1="asterisk" 	g2="G" 	k="18" />
<hkern g1="asterisk" 	g2="AE" 	k="178" />
<hkern g1="quotedbl,quotesingle" 	g2="V" 	k="-23" />
<hkern g1="quotedbl,quotesingle" 	g2="W" 	k="-18" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="145" />
<hkern g1="quotedbl,quotesingle" 	g2="J" 	k="225" />
<hkern g1="quotedbl,quotesingle" 	g2="C,Ccedilla" 	k="45" />
<hkern g1="quotedbl,quotesingle" 	g2="G" 	k="45" />
<hkern g1="quotedbl,quotesingle" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="45" />
<hkern g1="quotedbl,quotesingle" 	g2="Q" 	k="45" />
<hkern g1="quotedbl,quotesingle" 	g2="AE" 	k="246" />
<hkern g1="quotedbl,quotesingle" 	g2="Z" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="V" 	k="-35" />
<hkern g1="quoteleft,quotedblleft" 	g2="W" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-23" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="137" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="211" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,Ccedilla" 	k="37" />
<hkern g1="quoteleft,quotedblleft" 	g2="G" 	k="37" />
<hkern g1="quoteleft,quotedblleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="37" />
<hkern g1="quoteleft,quotedblleft" 	g2="Q" 	k="37" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="242" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,ccedilla" 	k="33" />
<hkern g1="quoteleft,quotedblleft" 	g2="g" 	k="16" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="123" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="V" 	k="135" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="61" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="X" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="141" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-47" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="J" 	k="-51" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,Ccedilla" 	k="45" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="G" 	k="45" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Q" 	k="55" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="AE" 	k="-37" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Z" 	k="-37" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="35" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="S" 	k="-29" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="141" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="59" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="158" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="80" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="37" />
<hkern g1="guillemotright,guilsinglright" 	g2="X" 	k="92" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="143" />
<hkern g1="guillemotright,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="37" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="76" />
<hkern g1="guillemotright,guilsinglright" 	g2="AE" 	k="80" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="78" />
<hkern g1="guillemotright,guilsinglright" 	g2="S" 	k="37" />
<hkern g1="hyphen,endash,emdash" 	g2="T" 	k="92" />
<hkern g1="hyphen,endash,emdash" 	g2="V" 	k="20" />
<hkern g1="hyphen,endash,emdash" 	g2="X" 	k="55" />
<hkern g1="hyphen,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="92" />
<hkern g1="hyphen,endash,emdash" 	g2="J" 	k="51" />
<hkern g1="hyphen,endash,emdash" 	g2="AE" 	k="25" />
<hkern g1="hyphen,endash,emdash" 	g2="Z" 	k="29" />
<hkern g1="comma,period,ellipsis" 	g2="T" 	k="129" />
<hkern g1="comma,period,ellipsis" 	g2="V" 	k="141" />
<hkern g1="comma,period,ellipsis" 	g2="W" 	k="74" />
<hkern g1="comma,period,ellipsis" 	g2="X" 	k="-18" />
<hkern g1="comma,period,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="147" />
<hkern g1="comma,period,ellipsis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-45" />
<hkern g1="comma,period,ellipsis" 	g2="J" 	k="-51" />
<hkern g1="comma,period,ellipsis" 	g2="C,Ccedilla" 	k="53" />
<hkern g1="comma,period,ellipsis" 	g2="G" 	k="53" />
<hkern g1="comma,period,ellipsis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="59" />
<hkern g1="comma,period,ellipsis" 	g2="Q" 	k="59" />
<hkern g1="comma,period,ellipsis" 	g2="AE" 	k="-35" />
<hkern g1="comma,period,ellipsis" 	g2="Z" 	k="-35" />
<hkern g1="comma,period,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="41" />
<hkern g1="comma,period,ellipsis" 	g2="S" 	k="-25" />
<hkern g1="colon,semicolon" 	g2="T" 	k="156" />
<hkern g1="colon,semicolon" 	g2="V" 	k="37" />
<hkern g1="colon,semicolon" 	g2="W" 	k="23" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="74" />
<hkern g1="colon,semicolon" 	g2="C,Ccedilla" 	k="18" />
<hkern g1="colon,semicolon" 	g2="G" 	k="18" />
<hkern g1="colon,semicolon" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="colon,semicolon" 	g2="Q" 	k="18" />
<hkern g1="colon,semicolon" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="slash" 	g2="T" 	k="-53" />
<hkern g1="slash" 	g2="V" 	k="-57" />
<hkern g1="slash" 	g2="W" 	k="-39" />
<hkern g1="slash" 	g2="X" 	k="-37" />
<hkern g1="slash" 	g2="Y,Yacute,Ydieresis" 	k="-61" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="92" />
<hkern g1="slash" 	g2="J" 	k="129" />
<hkern g1="slash" 	g2="C,Ccedilla" 	k="49" />
<hkern g1="slash" 	g2="G" 	k="49" />
<hkern g1="slash" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="47" />
<hkern g1="slash" 	g2="Q" 	k="47" />
<hkern g1="slash" 	g2="AE" 	k="141" />
<hkern g1="slash" 	g2="Z" 	k="-16" />
<hkern g1="ampersand" 	g2="T" 	k="57" />
<hkern g1="exclamdown" 	g2="T" 	k="152" />
<hkern g1="exclamdown" 	g2="V" 	k="53" />
<hkern g1="exclamdown" 	g2="W" 	k="37" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="86" />
<hkern g1="exclamdown" 	g2="Z" 	k="18" />
<hkern g1="exclamdown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="18" />
<hkern g1="questiondown" 	g2="T" 	k="203" />
<hkern g1="questiondown" 	g2="V" 	k="86" />
<hkern g1="questiondown" 	g2="W" 	k="72" />
<hkern g1="questiondown" 	g2="X" 	k="127" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="121" />
<hkern g1="questiondown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="84" />
<hkern g1="questiondown" 	g2="J" 	k="113" />
<hkern g1="questiondown" 	g2="C,Ccedilla" 	k="68" />
<hkern g1="questiondown" 	g2="G" 	k="68" />
<hkern g1="questiondown" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="68" />
<hkern g1="questiondown" 	g2="Q" 	k="68" />
<hkern g1="questiondown" 	g2="AE" 	k="129" />
<hkern g1="questiondown" 	g2="Z" 	k="125" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="80" />
<hkern g1="questiondown" 	g2="S" 	k="55" />
<hkern g1="at" 	g2="T" 	k="80" />
<hkern g1="at" 	g2="V" 	k="66" />
<hkern g1="at" 	g2="W" 	k="41" />
<hkern g1="at" 	g2="X" 	k="72" />
<hkern g1="at" 	g2="Y,Yacute,Ydieresis" 	k="98" />
<hkern g1="at" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="47" />
<hkern g1="at" 	g2="J" 	k="47" />
<hkern g1="at" 	g2="C,Ccedilla" 	k="18" />
<hkern g1="at" 	g2="G" 	k="18" />
<hkern g1="at" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="at" 	g2="Q" 	k="18" />
<hkern g1="at" 	g2="AE" 	k="70" />
<hkern g1="at" 	g2="Z" 	k="61" />
<hkern g1="at" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="at" 	g2="S" 	k="33" />
</font>
</defs></svg> 