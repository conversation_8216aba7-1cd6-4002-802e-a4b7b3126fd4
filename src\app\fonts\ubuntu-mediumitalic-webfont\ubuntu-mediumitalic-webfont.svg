<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="ubuntu_mediumitalic" horiz-adv-x="1163" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="485" />
<glyph unicode="&#xfb01;" horiz-adv-x="1366" d="M-41 -354q39 49 73 99t61.5 106.5t51 123t41.5 148.5l244 1024q25 104 67 187t104.5 139.5t149.5 86t201 29.5q72 0 129.5 -13t92.5 -30l-93 -194q-18 6 -53 16t-90 10q-61 0 -104 -17.5t-74 -48t-50.5 -74.5t-31.5 -95l-19 -72h359l-49 -205h-359l-153 -641 q-27 -111 -50.5 -196.5t-54 -157.5t-72.5 -134.5t-106 -123.5zM961 0l256 1071h247l-258 -1071h-245zM1253 1364q0 76 52.5 125t119.5 49q55 0 97.5 -34t42.5 -99q0 -76 -53 -124t-121 -48q-55 0 -96.5 32.5t-41.5 98.5z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1404" d="M-41 -354q39 49 73 99t61.5 106.5t51 123t41.5 148.5l244 1024q25 104 67 187t104.5 139.5t149.5 86t201 29.5q72 0 129.5 -13t92.5 -30l-93 -194q-18 6 -53 16t-90 10q-61 0 -104 -17.5t-74 -48t-50.5 -74.5t-31.5 -95l-19 -72h359l-49 -205h-359l-153 -641 q-27 -111 -50.5 -196.5t-54 -157.5t-72.5 -134.5t-106 -123.5zM1010 274q0 66 15 138.5t34 144.5l237 991l258 41q-63 -268 -126.5 -533t-129.5 -534q-10 -43 -20 -85t-14 -83q-4 -35 -1 -64.5t18 -51t46 -36t80 -18.5l-53 -204q-96 2 -163 24.5t-107 61t-57 92t-17 116.5z " />
<glyph unicode="&#xfb03;" horiz-adv-x="2177" d="M-41 -354q39 49 73 99t61.5 106.5t51 123t41.5 148.5l244 1024q25 104 67 187t104.5 139.5t149.5 86t201 29.5q82 0 148.5 -17.5t101.5 -33.5l-90 -195q-27 12 -71 23.5t-103 11.5q-61 0 -104 -17.5t-74 -48t-50.5 -74.5t-31.5 -95l-19 -72h359l-49 -205h-359l-153 -641 q-27 -111 -50.5 -196.5t-54 -157.5t-72.5 -134.5t-106 -123.5zM770 -354q39 49 73 99t61.5 106.5t51 123t41.5 148.5l244 1024q25 104 67 187t104.5 139.5t149.5 86t201 29.5q72 0 129.5 -13t92.5 -30l-93 -194q-18 6 -53 16t-90 10q-61 0 -104 -17.5t-74 -48t-50.5 -74.5 t-31.5 -95l-19 -72h359l-49 -205h-359l-153 -641q-27 -111 -50.5 -196.5t-54 -157.5t-72.5 -134.5t-106 -123.5zM1772 0l256 1071h247l-258 -1071h-245zM2064 1364q0 76 52.5 125t119.5 49q55 0 97.5 -34t42.5 -99q0 -76 -53 -124t-121 -48q-55 0 -96.5 32.5t-41.5 98.5z " />
<glyph unicode="&#xfb04;" horiz-adv-x="2215" d="M-41 -354q39 49 73 99t61.5 106.5t51 123t41.5 148.5l244 1024q25 104 67 187t104.5 139.5t149.5 86t201 29.5q82 0 148.5 -17.5t101.5 -33.5l-90 -195q-27 12 -71 23.5t-103 11.5q-61 0 -104 -17.5t-74 -48t-50.5 -74.5t-31.5 -95l-19 -72h359l-49 -205h-359l-153 -641 q-27 -111 -50.5 -196.5t-54 -157.5t-72.5 -134.5t-106 -123.5zM770 -354q39 49 73 99t61.5 106.5t51 123t41.5 148.5l244 1024q25 104 67 187t104.5 139.5t149.5 86t201 29.5q72 0 129.5 -13t92.5 -30l-93 -194q-18 6 -53 16t-90 10q-61 0 -104 -17.5t-74 -48t-50.5 -74.5 t-31.5 -95l-19 -72h359l-49 -205h-359l-153 -641q-27 -111 -50.5 -196.5t-54 -157.5t-72.5 -134.5t-106 -123.5zM1821 274q0 66 15 138.5t34 144.5l237 991l258 41q-63 -268 -126.5 -533t-129.5 -534q-10 -43 -20 -85t-14 -83q-4 -35 -1 -64.5t18 -51t46 -36t80 -18.5 l-53 -204q-96 2 -163 24.5t-107 61t-57 92t-17 116.5z" />
<glyph unicode="&#xd;" horiz-adv-x="485" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode=" "  horiz-adv-x="485" />
<glyph unicode="&#x09;" horiz-adv-x="485" />
<glyph unicode="&#xa0;" horiz-adv-x="485" />
<glyph unicode="!" horiz-adv-x="561" d="M152 117q0 86 53 134t121 48q66 0 106.5 -37t40.5 -108q0 -41 -15.5 -75t-40 -57.5t-56 -36t-64.5 -12.5q-66 0 -105.5 36t-39.5 108zM297 446q16 143 36.5 285.5t61.5 310.5l90 377h263l-91 -377q-16 -68 -37.5 -144.5t-46 -156t-51 -155.5t-51.5 -140h-174z" />
<glyph unicode="&#x22;" horiz-adv-x="931" d="M389 989q2 47 8.5 103.5t13.5 115t17 115.5t23 109l30 124h248l-30 -126q-25 -98 -65 -214t-87 -227h-158zM786 989q2 47 8.5 103.5t13.5 115t17 115.5t23 109l30 124h248l-30 -126q-25 -98 -65 -214t-87 -227h-158z" />
<glyph unicode="#" horiz-adv-x="1343" d="M180 360l47 195h193l131 309h-250l47 193h285l153 362h218l-154 -362h274l154 362h217l-154 -362h158l-47 -193h-190l-134 -309h250l-47 -195h-282l-154 -360h-217l151 360h-272l-154 -360h-217l152 360h-158zM637 555h274l133 309h-274z" />
<glyph unicode="$" d="M168 137l106 187q20 -14 52 -31t72 -31t87.5 -23.5t98.5 -9.5t102 8.5t92 29t66.5 57t25.5 96.5q0 74 -58 118t-146 85q-123 59 -199 144t-76 216q0 70 24.5 134.5t76 114.5t127 84t178.5 46l55 227h213l-57 -227q84 -12 150.5 -40t107.5 -54l-105 -178q-45 33 -115.5 54 t-156.5 21q-51 0 -97.5 -10t-80 -29.5t-54 -51.5t-20.5 -75q0 -37 10 -62.5t34 -48t59.5 -44t86.5 -47.5q57 -29 110.5 -60.5t93.5 -73.5t63.5 -96.5t23.5 -128.5q0 -92 -30.5 -163.5t-87 -122t-135.5 -81t-175 -40.5l-61 -254h-211l61 258q-53 6 -101 17t-89 26.5t-72 31 t-49 27.5z" />
<glyph unicode="%" horiz-adv-x="1796" d="M317 967q0 100 31 189t86 153.5t129 102.5t160 38q129 0 207 -71.5t78 -217.5q0 -102 -31 -190t-86 -153.5t-130 -103.5t-161 -38q-129 0 -206 72.5t-77 218.5zM369 0l1132 1419h246l-1135 -1419h-243zM506 977q0 -61 26.5 -98t83.5 -37q45 0 83 25.5t65 68.5t41 98.5 t14 114.5q0 63 -25.5 100t-82.5 37q-47 0 -84 -26.5t-64.5 -69.5t-42 -99.5t-14.5 -113.5zM1108 258q0 100 30.5 189.5t86 154t129 102t159.5 37.5q129 0 207 -71.5t78 -216.5q0 -102 -30.5 -190.5t-86 -154t-130 -103.5t-160.5 -38q-129 0 -206 73t-77 218zM1296 268 q0 -61 27 -98t84 -37q45 0 83 25.5t64.5 68.5t41 98.5t14.5 114.5q0 63 -25.5 100.5t-83.5 37.5q-47 0 -84 -27t-64.5 -70t-42 -99t-14.5 -114z" />
<glyph unicode="&#x26;" horiz-adv-x="1335" d="M154 322q0 86 26.5 154.5t75.5 123.5t119.5 101.5t159.5 89.5q-39 61 -60.5 126.5t-21.5 137.5q0 88 31.5 160.5t90 125t141.5 82t185 29.5q147 0 234.5 -78t87.5 -203q0 -117 -75 -219t-212 -172q-23 -12 -46.5 -23.5t-45.5 -21.5l205 -280q37 53 68.5 114.5t56.5 132.5 l217 -26q-37 -100 -91.5 -199.5t-125.5 -187.5q53 -74 96 -146.5t71 -142.5h-251q-14 31 -34 64.5t-42 68.5q-96 -72 -209 -111.5t-248 -39.5q-80 0 -153.5 19t-129 60t-88 105.5t-32.5 155.5zM397 356q0 -76 44.5 -127t142.5 -51q76 0 157.5 26.5t157.5 88.5l-260 352 q-104 -43 -173 -115.5t-69 -173.5zM678 1057q0 -41 17.5 -86t45.5 -94q55 25 103.5 52t84.5 59t56.5 71t20.5 88q0 53 -33 87t-99 34q-39 0 -73.5 -13.5t-62 -39t-44 -65.5t-16.5 -93z" />
<glyph unicode="'" horiz-adv-x="530" d="M397 989q2 47 8.5 103.5t13.5 115t17 115.5t23 109l30 124h248l-30 -126q-25 -98 -65 -214t-87 -227h-158z" />
<glyph unicode="(" horiz-adv-x="675" d="M252 264q0 188 43 370.5t124 352.5t196.5 325.5t261.5 291.5l161 -115q-123 -127 -223 -269.5t-172 -296t-110.5 -315.5t-38.5 -327q0 -150 24.5 -284t79.5 -263l-184 -109q-80 139 -121 302t-41 337z" />
<glyph unicode=")" horiz-adv-x="675" d="M-109 -264q123 127 223.5 269t172 296t110.5 315.5t39 327.5q0 150 -24.5 284t-79.5 263l184 108q80 -139 121 -301.5t41 -336.5q0 -188 -43 -370.5t-124 -353.5t-196.5 -326t-261.5 -290z" />
<glyph unicode="*" horiz-adv-x="964" d="M287 1030l69 213l19 -6q68 -23 143.5 -67t136.5 -85q-20 70 -38.5 149t-18.5 150v35h225v-35q0 -72 -18.5 -150.5t-38.5 -148.5q59 39 130 82t138 66l31 10l70 -213l-33 -10q-70 -23 -152 -29t-153 -8q66 -47 127 -104.5t106 -114.5l14 -19l-184 -129l-18 27 q-41 59 -74 135t-57 142q-14 -33 -29.5 -72t-33 -77t-37 -75t-40.5 -63l-12 -17l-182 129l20 29q43 59 105.5 111.5t120.5 97.5q-72 2 -149 7t-155 30z" />
<glyph unicode="+" d="M219 481l51 209h357l96 402h227l-96 -402h356l-51 -209h-356l-96 -401h-226l94 401h-356z" />
<glyph unicode="," horiz-adv-x="548" d="M0 -274q80 125 133 268t64 278h260q-12 -164 -92 -318.5t-189 -283.5z" />
<glyph unicode="-" horiz-adv-x="653" d="M170 471l53 227h545l-53 -227h-545z" />
<glyph unicode="." horiz-adv-x="542" d="M133 117q0 86 53 134t121 48q66 0 107 -37t41 -108q0 -41 -15.5 -75t-40 -57.5t-56.5 -36t-64 -12.5q-66 0 -106 36t-40 108z" />
<glyph unicode="/" horiz-adv-x="817" d="M-141 -379l1145 1983h249l-1144 -1983h-250z" />
<glyph unicode="0" d="M217 393q0 193 46 382.5t134 340t217 243.5t293 93q94 0 161 -31.5t110 -88t63.5 -134.5t20.5 -170q0 -193 -46.5 -382t-135.5 -339.5t-217 -244t-294 -93.5q-94 0 -160.5 32t-109.5 88t-62.5 134t-19.5 170zM455 426q0 -129 34.5 -185.5t114.5 -56.5q66 0 122 39 t103.5 103.5t83 148.5t60 174t38 179.5t13.5 166.5q0 129 -35 186.5t-115 57.5q-66 0 -123 -39t-103 -104.5t-82 -149.5t-61.5 -174t-37.5 -180t-12 -166z" />
<glyph unicode="1" d="M436 1124q129 47 266.5 121t251.5 174h176l-342 -1419h-247l260 1079q-66 -43 -155 -83t-175 -68z" />
<glyph unicode="2" d="M143 0q18 119 61.5 210t106 166t140 139.5t163.5 129.5q86 66 156 120t119 105.5t75.5 102.5t26.5 110q0 68 -43 111t-123 43q-100 0 -183 -43t-148 -96l-84 186q90 76 206.5 122t249.5 46q90 0 161 -27.5t119 -72.5t72.5 -105.5t24.5 -124.5q0 -92 -25.5 -164.5 t-74.5 -135t-121 -123t-162 -130.5q-53 -41 -110.5 -83t-109.5 -86t-97 -92t-76 -101h612l-49 -207h-887z" />
<glyph unicode="3" d="M129 59l90 203q45 -29 125 -57.5t188 -28.5q70 0 132.5 16.5t108.5 50t72.5 85t26.5 123.5q0 102 -78.5 151t-213.5 49h-82l47 199h102q57 0 114.5 13.5t102.5 41t74 71.5t29 107q0 78 -51.5 118t-139.5 40q-76 0 -150.5 -25.5t-140.5 -62.5l-61 188q78 47 180.5 79 t212.5 32q102 0 177 -27.5t124 -73.5t74 -106.5t25 -126.5q0 -121 -75 -217t-210 -147q78 -35 133 -115t55 -193q0 -96 -36.5 -182t-110.5 -151.5t-186.5 -104.5t-266.5 -39q-129 0 -230.5 29t-160.5 61z" />
<glyph unicode="4" d="M156 340l41 176q61 94 154 212t205 241t234.5 240.5t241.5 209.5h242l-211 -878h158l-50 -201h-157l-82 -340h-238l80 340h-618zM446 541h381l140 579q-63 -55 -132 -123.5t-137.5 -144.5t-133 -155t-118.5 -156z" />
<glyph unicode="5" d="M141 57l92 205q51 -29 127 -54.5t170 -25.5q160 0 255.5 68.5t95.5 202.5q0 72 -39 118.5t-106.5 74.5t-161 40t-201.5 14q66 190 124 366.5t105 352.5h711l-49 -207h-500q-12 -39 -26.5 -85t-29 -91t-27.5 -86t-24 -69q238 -27 354.5 -133.5t116.5 -274.5 q0 -111 -36.5 -203t-111.5 -158.5t-187.5 -104.5t-264.5 -38q-55 0 -111.5 8.5t-108.5 20.5t-95 27.5t-72 31.5z" />
<glyph unicode="6" d="M236 424q0 197 67.5 379t196.5 321.5t319.5 222.5t440.5 83h24t31 -3l-31 -200q-274 -4 -457.5 -104.5t-267.5 -299.5q55 27 127 44.5t143 17.5q170 0 264.5 -97.5t94.5 -263.5q0 -117 -37 -218t-107.5 -176t-171 -118t-225.5 -43q-92 0 -168 31t-130 89t-83.5 142 t-29.5 193zM475 430q0 -113 46 -184.5t151 -71.5q63 0 115.5 26.5t87 71.5t54 104.5t19.5 127.5q0 98 -50 142t-142 44q-127 0 -246 -53q-16 -53 -25.5 -106.5t-9.5 -100.5z" />
<glyph unicode="7" d="M291 0q43 158 124 330t181 334.5t209.5 304t210.5 237.5h-645l51 213h942l-45 -192q-55 -47 -124 -122t-143.5 -169t-149.5 -204.5t-141.5 -230.5t-122.5 -247t-91 -254h-256z" />
<glyph unicode="8" d="M195 305q0 135 79.5 243.5t239.5 199.5q-57 47 -89 109.5t-32 143.5q0 86 32 167t95.5 144.5t155.5 101.5t215 38q90 0 160.5 -25.5t119 -70.5t74 -104.5t25.5 -127.5q0 -117 -65.5 -222t-227.5 -185q72 -49 120 -118t48 -181q0 -90 -35 -172t-103.5 -143.5t-170 -97.5 t-236.5 -36q-90 0 -164 23.5t-128 66.5t-83.5 105.5t-29.5 140.5zM434 340q0 -78 47 -124t146 -46q55 0 105 17.5t88 48t60.5 74t22.5 96.5q0 90 -62.5 145t-162.5 98q-121 -53 -182.5 -137t-61.5 -172zM627 1020q0 -76 46 -126t132 -87q111 53 170 123.5t59 161.5 q0 66 -40 112.5t-134 46.5q-51 0 -94 -17t-73.5 -49t-48 -74t-17.5 -91z" />
<glyph unicode="9" d="M168 -4l35 199q29 -2 63.5 -1t50.5 3q242 23 389.5 121t225.5 284q-59 -31 -133 -46t-140 -15q-170 0 -264 97t-94 261q0 117 38 218.5t107.5 176t168 116.5t221.5 42q190 0 303.5 -119t113.5 -336q0 -201 -65.5 -382t-193.5 -319t-318.5 -220t-439.5 -82h-30t-38 2z M541 920q0 -98 51 -141.5t143 -43.5q123 0 246 53q16 51 24.5 105.5t8.5 107.5q0 104 -47 175t-150 71q-66 0 -117 -26.5t-85.5 -70.5t-54 -103t-19.5 -127z" />
<glyph unicode=":" horiz-adv-x="522" d="M133 117q0 86 53 134t121 48q66 0 107 -37t41 -108q0 -41 -15.5 -75t-40 -57.5t-56.5 -36t-64 -12.5q-66 0 -106 36t-40 108zM307 889q0 86 53 134t121 48q66 0 107 -37t41 -108q0 -41 -15.5 -75t-40 -57.5t-56.5 -36t-64 -12.5q-66 0 -106 36t-40 108z" />
<glyph unicode=";" horiz-adv-x="522" d="M0 -274q80 125 133 268t64 278h260q-12 -164 -92 -318.5t-189 -283.5zM309 889q0 86 53 134t121 48q66 0 107 -37t41 -108q0 -41 -15.5 -75t-40 -57.5t-56.5 -36t-64 -12.5q-66 0 -106 36t-40 108z" />
<glyph unicode="&#x3c;" d="M217 489l47 201l965 373l16 -211l-745 -272l610 -283l-109 -188z" />
<glyph unicode="=" d="M172 260l49 209h942l-51 -209h-940zM279 702l49 209h942l-51 -209h-940z" />
<glyph unicode="&#x3e;" d="M166 319l745 273l-610 282l109 189l784 -381l-47 -201l-965 -372z" />
<glyph unicode="?" horiz-adv-x="835" d="M236 117q0 86 53 134t121 48q66 0 106.5 -37t40.5 -108q0 -41 -15.5 -75t-40 -57.5t-56 -36t-64.5 -12.5q-66 0 -105.5 36t-39.5 108zM330 446q2 70 21.5 124.5t53 99.5t81 87t102.5 87q39 31 78 61.5t70.5 64.5t51 71.5t19.5 82.5q0 61 -41 91t-113 30 q-86 0 -159.5 -24.5t-118.5 -49.5l-31 195q66 37 160 61.5t192 24.5q170 0 264.5 -80t94.5 -215q0 -94 -37 -163.5t-93.5 -126t-121 -102.5t-120.5 -94t-95 -101.5t-41 -123.5h-217z" />
<glyph unicode="@" horiz-adv-x="1929" d="M221 354q0 236 80 438.5t222.5 351t337 232.5t421.5 84q154 0 283 -41t223 -124t147.5 -205.5t53.5 -288.5q0 -78 -12.5 -162t-39 -165t-70.5 -154.5t-105.5 -128t-144.5 -87t-190 -32.5q-123 0 -180 75q-59 -35 -125.5 -53t-140.5 -18q-158 0 -249 97t-91 271 q0 109 40 217.5t119 194.5t193.5 140.5t261.5 54.5q113 0 193 -19.5t129 -40.5l-147 -614q-4 -12 -6.5 -24.5t-2.5 -22.5q0 -63 74 -64q70 0 123 44t90 117t56.5 165t19.5 190q0 242 -135.5 371t-393.5 129q-164 0 -313 -66.5t-264 -187.5t-182.5 -289t-67.5 -372 q0 -141 43 -238.5t118 -158t177 -88t221 -27.5q96 0 180 13t131 23l-12 -176q-61 -16 -154.5 -28.5t-181.5 -12.5q-164 0 -298 46.5t-230 133.5t-148.5 213t-52.5 287zM868 465q0 -201 162 -201q84 0 166 41q0 25 4 48.5t10 47.5l109 451q-35 10 -102 10q-86 0 -151 -34.5 t-109 -91t-66.5 -128t-22.5 -143.5z" />
<glyph unicode="A" horiz-adv-x="1343" d="M14 0q125 217 239 409.5t221.5 366.5t213 333t211.5 310h240q66 -305 112.5 -666.5t89.5 -752.5h-260q-6 86 -13 171t-15 165h-576q-47 -80 -93 -163t-93 -173h-277zM588 541h444q-12 145 -29 297.5t-40 310.5q-78 -121 -173.5 -271.5t-201.5 -336.5z" />
<glyph unicode="B" horiz-adv-x="1298" d="M166 23l330 1374q92 20 194.5 28.5t206.5 8.5q86 0 175 -12.5t161 -48.5t118 -98.5t46 -160.5q0 -109 -60.5 -205t-203.5 -155q90 -37 139 -106.5t49 -162.5q0 -240 -187.5 -370.5t-541.5 -130.5q-104 0 -220 9t-206 30zM465 205q29 -4 76 -7t104 -3q68 0 141.5 10 t133 38.5t99.5 82t40 137.5q0 78 -59.5 128t-196.5 50h-234zM618 840h197q63 0 122.5 12t103.5 40t71 68.5t27 100.5q0 94 -71 129t-189 35q-47 0 -94.5 -3t-75.5 -8z" />
<glyph unicode="C" horiz-adv-x="1241" d="M248 547q0 172 57 334.5t166 289.5t264.5 204t351.5 77q129 0 224.5 -28.5t167.5 -69.5l-99 -207q-66 39 -140.5 60.5t-168.5 21.5q-129 0 -231.5 -57.5t-175 -150.5t-110.5 -212t-38 -242q0 -190 83 -281t243 -91q123 0 209 27.5t147 56.5l31 -218q-68 -35 -172.5 -63.5 t-253.5 -28.5q-131 0 -233.5 40t-174 116t-109.5 182.5t-38 239.5z" />
<glyph unicode="D" horiz-adv-x="1437" d="M166 23l330 1374q100 23 202.5 30t188.5 7q156 0 274.5 -40t198.5 -113t121 -176.5t41 -230.5q0 -190 -61.5 -354t-182.5 -282.5t-302 -186t-421 -67.5q-86 0 -192.5 9t-196.5 30zM469 211q23 -4 52.5 -5t92.5 -1q147 0 266 47t202 134t128 209t45 271q0 162 -92 256.5 t-301 94.5q-53 0 -85 -2.5t-68 -6.5z" />
<glyph unicode="E" horiz-adv-x="1171" d="M160 0l340 1419h880l-51 -215h-624l-84 -356h553l-52 -211h-551l-102 -422h672l-51 -215h-930z" />
<glyph unicode="F" horiz-adv-x="1105" d="M160 0l340 1419h874l-51 -215h-618l-89 -368h547l-51 -213h-545l-149 -623h-258z" />
<glyph unicode="G" horiz-adv-x="1341" d="M248 547q0 172 57 334.5t167 289.5t268.5 204t359.5 77q143 0 244.5 -35t162.5 -76l-100 -204q-53 35 -135 63.5t-187 28.5q-135 0 -240.5 -57.5t-179 -150.5t-111.5 -212t-38 -242q0 -374 328 -374q59 0 106 6t74 14l121 508h256l-164 -680q-53 -25 -167 -47.5 t-267 -22.5q-135 0 -239.5 40t-174.5 115t-105.5 181.5t-35.5 239.5z" />
<glyph unicode="H" horiz-adv-x="1425" d="M160 0l340 1419h258l-137 -569h581l137 569h258l-340 -1419h-258l152 631h-582l-151 -631h-258z" />
<glyph unicode="I" horiz-adv-x="585" d="M160 0l340 1419h258l-340 -1419h-258z" />
<glyph unicode="J" horiz-adv-x="1013" d="M43 96l119 195q41 -35 102.5 -66.5t153.5 -31.5q63 0 110 15t81 50t58.5 90t43.5 135l223 936h260l-233 -977q-25 -102 -61 -189t-97.5 -149.5t-154.5 -98.5t-234 -36q-125 0 -221.5 40t-149.5 87z" />
<glyph unicode="K" horiz-adv-x="1298" d="M160 0l340 1419h258l-137 -567q84 68 174 143.5t176 151.5t162.5 145.5t136.5 126.5h313q-84 -78 -179 -164t-194.5 -175t-201 -176t-199.5 -165q63 -66 131 -149.5t131.5 -180t120.5 -201t104 -208.5h-284q-41 82 -91.5 171t-106.5 174t-118.5 161t-124.5 133l-153 -639 h-258z" />
<glyph unicode="L" horiz-adv-x="1097" d="M160 0l340 1419h258l-289 -1202h629l-51 -217h-887z" />
<glyph unicode="M" horiz-adv-x="1765" d="M125 0q49 176 104.5 364.5t113.5 374t115.5 359.5t110.5 321h242q18 -72 42 -185.5t47.5 -242.5t47 -260t41.5 -237q70 109 155 237.5t170 255.5t163.5 240.5t136.5 191.5h244q-16 -156 -39 -332t-52.5 -360t-62.5 -369.5t-66 -357.5h-256q53 248 102.5 509t90.5 517 q-35 -49 -80 -118.5t-96 -146.5t-104.5 -159t-103.5 -158.5t-93 -142t-74 -113.5h-199q-10 68 -28.5 178.5t-41 232.5t-44 237.5t-37.5 191.5q-84 -256 -157 -519t-138 -509h-254z" />
<glyph unicode="N" horiz-adv-x="1472" d="M160 0l340 1419h207q57 -92 119.5 -210.5t122 -249t114.5 -261.5t96 -247l227 968h256l-339 -1419h-207q-39 119 -89.5 256t-106.5 274.5t-116.5 267.5t-121.5 238l-248 -1036h-254z" />
<glyph unicode="O" horiz-adv-x="1519" d="M248 539q0 164 53 327.5t156.5 293.5t255 211t348.5 81q119 0 219 -38t172 -109.5t112 -178t40 -243.5q0 -164 -53.5 -328t-156 -294t-254.5 -211t-349 -81q-119 0 -218.5 38t-172 109.5t-112.5 178t-40 244.5zM516 561q0 -168 71.5 -268t229.5 -100q117 0 213 58 t164 152.5t105.5 214t37.5 242.5q0 168 -72.5 268.5t-230.5 100.5q-117 0 -212 -58.5t-162.5 -152.5t-105.5 -214t-38 -243z" />
<glyph unicode="P" horiz-adv-x="1239" d="M160 0l334 1399q98 20 196 27.5t184 7.5q256 0 392.5 -100.5t136.5 -286.5q0 -147 -56.5 -251t-155.5 -169.5t-237.5 -95t-304.5 -29.5h-110l-121 -502h-258zM590 719h104q86 0 166 14.5t141.5 48t98.5 92t37 148.5q0 104 -77 149.5t-210 45.5q-39 0 -74 -2.5t-69 -6.5z " />
<glyph unicode="Q" horiz-adv-x="1519" d="M248 539q0 164 53 327.5t156.5 293.5t255 211t348.5 81q119 0 219 -38t172 -109.5t112 -178t40 -243.5q0 -150 -44 -298.5t-129 -273.5t-212 -214t-293 -115q-2 -6 -2 -17q0 -43 30.5 -70.5t83 -45t120 -26.5t140.5 -16l-81 -190q-119 8 -217.5 30.5t-169 62.5 t-109.5 101.5t-39 147.5v6t2 15q-98 14 -179 58t-137.5 114.5t-88 168t-31.5 218.5zM516 561q0 -168 71.5 -268t229.5 -100q117 0 213 58t164 152.5t105.5 214t37.5 242.5q0 168 -72.5 268.5t-230.5 100.5q-117 0 -212 -58.5t-162.5 -152.5t-105.5 -214t-38 -243z" />
<glyph unicode="R" horiz-adv-x="1277" d="M160 0l334 1399q98 20 199.5 27.5t185.5 7.5q254 0 386 -105.5t132 -279.5q0 -78 -21.5 -149t-67.5 -131t-119 -107.5t-175 -77.5q27 -47 57.5 -114t63 -143.5t63.5 -160.5t55 -166h-272q-25 74 -51.5 149.5t-55 146.5t-58.5 133.5t-58 111.5q-18 -2 -45 -2h-47h-119 l-129 -539h-258zM596 745h115q92 0 169.5 15.5t134 48.5t88.5 85t32 126q0 113 -82 155t-219 42q-29 0 -62 -2.5t-65 -6.5z" />
<glyph unicode="S" horiz-adv-x="1079" d="M74 88l114 203q20 -14 54 -32.5t77 -34t94.5 -26t108.5 -10.5q55 0 109.5 10.5t98.5 35t70.5 66.5t26.5 106q0 47 -20.5 80.5t-55 60t-78.5 50.5t-93 50q-53 27 -101.5 60.5t-85.5 76.5t-58.5 100.5t-21.5 135.5q0 94 36 173t106.5 136t174 90t240.5 33q63 0 122 -10 t110 -25.5t90 -34t61 -34.5l-112 -197q-43 35 -120 59.5t-169 24.5q-55 0 -105.5 -11.5t-88 -35t-59 -61t-21.5 -89.5q0 -47 15 -77.5t43 -54t64.5 -43t79.5 -44.5q63 -35 119.5 -69.5t99.5 -79.5t69 -103.5t26 -140.5q0 -115 -43 -201t-121 -142t-184.5 -85t-233.5 -29 q-84 0 -154.5 12.5t-126 31t-95.5 39t-62 36.5z" />
<glyph unicode="T" horiz-adv-x="1191" d="M338 1202l51 217h1104l-53 -217h-422l-289 -1202h-258l289 1202h-422z" />
<glyph unicode="U" horiz-adv-x="1386" d="M264 387q0 47 7.5 104.5t21.5 110.5l194 817h261l-203 -846q-10 -39 -16.5 -80.5t-6.5 -80.5q0 -45 11.5 -85t38 -69t69.5 -46t107 -17q150 0 229.5 88t120.5 264l209 872h260l-215 -897q-31 -131 -78 -233.5t-122 -173t-182.5 -108.5t-258.5 -38q-119 0 -204 32 t-138.5 87t-79 132t-25.5 167z" />
<glyph unicode="V" horiz-adv-x="1308" d="M334 1419h272q23 -301 50.5 -587.5t62.5 -526.5q76 115 156.5 250t162.5 279.5t160 293t148 291.5h280q-193 -362 -400.5 -719.5t-449.5 -699.5h-246q-63 330 -112 684t-84 735z" />
<glyph unicode="W" horiz-adv-x="1900" d="M344 715q0 336 25 704h258q-12 -197 -20.5 -390t-8.5 -392v-148.5t4 -150.5q63 104 131 220t133.5 234.5t128 237.5t117.5 227h225q6 -111 15.5 -231.5t21.5 -240t24.5 -234.5t24.5 -213q57 109 119 239t123 269t120.5 285.5t115.5 287.5h274q-162 -395 -327 -747.5 t-349 -671.5h-250q-16 111 -31.5 217.5t-28.5 216t-24.5 223t-21.5 236.5q-127 -236 -251 -456t-263 -437h-254q-16 178 -23.5 354t-7.5 361z" />
<glyph unicode="X" horiz-adv-x="1288" d="M20 0l650 764l-289 655h285l192 -481q102 119 199.5 242t177.5 239h289q-59 -84 -127 -171t-139.5 -174t-144.5 -172t-142 -163q82 -188 153.5 -365t135.5 -374h-275q-25 80 -49.5 150.5t-50 136t-52 129t-57.5 131.5l-457 -547h-299z" />
<glyph unicode="Y" horiz-adv-x="1218" d="M330 1419h274q35 -158 80 -315t98 -311q127 154 247 308t224 318h291q-80 -113 -159.5 -219t-162.5 -211.5t-171 -210t-183 -213.5l-135 -565h-258l135 565q-84 215 -152.5 427t-127.5 427z" />
<glyph unicode="Z" horiz-adv-x="1181" d="M66 0l38 162q57 76 130 165t154 183t168 189.5t173 185.5t167 171t151 146h-660l51 217h1002l-45 -190q-84 -74 -206 -192.5t-255 -257t-264 -285t-236 -277.5h735l-51 -217h-1052z" />
<glyph unicode="[" horiz-adv-x="696" d="M92 -379l475 1983h504l-47 -197h-268l-383 -1589h270l-49 -197h-502z" />
<glyph unicode="\" horiz-adv-x="804" d="M324 1604h231l217 -1983h-235z" />
<glyph unicode="]" horiz-adv-x="696" d="M-90 -379l47 197h268l383 1589h-270l49 197h502l-475 -1983h-504z" />
<glyph unicode="^" d="M242 733l561 686h203l231 -710l-211 -91l-172 541l-434 -534z" />
<glyph unicode="_" horiz-adv-x="1024" d="M-88 -379l47 207h1024l-49 -207h-1022z" />
<glyph unicode="`" horiz-adv-x="641" d="M307 1454l152 162l303 -322l-115 -114z" />
<glyph unicode="a" horiz-adv-x="1142" d="M195 387q0 150 53 279t148.5 223t226.5 148.5t286 54.5q80 0 161 -13.5t163 -50.5l-148 -612q-6 -23 -12 -74t-6 -96q0 -59 10 -115.5t33 -114.5l-221 -32q-25 49 -39 104q-51 -45 -126 -81t-169 -36q-96 0 -164.5 32t-111.5 88t-63.5 132t-20.5 164zM440 408 q0 -47 8.5 -88t28 -72t52 -49.5t83.5 -18.5q66 0 116 28t93 75q2 53 9.5 100t19.5 94l100 402q-39 8 -88 8q-92 0 -170 -40t-134 -106.5t-87 -152.5t-31 -180z" />
<glyph unicode="b" horiz-adv-x="1165" d="M160 45l358 1503l258 41l-131 -549q45 23 96.5 39.5t108.5 16.5q94 0 162.5 -32t113.5 -87t68 -130t23 -161q0 -141 -49.5 -270t-140.5 -227.5t-222 -157t-295 -58.5q-25 0 -66 2t-90 10.5t-100 23t-94 36.5zM442 193q18 -4 42 -7.5t59 -3.5q94 0 173 40t135 107.5 t87 154.5t31 182q0 47 -8.5 87t-29 69.5t-55 47t-88.5 17.5q-45 0 -98 -16.5t-98 -51.5z" />
<glyph unicode="c" horiz-adv-x="940" d="M199 406q0 139 44 265t126 221t200.5 150.5t270.5 55.5q80 0 146.5 -13.5t125.5 -40.5l-88 -198q-37 16 -82 28.5t-111 12.5q-88 0 -158.5 -36t-119.5 -97.5t-75.5 -145.5t-26.5 -178q0 -53 11 -97t36.5 -78t70.5 -52.5t113 -18.5q70 0 134.5 18.5t98.5 37.5l21 -203 q-47 -23 -125 -44.5t-176 -21.5q-113 0 -195 34t-136 92.5t-79.5 137.5t-25.5 171z" />
<glyph unicode="d" horiz-adv-x="1171" d="M195 385q0 137 48 265t137 225.5t218 156t293 58.5q20 0 49 -2.5t64 -10.5l112 471l258 41l-280 -1161q-10 -47 -15.5 -95t-5.5 -91q0 -55 10.5 -110.5t32.5 -115.5l-221 -32q-23 43 -39 104q-51 -45 -125 -81t-172 -36q-96 0 -165.5 32t-113.5 87t-64.5 131t-20.5 164z M440 408q0 -47 9.5 -88t30 -72t54 -49.5t82.5 -18.5q66 0 116 26.5t95 74.5q2 43 9.5 90t17.5 92l100 414q-39 8 -94 8q-94 0 -172 -40t-133 -106.5t-85 -152.5t-30 -178z" />
<glyph unicode="e" horiz-adv-x="1071" d="M199 387q0 137 44 265t127 227.5t199.5 159t261.5 59.5q156 0 242 -80t86 -205q0 -129 -63.5 -209t-165 -125t-229.5 -61.5t-259 -20.5q-2 -4 -2 -8v-20q0 -186 236 -187q139 0 278 58l21 -201q-49 -23 -139.5 -45.5t-204.5 -22.5q-115 0 -197 33t-134 90.5t-76.5 132 t-24.5 160.5zM459 575q246 8 357.5 59.5t111.5 153.5q0 41 -29.5 74t-103.5 33q-61 0 -116.5 -24.5t-98.5 -67.5t-75 -101.5t-46 -126.5z" />
<glyph unicode="f" horiz-adv-x="794" d="M-41 -354q39 49 73 99t61.5 106.5t51 123t41.5 148.5l244 1024q25 104 67 187t104.5 139.5t149.5 86t201 29.5q82 0 148.5 -17.5t101.5 -33.5l-90 -195q-27 12 -71 23.5t-103 11.5q-61 0 -104 -17.5t-74 -48t-50.5 -74.5t-31.5 -95l-19 -72h359l-49 -205h-359l-153 -641 q-27 -111 -50.5 -196.5t-54 -157.5t-72.5 -134.5t-106 -123.5z" />
<glyph unicode="g" horiz-adv-x="1165" d="M68 -303l88 201q55 -33 128 -53.5t167 -20.5q152 0 228.5 69.5t102.5 190.5l6 29q-41 -20 -98 -38t-121 -18q-180 0 -267 97.5t-87 259.5q0 147 51 272t144.5 216t224.5 142.5t291 51.5q78 0 167 -17.5t160 -56.5l-217 -909q-29 -121 -72.5 -214.5t-115.5 -155.5 t-177.5 -95t-254.5 -33q-109 0 -196 23.5t-152 58.5zM461 451q0 -90 39 -141.5t131 -51.5q55 0 113.5 20.5t97.5 51.5l131 547q-47 10 -92 10q-94 0 -171 -35t-132.5 -94.5t-86 -138t-30.5 -168.5z" />
<glyph unicode="h" horiz-adv-x="1153" d="M150 0l370 1548l258 41l-127 -526q41 10 89 19.5t96 9.5q90 0 154.5 -26t106.5 -71t61.5 -108.5t19.5 -136.5q0 -88 -25 -187l-135 -563h-248l129 537q10 47 20.5 96t10.5 92q0 68 -36 114t-136 46q-41 0 -82 -7.5t-74 -17.5l-205 -860h-247z" />
<glyph unicode="i" horiz-adv-x="555" d="M150 0l256 1071h247l-258 -1071h-245zM442 1364q0 76 52.5 125t119.5 49q55 0 97.5 -34t42.5 -99q0 -76 -53 -124t-121 -48q-55 0 -96.5 32.5t-41.5 98.5z" />
<glyph unicode="j" horiz-adv-x="565" d="M-209 -344l62 199q25 -10 56.5 -18.5t67.5 -8.5q84 0 127 58.5t66 156.5l246 1028h248l-248 -1032q-27 -111 -63 -190.5t-88 -131t-122.5 -76t-169.5 -24.5q-47 0 -97 11.5t-85 27.5zM455 1364q0 76 52 125t120 49q55 0 97 -34t42 -99q0 -76 -53 -124t-121 -48 q-55 0 -96 32.5t-41 98.5z" />
<glyph unicode="k" horiz-adv-x="1144" d="M150 0l370 1548l258 41l-223 -925q63 49 129 103t124 108.5t109.5 104.5t88.5 91h284q-49 -57 -112.5 -120.5t-133 -127t-143.5 -125t-143 -116.5q51 -53 102 -125t98.5 -150t86 -157.5t63.5 -149.5h-274q-29 68 -64 137.5t-76 136t-86 125t-90 103.5l-123 -502h-245z " />
<glyph unicode="l" horiz-adv-x="610" d="M215 274q0 66 15.5 138.5t33.5 144.5l238 991l258 41q-63 -268 -127 -533t-129 -534q-10 -43 -22.5 -85t-12.5 -83q0 -35 1 -64.5t16.5 -51t46 -36t79.5 -18.5l-53 -204q-96 2 -162.5 24.5t-106.5 61t-57.5 92t-17.5 116.5z" />
<glyph unicode="m" horiz-adv-x="1724" d="M150 0l245 1026q82 25 184.5 46.5t225.5 21.5q92 0 154.5 -27t105.5 -76q59 35 144 69t194 34q94 0 159.5 -27t106.5 -74t59.5 -111.5t18.5 -140.5q0 -84 -23 -178l-135 -563h-248l127 537q10 39 19.5 85t9.5 93q0 33 -7 64.5t-25.5 54t-50.5 37t-81 14.5 q-53 0 -104 -20.5t-86 -43.5q8 -39 8 -84q0 -41 -6 -86t-17 -88l-135 -563h-248l127 537q10 37 19.5 85t9.5 95q0 68 -31.5 118t-119.5 50q-41 0 -78 -5.5t-68 -13.5l-207 -866h-247z" />
<glyph unicode="n" horiz-adv-x="1153" d="M150 0l245 1026q82 25 187.5 46.5t230.5 21.5q188 0 276.5 -92.5t88.5 -247.5q0 -43 -7.5 -91.5t-17.5 -99.5l-135 -563h-248l127 537q10 45 20.5 92t12.5 90v8q0 67 -34 111q-36 47 -140 47q-88 0 -152 -19l-207 -866h-247z" />
<glyph unicode="o" horiz-adv-x="1153" d="M199 381q0 119 36.5 246t112.5 231.5t190.5 172t270.5 67.5q190 0 290.5 -112t100.5 -296q0 -121 -36 -248t-110.5 -231.5t-190 -172t-273.5 -67.5q-190 0 -290.5 113t-100.5 297zM444 403q0 -102 39 -161.5t138 -59.5q80 0 142 46t104 117t64.5 157t22.5 164 q0 102 -39 162.5t-137 60.5q-80 0 -142.5 -46t-104.5 -118t-64.5 -157t-22.5 -165z" />
<glyph unicode="p" horiz-adv-x="1155" d="M59 -379l338 1407q84 31 187.5 49.5t208.5 18.5q106 0 185 -32t129 -87t74.5 -130t24.5 -161q0 -145 -46 -273t-135 -224.5t-217 -152.5t-294 -56q-29 0 -58.5 3t-58.5 7l-90 -369h-248zM444 193q20 -4 43 -5.5t45 -1.5q98 0 177 38t134.5 102.5t85 151.5t29.5 186 q0 45 -10 84.5t-33.5 70.5t-63.5 49.5t-101 18.5q-41 0 -78 -5t-66 -14z" />
<glyph unicode="q" horiz-adv-x="1157" d="M195 387q0 147 50 276.5t143 224.5t227.5 150.5t299.5 55.5q76 0 162 -16.5t168 -53.5l-336 -1403h-247l96 404q-47 -23 -100.5 -36.5t-108.5 -13.5q-170 0 -262 112t-92 300zM440 408q0 -102 40 -163t136 -61q49 0 98.5 14.5t96.5 49.5l152 629q-18 4 -43 7t-48 3 q-100 0 -180 -39t-136 -104.5t-86 -152.5t-30 -183z" />
<glyph unicode="r" horiz-adv-x="817" d="M150 0l245 1022q86 29 186.5 50.5t217.5 21.5q25 0 55.5 -3.5t60 -7.5t55 -10t44.5 -14l-72 -207q-41 14 -88 22.5t-111 8.5q-35 0 -73.5 -5.5t-65.5 -13.5l-207 -864h-247z" />
<glyph unicode="s" horiz-adv-x="897" d="M92 57l88 195q14 -8 42 -21.5t64 -25.5t80 -21.5t93 -9.5q98 0 155.5 32t57.5 97q0 53 -37 85t-129 75q-49 23 -91 47.5t-73 57t-49.5 76.5t-18.5 106q0 160 117 254t338 94q102 0 183 -21.5t122 -44.5l-88 -192q-35 20 -100.5 39.5t-136.5 19.5q-35 0 -69 -6 t-60.5 -20.5t-44 -40t-17.5 -62.5q0 -47 38 -76.5t110 -62.5q68 -31 114.5 -59.5t77.5 -64.5t45.5 -80t14.5 -105q0 -76 -31 -140.5t-89.5 -110.5t-144.5 -73t-196 -27q-76 0 -136.5 10.5t-105.5 23.5t-75.5 27.5t-47.5 24.5z" />
<glyph unicode="t" horiz-adv-x="821" d="M209 260q0 49 7 101.5t22 111.5l208 881l259 41l-78 -324h350l-49 -205h-350l-103 -424q-10 -45 -14 -74.5t-4 -54.5t5 -48t20.5 -40.5t45 -29t80.5 -11.5q55 0 106.5 14.5t90.5 30.5l18 -198q-47 -20 -118.5 -39t-167.5 -19q-176 0 -252 77t-76 210z" />
<glyph unicode="u" horiz-adv-x="1161" d="M219 313q0 90 25 195l135 563h248l-133 -557q-27 -106 -27 -174q0 -33 6 -61.5t21.5 -50t43 -34t68.5 -12.5q68 0 121 28t98 75q6 88 29 180l143 606h248l-151 -637q-10 -47 -15.5 -94t-5.5 -94q0 -59 9.5 -116.5t31.5 -113.5l-221 -32q-12 23 -21.5 50t-17.5 56 q-51 -41 -127 -79t-176 -38q-90 0 -153.5 27t-103.5 73t-57.5 108.5t-17.5 131.5z" />
<glyph unicode="v" horiz-adv-x="1046" d="M254 1071h254q2 -90 7 -194.5t13.5 -210t19.5 -203.5t25 -178q53 70 115 167t119 204.5t104 216t76 198.5h258q-49 -141 -122.5 -290.5t-159.5 -291t-177.5 -268.5t-173.5 -221h-213q-51 209 -92 484.5t-53 586.5z" />
<glyph unicode="w" horiz-adv-x="1660" d="M291 1071h246q0 -86 2 -185.5t7 -202.5t12 -203.5t20 -188.5q57 88 113.5 187.5t107.5 200.5t95 201.5t77 190.5h213q0 -84 2 -178t7 -194.5t12 -204t20 -203.5q53 78 108.5 178t106.5 205.5t93 209t69 187.5h258q-43 -123 -105.5 -261t-140.5 -279.5t-167 -277.5 t-185 -253h-205q-23 172 -38.5 339t-19.5 339q-39 -88 -88 -181.5t-102 -182.5t-106.5 -170t-98.5 -144h-205q-18 94 -35.5 215t-32 258t-25.5 288.5t-15 309.5z" />
<glyph unicode="x" horiz-adv-x="1021" d="M18 0l490 549l-264 522h260l170 -360q78 94 145.5 184t122.5 176h256q-86 -127 -190.5 -259t-227.5 -267q74 -141 135.5 -275.5t108.5 -269.5h-250q-33 96 -75 192.5t-85 182.5l-325 -375h-271z" />
<glyph unicode="y" horiz-adv-x="1011" d="M-115 -336l80 199q29 -16 65 -26.5t79 -10.5q96 0 168.5 50t132.5 136q-66 227 -111 493.5t-59 565.5h249q2 -86 9.5 -190.5t18.5 -214t27.5 -216t37.5 -196.5q61 98 114.5 206.5t98.5 216t78.5 209t58.5 185.5h262q-41 -117 -90 -243t-109.5 -252.5t-130 -252.5 t-149.5 -245q-59 -90 -123 -173t-138.5 -146.5t-164.5 -102.5t-205 -39q-63 0 -113.5 14.5t-85.5 32.5z" />
<glyph unicode="z" horiz-adv-x="958" d="M61 0l37 150q61 82 146.5 180t177.5 195.5t183 186.5t163 154h-475l49 205h799l-41 -170q-35 -31 -87 -79t-113.5 -107.5t-129.5 -126t-133.5 -134t-124.5 -132t-104 -117.5h534l-49 -205h-832z" />
<glyph unicode="{" horiz-adv-x="729" d="M203 514l47 195q61 0 104 19.5t73 51t47.5 72.5t27.5 84l67 276q25 102 62 176t97 122.5t148.5 71t215.5 22.5h14l-49 -197q-61 0 -104.5 -8t-72 -28.5t-47 -56.5t-32.5 -93l-78 -328q-27 -109 -77 -175.5t-148 -105.5q55 -35 76.5 -81t21.5 -103q0 -31 -5 -63.5 t-13 -67.5l-70 -295q-12 -53 -12 -86q0 -61 41 -79.5t137 -18.5l-49 -197q-88 0 -156 11.5t-114 39t-70.5 74.5t-24.5 119q0 39 10 86l74 299q16 76 16 127q0 72 -39.5 102.5t-117.5 34.5z" />
<glyph unicode="|" horiz-adv-x="608" d="M92 -379l477 1983h232l-477 -1983h-232z" />
<glyph unicode="}" horiz-adv-x="729" d="M-92 -379l49 197q61 0 103 8t72 28.5t48.5 56.5t32.5 93l78 328q27 109 77 175t148 105q-55 35 -76.5 81t-21.5 104q0 31 5 63.5t13 67.5l70 295q12 51 12 86q0 61 -41 79.5t-137 18.5l49 197q88 0 155.5 -11.5t114 -39t71 -74.5t24.5 -119q0 -39 -11 -86l-73 -299 q-16 -76 -17 -127q0 -72 40 -102.5t118 -34.5l-47 -195q-61 0 -104.5 -19.5t-73 -51t-47 -72.5t-27.5 -84l-68 -277q-25 -102 -61.5 -177t-97 -122t-148.5 -69.5t-215 -22.5h-14z" />
<glyph unicode="~" d="M195 471q18 57 48.5 115.5t76 104.5t103.5 75t132 29q70 0 119 -26t90 -56.5t79 -56t87 -25.5q53 0 88 40t72 115l161 -49q-23 -61 -53.5 -118.5t-73.5 -103.5t-101 -73.5t-132 -27.5q-70 0 -119 25.5t-90 56t-79 56.5t-87 26q-53 0 -88 -40t-72 -116z" />
<glyph unicode="&#xa1;" horiz-adv-x="544" d="M53 -369l90 377q16 68 38 144.5t46.5 156.5t51 156t51.5 139h174q-16 -143 -37 -285.5t-61 -310.5l-91 -377h-262zM319 893q0 86 54.5 134t122.5 48q66 0 105.5 -37t39.5 -108q0 -41 -15.5 -75t-40 -57.5t-56 -35.5t-64.5 -12q-66 0 -106 35.5t-40 107.5z" />
<glyph unicode="&#xa2;" d="M315 469q0 121 36 229.5t100.5 194.5t155.5 146.5t204 84.5l70 295h229l-70 -286q47 -6 91.5 -19.5t87.5 -36.5l-86 -194q-82 47 -193 47q-84 0 -152.5 -35t-117.5 -96.5t-76 -144.5t-27 -177q0 -102 51.5 -155.5t157.5 -53.5q70 0 138.5 19.5t109.5 40.5l18 -201 q-47 -23 -114.5 -42t-143.5 -24l-67 -284h-230l74 305q-117 37 -181.5 132t-64.5 255z" />
<glyph unicode="&#xa3;" d="M240 0q66 156 119 303.5t81 280.5h-184l47 200h184l48 185q35 137 87 229t121.5 147.5t153.5 78t180 22.5q92 0 168 -21.5t125 -50.5l-104 -188q-47 23 -97.5 36t-105.5 13q-51 0 -96 -13.5t-82 -47t-67 -91t-50 -145.5l-37 -154h375l-49 -200h-373l-16 -60 q-20 -78 -48 -159.5t-57 -155.5h576l-52 -209h-847z" />
<glyph unicode="&#xa4;" d="M229 362l148 146q-49 86 -49 203q0 115 49 200l-148 146l156 153l152 -149q85 47 200 47t203 -47l152 149l155 -153l-149 -146q51 -90 51 -200q0 -115 -51 -201l149 -148l-155 -153l-154 149q-90 -47 -201 -47q-115 0 -202 47l-152 -147zM532 711q0 -104 59.5 -160.5 t145.5 -56.5t145.5 56t59.5 161q0 102 -59.5 158.5t-145.5 56.5t-145.5 -56.5t-59.5 -158.5z" />
<glyph unicode="&#xa5;" d="M176 221l41 170h315l41 176h-313l41 170h260q-68 174 -126 350.5t-95 331.5h254q31 -145 73 -290.5t97 -292.5q115 135 223.5 282.5t206.5 300.5h272q-119 -174 -252 -346t-274 -336h242l-41 -170h-316l-41 -176h316l-41 -170h-316l-53 -221h-252l54 221h-316z" />
<glyph unicode="&#xa6;" horiz-adv-x="608" d="M92 -379l193 799h231l-192 -799h-232zM377 805l192 799h232l-193 -799h-231z" />
<glyph unicode="&#xa7;" horiz-adv-x="1007" d="M59 -143l88 182q51 -29 120 -48.5t149 -19.5q125 0 185.5 43t60.5 105q0 82 -82 121q-82 41 -148.5 71.5t-114 66.5t-73 84t-25.5 124q0 104 69.5 198.5t180.5 153.5q-29 33 -45 77t-16 91q0 74 31.5 137.5t91 109.5t145.5 72.5t194 26.5q96 0 177 -20.5t143 -53.5 l-88 -182q-49 25 -108.5 42t-131.5 17q-37 0 -74.5 -7t-68.5 -23.5t-50.5 -43t-19.5 -67.5q0 -29 14.5 -49.5t37 -36.5t50 -28.5t54.5 -24.5q63 -27 114.5 -54.5t86 -63.5t53 -81t18.5 -106q0 -94 -55 -178t-141 -144q-12 -10 -25.5 -17t-28.5 -16q31 -35 51.5 -78.5 t20.5 -103.5q0 -72 -30.5 -136.5t-91 -113.5t-151.5 -78.5t-214 -29.5q-102 0 -194.5 21.5t-158.5 60.5zM442 621q0 -41 18.5 -67t51.5 -47.5t79 -42t99 -50.5q66 37 114 93t48 126q0 41 -18.5 68.5t-52 49t-79 42t-98.5 49.5q-61 -35 -111.5 -93t-50.5 -128z" />
<glyph unicode="&#xa8;" horiz-adv-x="937" d="M379 1366q0 35 13.5 64.5t33.5 50t47 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86zM801 1366q0 35 13 64.5t33.5 50t47.5 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5 q-53 0 -88 33t-35 86z" />
<glyph unicode="&#xa9;" horiz-adv-x="1667" d="M285 711q0 174 57 312t154.5 233.5t225.5 145.5t269 50t269.5 -50t225.5 -145.5t154.5 -233.5t57.5 -312t-57.5 -312.5t-154.5 -233.5t-225 -145.5t-270 -50.5q-141 0 -269 50.5t-225.5 145.5t-154.5 233.5t-57 312.5zM463 711q0 -129 39 -235.5t109.5 -182.5t166.5 -117 t213 -41t213 41t166 117t109 182.5t39 235.5t-39 234.5t-109 181t-166 116.5t-213 41t-213 -41t-166.5 -116.5t-109.5 -181t-39 -234.5zM635 713q0 94 29.5 169.5t81 127t121 79t149.5 27.5q104 0 167.5 -27.5t82.5 -39.5l-56 -152q-27 16 -71.5 31.5t-110.5 15.5 q-86 0 -141.5 -58.5t-55.5 -168.5q0 -49 11.5 -92t36 -76t62.5 -51.5t91 -18.5q72 0 119 15.5t76 29.5l47 -155q-23 -14 -91.5 -38t-162.5 -24q-180 0 -282.5 105.5t-102.5 300.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="880" d="M317 950q0 113 41 205t113 156.5t168 100.5t209 36q55 0 118.5 -10.5t110.5 -24.5q-16 -53 -36.5 -124.5t-40 -148.5t-33 -151t-13.5 -131q0 -43 8.5 -87t30.5 -91l-155 -23q-8 10 -16.5 29t-12.5 35q-47 -31 -103.5 -51.5t-117.5 -20.5q-70 0 -120 23.5t-84 64.5 t-50.5 95.5t-16.5 117.5zM500 969q0 -59 28.5 -106.5t104.5 -47.5t139 47q-2 8 -2 17.5v17.5q0 29 8 69t19 78l63 236q-8 4 -22.5 5t-26.5 1q-68 0 -124 -24.5t-98 -67.5t-65.5 -100.5t-23.5 -124.5z" />
<glyph unicode="&#xab;" horiz-adv-x="1126" d="M190 594l433 459l155 -92l-321 -398l151 -377l-166 -77zM679 594l433 459l155 -92l-321 -398l151 -377l-166 -77z" />
<glyph unicode="&#xac;" d="M238 604l51 209h940l-172 -717h-226l123 508h-716z" />
<glyph unicode="&#xad;" horiz-adv-x="653" d="M170 471l53 227h545l-53 -227h-545z" />
<glyph unicode="&#xae;" horiz-adv-x="1667" d="M285 711q0 174 58 312t155.5 233.5t225.5 145.5t267 50q141 0 269.5 -50t225.5 -145.5t154.5 -233.5t57.5 -312q0 -176 -57.5 -313.5t-154.5 -232.5t-225 -145.5t-270 -50.5q-139 0 -267 50.5t-225.5 145.5t-155.5 232.5t-58 313.5zM465 711q0 -129 39 -235.5 t108.5 -182.5t165.5 -117t213 -41t213 41t167 117t110 182.5t39 235.5t-39 234.5t-110 181t-167 116.5t-213 41t-213 -41t-165.5 -116.5t-108.5 -181t-39 -234.5zM713 332v747q63 16 124.5 22.5t108.5 6.5q170 0 257 -63.5t87 -192.5q0 -70 -33.5 -122t-97.5 -81 q41 -59 88 -137t92 -180h-184q-41 86 -82 156.5t-78 117.5h-110v-274h-172zM885 745h57q80 0 127 21.5t47 87.5q0 57 -44 80.5t-111 23.5h-36t-40 -4v-209z" />
<glyph unicode="&#xaf;" horiz-adv-x="747" d="M344 1292l43 178h617l-43 -178h-617z" />
<glyph unicode="&#xb0;" horiz-adv-x="735" d="M322 1292q0 70 24.5 126.5t67.5 95t98.5 59t116.5 20.5t117.5 -20.5t99.5 -59t67.5 -95t24.5 -126.5t-24.5 -126t-67.5 -95t-99.5 -59.5t-117.5 -20.5t-116.5 20.5t-98.5 59.5t-67.5 95.5t-24.5 125.5zM498 1292q0 -63 39 -99t92 -36t92 36t39 99t-39 100.5t-92 37.5 t-92 -37t-39 -101z" />
<glyph unicode="&#xb1;" d="M102 0l52 209h940l-52 -209h-940zM266 686l51 209h357l92 385h227l-92 -385h356l-51 -209h-356l-90 -381h-225l90 381h-359z" />
<glyph unicode="&#xb2;" horiz-adv-x="743" d="M238 631q4 70 22.5 124t52 100t83.5 86t120 81q55 33 93 57.5t60.5 47t32 44t9.5 50.5q0 35 -24.5 53t-63.5 18q-61 0 -115.5 -27.5t-95.5 -62.5l-62 131q53 51 131 85t170 34q111 0 176.5 -52t65.5 -155q0 -61 -23.5 -108t-64.5 -88t-96.5 -79t-120.5 -81 q-35 -23 -70 -47.5t-47 -53.5h365l-35 -157h-563z" />
<glyph unicode="&#xb3;" horiz-adv-x="743" d="M231 676l70 143q37 -23 92.5 -37t110.5 -14q37 0 71.5 6t61.5 20.5t43 38t16 58.5q0 90 -161 90h-74l35 137h71q31 0 61.5 7.5t55.5 21.5t40 34.5t15 45.5q0 72 -104 71q-53 0 -106.5 -16t-90.5 -35l-35 135q43 25 122 47.5t153 22.5q111 0 172 -45t61 -137 q0 -72 -38.5 -125t-108.5 -84q47 -23 76.5 -65t29.5 -103q0 -72 -29.5 -124t-80.5 -87t-120 -51.5t-144 -16.5q-82 0 -149.5 16.5t-115.5 45.5z" />
<glyph unicode="&#xb4;" horiz-adv-x="681" d="M289 1317l354 305l133 -168l-387 -260z" />
<glyph unicode="&#xb5;" horiz-adv-x="1206" d="M59 -360l342 1431h248l-129 -541q-10 -41 -21.5 -92t-11.5 -100q0 -31 7.5 -59.5t25 -50t47 -34t76.5 -12.5q53 0 106.5 19.5t110.5 70.5q4 106 29 211l141 588h248l-150 -622q-10 -45 -17 -82t-7 -68q0 -51 25.5 -81t95.5 -36l-72 -202q-106 2 -165.5 30.5t-88.5 75.5 q-72 -53 -153.5 -81t-167.5 -28q-53 0 -103.5 18.5t-83.5 57.5q-4 -49 -14 -104t-27 -125l-45 -184h-246z" />
<glyph unicode="&#xb6;" horiz-adv-x="1380" d="M307 881q0 143 65.5 247.5t177.5 172t260 100.5t316 33q92 0 189.5 -8.5t189.5 -26.5l-426 -1778h-225l387 1608q-14 4 -47 7t-68 3h-36.5t-38.5 -2l-389 -1616h-226l219 911q-176 16 -262 106.5t-86 242.5z" />
<glyph unicode="&#xb7;" horiz-adv-x="542" d="M250 584q0 86 53 134t121 48q66 0 107 -37t41 -108q0 -41 -15.5 -75t-40 -57.5t-56.5 -36t-64 -12.5q-66 0 -106 36t-40 108z" />
<glyph unicode="&#xb8;" horiz-adv-x="722" d="M61 -381l62 139q23 -10 52.5 -17t64.5 -7q37 0 58 12t21 37t-15 38t-50 29l-27 13q12 20 29.5 47t36 52.5t35 47t22.5 31.5h162q-16 -23 -45 -61.5t-45 -63.5q86 -43 86 -131q0 -53 -20.5 -91t-56.5 -63.5t-83 -38t-100 -12.5q-113 0 -187 39z" />
<glyph unicode="&#xb9;" horiz-adv-x="743" d="M365 1268q96 33 183 75.5t152 92.5h142l-193 -805h-190l137 573q-47 -25 -102.5 -46.5t-108.5 -37.5z" />
<glyph unicode="&#xba;" horiz-adv-x="929" d="M328 926q0 92 27.5 186t84 169t143.5 123t208 48q141 0 213.5 -73.5t72.5 -202.5q0 -92 -27.5 -186.5t-84 -169.5t-144.5 -123t-207 -48q-141 0 -213.5 74t-72.5 203zM512 940q0 -72 34 -103.5t93 -31.5q55 0 101 30.5t80 81t53.5 115t19.5 129.5q0 72 -34 103.5 t-93 31.5q-55 0 -102.5 -30.5t-81 -80.5t-52 -114.5t-18.5 -130.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="1126" d="M137 201l322 397l-152 377l166 78l252 -486l-432 -458zM626 201l322 397l-152 377l166 78l252 -486l-432 -458z" />
<glyph unicode="&#xbc;" horiz-adv-x="1798" d="M345 1268q96 33 183 75.5t152 92.5h142l-193 -805h-190l137 573q-47 -25 -102.5 -46.5t-108.5 -37.5zM365 0l1098 1419h235l-1095 -1419h-238zM1106 188l27 117q43 63 101.5 131t125 132.5t138 126t145.5 112.5h168l-115 -480h98l-34 -139h-99l-45 -186h-168l45 186h-387 zM1313 327h215l72 295q-72 -59 -152 -138t-135 -157z" />
<glyph unicode="&#xbd;" horiz-adv-x="1798" d="M320 0l1098 1419h235l-1095 -1419h-238zM355 1268q96 33 183 75.5t152 92.5h142l-193 -805h-190l137 573q-47 -25 -102.5 -46.5t-108.5 -37.5zM1160 2q4 70 22.5 124t52 100t83.5 86t120 81q55 33 93 57.5t60.5 47t32 44t9.5 50.5q0 35 -24.5 53t-63.5 18 q-61 0 -115.5 -27.5t-95.5 -62.5l-62 131q53 51 131 85t170 34q111 0 176.5 -52t65.5 -155q0 -61 -23.5 -108t-64.5 -88t-96.5 -79t-120.5 -81q-35 -23 -70 -47.5t-47 -53.5h365l-35 -157h-563z" />
<glyph unicode="&#xbe;" horiz-adv-x="1798" d="M200 676l70 143q37 -23 92.5 -37t110.5 -14q37 0 71.5 6t61.5 20.5t43 38t16 58.5q0 90 -161 90h-74l35 137h71q31 0 61.5 7.5t55.5 21.5t40 34.5t15 45.5q0 72 -104 71q-53 0 -106.5 -16t-90.5 -35l-35 135q43 25 122 47.5t153 22.5q111 0 172 -45t61 -137 q0 -72 -38.5 -125t-108.5 -84q47 -23 76.5 -65t29.5 -103q0 -72 -29.5 -124t-80.5 -87t-120 -51.5t-144 -16.5q-82 0 -149.5 16.5t-115.5 45.5zM410 0l1098 1419h235l-1095 -1419h-238zM1126 188l27 117q43 63 101.5 131t125 132.5t138 126t145.5 112.5h168l-115 -480h98 l-34 -139h-99l-45 -186h-168l45 186h-387zM1333 327h215l72 295q-72 -59 -152 -138t-135 -157z" />
<glyph unicode="&#xbf;" horiz-adv-x="847" d="M74 -104q0 70 19.5 126t53 102t78.5 86t94 79l70 55q63 51 118.5 113.5t57.5 138.5h219q-2 -68 -23.5 -123t-55 -101t-79 -86t-94.5 -79q-82 -63 -133 -117.5t-64 -87.5t-13 -63q0 -27 10 -53.5t44 -49t99 -22.5q86 0 160 24.5t119 48.5l30 -194q-66 -37 -160 -61.5 t-192 -24.5q-170 0 -264 78.5t-94 210.5zM555 889q0 86 53.5 134t120.5 48q66 0 107 -37t41 -108q0 -41 -15.5 -75t-40 -57.5t-56.5 -36t-65 -12.5q-66 0 -105.5 36t-39.5 108z" />
<glyph unicode="&#xc0;" horiz-adv-x="1343" d="M14 0q125 217 239 409.5t221.5 366.5t213 333t211.5 310h240q66 -305 112.5 -666.5t89.5 -752.5h-260q-6 86 -13 171t-15 165h-576q-47 -80 -93 -163t-93 -173h-277zM588 541h444q-12 145 -29 297.5t-40 310.5q-78 -121 -173.5 -271.5t-201.5 -336.5zM760 1786l152 162 l303 -322l-115 -114z" />
<glyph unicode="&#xc1;" horiz-adv-x="1343" d="M14 0q125 217 239 409.5t221.5 366.5t213 333t211.5 310h240q66 -305 112.5 -666.5t89.5 -752.5h-260q-6 86 -13 171t-15 165h-576q-47 -80 -93 -163t-93 -173h-277zM588 541h444q-12 145 -29 297.5t-40 310.5q-78 -121 -173.5 -271.5t-201.5 -336.5zM840 1649l354 305 l133 -168l-387 -260z" />
<glyph unicode="&#xc2;" horiz-adv-x="1343" d="M14 0q125 217 239 409.5t221.5 366.5t213 333t211.5 310h240q66 -305 112.5 -666.5t89.5 -752.5h-260q-6 86 -13 171t-15 165h-576q-47 -80 -93 -163t-93 -173h-277zM588 541h444q-12 145 -29 297.5t-40 310.5q-78 -121 -173.5 -271.5t-201.5 -336.5zM752 1626l362 310 l234 -326l-107 -96l-166 180l-241 -176z" />
<glyph unicode="&#xc3;" horiz-adv-x="1343" d="M14 0q125 217 239 409.5t221.5 366.5t213 333t211.5 310h240q66 -305 112.5 -666.5t89.5 -752.5h-260q-6 86 -13 171t-15 165h-576q-47 -80 -93 -163t-93 -173h-277zM588 541h444q-12 145 -29 297.5t-40 310.5q-78 -121 -173.5 -271.5t-201.5 -336.5zM715 1641 q43 96 104 147t154 51q41 0 72.5 -12t58 -26.5t51 -26.5t51.5 -12q35 0 61.5 24.5t55.5 67.5l117 -72q-45 -98 -107 -148.5t-154 -50.5q-37 0 -67.5 12.5t-58 26t-53 25.5t-52.5 12q-35 0 -61.5 -23.5t-55.5 -66.5z" />
<glyph unicode="&#xc4;" horiz-adv-x="1343" d="M14 0q125 217 239 409.5t221.5 366.5t213 333t211.5 310h240q66 -305 112.5 -666.5t89.5 -752.5h-260q-6 86 -13 171t-15 165h-576q-47 -80 -93 -163t-93 -173h-277zM588 541h444q-12 145 -29 297.5t-40 310.5q-78 -121 -173.5 -271.5t-201.5 -336.5zM735 1690 q0 35 13.5 64.5t33.5 50t47 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86zM1157 1690q0 35 13 64.5t33.5 50t47.5 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33 t-35 86z" />
<glyph unicode="&#xc5;" horiz-adv-x="1343" d="M14 0q119 209 229.5 396.5t215 356.5t207 323.5t204.5 301.5q-29 29 -46 69t-17 93q0 51 18.5 93t49 70t71.5 43t86 15t86 -15t73 -43t50.5 -70t18.5 -93q0 -68 -30 -117t-77 -75q59 -295 103 -637t85 -711h-260q-6 86 -13 171t-15 165h-576q-47 -80 -93 -163t-93 -173 h-277zM588 541h444q-12 145 -29 297.5t-40 310.5q-78 -121 -173.5 -271.5t-201.5 -336.5zM932 1540q0 -49 29.5 -76.5t70.5 -27.5t71 27.5t30 76.5t-30 77t-71 28t-70.5 -28t-29.5 -77z" />
<glyph unicode="&#xc6;" horiz-adv-x="1947" d="M4 0q168 221 318.5 412.5t294 363.5t284.5 331t293 312h963l-54 -217h-608l-84 -354h537l-52 -211h-534l-102 -420h655l-51 -217h-903l83 350h-501q-66 -86 -133.5 -176t-126.5 -174h-279zM700 557h394l151 629q-61 -66 -131 -142.5t-140.5 -159.5t-140 -166t-133.5 -161 z" />
<glyph unicode="&#xc7;" horiz-adv-x="1241" d="M248 547q0 172 57 334.5t166 289.5t264.5 204t351.5 77q129 0 224.5 -28.5t167.5 -69.5l-99 -207q-66 39 -140.5 60.5t-168.5 21.5q-129 0 -231.5 -57.5t-175 -150.5t-110.5 -212t-38 -242q0 -190 83 -281t243 -91q123 0 209 27.5t147 56.5l31 -218q-66 -35 -167 -62.5 t-245 -29.5q-25 -30 -37 -53q86 -43 86 -131q0 -53 -20.5 -91t-56 -63.5t-83 -38t-100.5 -12.5q-113 0 -186 39l61 139q23 -10 52.5 -17t64.5 -7q37 0 58.5 12t21.5 37t-15.5 38t-50.5 29l-26 13q14 25 35.5 57.5t42.5 61.5q-199 39 -307.5 187t-108.5 378z" />
<glyph unicode="&#xc8;" horiz-adv-x="1171" d="M160 0l340 1419h880l-51 -215h-624l-84 -356h553l-52 -211h-551l-102 -422h672l-51 -215h-930zM708 1786l152 162l303 -322l-115 -114z" />
<glyph unicode="&#xc9;" horiz-adv-x="1171" d="M160 0l340 1419h880l-51 -215h-624l-84 -356h553l-52 -211h-551l-102 -422h672l-51 -215h-930zM809 1649l354 305l133 -168l-387 -260z" />
<glyph unicode="&#xca;" horiz-adv-x="1171" d="M160 0l340 1419h880l-51 -215h-624l-84 -356h553l-52 -211h-551l-102 -422h672l-51 -215h-930zM694 1626l362 310l234 -326l-107 -96l-166 180l-241 -176z" />
<glyph unicode="&#xcb;" horiz-adv-x="1171" d="M160 0l340 1419h880l-51 -215h-624l-84 -356h553l-52 -211h-551l-102 -422h672l-51 -215h-930zM651 1690q0 35 13.5 64.5t33.5 50t47 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86zM1073 1690q0 35 13 64.5 t33.5 50t47.5 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86z" />
<glyph unicode="&#xcc;" horiz-adv-x="585" d="M160 0l340 1419h258l-340 -1419h-258zM373 1786l152 162l303 -322l-115 -114z" />
<glyph unicode="&#xcd;" horiz-adv-x="585" d="M160 0l340 1419h258l-340 -1419h-258zM490 1649l354 305l133 -168l-387 -260z" />
<glyph unicode="&#xce;" horiz-adv-x="585" d="M160 0l340 1419h258l-340 -1419h-258zM377 1626l362 310l234 -326l-107 -96l-166 180l-241 -176z" />
<glyph unicode="&#xcf;" horiz-adv-x="585" d="M160 0l340 1419h258l-340 -1419h-258zM350 1690q0 35 13.5 64.5t33.5 50t47 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86zM772 1690q0 35 13 64.5t33.5 50t47.5 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5 t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86z" />
<glyph unicode="&#xd0;" horiz-adv-x="1480" d="M193 641l45 195h165l136 561q100 23 202.5 30t188.5 7q156 0 274.5 -40t198.5 -113t121 -176.5t41 -230.5q0 -190 -61.5 -354t-182.5 -282.5t-302 -186t-421 -67.5q-86 0 -192.5 9t-196.5 30l147 618h-163zM512 211q23 -4 52.5 -5t92.5 -1q147 0 266 47t202 134t128 209 t45 271q0 162 -92 256.5t-301 94.5q-53 0 -85 -2.5t-68 -6.5l-90 -372h262l-47 -195h-263z" />
<glyph unicode="&#xd1;" horiz-adv-x="1472" d="M160 0l340 1419h207q57 -92 119.5 -210.5t122 -249t114.5 -261.5t96 -247l227 968h256l-339 -1419h-207q-39 119 -89.5 256t-106.5 274.5t-116.5 267.5t-121.5 238l-248 -1036h-254zM773 1641q43 96 104 147t154 51q41 0 72.5 -12t58 -26.5t51 -26.5t51.5 -12 q35 0 61.5 24.5t55.5 67.5l117 -72q-45 -98 -107 -148.5t-154 -50.5q-37 0 -67.5 12.5t-58 26t-53 25.5t-52.5 12q-35 0 -61.5 -23.5t-55.5 -66.5z" />
<glyph unicode="&#xd2;" horiz-adv-x="1519" d="M248 539q0 164 53 327.5t156.5 293.5t255 211t348.5 81q119 0 219 -38t172 -109.5t112 -178t40 -243.5q0 -164 -53.5 -328t-156 -294t-254.5 -211t-349 -81q-119 0 -218.5 38t-172 109.5t-112.5 178t-40 244.5zM516 561q0 -168 71.5 -268t229.5 -100q117 0 213 58 t164 152.5t105.5 214t37.5 242.5q0 168 -72.5 268.5t-230.5 100.5q-117 0 -212 -58.5t-162.5 -152.5t-105.5 -214t-38 -243zM887 1786l152 162l303 -322l-115 -114z" />
<glyph unicode="&#xd3;" horiz-adv-x="1519" d="M248 539q0 164 53 327.5t156.5 293.5t255 211t348.5 81q119 0 219 -38t172 -109.5t112 -178t40 -243.5q0 -164 -53.5 -328t-156 -294t-254.5 -211t-349 -81q-119 0 -218.5 38t-172 109.5t-112.5 178t-40 244.5zM516 561q0 -168 71.5 -268t229.5 -100q117 0 213 58 t164 152.5t105.5 214t37.5 242.5q0 168 -72.5 268.5t-230.5 100.5q-117 0 -212 -58.5t-162.5 -152.5t-105.5 -214t-38 -243zM897 1649l354 305l133 -168l-387 -260z" />
<glyph unicode="&#xd4;" horiz-adv-x="1519" d="M248 539q0 164 53 327.5t156.5 293.5t255 211t348.5 81q119 0 219 -38t172 -109.5t112 -178t40 -243.5q0 -164 -53.5 -328t-156 -294t-254.5 -211t-349 -81q-119 0 -218.5 38t-172 109.5t-112.5 178t-40 244.5zM516 561q0 -168 71.5 -268t229.5 -100q117 0 213 58 t164 152.5t105.5 214t37.5 242.5q0 168 -72.5 268.5t-230.5 100.5q-117 0 -212 -58.5t-162.5 -152.5t-105.5 -214t-38 -243zM809 1626l362 310l234 -326l-107 -96l-166 180l-241 -176z" />
<glyph unicode="&#xd5;" horiz-adv-x="1519" d="M248 539q0 164 53 327.5t156.5 293.5t255 211t348.5 81q119 0 219 -38t172 -109.5t112 -178t40 -243.5q0 -164 -53.5 -328t-156 -294t-254.5 -211t-349 -81q-119 0 -218.5 38t-172 109.5t-112.5 178t-40 244.5zM516 561q0 -168 71.5 -268t229.5 -100q117 0 213 58 t164 152.5t105.5 214t37.5 242.5q0 168 -72.5 268.5t-230.5 100.5q-117 0 -212 -58.5t-162.5 -152.5t-105.5 -214t-38 -243zM766 1641q43 96 104 147t154 51q41 0 72.5 -12t58 -26.5t51 -26.5t51.5 -12q35 0 61.5 24.5t55.5 67.5l117 -72q-45 -98 -107 -148.5t-154 -50.5 q-37 0 -67.5 12.5t-58 26t-53 25.5t-52.5 12q-35 0 -61.5 -23.5t-55.5 -66.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="1519" d="M248 539q0 164 53 327.5t156.5 293.5t255 211t348.5 81q119 0 219 -38t172 -109.5t112 -178t40 -243.5q0 -164 -53.5 -328t-156 -294t-254.5 -211t-349 -81q-119 0 -218.5 38t-172 109.5t-112.5 178t-40 244.5zM516 561q0 -168 71.5 -268t229.5 -100q117 0 213 58 t164 152.5t105.5 214t37.5 242.5q0 168 -72.5 268.5t-230.5 100.5q-117 0 -212 -58.5t-162.5 -152.5t-105.5 -214t-38 -243zM787 1690q0 35 13.5 64.5t33.5 50t47 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86z M1209 1690q0 35 13 64.5t33.5 50t47.5 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86z" />
<glyph unicode="&#xd7;" d="M215 322l363 290l-218 287l170 135l215 -284l361 292l119 -155l-361 -293l215 -285l-168 -137l-215 287l-360 -293z" />
<glyph unicode="&#xd8;" horiz-adv-x="1519" d="M213 25l139 157q-47 70 -71.5 158t-24.5 199q0 164 53 327.5t156.5 293.5t255 211t348.5 81q92 0 173 -23.5t147 -66.5l124 141l138 -108l-138 -156q47 -70 73 -158t26 -198q0 -164 -53.5 -328t-156 -294t-254.5 -211t-349 -81q-188 0 -322 90l-125 -143zM524 561 q0 -94 21 -162l680 777q-70 53 -183 53q-117 0 -212 -58.5t-162.5 -152.5t-105.5 -214t-38 -243zM641 246q72 -53 184 -53q117 0 213.5 58t164 152.5t105.5 214t38 242.5q0 94 -21 162z" />
<glyph unicode="&#xd9;" horiz-adv-x="1386" d="M264 387q0 47 7.5 104.5t21.5 110.5l194 817h261l-203 -846q-10 -39 -16.5 -80.5t-6.5 -80.5q0 -45 11.5 -85t38 -69t69.5 -46t107 -17q150 0 229.5 88t120.5 264l209 872h260l-215 -897q-31 -131 -78 -233.5t-122 -173t-182.5 -108.5t-258.5 -38q-119 0 -204 32 t-138.5 87t-79 132t-25.5 167zM766 1786l152 162l303 -322l-115 -114z" />
<glyph unicode="&#xda;" horiz-adv-x="1386" d="M264 387q0 47 7.5 104.5t21.5 110.5l194 817h261l-203 -846q-10 -39 -16.5 -80.5t-6.5 -80.5q0 -45 11.5 -85t38 -69t69.5 -46t107 -17q150 0 229.5 88t120.5 264l209 872h260l-215 -897q-31 -131 -78 -233.5t-122 -173t-182.5 -108.5t-258.5 -38q-119 0 -204 32 t-138.5 87t-79 132t-25.5 167zM854 1649l354 305l133 -168l-387 -260z" />
<glyph unicode="&#xdb;" horiz-adv-x="1386" d="M264 387q0 47 7.5 104.5t21.5 110.5l194 817h261l-203 -846q-10 -39 -16.5 -80.5t-6.5 -80.5q0 -45 11.5 -85t38 -69t69.5 -46t107 -17q150 0 229.5 88t120.5 264l209 872h260l-215 -897q-31 -131 -78 -233.5t-122 -173t-182.5 -108.5t-258.5 -38q-119 0 -204 32 t-138.5 87t-79 132t-25.5 167zM760 1626l362 310l234 -326l-107 -96l-166 180l-241 -176z" />
<glyph unicode="&#xdc;" horiz-adv-x="1386" d="M264 387q0 47 7.5 104.5t21.5 110.5l194 817h261l-203 -846q-10 -39 -16.5 -80.5t-6.5 -80.5q0 -45 11.5 -85t38 -69t69.5 -46t107 -17q150 0 229.5 88t120.5 264l209 872h260l-215 -897q-31 -131 -78 -233.5t-122 -173t-182.5 -108.5t-258.5 -38q-119 0 -204 32 t-138.5 87t-79 132t-25.5 167zM729 1690q0 35 13.5 64.5t33.5 50t47 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86zM1151 1690q0 35 13 64.5t33.5 50t47.5 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48 t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86z" />
<glyph unicode="&#xdd;" horiz-adv-x="1218" d="M330 1419h274q35 -158 80 -315t98 -311q127 154 247 308t224 318h291q-80 -113 -159.5 -219t-162.5 -211.5t-171 -210t-183 -213.5l-135 -565h-258l135 565q-84 215 -152.5 427t-127.5 427zM768 1649l354 305l133 -168l-387 -260z" />
<glyph unicode="&#xde;" horiz-adv-x="1286" d="M158 0l340 1419h260l-53 -219q35 4 82 5t81 1q125 0 218.5 -28.5t155 -78.5t91 -121t29.5 -153q0 -117 -46 -217t-139.5 -174t-231.5 -116t-322 -42h-142l-65 -276h-258zM535 496h114q86 0 167 13t142.5 49t99.5 96.5t38 152.5q0 100 -79 140t-214 40q-55 0 -92 -2 t-60 -6z" />
<glyph unicode="&#xdf;" horiz-adv-x="1271" d="M-45 -354q39 49 72.5 99t61.5 106.5t51.5 123t41.5 148.5l246 1026q29 119 80 202t119.5 136t154.5 77.5t185 24.5q82 0 150.5 -21.5t118.5 -60.5t77.5 -92t27.5 -117q0 -94 -43 -162.5t-131 -148.5q-63 -57 -107 -101t-44 -108q0 -43 37 -73.5t90 -67.5 q74 -53 121 -115.5t47 -161.5q0 -98 -37 -169.5t-100.5 -119.5t-147.5 -72t-178 -24q-92 0 -174 22.5t-127 49.5l90 197q41 -25 98.5 -44.5t126.5 -19.5q88 0 144.5 41t56.5 117q0 43 -25.5 78t-85.5 76q-61 39 -98 70.5t-56.5 62t-26.5 61.5t-7 66q0 80 44 139t100.5 112.5 t108.5 103.5t62.5 86t10.5 77q0 43 -34 75.5t-112 32.5q-100 0 -173 -60.5t-103 -191.5l-215 -901q-27 -111 -50.5 -196.5t-54.5 -157.5t-73 -134.5t-105 -123.5z" />
<glyph unicode="&#xe0;" horiz-adv-x="1142" d="M195 387q0 150 53 279t148.5 223t226.5 148.5t286 54.5q80 0 161 -13.5t163 -50.5l-148 -612q-6 -23 -12 -74t-6 -96q0 -59 10 -115.5t33 -114.5l-221 -32q-25 49 -39 104q-51 -45 -126 -81t-169 -36q-96 0 -164.5 32t-111.5 88t-63.5 132t-20.5 164zM440 408 q0 -47 8.5 -88t28 -72t52 -49.5t83.5 -18.5q66 0 116 28t93 75q2 53 9.5 100t19.5 94l100 402q-39 8 -88 8q-92 0 -170 -40t-134 -106.5t-87 -152.5t-31 -180zM665 1454l152 162l303 -322l-115 -114z" />
<glyph unicode="&#xe1;" horiz-adv-x="1142" d="M195 387q0 150 53 279t148.5 223t226.5 148.5t286 54.5q80 0 161 -13.5t163 -50.5l-148 -612q-6 -23 -12 -74t-6 -96q0 -59 10 -115.5t33 -114.5l-221 -32q-25 49 -39 104q-51 -45 -126 -81t-169 -36q-96 0 -164.5 32t-111.5 88t-63.5 132t-20.5 164zM440 408 q0 -47 8.5 -88t28 -72t52 -49.5t83.5 -18.5q66 0 116 28t93 75q2 53 9.5 100t19.5 94l100 402q-39 8 -88 8q-92 0 -170 -40t-134 -106.5t-87 -152.5t-31 -180zM701 1317l354 305l133 -168l-387 -260z" />
<glyph unicode="&#xe2;" horiz-adv-x="1142" d="M195 387q0 150 53 279t148.5 223t226.5 148.5t286 54.5q80 0 161 -13.5t163 -50.5l-148 -612q-6 -23 -12 -74t-6 -96q0 -59 10 -115.5t33 -114.5l-221 -32q-25 49 -39 104q-51 -45 -126 -81t-169 -36q-96 0 -164.5 32t-111.5 88t-63.5 132t-20.5 164zM440 408 q0 -47 8.5 -88t28 -72t52 -49.5t83.5 -18.5q66 0 116 28t93 75q2 53 9.5 100t19.5 94l100 402q-39 8 -88 8q-92 0 -170 -40t-134 -106.5t-87 -152.5t-31 -180zM580 1296l362 310l234 -326l-107 -96l-166 180l-241 -176z" />
<glyph unicode="&#xe3;" horiz-adv-x="1142" d="M195 387q0 150 53 279t148.5 223t226.5 148.5t286 54.5q80 0 161 -13.5t163 -50.5l-148 -612q-6 -23 -12 -74t-6 -96q0 -59 10 -115.5t33 -114.5l-221 -32q-25 49 -39 104q-51 -45 -126 -81t-169 -36q-96 0 -164.5 32t-111.5 88t-63.5 132t-20.5 164zM440 408 q0 -47 8.5 -88t28 -72t52 -49.5t83.5 -18.5q66 0 116 28t93 75q2 53 9.5 100t19.5 94l100 402q-39 8 -88 8q-92 0 -170 -40t-134 -106.5t-87 -152.5t-31 -180zM553 1315q43 96 104 147t154 51q41 0 72.5 -12t58 -26.5t51 -26.5t51.5 -12q35 0 61.5 24.5t55.5 67.5l117 -72 q-45 -98 -107 -148.5t-154 -50.5q-37 0 -67.5 12.5t-58 26t-53 25.5t-52.5 12q-35 0 -61.5 -23.5t-55.5 -66.5z" />
<glyph unicode="&#xe4;" horiz-adv-x="1142" d="M195 387q0 150 53 279t148.5 223t226.5 148.5t286 54.5q80 0 161 -13.5t163 -50.5l-148 -612q-6 -23 -12 -74t-6 -96q0 -59 10 -115.5t33 -114.5l-221 -32q-25 49 -39 104q-51 -45 -126 -81t-169 -36q-96 0 -164.5 32t-111.5 88t-63.5 132t-20.5 164zM440 408 q0 -47 8.5 -88t28 -72t52 -49.5t83.5 -18.5q66 0 116 28t93 75q2 53 9.5 100t19.5 94l100 402q-39 8 -88 8q-92 0 -170 -40t-134 -106.5t-87 -152.5t-31 -180zM549 1366q0 35 13.5 64.5t33.5 50t47 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32 t-53.5 -11.5q-53 0 -88 33t-35 86zM971 1366q0 35 13 64.5t33.5 50t47.5 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86z" />
<glyph unicode="&#xe5;" horiz-adv-x="1142" d="M195 387q0 150 53 279t148.5 223t226.5 148.5t286 54.5q80 0 161 -13.5t163 -50.5l-148 -612q-6 -23 -12 -74t-6 -96q0 -59 10 -115.5t33 -114.5l-221 -32q-25 49 -39 104q-51 -45 -126 -81t-169 -36q-96 0 -164.5 32t-111.5 88t-63.5 132t-20.5 164zM440 408 q0 -47 8.5 -88t28 -72t52 -49.5t83.5 -18.5q66 0 116 28t93 75q2 53 9.5 100t19.5 94l100 402q-39 8 -88 8q-92 0 -170 -40t-134 -106.5t-87 -152.5t-31 -180zM678 1409q0 51 18 93t49 69.5t72 43t86 15.5t86 -15.5t72.5 -43t50 -69.5t18.5 -93q0 -53 -18.5 -94t-50 -69.5 t-72.5 -44t-86 -15.5t-86 15.5t-72 44t-49 69.5t-18 94zM802 1409q0 -49 30 -76.5t71 -27.5t70.5 27.5t29.5 76.5t-29.5 76.5t-70.5 27.5t-71 -27.5t-30 -76.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1722" d="M152 242q0 133 64.5 215t166.5 128t230 62.5t259 18.5q2 12 2 28q0 178 -235 178q-139 0 -279 -57l-20 201q49 23 139 45t205 22q135 0 225 -52t138 -134q82 92 191.5 146.5t244.5 54.5q156 0 241.5 -80t85.5 -205q0 -129 -63.5 -209t-164.5 -125t-229 -61.5t-259 -20.5 q-2 -12 -2 -28q0 -186 235 -187q139 0 279 58l20 -201q-49 -23 -139 -45.5t-205 -22.5q-141 0 -231 53.5t-140 137.5q-80 -92 -189.5 -148.5t-242.5 -56.5q-156 0 -241.5 80t-85.5 205zM383 266q0 -41 29.5 -73.5t103.5 -32.5q61 0 117.5 24.5t101.5 68.5t77 104.5t44 131.5 q-248 -4 -360.5 -58t-112.5 -165zM1110 575q246 8 357.5 59.5t111.5 153.5q0 41 -29.5 74t-103.5 33q-61 0 -116.5 -24.5t-98.5 -67.5t-75 -101.5t-46 -126.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="940" d="M199 406q0 139 44 265t126 221t200.5 150.5t270.5 55.5q80 0 146.5 -13.5t125.5 -40.5l-88 -198q-37 16 -82 28.5t-111 12.5q-88 0 -158.5 -36t-119.5 -97.5t-75.5 -145.5t-26.5 -178q0 -53 11 -97t36.5 -78t70.5 -52.5t113 -18.5q70 0 134.5 18.5t98.5 37.5l21 -203 q-47 -23 -120 -42.5t-165 -23.5l-39 -55q86 -43 86 -131q0 -53 -20.5 -91t-56 -63.5t-82.5 -38t-101 -12.5q-113 0 -186 39l61 139q23 -10 52.5 -17t64.5 -7q37 0 58.5 12t21.5 37t-15.5 38t-50.5 29l-26 13q14 25 36.5 59.5t45.5 63.5q-156 37 -228.5 151.5t-72.5 268.5z " />
<glyph unicode="&#xe8;" horiz-adv-x="1071" d="M199 387q0 137 44 265t127 227.5t199.5 159t261.5 59.5q156 0 242 -80t86 -205q0 -129 -63.5 -209t-165 -125t-229.5 -61.5t-259 -20.5q-2 -4 -2 -8v-20q0 -186 236 -187q139 0 278 58l21 -201q-49 -23 -139.5 -45.5t-204.5 -22.5q-115 0 -197 33t-134 90.5t-76.5 132 t-24.5 160.5zM459 575q246 8 357.5 59.5t111.5 153.5q0 41 -29.5 74t-103.5 33q-61 0 -116.5 -24.5t-98.5 -67.5t-75 -101.5t-46 -126.5zM598 1454l152 162l303 -322l-115 -114z" />
<glyph unicode="&#xe9;" horiz-adv-x="1071" d="M199 387q0 137 44 265t127 227.5t199.5 159t261.5 59.5q156 0 242 -80t86 -205q0 -129 -63.5 -209t-165 -125t-229.5 -61.5t-259 -20.5q-2 -4 -2 -8v-20q0 -186 236 -187q139 0 278 58l21 -201q-49 -23 -139.5 -45.5t-204.5 -22.5q-115 0 -197 33t-134 90.5t-76.5 132 t-24.5 160.5zM459 575q246 8 357.5 59.5t111.5 153.5q0 41 -29.5 74t-103.5 33q-61 0 -116.5 -24.5t-98.5 -67.5t-75 -101.5t-46 -126.5zM641 1317l354 305l133 -168l-387 -260z" />
<glyph unicode="&#xea;" horiz-adv-x="1071" d="M199 387q0 137 44 265t127 227.5t199.5 159t261.5 59.5q156 0 242 -80t86 -205q0 -129 -63.5 -209t-165 -125t-229.5 -61.5t-259 -20.5q-2 -4 -2 -8v-20q0 -186 236 -187q139 0 278 58l21 -201q-49 -23 -139.5 -45.5t-204.5 -22.5q-115 0 -197 33t-134 90.5t-76.5 132 t-24.5 160.5zM459 575q246 8 357.5 59.5t111.5 153.5q0 41 -29.5 74t-103.5 33q-61 0 -116.5 -24.5t-98.5 -67.5t-75 -101.5t-46 -126.5zM553 1296l362 310l234 -326l-107 -96l-166 180l-241 -176z" />
<glyph unicode="&#xeb;" horiz-adv-x="1071" d="M199 387q0 137 44 265t127 227.5t199.5 159t261.5 59.5q156 0 242 -80t86 -205q0 -129 -63.5 -209t-165 -125t-229.5 -61.5t-259 -20.5q-2 -4 -2 -8v-20q0 -186 236 -187q139 0 278 58l21 -201q-49 -23 -139.5 -45.5t-204.5 -22.5q-115 0 -197 33t-134 90.5t-76.5 132 t-24.5 160.5zM459 575q246 8 357.5 59.5t111.5 153.5q0 41 -29.5 74t-103.5 33q-61 0 -116.5 -24.5t-98.5 -67.5t-75 -101.5t-46 -126.5zM551 1366q0 35 13.5 64.5t33.5 50t47 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33 t-35 86zM973 1366q0 35 13 64.5t33.5 50t47.5 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86z" />
<glyph unicode="&#xec;" horiz-adv-x="555" d="M150 0l256 1071h247l-258 -1071h-245zM293 1454l152 162l303 -322l-115 -114z" />
<glyph unicode="&#xed;" horiz-adv-x="555" d="M150 0l256 1071h247l-258 -1071h-245zM387 1317l354 305l133 -168l-387 -260z" />
<glyph unicode="&#xee;" horiz-adv-x="555" d="M150 0l256 1071h247l-258 -1071h-245zM275 1296l362 310l234 -326l-107 -96l-166 180l-241 -176z" />
<glyph unicode="&#xef;" horiz-adv-x="555" d="M150 0l256 1071h247l-258 -1071h-245zM258 1366q0 35 13.5 64.5t33.5 50t47 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86zM680 1366q0 35 13 64.5t33.5 50t47.5 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5 t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86z" />
<glyph unicode="&#xf0;" horiz-adv-x="1185" d="M199 377q0 154 44 270.5t121.5 195.5t182 118.5t225.5 39.5q70 0 128 -16t101 -39q-4 66 -17 126.5t-40 119.5l-254 -80l-18 150l198 63q-31 43 -67.5 78t-79.5 70l174 116q49 -35 98 -84t90 -112l281 92l18 -152l-223 -71q39 -90 59.5 -194.5t20.5 -223.5 q0 -88 -13.5 -187.5t-44 -197t-79.5 -185.5t-121 -155.5t-168 -107.5t-221 -40q-90 0 -163 31t-125 84t-79.5 128t-27.5 163zM446 389q0 -92 45.5 -147.5t135.5 -55.5q84 0 147.5 48.5t107.5 126t69.5 175t33.5 197.5q-55 35 -110.5 50.5t-106.5 15.5q-78 0 -138.5 -30 t-101.5 -84t-61.5 -130t-20.5 -166z" />
<glyph unicode="&#xf1;" horiz-adv-x="1153" d="M150 0l245 1026q82 25 187.5 46.5t230.5 21.5q188 0 276.5 -92.5t88.5 -247.5q0 -43 -7.5 -91.5t-17.5 -99.5l-135 -563h-248l127 537q10 45 20.5 92t12.5 90q2 72 -34 119t-140 47q-88 0 -152 -19l-207 -866h-247zM525 1315q43 96 104 147t154 51q41 0 72.5 -12 t58 -26.5t51 -26.5t51.5 -12q35 0 61.5 24.5t55.5 67.5l117 -72q-45 -98 -107 -148.5t-154 -50.5q-37 0 -67.5 12.5t-58 26t-53 25.5t-52.5 12q-35 0 -61.5 -23.5t-55.5 -66.5z" />
<glyph unicode="&#xf2;" horiz-adv-x="1153" d="M199 381q0 119 36.5 246t112.5 231.5t190.5 172t270.5 67.5q190 0 290.5 -112t100.5 -296q0 -121 -36 -248t-110.5 -231.5t-190 -172t-273.5 -67.5q-190 0 -290.5 113t-100.5 297zM444 403q0 -102 39 -161.5t138 -59.5q80 0 142 46t104 117t64.5 157t22.5 164 q0 102 -39 162.5t-137 60.5q-80 0 -142.5 -46t-104.5 -118t-64.5 -157t-22.5 -165zM581 1454l152 162l303 -322l-115 -114z" />
<glyph unicode="&#xf3;" horiz-adv-x="1153" d="M199 381q0 119 36.5 246t112.5 231.5t190.5 172t270.5 67.5q190 0 290.5 -112t100.5 -296q0 -121 -36 -248t-110.5 -231.5t-190 -172t-273.5 -67.5q-190 0 -290.5 113t-100.5 297zM444 403q0 -102 39 -161.5t138 -59.5q80 0 142 46t104 117t64.5 157t22.5 164 q0 102 -39 162.5t-137 60.5q-80 0 -142.5 -46t-104.5 -118t-64.5 -157t-22.5 -165zM668 1317l354 305l133 -168l-387 -260z" />
<glyph unicode="&#xf4;" horiz-adv-x="1153" d="M199 381q0 119 36.5 246t112.5 231.5t190.5 172t270.5 67.5q190 0 290.5 -112t100.5 -296q0 -121 -36 -248t-110.5 -231.5t-190 -172t-273.5 -67.5q-190 0 -290.5 113t-100.5 297zM444 403q0 -102 39 -161.5t138 -59.5q80 0 142 46t104 117t64.5 157t22.5 164 q0 102 -39 162.5t-137 60.5q-80 0 -142.5 -46t-104.5 -118t-64.5 -157t-22.5 -165zM559 1296l362 310l234 -326l-107 -96l-166 180l-241 -176z" />
<glyph unicode="&#xf5;" horiz-adv-x="1153" d="M199 381q0 119 36.5 246t112.5 231.5t190.5 172t270.5 67.5q190 0 290.5 -112t100.5 -296q0 -121 -36 -248t-110.5 -231.5t-190 -172t-273.5 -67.5q-190 0 -290.5 113t-100.5 297zM444 403q0 -102 39 -161.5t138 -59.5q80 0 142 46t104 117t64.5 157t22.5 164 q0 102 -39 162.5t-137 60.5q-80 0 -142.5 -46t-104.5 -118t-64.5 -157t-22.5 -165zM517 1315q43 96 104 147t154 51q41 0 72.5 -12t58 -26.5t51 -26.5t51.5 -12q35 0 61.5 24.5t55.5 67.5l117 -72q-45 -98 -107 -148.5t-154 -50.5q-37 0 -67.5 12.5t-58 26t-53 25.5 t-52.5 12q-35 0 -61.5 -23.5t-55.5 -66.5z" />
<glyph unicode="&#xf6;" horiz-adv-x="1153" d="M199 381q0 119 36.5 246t112.5 231.5t190.5 172t270.5 67.5q190 0 290.5 -112t100.5 -296q0 -121 -36 -248t-110.5 -231.5t-190 -172t-273.5 -67.5q-190 0 -290.5 113t-100.5 297zM444 403q0 -102 39 -161.5t138 -59.5q80 0 142 46t104 117t64.5 157t22.5 164 q0 102 -39 162.5t-137 60.5q-80 0 -142.5 -46t-104.5 -118t-64.5 -157t-22.5 -165zM531 1366q0 35 13.5 64.5t33.5 50t47 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86zM953 1366q0 35 13 64.5t33.5 50t47.5 32 t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86z" />
<glyph unicode="&#xf7;" d="M217 481l51 209h940l-51 -209h-940zM463 156q0 76 50 126t122 50q55 0 91 -32t36 -91q0 -76 -50 -127t-122 -51q-55 0 -91 32.5t-36 92.5zM657 963q0 76 50 126t122 50q55 0 91 -32t36 -91q0 -76 -50 -127t-122 -51q-55 0 -91 32.5t-36 92.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1153" d="M137 14l115 131q-53 100 -53 236q0 119 36.5 246t112.5 231.5t190.5 172t270.5 67.5q133 0 227 -60l94 109l119 -92l-108 -125q59 -100 59 -240q0 -121 -36 -248t-110.5 -231.5t-190 -172t-273.5 -67.5q-141 0 -236 64l-98 -113zM444 365l437 495q-42 29 -103 29 q-80 0 -142.5 -46t-104.5 -118t-64.5 -157t-22.5 -165v-38zM512 213q39 -31 109 -31q80 0 142 46t104 117t64.5 157t22.5 164v24t-2 25z" />
<glyph unicode="&#xf9;" horiz-adv-x="1161" d="M219 313q0 90 25 195l135 563h248l-133 -557q-27 -106 -27 -174q0 -33 6 -61.5t21.5 -50t43 -34t68.5 -12.5q68 0 121 28t98 75q6 88 29 180l143 606h248l-151 -637q-10 -47 -15.5 -94t-5.5 -94q0 -59 9.5 -116.5t31.5 -113.5l-221 -32q-12 23 -21.5 50t-17.5 56 q-51 -41 -127 -79t-176 -38q-90 0 -153.5 27t-103.5 73t-57.5 108.5t-17.5 131.5zM563 1454l152 162l303 -322l-115 -114z" />
<glyph unicode="&#xfa;" horiz-adv-x="1161" d="M219 313q0 90 25 195l135 563h248l-133 -557q-27 -106 -27 -174q0 -33 6 -61.5t21.5 -50t43 -34t68.5 -12.5q68 0 121 28t98 75q6 88 29 180l143 606h248l-151 -637q-10 -47 -15.5 -94t-5.5 -94q0 -59 9.5 -116.5t31.5 -113.5l-221 -32q-12 23 -21.5 50t-17.5 56 q-51 -41 -127 -79t-176 -38q-90 0 -153.5 27t-103.5 73t-57.5 108.5t-17.5 131.5zM674 1317l354 305l133 -168l-387 -260z" />
<glyph unicode="&#xfb;" horiz-adv-x="1161" d="M219 313q0 90 25 195l135 563h248l-133 -557q-27 -106 -27 -174q0 -33 6 -61.5t21.5 -50t43 -34t68.5 -12.5q68 0 121 28t98 75q6 88 29 180l143 606h248l-151 -637q-10 -47 -15.5 -94t-5.5 -94q0 -59 9.5 -116.5t31.5 -113.5l-221 -32q-12 23 -21.5 50t-17.5 56 q-51 -41 -127 -79t-176 -38q-90 0 -153.5 27t-103.5 73t-57.5 108.5t-17.5 131.5zM565 1296l362 310l234 -326l-107 -96l-166 180l-241 -176z" />
<glyph unicode="&#xfc;" horiz-adv-x="1161" d="M219 313q0 90 25 195l135 563h248l-133 -557q-27 -106 -27 -174q0 -33 6 -61.5t21.5 -50t43 -34t68.5 -12.5q68 0 121 28t98 75q6 88 29 180l143 606h248l-151 -637q-10 -47 -15.5 -94t-5.5 -94q0 -59 9.5 -116.5t31.5 -113.5l-221 -32q-12 23 -21.5 50t-17.5 56 q-51 -41 -127 -79t-176 -38q-90 0 -153.5 27t-103.5 73t-57.5 108.5t-17.5 131.5zM533 1366q0 35 13.5 64.5t33.5 50t47 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86zM955 1366q0 35 13 64.5t33.5 50t47.5 32 t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86z" />
<glyph unicode="&#xfd;" horiz-adv-x="1011" d="M-115 -336l80 199q29 -16 65 -26.5t79 -10.5q96 0 168.5 50t132.5 136q-66 227 -111 493.5t-59 565.5h249q2 -86 9.5 -190.5t18.5 -214t27.5 -216t37.5 -196.5q61 98 114.5 206.5t98.5 216t78.5 209t58.5 185.5h262q-41 -117 -90 -243t-109.5 -252.5t-130 -252.5 t-149.5 -245q-59 -90 -123 -173t-138.5 -146.5t-164.5 -102.5t-205 -39q-63 0 -113.5 14.5t-85.5 32.5zM565 1317l354 305l133 -168l-387 -260z" />
<glyph unicode="&#xfe;" horiz-adv-x="1155" d="M59 -379l463 1927l258 41l-123 -508q35 6 68 10.5t68 4.5q106 0 185 -32t129 -87t74.5 -130t24.5 -161q0 -145 -46 -273t-135 -224.5t-217 -152.5t-294 -56q-29 0 -58.5 3t-58.5 7l-90 -369h-248zM444 193q20 -4 43 -5.5t45 -1.5q98 0 177 38t134.5 102.5t85 151.5 t29.5 186q0 45 -10 84.5t-33.5 70.5t-63.5 49.5t-101 18.5q-41 0 -78 -5t-66 -14z" />
<glyph unicode="&#xff;" horiz-adv-x="1011" d="M-115 -336l80 199q29 -16 65 -26.5t79 -10.5q96 0 168.5 50t132.5 136q-66 227 -111 493.5t-59 565.5h249q2 -86 9.5 -190.5t18.5 -214t27.5 -216t37.5 -196.5q61 98 114.5 206.5t98.5 216t78.5 209t58.5 185.5h262q-41 -117 -90 -243t-109.5 -252.5t-130 -252.5 t-149.5 -245q-59 -90 -123 -173t-138.5 -146.5t-164.5 -102.5t-205 -39q-63 0 -113.5 14.5t-85.5 32.5zM436 1366q0 35 13.5 64.5t33.5 50t47 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86zM858 1366q0 35 13 64.5 t33.5 50t47.5 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86z" />
<glyph unicode="&#x152;" horiz-adv-x="1982" d="M248 549q0 193 63.5 355.5t184 280.5t299 183.5t407.5 65.5q53 0 101.5 -3.5t113.5 -11.5h774l-53 -217h-596l-84 -354h524l-51 -211h-524l-100 -420h643l-52 -217h-796q-47 -8 -103.5 -12t-115.5 -4q-139 0 -256 31.5t-201 100t-131 176t-47 257.5zM514 559 q0 -80 22.5 -145.5t69.5 -112.5t120 -73.5t173 -26.5q41 0 81 3t73 7l237 995q-16 4 -56 7.5t-87 3.5q-154 0 -271.5 -54.5t-198.5 -144.5t-122 -210t-41 -249z" />
<glyph unicode="&#x153;" horiz-adv-x="1789" d="M199 381q0 119 36.5 246t112.5 231.5t190.5 172t270.5 67.5q117 0 197.5 -52.5t128.5 -138.5q80 86 184 138.5t231 52.5q156 0 242 -80t86 -205q0 -129 -63.5 -209t-165 -125t-229.5 -61.5t-259 -20.5q-2 -12 -2 -28q0 -186 236 -187q139 0 278 58l21 -201 q-49 -23 -139.5 -45.5t-204.5 -22.5q-135 0 -223.5 52.5t-135.5 136.5q-74 -80 -173 -134.5t-228 -54.5q-190 0 -290.5 113t-100.5 297zM444 403q0 -102 39 -161.5t138 -59.5q80 0 142 46t104 117t64.5 157t22.5 164q0 102 -39 162.5t-137 60.5q-80 0 -142.5 -46 t-104.5 -118t-64.5 -157t-22.5 -165zM1178 575q246 8 357.5 59.5t111.5 153.5q0 41 -30 74t-104 33q-61 0 -116.5 -24.5t-98.5 -67.5t-74.5 -101.5t-45.5 -126.5z" />
<glyph unicode="&#x178;" horiz-adv-x="1218" d="M330 1419h274q35 -158 80 -315t98 -311q127 154 247 308t224 318h291q-80 -113 -159.5 -219t-162.5 -211.5t-171 -210t-183 -213.5l-135 -565h-258l135 565q-84 215 -152.5 427t-127.5 427zM629 1690q0 35 13.5 64.5t33.5 50t47 32t53 11.5q53 0 89 -34t36 -91 q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86zM1051 1690q0 35 13 64.5t33.5 50t47.5 32t53 11.5q53 0 89 -34t36 -91q0 -33 -13 -60.5t-34.5 -48t-48 -32t-53.5 -11.5q-53 0 -88 33t-35 86z" />
<glyph unicode="&#x2c6;" horiz-adv-x="796" d="M248 1296l362 310l234 -326l-107 -96l-166 180l-241 -176z" />
<glyph unicode="&#x2dc;" horiz-adv-x="770" d="M193 1315q43 96 104 147t154 51q41 0 72.5 -12t58 -26.5t51 -26.5t51.5 -12q35 0 61.5 24.5t55.5 67.5l117 -72q-45 -98 -107 -148.5t-154 -50.5q-37 0 -67.5 12.5t-58 26t-53 25.5t-52.5 12q-35 0 -61.5 -23.5t-55.5 -66.5z" />
<glyph unicode="&#x2000;" horiz-adv-x="977" />
<glyph unicode="&#x2001;" horiz-adv-x="1954" />
<glyph unicode="&#x2002;" horiz-adv-x="977" />
<glyph unicode="&#x2003;" horiz-adv-x="1954" />
<glyph unicode="&#x2004;" horiz-adv-x="651" />
<glyph unicode="&#x2005;" horiz-adv-x="488" />
<glyph unicode="&#x2006;" horiz-adv-x="325" />
<glyph unicode="&#x2007;" horiz-adv-x="325" />
<glyph unicode="&#x2008;" horiz-adv-x="244" />
<glyph unicode="&#x2009;" horiz-adv-x="390" />
<glyph unicode="&#x200a;" horiz-adv-x="108" />
<glyph unicode="&#x2010;" horiz-adv-x="653" d="M170 471l53 227h545l-53 -227h-545z" />
<glyph unicode="&#x2011;" horiz-adv-x="653" d="M170 471l53 227h545l-53 -227h-545z" />
<glyph unicode="&#x2012;" horiz-adv-x="653" d="M170 471l53 227h545l-53 -227h-545z" />
<glyph unicode="&#x2013;" horiz-adv-x="1013" d="M109 481l49 209h1024l-49 -209h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2037" d="M109 481l49 209h2048l-49 -209h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="514" d="M367 1038q8 76 34.5 151t65.5 143.5t85 130t95 110.5l156 -66q-72 -109 -119 -224.5t-63 -244.5h-254z" />
<glyph unicode="&#x2019;" horiz-adv-x="514" d="M330 1083q72 109 119 224.5t63 244.5h254q-8 -76 -34.5 -150.5t-65.5 -143t-85 -130t-96 -110.5z" />
<glyph unicode="&#x201a;" horiz-adv-x="514" d="M16 -197q72 109 119.5 224.5t63.5 244.5h254q-8 -76 -35 -150.5t-66 -143t-85 -130t-95 -110.5z" />
<glyph unicode="&#x201c;" horiz-adv-x="921" d="M367 1038q8 76 34.5 151t65.5 143.5t85 130t95 110.5l156 -66q-72 -109 -119 -224.5t-63 -244.5h-254zM775 1038q8 76 34.5 151t65.5 143.5t85 130t95 110.5l156 -66q-72 -109 -119 -224.5t-63 -244.5h-254z" />
<glyph unicode="&#x201d;" horiz-adv-x="921" d="M329 1083q72 109 119.5 224.5t63.5 244.5h254q-8 -76 -35 -150.5t-66 -143t-85 -130t-95 -110.5zM737 1083q72 109 119.5 224.5t63.5 244.5h254q-8 -76 -35 -150.5t-66 -143t-85 -130t-95 -110.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="923" d="M16 -197q72 109 119.5 224.5t63.5 244.5h254q-8 -76 -35 -150.5t-66 -143t-85 -130t-95 -110.5zM426 -197q72 109 119.5 224.5t63.5 244.5h254q-8 -76 -35 -150.5t-66 -143t-85 -130t-95 -110.5z" />
<glyph unicode="&#x2022;" horiz-adv-x="757" d="M258 723q0 55 19.5 105.5t55.5 87t87 59t117 22.5q63 0 114 -22.5t88 -59t56.5 -87t19.5 -105.5t-19.5 -105.5t-56.5 -88t-88 -60.5t-114 -23q-66 0 -117 23t-87 60.5t-55.5 88t-19.5 105.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="2107" d="M219 117q0 86 53 134t121 48q66 0 107 -37t41 -108q0 -41 -15.5 -75t-40 -57.5t-56.5 -36t-64 -12.5q-66 0 -106 36t-40 108zM915 117q0 86 53 134t121 48q66 0 107 -37t41 -108q0 -41 -15.5 -75t-40 -57.5t-56.5 -36t-64 -12.5q-66 0 -106 36t-40 108zM1612 117 q0 86 53 134t121 48q66 0 107 -37t41 -108q0 -41 -15.5 -75t-40 -57.5t-56.5 -36t-64 -12.5q-66 0 -106 36t-40 108z" />
<glyph unicode="&#x202f;" horiz-adv-x="390" />
<glyph unicode="&#x2039;" horiz-adv-x="636" d="M190 594l433 459l155 -92l-321 -398l151 -377l-166 -77z" />
<glyph unicode="&#x203a;" horiz-adv-x="636" d="M137 201l322 397l-152 377l166 78l252 -486l-432 -458z" />
<glyph unicode="&#x205f;" horiz-adv-x="488" />
<glyph unicode="&#x20ac;" d="M203 455l41 176h147q12 84 35 170h-143l41 174h155q43 102 104.5 188t145.5 149.5t191.5 98.5t246.5 35q74 0 135.5 -12.5t124.5 -34.5l-98 -197q-43 16 -89 26.5t-107 10.5q-152 0 -247 -73.5t-153 -190.5h484l-76 -174h-471q-10 -41 -19.5 -84t-13.5 -86h430l-74 -176 h-366q2 -80 20.5 -133.5t52 -84t79.5 -44t102 -13.5q86 0 151.5 17.5t120.5 38.5l10 -201q-55 -25 -143 -44.5t-178 -19.5q-213 0 -334 116t-127 368h-178z" />
<glyph unicode="&#x2122;" horiz-adv-x="1703" d="M264 1251v168h617v-168h-213v-534h-191v534h-213zM930 717q20 260 33.5 427t29.5 275h191q37 -74 90 -178t106 -215q55 111 110.5 215.5t90.5 177.5h195q18 -109 29.5 -275.5t27.5 -426.5h-180q-4 109 -10.5 233.5t-14.5 229.5q-25 -41 -53.5 -94.5t-55 -105.5t-49 -98 t-34.5 -73h-107q-12 27 -34.5 72t-50 97t-56.5 105.5t-51 96.5q-8 -104 -14.5 -229.5t-10.5 -233.5h-182z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1075" d="M0 0v1075h1075v-1075h-1075z" />
<hkern u1="K" u2="&#xef;" k="-70" />
<hkern u1="T" u2="&#xef;" k="-94" />
<hkern u1="T" u2="&#xee;" k="-25" />
<hkern u1="V" u2="&#xef;" k="-94" />
<hkern u1="V" u2="&#xec;" k="-25" />
<hkern u1="W" u2="&#xef;" k="-35" />
<hkern u1="Y" u2="&#xef;" k="-94" />
<hkern u1="Y" u2="&#xec;" k="-25" />
<hkern u1="f" u2="&#xef;" k="-119" />
<hkern u1="f" u2="&#xec;" k="-94" />
<hkern u1="&#xdd;" u2="&#xef;" k="-94" />
<hkern u1="&#xdd;" u2="&#xec;" k="-25" />
<hkern u1="&#x178;" u2="&#xef;" k="-94" />
<hkern u1="&#x178;" u2="&#xec;" k="-25" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="backslash" 	k="23" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="x" 	k="-27" />
<hkern g1="b" 	g2="backslash" 	k="45" />
<hkern g1="b" 	g2="v" 	k="16" />
<hkern g1="b" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="b" 	g2="z" 	k="14" />
<hkern g1="b" 	g2="quoteright,quotedblright" 	k="100" />
<hkern g1="b" 	g2="quoteleft,quotedblleft" 	k="100" />
<hkern g1="b" 	g2="hyphen,uni00AD,endash,emdash" 	k="-16" />
<hkern g1="b" 	g2="asterisk" 	k="100" />
<hkern g1="b" 	g2="quotedbl,quotesingle" 	k="100" />
<hkern g1="c,ccedilla" 	g2="x" 	k="-27" />
<hkern g1="c,ccedilla" 	g2="y,yacute,ydieresis" 	k="-29" />
<hkern g1="c,ccedilla" 	g2="z" 	k="-29" />
<hkern g1="c,ccedilla" 	g2="hyphen,uni00AD,endash,emdash" 	k="23" />
<hkern g1="c,ccedilla" 	g2="parenright" 	k="-27" />
<hkern g1="c,ccedilla" 	g2="comma,period,ellipsis" 	k="-27" />
<hkern g1="c,ccedilla" 	g2="slash" 	k="-33" />
<hkern g1="c,ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="18" />
<hkern g1="c,ccedilla" 	g2="c,ccedilla" 	k="23" />
<hkern g1="c,ccedilla" 	g2="d" 	k="18" />
<hkern g1="c,ccedilla" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="23" />
<hkern g1="c,ccedilla" 	g2="g" 	k="18" />
<hkern g1="c,ccedilla" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="23" />
<hkern g1="c,ccedilla" 	g2="q" 	k="18" />
<hkern g1="c,ccedilla" 	g2="s" 	k="-25" />
<hkern g1="c,ccedilla" 	g2="w" 	k="-16" />
<hkern g1="c,ccedilla" 	g2="braceright" 	k="-16" />
<hkern g1="c,ccedilla" 	g2="eth" 	k="23" />
<hkern g1="d" 	g2="x" 	k="-29" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="backslash" 	k="23" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="y,yacute,ydieresis" 	k="-14" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="33" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="33" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="asterisk" 	k="45" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="33" />
<hkern g1="f" 	g2="backslash" 	k="-145" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-113" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-113" />
<hkern g1="f" 	g2="hyphen,uni00AD,endash,emdash" 	k="23" />
<hkern g1="f" 	g2="asterisk" 	k="-90" />
<hkern g1="f" 	g2="quotedbl,quotesingle" 	k="-113" />
<hkern g1="f" 	g2="parenright" 	k="-131" />
<hkern g1="f" 	g2="comma,period,ellipsis" 	k="94" />
<hkern g1="f" 	g2="w" 	k="-35" />
<hkern g1="f" 	g2="braceright" 	k="-113" />
<hkern g1="f" 	g2="question" 	k="-100" />
<hkern g1="f" 	g2="ampersand" 	k="23" />
<hkern g1="f" 	g2="bracketright" 	k="-123" />
<hkern g1="f" 	g2="j" 	k="-16" />
<hkern g1="f" 	g2="exclam" 	k="-63" />
<hkern g1="g" 	g2="backslash" 	k="23" />
<hkern g1="g" 	g2="j" 	k="-49" />
<hkern g1="h" 	g2="backslash" 	k="37" />
<hkern g1="h" 	g2="v" 	k="16" />
<hkern g1="h" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="h" 	g2="quoteright,quotedblright" 	k="90" />
<hkern g1="h" 	g2="quoteleft,quotedblleft" 	k="68" />
<hkern g1="h" 	g2="asterisk" 	k="90" />
<hkern g1="h" 	g2="quotedbl,quotesingle" 	k="90" />
<hkern g1="h" 	g2="w" 	k="16" />
<hkern g1="j" 	g2="j" 	k="-57" />
<hkern g1="k" 	g2="backslash" 	k="23" />
<hkern g1="k" 	g2="z" 	k="-39" />
<hkern g1="k" 	g2="hyphen,uni00AD,endash,emdash" 	k="49" />
<hkern g1="k" 	g2="slash" 	k="-27" />
<hkern g1="k" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="84" />
<hkern g1="k" 	g2="c,ccedilla" 	k="51" />
<hkern g1="k" 	g2="d" 	k="47" />
<hkern g1="k" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="51" />
<hkern g1="k" 	g2="g" 	k="47" />
<hkern g1="k" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="k" 	g2="q" 	k="47" />
<hkern g1="k" 	g2="s" 	k="-29" />
<hkern g1="k" 	g2="eth" 	k="51" />
<hkern g1="k" 	g2="question" 	k="41" />
<hkern g1="k" 	g2="l" 	k="31" />
<hkern g1="k" 	g2="t" 	k="27" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="27" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="68" />
<hkern g1="l,uniFB02,uniFB04" 	g2="x" 	k="-49" />
<hkern g1="l,uniFB02,uniFB04" 	g2="v" 	k="27" />
<hkern g1="l,uniFB02,uniFB04" 	g2="y,yacute,ydieresis" 	k="10" />
<hkern g1="l,uniFB02,uniFB04" 	g2="z" 	k="-29" />
<hkern g1="l,uniFB02,uniFB04" 	g2="slash" 	k="-27" />
<hkern g1="l,uniFB02,uniFB04" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="27" />
<hkern g1="l,uniFB02,uniFB04" 	g2="c,ccedilla" 	k="18" />
<hkern g1="l,uniFB02,uniFB04" 	g2="d" 	k="27" />
<hkern g1="l,uniFB02,uniFB04" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="18" />
<hkern g1="l,uniFB02,uniFB04" 	g2="g" 	k="27" />
<hkern g1="l,uniFB02,uniFB04" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="l,uniFB02,uniFB04" 	g2="q" 	k="27" />
<hkern g1="l,uniFB02,uniFB04" 	g2="w" 	k="23" />
<hkern g1="l,uniFB02,uniFB04" 	g2="eth" 	k="18" />
<hkern g1="m,n,ntilde" 	g2="backslash" 	k="37" />
<hkern g1="m,n,ntilde" 	g2="v" 	k="16" />
<hkern g1="m,n,ntilde" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="m,n,ntilde" 	g2="quoteright,quotedblright" 	k="90" />
<hkern g1="m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="68" />
<hkern g1="m,n,ntilde" 	g2="asterisk" 	k="82" />
<hkern g1="m,n,ntilde" 	g2="quotedbl,quotesingle" 	k="90" />
<hkern g1="m,n,ntilde" 	g2="w" 	k="16" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="backslash" 	k="45" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="v" 	k="16" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteright,quotedblright" 	k="100" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteleft,quotedblleft" 	k="100" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="hyphen,uni00AD,endash,emdash" 	k="-16" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="asterisk" 	k="106" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotedbl,quotesingle" 	k="100" />
<hkern g1="p" 	g2="backslash" 	k="45" />
<hkern g1="p" 	g2="v" 	k="16" />
<hkern g1="p" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="p" 	g2="z" 	k="14" />
<hkern g1="p" 	g2="quoteright,quotedblright" 	k="100" />
<hkern g1="p" 	g2="quoteleft,quotedblleft" 	k="100" />
<hkern g1="p" 	g2="hyphen,uni00AD,endash,emdash" 	k="-16" />
<hkern g1="p" 	g2="asterisk" 	k="100" />
<hkern g1="p" 	g2="quotedbl,quotesingle" 	k="100" />
<hkern g1="q" 	g2="j" 	k="-33" />
<hkern g1="r" 	g2="v" 	k="-14" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-23" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-23" />
<hkern g1="r" 	g2="hyphen,uni00AD,endash,emdash" 	k="23" />
<hkern g1="r" 	g2="asterisk" 	k="-23" />
<hkern g1="r" 	g2="quotedbl,quotesingle" 	k="-23" />
<hkern g1="r" 	g2="comma,period,ellipsis" 	k="100" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="14" />
<hkern g1="r" 	g2="d" 	k="14" />
<hkern g1="r" 	g2="g" 	k="14" />
<hkern g1="r" 	g2="q" 	k="14" />
<hkern g1="r" 	g2="w" 	k="-39" />
<hkern g1="s" 	g2="slash" 	k="-23" />
<hkern g1="t" 	g2="x" 	k="-59" />
<hkern g1="t" 	g2="z" 	k="-35" />
<hkern g1="t" 	g2="hyphen,uni00AD,endash,emdash" 	k="23" />
<hkern g1="t" 	g2="parenright" 	k="-27" />
<hkern g1="t" 	g2="comma,period,ellipsis" 	k="-27" />
<hkern g1="t" 	g2="slash" 	k="-33" />
<hkern g1="t" 	g2="s" 	k="-31" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="33" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="backslash" 	k="23" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="x" 	k="-29" />
<hkern g1="v" 	g2="x" 	k="-41" />
<hkern g1="v" 	g2="v" 	k="-37" />
<hkern g1="v" 	g2="y,yacute,ydieresis" 	k="-37" />
<hkern g1="v" 	g2="comma,period,ellipsis" 	k="39" />
<hkern g1="v" 	g2="slash" 	k="45" />
<hkern g1="v" 	g2="w" 	k="-29" />
<hkern g1="w" 	g2="x" 	k="-41" />
<hkern g1="w" 	g2="v" 	k="-37" />
<hkern g1="w" 	g2="y,yacute,ydieresis" 	k="-37" />
<hkern g1="w" 	g2="comma,period,ellipsis" 	k="39" />
<hkern g1="w" 	g2="slash" 	k="33" />
<hkern g1="w" 	g2="w" 	k="-29" />
<hkern g1="x" 	g2="x" 	k="-63" />
<hkern g1="x" 	g2="y,yacute,ydieresis" 	k="-25" />
<hkern g1="x" 	g2="z" 	k="-39" />
<hkern g1="x" 	g2="hyphen,uni00AD,endash,emdash" 	k="29" />
<hkern g1="x" 	g2="comma,period,ellipsis" 	k="-33" />
<hkern g1="x" 	g2="slash" 	k="-33" />
<hkern g1="x" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="18" />
<hkern g1="x" 	g2="d" 	k="18" />
<hkern g1="x" 	g2="g" 	k="18" />
<hkern g1="x" 	g2="q" 	k="18" />
<hkern g1="x" 	g2="s" 	k="-31" />
<hkern g1="x" 	g2="guillemotleft,guilsinglleft" 	k="45" />
<hkern g1="y,yacute,ydieresis" 	g2="x" 	k="-33" />
<hkern g1="y,yacute,ydieresis" 	g2="v" 	k="-29" />
<hkern g1="y,yacute,ydieresis" 	g2="y,yacute,ydieresis" 	k="-29" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,ellipsis" 	k="39" />
<hkern g1="y,yacute,ydieresis" 	g2="w" 	k="-18" />
<hkern g1="z" 	g2="x" 	k="-33" />
<hkern g1="z" 	g2="v" 	k="-29" />
<hkern g1="z" 	g2="y,yacute,ydieresis" 	k="-29" />
<hkern g1="z" 	g2="w" 	k="-18" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="23" />
<hkern g1="germandbls" 	g2="backslash" 	k="55" />
<hkern g1="germandbls" 	g2="v" 	k="78" />
<hkern g1="germandbls" 	g2="y,yacute,ydieresis" 	k="72" />
<hkern g1="germandbls" 	g2="quoteright,quotedblright" 	k="53" />
<hkern g1="germandbls" 	g2="quoteleft,quotedblleft" 	k="53" />
<hkern g1="germandbls" 	g2="asterisk" 	k="78" />
<hkern g1="germandbls" 	g2="quotedbl,quotesingle" 	k="53" />
<hkern g1="germandbls" 	g2="w" 	k="72" />
<hkern g1="germandbls" 	g2="j" 	k="-49" />
<hkern g1="germandbls" 	g2="guillemotleft,guilsinglleft" 	k="45" />
<hkern g1="thorn" 	g2="backslash" 	k="45" />
<hkern g1="thorn" 	g2="v" 	k="16" />
<hkern g1="thorn" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="thorn" 	g2="z" 	k="14" />
<hkern g1="thorn" 	g2="quoteright,quotedblright" 	k="100" />
<hkern g1="thorn" 	g2="quoteleft,quotedblleft" 	k="100" />
<hkern g1="thorn" 	g2="hyphen,uni00AD,endash,emdash" 	k="-16" />
<hkern g1="thorn" 	g2="asterisk" 	k="100" />
<hkern g1="thorn" 	g2="quotedbl,quotesingle" 	k="100" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="113" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v" 	k="74" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="y,yacute,ydieresis" 	k="35" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="137" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="158" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="hyphen,uni00AD,endash,emdash" 	k="35" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk" 	k="193" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle" 	k="137" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="parenright" 	k="-23" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="comma,period,ellipsis" 	k="-33" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="slash" 	k="-33" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="l" 	k="25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guillemotleft,guilsinglleft" 	k="27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="203" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="55" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="117" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="W" 	k="68" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="174" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="colon,semicolon" 	k="-33" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="exclamdown" 	k="-18" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-63" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="J" 	k="-45" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="S" 	k="-31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="X" 	k="-57" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Z" 	k="-35" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="AE" 	k="-74" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,Ccedilla" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="G" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Q" 	k="61" />
<hkern g1="B" 	g2="asterisk" 	k="23" />
<hkern g1="B" 	g2="T" 	k="61" />
<hkern g1="B" 	g2="V" 	k="18" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="55" />
<hkern g1="C,Ccedilla" 	g2="hyphen,uni00AD,endash,emdash" 	k="139" />
<hkern g1="C,Ccedilla" 	g2="parenright" 	k="-23" />
<hkern g1="C,Ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="55" />
<hkern g1="C,Ccedilla" 	g2="c,ccedilla" 	k="55" />
<hkern g1="C,Ccedilla" 	g2="d" 	k="55" />
<hkern g1="C,Ccedilla" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="55" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="55" />
<hkern g1="C,Ccedilla" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="55" />
<hkern g1="C,Ccedilla" 	g2="q" 	k="55" />
<hkern g1="C,Ccedilla" 	g2="eth" 	k="55" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="92" />
<hkern g1="C,Ccedilla" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="57" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="-25" />
<hkern g1="C,Ccedilla" 	g2="V" 	k="-27" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="-27" />
<hkern g1="C,Ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-39" />
<hkern g1="C,Ccedilla" 	g2="J" 	k="-29" />
<hkern g1="C,Ccedilla" 	g2="X" 	k="-33" />
<hkern g1="C,Ccedilla" 	g2="AE" 	k="-47" />
<hkern g1="C,Ccedilla" 	g2="C,Ccedilla" 	k="57" />
<hkern g1="C,Ccedilla" 	g2="G" 	k="57" />
<hkern g1="C,Ccedilla" 	g2="Q" 	k="57" />
<hkern g1="D,Eth" 	g2="quoteright,quotedblright" 	k="49" />
<hkern g1="D,Eth" 	g2="quoteleft,quotedblleft" 	k="63" />
<hkern g1="D,Eth" 	g2="hyphen,uni00AD,endash,emdash" 	k="-27" />
<hkern g1="D,Eth" 	g2="asterisk" 	k="33" />
<hkern g1="D,Eth" 	g2="quotedbl,quotesingle" 	k="49" />
<hkern g1="D,Eth" 	g2="comma,period,ellipsis" 	k="27" />
<hkern g1="D,Eth" 	g2="slash" 	k="61" />
<hkern g1="D,Eth" 	g2="question" 	k="33" />
<hkern g1="D,Eth" 	g2="T" 	k="129" />
<hkern g1="D,Eth" 	g2="V" 	k="47" />
<hkern g1="D,Eth" 	g2="W" 	k="29" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="90" />
<hkern g1="D,Eth" 	g2="J" 	k="27" />
<hkern g1="D,Eth" 	g2="X" 	k="68" />
<hkern g1="D,Eth" 	g2="AE" 	k="45" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,ccedilla" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="d" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="g" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="q" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="eth" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="J" 	k="-23" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="X" 	k="-18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="AE" 	k="-33" />
<hkern g1="F" 	g2="z" 	k="23" />
<hkern g1="F" 	g2="comma,period,ellipsis" 	k="113" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="27" />
<hkern g1="F" 	g2="c,ccedilla" 	k="27" />
<hkern g1="F" 	g2="d" 	k="27" />
<hkern g1="F" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="27" />
<hkern g1="F" 	g2="g" 	k="27" />
<hkern g1="F" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="F" 	g2="q" 	k="27" />
<hkern g1="F" 	g2="s" 	k="16" />
<hkern g1="F" 	g2="eth" 	k="27" />
<hkern g1="F" 	g2="ampersand" 	k="55" />
<hkern g1="F" 	g2="T" 	k="-23" />
<hkern g1="F" 	g2="V" 	k="-37" />
<hkern g1="F" 	g2="Y,Yacute,Ydieresis" 	k="-39" />
<hkern g1="F" 	g2="colon,semicolon" 	k="33" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="96" />
<hkern g1="F" 	g2="J" 	k="205" />
<hkern g1="F" 	g2="AE" 	k="145" />
<hkern g1="G" 	g2="T" 	k="25" />
<hkern g1="J" 	g2="J" 	k="41" />
<hkern g1="K" 	g2="v" 	k="61" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="39" />
<hkern g1="K" 	g2="hyphen,uni00AD,endash,emdash" 	k="117" />
<hkern g1="K" 	g2="asterisk" 	k="23" />
<hkern g1="K" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="72" />
<hkern g1="K" 	g2="c,ccedilla" 	k="72" />
<hkern g1="K" 	g2="d" 	k="72" />
<hkern g1="K" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="72" />
<hkern g1="K" 	g2="g" 	k="72" />
<hkern g1="K" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="72" />
<hkern g1="K" 	g2="q" 	k="72" />
<hkern g1="K" 	g2="w" 	k="55" />
<hkern g1="K" 	g2="eth" 	k="72" />
<hkern g1="K" 	g2="ampersand" 	k="49" />
<hkern g1="K" 	g2="t" 	k="23" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="72" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="121" />
<hkern g1="K" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="104" />
<hkern g1="K" 	g2="T" 	k="-31" />
<hkern g1="K" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="49" />
<hkern g1="K" 	g2="V" 	k="-47" />
<hkern g1="K" 	g2="W" 	k="-23" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="-49" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-72" />
<hkern g1="K" 	g2="J" 	k="-35" />
<hkern g1="K" 	g2="S" 	k="-27" />
<hkern g1="K" 	g2="X" 	k="-63" />
<hkern g1="K" 	g2="Z" 	k="-25" />
<hkern g1="K" 	g2="AE" 	k="-78" />
<hkern g1="K" 	g2="C,Ccedilla" 	k="104" />
<hkern g1="K" 	g2="G" 	k="104" />
<hkern g1="K" 	g2="Q" 	k="104" />
<hkern g1="K" 	g2="guillemotright,guilsinglright" 	k="51" />
<hkern g1="K" 	g2="at" 	k="33" />
<hkern g1="L" 	g2="backslash" 	k="199" />
<hkern g1="L" 	g2="v" 	k="139" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="84" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="244" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="260" />
<hkern g1="L" 	g2="hyphen,uni00AD,endash,emdash" 	k="180" />
<hkern g1="L" 	g2="asterisk" 	k="264" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="244" />
<hkern g1="L" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="94" />
<hkern g1="L" 	g2="c,ccedilla" 	k="94" />
<hkern g1="L" 	g2="d" 	k="94" />
<hkern g1="L" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="94" />
<hkern g1="L" 	g2="g" 	k="94" />
<hkern g1="L" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="94" />
<hkern g1="L" 	g2="q" 	k="94" />
<hkern g1="L" 	g2="w" 	k="119" />
<hkern g1="L" 	g2="eth" 	k="94" />
<hkern g1="L" 	g2="question" 	k="63" />
<hkern g1="L" 	g2="ampersand" 	k="33" />
<hkern g1="L" 	g2="t" 	k="55" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="63" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="158" />
<hkern g1="L" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="131" />
<hkern g1="L" 	g2="T" 	k="281" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="115" />
<hkern g1="L" 	g2="V" 	k="240" />
<hkern g1="L" 	g2="W" 	k="150" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="283" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-49" />
<hkern g1="L" 	g2="J" 	k="-47" />
<hkern g1="L" 	g2="S" 	k="-27" />
<hkern g1="L" 	g2="X" 	k="-45" />
<hkern g1="L" 	g2="Z" 	k="-23" />
<hkern g1="L" 	g2="AE" 	k="-57" />
<hkern g1="L" 	g2="C,Ccedilla" 	k="131" />
<hkern g1="L" 	g2="G" 	k="131" />
<hkern g1="L" 	g2="Q" 	k="131" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="25" />
<hkern g1="L" 	g2="at" 	k="78" />
<hkern g1="M" 	g2="T" 	k="23" />
<hkern g1="M" 	g2="V" 	k="18" />
<hkern g1="M" 	g2="Y,Yacute,Ydieresis" 	k="37" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="49" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="63" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="hyphen,uni00AD,endash,emdash" 	k="-27" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="asterisk" 	k="33" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="49" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,ellipsis" 	k="27" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="61" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="question" 	k="41" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="106" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="47" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="29" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="90" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="27" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="68" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="45" />
<hkern g1="P" 	g2="x" 	k="-35" />
<hkern g1="P" 	g2="v" 	k="-45" />
<hkern g1="P" 	g2="y,yacute,ydieresis" 	k="-53" />
<hkern g1="P" 	g2="comma,period,ellipsis" 	k="123" />
<hkern g1="P" 	g2="slash" 	k="92" />
<hkern g1="P" 	g2="w" 	k="-33" />
<hkern g1="P" 	g2="question" 	k="49" />
<hkern g1="P" 	g2="ampersand" 	k="23" />
<hkern g1="P" 	g2="T" 	k="35" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="106" />
<hkern g1="P" 	g2="J" 	k="190" />
<hkern g1="P" 	g2="X" 	k="76" />
<hkern g1="P" 	g2="AE" 	k="125" />
<hkern g1="Q" 	g2="quoteright,quotedblright" 	k="49" />
<hkern g1="Q" 	g2="quoteleft,quotedblleft" 	k="63" />
<hkern g1="Q" 	g2="hyphen,uni00AD,endash,emdash" 	k="-27" />
<hkern g1="Q" 	g2="asterisk" 	k="33" />
<hkern g1="Q" 	g2="quotedbl,quotesingle" 	k="49" />
<hkern g1="Q" 	g2="comma,period,ellipsis" 	k="27" />
<hkern g1="Q" 	g2="slash" 	k="61" />
<hkern g1="Q" 	g2="question" 	k="29" />
<hkern g1="Q" 	g2="T" 	k="106" />
<hkern g1="Q" 	g2="V" 	k="47" />
<hkern g1="Q" 	g2="W" 	k="29" />
<hkern g1="Q" 	g2="Y,Yacute,Ydieresis" 	k="90" />
<hkern g1="Q" 	g2="J" 	k="27" />
<hkern g1="Q" 	g2="X" 	k="68" />
<hkern g1="Q" 	g2="AE" 	k="45" />
<hkern g1="R" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="23" />
<hkern g1="R" 	g2="c,ccedilla" 	k="23" />
<hkern g1="R" 	g2="d" 	k="23" />
<hkern g1="R" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="23" />
<hkern g1="R" 	g2="g" 	k="23" />
<hkern g1="R" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="23" />
<hkern g1="R" 	g2="q" 	k="23" />
<hkern g1="R" 	g2="eth" 	k="23" />
<hkern g1="R" 	g2="question" 	k="18" />
<hkern g1="R" 	g2="ampersand" 	k="23" />
<hkern g1="R" 	g2="T" 	k="61" />
<hkern g1="R" 	g2="V" 	k="27" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="49" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-47" />
<hkern g1="R" 	g2="J" 	k="-18" />
<hkern g1="R" 	g2="X" 	k="-41" />
<hkern g1="R" 	g2="Z" 	k="-16" />
<hkern g1="R" 	g2="AE" 	k="-55" />
<hkern g1="S" 	g2="v" 	k="51" />
<hkern g1="S" 	g2="y,yacute,ydieresis" 	k="45" />
<hkern g1="S" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="S" 	g2="w" 	k="49" />
<hkern g1="T" 	g2="backslash" 	k="-55" />
<hkern g1="T" 	g2="x" 	k="35" />
<hkern g1="T" 	g2="v" 	k="39" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="39" />
<hkern g1="T" 	g2="z" 	k="80" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="-23" />
<hkern g1="T" 	g2="quoteleft,quotedblleft" 	k="-23" />
<hkern g1="T" 	g2="hyphen,uni00AD,endash,emdash" 	k="117" />
<hkern g1="T" 	g2="quotedbl,quotesingle" 	k="-23" />
<hkern g1="T" 	g2="parenright" 	k="-23" />
<hkern g1="T" 	g2="comma,period,ellipsis" 	k="123" />
<hkern g1="T" 	g2="slash" 	k="119" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="139" />
<hkern g1="T" 	g2="c,ccedilla" 	k="135" />
<hkern g1="T" 	g2="d" 	k="139" />
<hkern g1="T" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="135" />
<hkern g1="T" 	g2="g" 	k="139" />
<hkern g1="T" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="135" />
<hkern g1="T" 	g2="q" 	k="139" />
<hkern g1="T" 	g2="s" 	k="109" />
<hkern g1="T" 	g2="w" 	k="33" />
<hkern g1="T" 	g2="eth" 	k="135" />
<hkern g1="T" 	g2="question" 	k="-35" />
<hkern g1="T" 	g2="ampersand" 	k="49" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="106" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="125" />
<hkern g1="T" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="53" />
<hkern g1="T" 	g2="T" 	k="-39" />
<hkern g1="T" 	g2="V" 	k="-55" />
<hkern g1="T" 	g2="W" 	k="-31" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-57" />
<hkern g1="T" 	g2="colon,semicolon" 	k="74" />
<hkern g1="T" 	g2="exclamdown" 	k="74" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="152" />
<hkern g1="T" 	g2="J" 	k="215" />
<hkern g1="T" 	g2="X" 	k="-25" />
<hkern g1="T" 	g2="AE" 	k="160" />
<hkern g1="T" 	g2="C,Ccedilla" 	k="53" />
<hkern g1="T" 	g2="G" 	k="53" />
<hkern g1="T" 	g2="Q" 	k="53" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="90" />
<hkern g1="T" 	g2="at" 	k="86" />
<hkern g1="T" 	g2="M" 	k="14" />
<hkern g1="T" 	g2="m,n,ntilde" 	k="109" />
<hkern g1="T" 	g2="p" 	k="109" />
<hkern g1="T" 	g2="r" 	k="109" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="23" />
<hkern g1="V" 	g2="backslash" 	k="-39" />
<hkern g1="V" 	g2="z" 	k="27" />
<hkern g1="V" 	g2="quoteleft,quotedblleft" 	k="-14" />
<hkern g1="V" 	g2="hyphen,uni00AD,endash,emdash" 	k="27" />
<hkern g1="V" 	g2="comma,period,ellipsis" 	k="104" />
<hkern g1="V" 	g2="slash" 	k="68" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="82" />
<hkern g1="V" 	g2="c,ccedilla" 	k="61" />
<hkern g1="V" 	g2="d" 	k="82" />
<hkern g1="V" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="61" />
<hkern g1="V" 	g2="g" 	k="82" />
<hkern g1="V" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="V" 	g2="q" 	k="82" />
<hkern g1="V" 	g2="eth" 	k="61" />
<hkern g1="V" 	g2="question" 	k="-49" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="37" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="23" />
<hkern g1="V" 	g2="T" 	k="-49" />
<hkern g1="V" 	g2="V" 	k="-63" />
<hkern g1="V" 	g2="W" 	k="-37" />
<hkern g1="V" 	g2="Y,Yacute,Ydieresis" 	k="-68" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="94" />
<hkern g1="V" 	g2="J" 	k="145" />
<hkern g1="V" 	g2="X" 	k="-33" />
<hkern g1="V" 	g2="AE" 	k="123" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="23" />
<hkern g1="V" 	g2="m,n,ntilde" 	k="41" />
<hkern g1="V" 	g2="p" 	k="41" />
<hkern g1="V" 	g2="r" 	k="41" />
<hkern g1="W" 	g2="backslash" 	k="-16" />
<hkern g1="W" 	g2="comma,period,ellipsis" 	k="35" />
<hkern g1="W" 	g2="slash" 	k="35" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="23" />
<hkern g1="W" 	g2="c,ccedilla" 	k="14" />
<hkern g1="W" 	g2="d" 	k="23" />
<hkern g1="W" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="14" />
<hkern g1="W" 	g2="g" 	k="23" />
<hkern g1="W" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="W" 	g2="q" 	k="23" />
<hkern g1="W" 	g2="eth" 	k="14" />
<hkern g1="W" 	g2="T" 	k="-25" />
<hkern g1="W" 	g2="V" 	k="-39" />
<hkern g1="W" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="37" />
<hkern g1="W" 	g2="J" 	k="72" />
<hkern g1="W" 	g2="AE" 	k="53" />
<hkern g1="X" 	g2="v" 	k="39" />
<hkern g1="X" 	g2="y,yacute,ydieresis" 	k="27" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="45" />
<hkern g1="X" 	g2="hyphen,uni00AD,endash,emdash" 	k="49" />
<hkern g1="X" 	g2="comma,period,ellipsis" 	k="-33" />
<hkern g1="X" 	g2="slash" 	k="-27" />
<hkern g1="X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="51" />
<hkern g1="X" 	g2="c,ccedilla" 	k="51" />
<hkern g1="X" 	g2="d" 	k="51" />
<hkern g1="X" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="51" />
<hkern g1="X" 	g2="g" 	k="51" />
<hkern g1="X" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="X" 	g2="q" 	k="51" />
<hkern g1="X" 	g2="w" 	k="33" />
<hkern g1="X" 	g2="eth" 	k="51" />
<hkern g1="X" 	g2="ampersand" 	k="16" />
<hkern g1="X" 	g2="t" 	k="27" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="53" />
<hkern g1="X" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="X" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="74" />
<hkern g1="X" 	g2="V" 	k="-25" />
<hkern g1="X" 	g2="Y,Yacute,Ydieresis" 	k="-27" />
<hkern g1="X" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-51" />
<hkern g1="X" 	g2="J" 	k="-23" />
<hkern g1="X" 	g2="X" 	k="-45" />
<hkern g1="X" 	g2="Z" 	k="-23" />
<hkern g1="X" 	g2="AE" 	k="-57" />
<hkern g1="X" 	g2="C,Ccedilla" 	k="74" />
<hkern g1="X" 	g2="G" 	k="74" />
<hkern g1="X" 	g2="Q" 	k="74" />
<hkern g1="X" 	g2="guillemotright,guilsinglright" 	k="31" />
<hkern g1="X" 	g2="at" 	k="29" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="backslash" 	k="-47" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="16" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="53" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="76" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,ellipsis" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="80" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="158" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="129" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="80" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="question" 	k="-49" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="ampersand" 	k="45" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="57" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="94" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="47" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-53" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="V" 	k="-70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="W" 	k="-45" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="39" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="exclamdown" 	k="39" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="145" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="197" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="X" 	k="-37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="158" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="39" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="at" 	k="45" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="M" 	k="23" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,ntilde" 	k="68" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="-72" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,Ccedilla" 	k="47" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="G" 	k="47" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Q" 	k="47" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,ccedilla" 	k="129" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d" 	k="158" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="129" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="158" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="p" 	k="68" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="q" 	k="158" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="r" 	k="68" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="eth" 	k="129" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="-33" />
<hkern g1="Z" 	g2="C,Ccedilla" 	k="33" />
<hkern g1="Z" 	g2="G" 	k="33" />
<hkern g1="Z" 	g2="Q" 	k="33" />
<hkern g1="Z" 	g2="c,ccedilla" 	k="55" />
<hkern g1="Z" 	g2="d" 	k="55" />
<hkern g1="Z" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="55" />
<hkern g1="Z" 	g2="g" 	k="55" />
<hkern g1="Z" 	g2="q" 	k="55" />
<hkern g1="Z" 	g2="eth" 	k="55" />
<hkern g1="Z" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="33" />
<hkern g1="Z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="55" />
<hkern g1="Z" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="55" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="27" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="49" />
<hkern g1="Z" 	g2="guillemotright,guilsinglright" 	k="18" />
<hkern g1="Z" 	g2="hyphen,uni00AD,endash,emdash" 	k="104" />
<hkern g1="Z" 	g2="ampersand" 	k="33" />
<hkern g1="Z" 	g2="at" 	k="23" />
<hkern g1="Z" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-33" />
<hkern g1="Z" 	g2="J" 	k="-31" />
<hkern g1="Z" 	g2="T" 	k="-16" />
<hkern g1="Z" 	g2="V" 	k="-31" />
<hkern g1="Z" 	g2="X" 	k="-29" />
<hkern g1="Z" 	g2="AE" 	k="-41" />
<hkern g1="Thorn" 	g2="Y,Yacute,Ydieresis" 	k="104" />
<hkern g1="Thorn" 	g2="hyphen,uni00AD,endash,emdash" 	k="-35" />
<hkern g1="Thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="33" />
<hkern g1="Thorn" 	g2="J" 	k="98" />
<hkern g1="Thorn" 	g2="T" 	k="145" />
<hkern g1="Thorn" 	g2="V" 	k="57" />
<hkern g1="Thorn" 	g2="X" 	k="86" />
<hkern g1="Thorn" 	g2="Z" 	k="49" />
<hkern g1="Thorn" 	g2="comma,period,ellipsis" 	k="55" />
<hkern g1="Thorn" 	g2="quoteright,quotedblright" 	k="74" />
<hkern g1="Thorn" 	g2="quoteleft,quotedblleft" 	k="82" />
<hkern g1="Thorn" 	g2="backslash" 	k="51" />
<hkern g1="Thorn" 	g2="asterisk" 	k="45" />
<hkern g1="Thorn" 	g2="quotedbl,quotesingle" 	k="74" />
<hkern g1="Thorn" 	g2="question" 	k="90" />
<hkern g1="parenleft" 	g2="Y,Yacute,Ydieresis" 	k="-33" />
<hkern g1="parenleft" 	g2="C,Ccedilla" 	k="23" />
<hkern g1="parenleft" 	g2="G" 	k="23" />
<hkern g1="parenleft" 	g2="Q" 	k="23" />
<hkern g1="parenleft" 	g2="c,ccedilla" 	k="33" />
<hkern g1="parenleft" 	g2="d" 	k="39" />
<hkern g1="parenleft" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="33" />
<hkern g1="parenleft" 	g2="g" 	k="39" />
<hkern g1="parenleft" 	g2="q" 	k="39" />
<hkern g1="parenleft" 	g2="eth" 	k="33" />
<hkern g1="parenleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="23" />
<hkern g1="parenleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="39" />
<hkern g1="parenleft" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="33" />
<hkern g1="parenleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="23" />
<hkern g1="parenleft" 	g2="T" 	k="-23" />
<hkern g1="parenleft" 	g2="V" 	k="-33" />
<hkern g1="parenleft" 	g2="AE" 	k="47" />
<hkern g1="parenleft" 	g2="y,yacute,ydieresis" 	k="-45" />
<hkern g1="parenleft" 	g2="j" 	k="-203" />
<hkern g1="parenleft" 	g2="parenright" 	k="-164" />
<hkern g1="parenright" 	g2="Y,Yacute,Ydieresis" 	k="92" />
<hkern g1="parenright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="55" />
<hkern g1="parenright" 	g2="J" 	k="43" />
<hkern g1="parenright" 	g2="T" 	k="86" />
<hkern g1="parenright" 	g2="V" 	k="55" />
<hkern g1="parenright" 	g2="X" 	k="57" />
<hkern g1="parenright" 	g2="Z" 	k="55" />
<hkern g1="parenright" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="parenright" 	g2="W" 	k="31" />
<hkern g1="bracketleft" 	g2="Y,Yacute,Ydieresis" 	k="-23" />
<hkern g1="bracketleft" 	g2="y,yacute,ydieresis" 	k="-84" />
<hkern g1="bracketleft" 	g2="j" 	k="-213" />
<hkern g1="bracketleft" 	g2="bracketright" 	k="-164" />
<hkern g1="braceleft" 	g2="AE" 	k="55" />
<hkern g1="braceleft" 	g2="y,yacute,ydieresis" 	k="-84" />
<hkern g1="braceleft" 	g2="j" 	k="-205" />
<hkern g1="braceleft" 	g2="braceright" 	k="-164" />
<hkern g1="asterisk" 	g2="Y,Yacute,Ydieresis" 	k="23" />
<hkern g1="asterisk" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="55" />
<hkern g1="asterisk" 	g2="J" 	k="252" />
<hkern g1="asterisk" 	g2="T" 	k="23" />
<hkern g1="asterisk" 	g2="X" 	k="23" />
<hkern g1="asterisk" 	g2="AE" 	k="135" />
<hkern g1="asterisk" 	g2="y,yacute,ydieresis" 	k="-68" />
<hkern g1="asterisk" 	g2="v" 	k="-68" />
<hkern g1="asterisk" 	g2="w" 	k="-45" />
<hkern g1="asterisk" 	g2="x" 	k="-68" />
<hkern g1="quotedbl,quotesingle" 	g2="Y,Yacute,Ydieresis" 	k="-23" />
<hkern g1="quotedbl,quotesingle" 	g2="C,Ccedilla" 	k="14" />
<hkern g1="quotedbl,quotesingle" 	g2="G" 	k="14" />
<hkern g1="quotedbl,quotesingle" 	g2="Q" 	k="14" />
<hkern g1="quotedbl,quotesingle" 	g2="c,ccedilla" 	k="68" />
<hkern g1="quotedbl,quotesingle" 	g2="d" 	k="78" />
<hkern g1="quotedbl,quotesingle" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="68" />
<hkern g1="quotedbl,quotesingle" 	g2="g" 	k="78" />
<hkern g1="quotedbl,quotesingle" 	g2="q" 	k="78" />
<hkern g1="quotedbl,quotesingle" 	g2="eth" 	k="68" />
<hkern g1="quotedbl,quotesingle" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="quotedbl,quotesingle" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="78" />
<hkern g1="quotedbl,quotesingle" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="68" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="115" />
<hkern g1="quotedbl,quotesingle" 	g2="T" 	k="-23" />
<hkern g1="quotedbl,quotesingle" 	g2="V" 	k="-23" />
<hkern g1="quotedbl,quotesingle" 	g2="AE" 	k="242" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-23" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,Ccedilla" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="G" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="Q" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,ccedilla" 	k="33" />
<hkern g1="quoteleft,quotedblleft" 	g2="d" 	k="55" />
<hkern g1="quoteleft,quotedblleft" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="33" />
<hkern g1="quoteleft,quotedblleft" 	g2="g" 	k="55" />
<hkern g1="quoteleft,quotedblleft" 	g2="q" 	k="55" />
<hkern g1="quoteleft,quotedblleft" 	g2="eth" 	k="33" />
<hkern g1="quoteleft,quotedblleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="55" />
<hkern g1="quoteleft,quotedblleft" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="33" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="115" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="197" />
<hkern g1="quoteleft,quotedblleft" 	g2="T" 	k="-23" />
<hkern g1="quoteleft,quotedblleft" 	g2="V" 	k="-23" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="242" />
<hkern g1="quoteleft,quotedblleft" 	g2="W" 	k="-23" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="-23" />
<hkern g1="quoteright,quotedblright" 	g2="C,Ccedilla" 	k="14" />
<hkern g1="quoteright,quotedblright" 	g2="G" 	k="14" />
<hkern g1="quoteright,quotedblright" 	g2="Q" 	k="14" />
<hkern g1="quoteright,quotedblright" 	g2="c,ccedilla" 	k="68" />
<hkern g1="quoteright,quotedblright" 	g2="d" 	k="78" />
<hkern g1="quoteright,quotedblright" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="68" />
<hkern g1="quoteright,quotedblright" 	g2="g" 	k="78" />
<hkern g1="quoteright,quotedblright" 	g2="q" 	k="78" />
<hkern g1="quoteright,quotedblright" 	g2="r" 	k="76" />
<hkern g1="quoteright,quotedblright" 	g2="eth" 	k="68" />
<hkern g1="quoteright,quotedblright" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="78" />
<hkern g1="quoteright,quotedblright" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="68" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="115" />
<hkern g1="quoteright,quotedblright" 	g2="T" 	k="-23" />
<hkern g1="quoteright,quotedblright" 	g2="V" 	k="-23" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="242" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="203" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,Ccedilla" 	k="68" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="G" 	k="68" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Q" 	k="68" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="68" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="203" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="V" 	k="180" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="j" 	k="-100" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="90" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="v" 	k="55" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="w" 	k="45" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="s" 	k="-23" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="90" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="113" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="33" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="X" 	k="16" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="127" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="90" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="137" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="53" />
<hkern g1="guillemotright,guilsinglright" 	g2="X" 	k="35" />
<hkern g1="guillemotright,guilsinglright" 	g2="AE" 	k="68" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="23" />
<hkern g1="guillemotright,guilsinglright" 	g2="x" 	k="33" />
<hkern g1="guillemotright,guilsinglright" 	g2="S" 	k="33" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="92" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="C,Ccedilla" 	k="-27" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="G" 	k="-27" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Q" 	k="-27" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="c,ccedilla" 	k="-16" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="d" 	k="-16" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="-16" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="g" 	k="-16" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="q" 	k="-16" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="eth" 	k="-16" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-27" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-16" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-16" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="23" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="J" 	k="162" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="T" 	k="135" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="V" 	k="57" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="X" 	k="49" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="AE" 	k="59" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="x" 	k="29" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="S" 	k="55" />
<hkern g1="comma,period,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="190" />
<hkern g1="comma,period,ellipsis" 	g2="C,Ccedilla" 	k="100" />
<hkern g1="comma,period,ellipsis" 	g2="G" 	k="100" />
<hkern g1="comma,period,ellipsis" 	g2="Q" 	k="100" />
<hkern g1="comma,period,ellipsis" 	g2="c,ccedilla" 	k="68" />
<hkern g1="comma,period,ellipsis" 	g2="d" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="68" />
<hkern g1="comma,period,ellipsis" 	g2="g" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="q" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="eth" 	k="68" />
<hkern g1="comma,period,ellipsis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="100" />
<hkern g1="comma,period,ellipsis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="68" />
<hkern g1="comma,period,ellipsis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="33" />
<hkern g1="comma,period,ellipsis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-33" />
<hkern g1="comma,period,ellipsis" 	g2="T" 	k="190" />
<hkern g1="comma,period,ellipsis" 	g2="V" 	k="180" />
<hkern g1="comma,period,ellipsis" 	g2="AE" 	k="-55" />
<hkern g1="comma,period,ellipsis" 	g2="y,yacute,ydieresis" 	k="49" />
<hkern g1="comma,period,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="55" />
<hkern g1="comma,period,ellipsis" 	g2="W" 	k="90" />
<hkern g1="comma,period,ellipsis" 	g2="v" 	k="119" />
<hkern g1="comma,period,ellipsis" 	g2="w" 	k="90" />
<hkern g1="comma,period,ellipsis" 	g2="l" 	k="23" />
<hkern g1="comma,period,ellipsis" 	g2="t" 	k="33" />
<hkern g1="comma,period,ellipsis" 	g2="zero" 	k="33" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="colon,semicolon" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-27" />
<hkern g1="colon,semicolon" 	g2="T" 	k="98" />
<hkern g1="colon,semicolon" 	g2="V" 	k="39" />
<hkern g1="colon,semicolon" 	g2="AE" 	k="-23" />
<hkern g1="backslash" 	g2="Y,Yacute,Ydieresis" 	k="123" />
<hkern g1="backslash" 	g2="C,Ccedilla" 	k="76" />
<hkern g1="backslash" 	g2="G" 	k="76" />
<hkern g1="backslash" 	g2="Q" 	k="76" />
<hkern g1="backslash" 	g2="d" 	k="-23" />
<hkern g1="backslash" 	g2="g" 	k="-23" />
<hkern g1="backslash" 	g2="p" 	k="-29" />
<hkern g1="backslash" 	g2="q" 	k="-23" />
<hkern g1="backslash" 	g2="r" 	k="-29" />
<hkern g1="backslash" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="76" />
<hkern g1="backslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-23" />
<hkern g1="backslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-33" />
<hkern g1="backslash" 	g2="T" 	k="180" />
<hkern g1="backslash" 	g2="V" 	k="113" />
<hkern g1="backslash" 	g2="AE" 	k="-59" />
<hkern g1="backslash" 	g2="y,yacute,ydieresis" 	k="-139" />
<hkern g1="backslash" 	g2="j" 	k="-281" />
<hkern g1="backslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="72" />
<hkern g1="backslash" 	g2="W" 	k="68" />
<hkern g1="backslash" 	g2="v" 	k="33" />
<hkern g1="backslash" 	g2="w" 	k="33" />
<hkern g1="backslash" 	g2="x" 	k="-23" />
<hkern g1="backslash" 	g2="m,n,ntilde" 	k="-29" />
<hkern g1="backslash" 	g2="germandbls" 	k="-29" />
<hkern g1="backslash" 	g2="thorn" 	k="-29" />
<hkern g1="backslash" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="-29" />
<hkern g1="backslash" 	g2="H" 	k="39" />
<hkern g1="backslash" 	g2="B" 	k="39" />
<hkern g1="backslash" 	g2="D,Eth" 	k="39" />
<hkern g1="backslash" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="39" />
<hkern g1="backslash" 	g2="F" 	k="39" />
<hkern g1="backslash" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="39" />
<hkern g1="backslash" 	g2="L" 	k="39" />
<hkern g1="backslash" 	g2="M" 	k="16" />
<hkern g1="backslash" 	g2="N,Ntilde" 	k="39" />
<hkern g1="backslash" 	g2="P" 	k="39" />
<hkern g1="backslash" 	g2="R" 	k="39" />
<hkern g1="backslash" 	g2="Thorn" 	k="39" />
<hkern g1="slash" 	g2="Y,Yacute,Ydieresis" 	k="-94" />
<hkern g1="slash" 	g2="c,ccedilla" 	k="39" />
<hkern g1="slash" 	g2="d" 	k="45" />
<hkern g1="slash" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="39" />
<hkern g1="slash" 	g2="g" 	k="45" />
<hkern g1="slash" 	g2="q" 	k="45" />
<hkern g1="slash" 	g2="eth" 	k="39" />
<hkern g1="slash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="45" />
<hkern g1="slash" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="39" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="72" />
<hkern g1="slash" 	g2="J" 	k="102" />
<hkern g1="slash" 	g2="T" 	k="-84" />
<hkern g1="slash" 	g2="V" 	k="-100" />
<hkern g1="slash" 	g2="X" 	k="-45" />
<hkern g1="slash" 	g2="AE" 	k="199" />
<hkern g1="slash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-27" />
<hkern g1="slash" 	g2="W" 	k="-72" />
<hkern g1="slash" 	g2="l" 	k="-33" />
<hkern g1="slash" 	g2="H" 	k="-27" />
<hkern g1="slash" 	g2="B" 	k="-27" />
<hkern g1="slash" 	g2="D,Eth" 	k="-27" />
<hkern g1="slash" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="-27" />
<hkern g1="slash" 	g2="F" 	k="-27" />
<hkern g1="slash" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="-27" />
<hkern g1="slash" 	g2="L" 	k="-27" />
<hkern g1="slash" 	g2="N,Ntilde" 	k="-27" />
<hkern g1="slash" 	g2="P" 	k="-27" />
<hkern g1="slash" 	g2="R" 	k="-27" />
<hkern g1="slash" 	g2="Thorn" 	k="-27" />
<hkern g1="slash" 	g2="b" 	k="-33" />
<hkern g1="slash" 	g2="h" 	k="-33" />
<hkern g1="slash" 	g2="K" 	k="-27" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ydieresis" 	k="127" />
<hkern g1="ampersand" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-27" />
<hkern g1="ampersand" 	g2="T" 	k="190" />
<hkern g1="ampersand" 	g2="V" 	k="90" />
<hkern g1="ampersand" 	g2="AE" 	k="-35" />
<hkern g1="ampersand" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="72" />
<hkern g1="ampersand" 	g2="W" 	k="33" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="exclamdown" 	g2="T" 	k="98" />
<hkern g1="exclamdown" 	g2="V" 	k="39" />
<hkern g1="exclamdown" 	g2="j" 	k="-131" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="152" />
<hkern g1="questiondown" 	g2="T" 	k="180" />
<hkern g1="questiondown" 	g2="V" 	k="76" />
<hkern g1="questiondown" 	g2="y,yacute,ydieresis" 	k="-102" />
<hkern g1="questiondown" 	g2="j" 	k="-233" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="37" />
<hkern g1="questiondown" 	g2="W" 	k="29" />
<hkern g1="at" 	g2="Y,Yacute,Ydieresis" 	k="78" />
<hkern g1="at" 	g2="J" 	k="45" />
<hkern g1="at" 	g2="T" 	k="113" />
<hkern g1="at" 	g2="X" 	k="23" />
</font>
</defs></svg> 