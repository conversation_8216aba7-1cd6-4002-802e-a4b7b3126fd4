@import 'styles/vendors/bootstrap/share';
@import 'styles/variables';

.upload-box {
  position: relative;
}

.upload-inner {
  padding: 35px 30px;
  margin-bottom: 15px;
  border-radius: 2px;
  border: 1px solid #dcdcdc;
  background: #ffffff;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.upload-options {
  padding: 10px 0;
  margin: 0;
  font-size: 22px;
  line-height: 22px;

  li {
    color: #656565;
    display: inline-block;
    vertical-align: middle;
    margin: 0 4px;
    cursor: pointer;
    min-width: 20px;
    min-height: 20px;
  }

  .icon {
    &.icon-share-document {
      font-size: 14px;
    }
  }
}

.upload-content {
  display: flex;
  font-size: 12px;
  line-height: inherit;

  h5 {
    font-size: 15px;
    margin-bottom: 9px;
    &:only-child {
      margin: 0;
    }
  }

  p {
    margin: 0;
  }

  .info-box {
    width: 25px;
  }

  .content-wrap {
    padding: 0 10px;
    width: calc(100% - 25px);

    .required-document {
      color: red;
      font-size: 20px;
      position: relative;
      top: 7px;
    }
  }
}

:host ::ng-deep .radio-wrap {
  position: absolute;
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  @include media-breakpoint-up(md) {
    padding: 0 5px;
  }

  .rtl & {
    right: auto;
    left: 100%;
  }
}

/* Upload styles*/
:host ::ng-deep .bz-tarya-fileupload {
  font-size: 14px;

  .p-fileupload {
    .p-fileupload-buttonbar {
      padding: 0;
      border: none;
      background: transparent;

      .p-button-icon-left,
      button {
        display: none;
      }

      .p-button {
        padding: 6px;
        width: 25px;
        height: 25px;
        position: relative;
        font-size: 22px;

        &:after {
          pointer-events: none;
          font-family: 'icomoon', sans-serif !important;
          content: '\e904';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }

      .p-button-text {
        padding: 0;
        background: transparent;
      }
    }

    .p-button {
      border-radius: 3px;
      color: #656565;
      background: transparent;
      padding: 6px;
      width: 25px;
      height: 25px;
      border: none;
      position: relative;

      &:hover {
        border: none;
      }
    }

    .p-fileupload-content {
      border: none;
      padding: 2px 62px;
      position: absolute;
      left: 0;
      bottom: 0;
      background: transparent;
      width: 100%;
    }

    .p-progressbar {
      display: none;
    }

    .p-messages-error {
      text-align: left;
      color: #dd0000;
      border: none;
      padding: 2px 65px;
      position: absolute;
      left: 0;
      bottom: 0;
      background: transparent;
      width: 100%;

      .rtl & {
        text-align: right;
      }

      .p-messages-icon,
      .p-messages-summary,
      .p-messages-close {
        display: none;
      }
    }

    .p-fileupload-row {
      color: #008000;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      div {
        &:first-child,
        &:last-child {
          display: none;
        }
      }
    }

    .p-fileupload-files {
      display: block;
    }

    .p-fileupload-row > div {
      padding: 5px;
      display: block;
    }
  }
}

:host ::ng-deep .bz-info-tooltip {
  cursor: default;
}
