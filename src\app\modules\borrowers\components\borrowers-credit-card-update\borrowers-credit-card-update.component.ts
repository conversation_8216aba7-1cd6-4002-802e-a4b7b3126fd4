import { Component, OnInit, Renderer2 } from '@angular/core';
import { Dom<PERSON>anitizer, SafeResourceUrl } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { delay, takeUntil, tap } from 'rxjs/operators';
import { HeaderService } from '~tarya/modules/shared/services/header.service';
import { Base } from '~tarya/modules/table-generator/shared/base';
import { PaymentType } from '~tarya/modules/tarya-spread/enums/payment-type.enum';
import { CreditCardStatuses } from '../../enums/credit-card-statuses';
import { RelatedPages } from '../../enums/related-page';
import { CreditCard } from '../../models/credit-card';
import { Loan } from '../../models/loan';
import { LoanTracker } from '../../models/loan-tracker';
import { BorrowersCreditCardService } from '../../services/borrowers-credit-card.service';
import { BorrowersService } from '../../services/borrowers.service';

@Component({
  selector: 'app-borrowers-credit-card-update',
  templateUrl: './borrowers-credit-card-update.component.html',
  styleUrls: ['./borrowers-credit-card-update.component.scss'],
})
export class BorrowersCreditCardUpdateComponent extends Base implements OnInit {
  iframeLink: SafeResourceUrl;
  updateFailed = true;
  btnTryDifferentCardEnabled = false;
  loanNumbers: LoanTracker;
  subtitleText: string;
  selectedLoanIndex: number;
  selectedLoan: Loan;
  creditCardMask = '**** **** ****';
  currentCreditCard: CreditCard;
  creditCardStatuses = CreditCardStatuses;
  private allCreditCards: CreditCard[];
  private updatedLoansIndexes: number[] = [];
  private previousPage = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private borrowersCreditCardService: BorrowersCreditCardService,
    private borrowersService: BorrowersService,
    private sanitizer: DomSanitizer,
    private renderer: Renderer2,
    private headerService: HeaderService,
    private translateService: TranslateService
  ) {
    super();
  }

  ngOnInit() {
    this.route.queryParams.subscribe(
      ({ prevPage }) => (this.previousPage = prevPage)
    );
    this.getCreditCardResponse();
    this.selectedLoanIndex = 0;
    this.allCreditCards = this.route.snapshot.data.paymentMethods.creditCards;
    this.currentCreditCard = this.getCurrentCreditCard();
    if (!this.currentCreditCard) {
      this.router.navigate(['/app/borrowers/']);
    }
    this.updateLoansForSidebar();
    this.route.params.subscribe(({ lastDigits, id }) => {
      if (lastDigits !== this.currentCreditCard.creditCardLastDigits) {
        this.allCreditCards =
          this.route.snapshot.data.paymentMethods.creditCards;
      }
      this.currentCreditCard = this.getCurrentCreditCard();
      if (!this.currentCreditCard) {
        this.router.navigate(['/app/borrowers/']);
      }
      this.selectedLoan = this.getSelectedLoan(+id);
      this.borrowersService.selectedLoanId.next(+id);
      this.updateLoansForSidebar();
      this.getIframe();
    });
    this.translateService.onLangChange
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => this.getIframe());
  }

  tryDifferentCard() {
    this.getIframe();
    this.btnTryDifferentCardEnabled = false;
  }

  onCreditCardUpdateSuccess() {
    return this.borrowersCreditCardService
      .onUpdateSuccess(this.selectedLoan.id)
      .pipe(
        delay(2000),
        tap(() => {
          const expiredOrFailedCreditCards = this.allCreditCards.filter(
            (creditCard) => creditCard.failed || creditCard.expired
          );
          let selectedLoanIndex = this.currentCreditCard.loans.findIndex(
            ({ id }) => id === this.selectedLoan.id
          );
          if (!this.updatedLoansIndexes.includes(selectedLoanIndex)) {
            this.updatedLoansIndexes.push(selectedLoanIndex);
          }
          this.headerService.setUpdatedExpiredCreditCardLoanId(
            this.selectedLoan.id
          );
          if (
            selectedLoanIndex >= 0 &&
            selectedLoanIndex !== this.currentCreditCard.loans.length - 1
          ) {
            selectedLoanIndex += 1;
            const nextLoanId =
              this.currentCreditCard.loans[selectedLoanIndex].id;
            const lastFourDigits = this.route.snapshot.params.lastDigits;
            this.router.navigate([
              '/app/borrowers/credit-card-update',
              lastFourDigits,
              nextLoanId,
            ]);
          }
          if (
            this.updatedLoansIndexes.length ===
              this.currentCreditCard.loans.length &&
            expiredOrFailedCreditCards.length > 0
          ) {
            this.updatedLoansIndexes = [];
            this.router.navigate(['/app/borrowers/']);
          }
          if (this.previousPage === RelatedPages.DebtPaymentPage) {
            this.router.navigate(
              ['../../../', 'debt-payment', 'payment-method'],
              {
                relativeTo: this.route,
              }
            );
          }
        })
      );
  }

  private getIframe() {
    const currentLang = this.translateService.currentLang;
    const detailsForCreditCard = {
      failureUrl: `${location.origin}/app/borrowers/payment-error`,
      language: currentLang.toUpperCase(),
      loanId: this.selectedLoan.id,
      successUrl: `${location.origin}/app/borrowers/payment-success`,
      operationType: 'update',
    };
    this.loanNumbers = this.updateSubtitleText();
    this.borrowersCreditCardService
      .getCreditCardIframe(detailsForCreditCard)
      .pipe(takeUntil(this.destroy$))
      .subscribe((link) => {
        this.iframeLink = this.sanitizer.bypassSecurityTrustResourceUrl(
          link.url
        );
      });
  }

  private getCurrentCreditCard(): CreditCard {
    const expiredCreditCardLastDigits = this.route.snapshot.params.lastDigits;
    const selectedLoanId = +this.route.snapshot.params.id;

    return this.allCreditCards.find(
      (creditCard: CreditCard) =>
        creditCard.creditCardLastDigits === expiredCreditCardLastDigits &&
        creditCard.loans.find((loan) => loan.id === selectedLoanId)
    );
  }

  private updateSubtitleText(): LoanTracker {
    let nextLoanNumber = null;
    this.subtitleText = 'credit-card-update.subtitle.last-loan-updated';
    if (this.selectedLoanIndex !== this.currentCreditCard.loans.length - 1) {
      nextLoanNumber =
        this.currentCreditCard.loans[this.selectedLoanIndex + 1].id;
      this.subtitleText = 'credit-card-update.subtitle.single-loan-updated';
    }
    return {
      updatedLoanNumber: +this.selectedLoan.id,
      nextLoanNumber,
    };
  }

  private getCreditCardResponse() {
    this.renderer.listen('window', 'message', (event) => {
      if (event.data === PaymentType.CARD_SUCCESS) {
        this.updateFailed = false;
        this.onCreditCardUpdateSuccess()
          .pipe(takeUntil(this.destroy$))
          .subscribe();
      } else if (event.data === PaymentType.CARD_ERROR) {
        this.updateFailed = true;
        this.btnTryDifferentCardEnabled = true;
      } else if (event.data === 'refresh-iframe') {
        this.tryDifferentCard();
      }
    });
  }

  private getSelectedLoan(id: number): Loan {
    return this.currentCreditCard.loans.find((loan) => loan.id === +id);
  }

  private updateLoansForSidebar() {
    const creditCardStatus = this.currentCreditCard.failed
      ? this.creditCardStatuses.Failed
      : this.currentCreditCard.expired
      ? this.creditCardStatuses.Expired
      : this.creditCardStatuses.Updated;
    this.currentCreditCard.loans.forEach(
      (loan, index) =>
        (loan.creditCardStatus = this.updatedLoansIndexes.includes(index)
          ? this.creditCardStatuses.Updated
          : creditCardStatus)
    );
    this.borrowersService.loansForSidebar.next({
      loansFromCreditCard: this.currentCreditCard.loans,
    });
  }
}
