<div class="register-steps-wrap">
    <div class="col-md-5 no-padding m-auto sms-verification-holder">
        <div class="loan-info-title">
            <h2>{{ 'register.sms-verification.step-name'| translate }}</h2>
        </div>
        <div class="form-holder">
            <form action="" class="register-steps-form" [formGroup]="smsForm">
                <div class="form-group-wrap">
                    <div class="form-group">
                        <div class="label-holder">
                            <div class="sms-info">
                                <p>{{ 'register.sms-verification.sms-verification-text'| translate }}</p>
                            </div>
                        </div>
                        <div class="input-holder">
                            <input type="text" class="form-control" formControlName="smsCode">
                        </div>
                        <control-validation-errors
                                [control]="smsForm.controls['smsCode']"
                                translateTag="error">
                        </control-validation-errors>
                    </div>
                </div>
                <div class="input-info">
                    <p>{{ 'register.sms-verification.code-not-accepted'| translate }} <a href="#" (click)="this.resendSms($event)">{{ 'register.sms-verification.send-again'| translate }}</a></p>
                </div>
                <div class="btn-holder" [ngClass]="{'stop-click-wrap': disabledBtn}">
                    <button type="button" class="btn btn-primary" [ngClass]="{'stop-click': disabledBtn}" [disabled]="!smsForm.valid" (click)="createBusinessAccountAndLoanRequest($event)">{{ 'register.continue'| translate }}</button>
                </div>
            </form>
        </div>
    </div>
</div>