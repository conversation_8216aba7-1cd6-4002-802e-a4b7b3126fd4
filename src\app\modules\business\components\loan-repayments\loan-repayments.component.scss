@import 'styles/vendors/bootstrap/share';
@import 'styles/variables';

:host ::ng-deep .discharge-table-wrap {
  color: #3d3d3d;
  @include media-breakpoint-up(md) {
    padding: 15px;
  }

  .radio-row {
    margin-bottom: 10px;
  }

  .loan-type-inputs {
    margin: 0 -15px;
    font-size: 15px;
    padding: 10px 0 20px;
    @include media-breakpoint-up(xl) {
      display: flex;
      white-space: nowrap;
    }

    .form-control {
      text-align: center;
      height: 29px;
      width: 57px;
      border: 1px solid #b2b2b2;
      border-radius: 2px;
      background: #fff;
      font-size: 14px;
      vertical-align: top;
      display: inline-block;
    }

    .input-wrap {
      margin: 0 5px;
      height: 29px;
      width: 57px;
      display: inline-block;
      vertical-align: top;
    }
    .inputs-col,
    .btn-col {
      padding: 0 15px;
      margin: 0 0 15px;
      @include media-breakpoint-up(xl) {
        margin: 0;
      }

      span {
        display: inline-block;
        vertical-align: top;
        line-height: 30px;
      }
    }

    .inputs-col {
      position: relative;

      + .inputs-col {
        padding-top: 10px;
        @include media-breakpoint-up(xl) {
          padding-top: 0;
        }

        &:after {
          content: '+';
          position: absolute;
          top: -10px;
          left: -5px;
          @include media-breakpoint-up(xl) {
            transform: translateY(-50%);
            top: 50%;
          }

          .rtl & {
            left: auto;
            right: -5px;
          }
        }
      }
    }

    .btn {
      font-size: 14px;

      &.btn-primary {
        padding: 5px 10px;
        min-width: 76px;
      }
    }
  }
}

:host ::ng-deep .heading-wrap {
  margin-bottom: 15px;

  .heading {
    margin: 0;
    display: inline-block;
    vertical-align: top;
    padding: 0 30px 0 0;
    position: relative;

    .rtl & {
      padding: 0 0 0 30px;
    }
  }
}

:host ::ng-deep .bz-info-tooltip {
  position: absolute;
  right: 0;
  top: 2px;

  .rtl & {
    right: auto;
    left: 0;
  }
}

.loan-type-desc {
  color: #575757;
  font-size: 14px;
  margin-bottom: 30px;
  @include media-breakpoint-up(md) {
    margin-bottom: 54px;
  }
}

:host ::ng-deep .tips-list {
  ul {
    padding: 0;
  }

  li {
    padding: 0 15px;
    position: relative;

    &:after {
      content: '';
      height: 8px;
      position: absolute;
      width: 8px;
      background: #000;
      border-radius: 50%;
      left: 0;
      top: 7px;

      .rtl & {
        left: auto;
        right: 0;
      }
    }

    &.paid-up {
      &:after {
        background: #7ed321;
      }
    }

    &.future-payment {
      &:after {
        background: #ff8b00;
      }
    }

    &.overdue {
      &:after {
        background: #d0021b;
      }
    }

    &.last-payment-balloon {
      &:after {
        background: #267ffa;
      }
    }
  }
}

:host ::ng-deep .boxes-row {
  padding-bottom: 15px;

  .year-title {
    display: block;
    margin-bottom: 20px;
  }

  .boxes-list {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -5px;
    padding: 0;

    .boxes-list-item {
      width: 110px;
      padding: 0 5px;
      margin-bottom: 10px;
      //width: 50%;
      //padding: 0 5px;
      //margin-bottom: 10px;
      //@include media-breakpoint-up(sm) {
      //    width: 33.33%;
      //}
      //@include media-breakpoint-up(md) {
      //    width: 25%;
      //}
      //
      //@include media-breakpoint-up(lg) {
      //    width: 16.66%;
      //}
    }

    .box-content {
      display: block;
      text-align: center;
    }

    .label {
      display: block;
      margin-bottom: 11px;
      padding: 0 15px 0 0;

      .rtl & {
        padding: 0 0 0 15px;
      }
    }
  }

  .box-holder {
    height: 70px;
    border: 1px solid #b2b2b2;
    border-radius: 2px;
    background: #fff;
    position: relative;
    padding: 2px 5px;
    cursor: pointer;

    &:after {
      content: '';
      height: 8px;
      position: absolute;
      width: 8px;
      background: #000;
      border-radius: 50%;
      right: 4px;
      top: 9px;

      .rtl & {
        right: auto;
        left: 4px;
      }
    }

    &.active,
    &:hover {
      background: #f8f8f8;
    }

    &.paid-up {
      &:after {
        background: #7ed321;
      }
    }

    &.future-payment {
      &:after {
        background: #ff8b00;
      }
    }

    &.overdue {
      &:after {
        background: #d0021b;
      }
    }

    &.last-payment-balloon {
      &:after {
        background: #267ffa;
      }
    }
  }
}

.box-section-wrap {
  padding: 30px 0;

  .box-section {
    border-bottom: 1px solid #cbcbcb;
    border-top: 1px solid #cbcbcb;
  }
}

.box-section {
  padding: 30px 0;
}
