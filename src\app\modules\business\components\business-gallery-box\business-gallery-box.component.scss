@import 'styles/vendors/bootstrap/share';
@import 'styles/variables';

.gallery-inner {
  position: relative;
  font-size: 14px;
  display: block;
  color: #3d3d3d;
  text-decoration: none;
  transition: box-shadow 0.3s ease-in-out;
  width: 100%;
  min-height: 335px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #dedede;
  //box-shadow: 0 2px 4px 0 rgba(0,0,0,0.06);

  &:hover {
    box-shadow: 0 3px 5px 0 rgba(0, 0, 0, 0.36);
  }
}

:host ::ng-deep .btn-settings {
  position: absolute;
  padding: 5px;
  font-size: 20px;
  top: 10px;
  right: 10px;
  color: #a0a0a0;

  .rtl & {
    right: auto;
    left: 10px;
  }
}

.gallery-body {
  padding: 30px;
  min-height: 255px;
  border-bottom: 1px solid #cdcdcd;
  @include media-breakpoint-up(md) {
    padding: 30px;
  }
  @include media-breakpoint-up(lg) {
    padding: 60px 30px 30px;
  }

  .logo-holder {
    overflow: hidden;
    background: #f3f3f3;
    border: 1px solid #979797;
    margin: 0 auto 10px;
    max-width: 125px;
    height: 125px;
    border-radius: 50%;
    color: #cdcdcd;
    position: relative;
    line-height: 1;

    .img-wrap {
      position: absolute;
      background-position: 50% 50%;
      background-size: cover;
      top: 0;
      left: 0;
      height: 100%;
      z-index: 3;
      width: 100%;
      display: block;
      border-radius: 50%;
    }

    .placeholder {
      z-index: 2;
      position: absolute;
      top: 50%;
      left: 50%;
      font-size: 50px;
      transform: translate(-50%, -50%);
    }
  }

  strong {
    display: block;
    text-align: center;
    font-size: 18px;
    color: #000;
  }
}

.gallery-footer {
  padding: 20px;

  .footer-inner {
    //margin: 0 -10px;
    //@include media-breakpoint-up(md) {
    //  display: flex;
    //  flex-wrap: wrap;
    //  justify-content: space-between;
    //  align-items: center;
    //}
  }

  .footer-col {
    width: 100%;
    //padding: 0 10px;
    //margin-bottom: 20px;
    font-size: 15px;
    @include media-breakpoint-up(md) {
      margin: 0;
    }

    ul {
      margin: 0;
      padding: 0;
      list-style: none;

      li {
        display: block;
        padding: 5px;

        span {
          display: inline-block;
          vertical-align: top;
        }
      }
    }

    .date {
      font-size: 14px;
      color: #575757;
    }
  }
}
