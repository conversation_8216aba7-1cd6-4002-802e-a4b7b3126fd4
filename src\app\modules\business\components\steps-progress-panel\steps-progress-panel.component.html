<div class="steps-panel-wrap">
  <a [routerLink]="backLink" class="close-btn"></a>
  <div class="flags" *ngIf="testMode">
    <span class="en" (click)="this.switchToEnglish()">EN</span> <span class="he" (click)="this.switchToHebrew()">HE</span>
  </div>
  <div class="step-holder clearfix">
    <strong class="steps-panel-heading">{{ panelHeading | translate }}</strong>
    <div class="steps-section" *ngIf="items">
      <p-steps *ngIf="!hidePanel" [model]="items" styleClass="steps-custom" [(activeIndex)]="activeIndex"></p-steps>
    </div>
  </div>
</div>
