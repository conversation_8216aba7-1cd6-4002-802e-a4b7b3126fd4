<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="ubuntuitalic" horiz-adv-x="1155" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="473" />
<glyph unicode="&#xfb01;" horiz-adv-x="1271" d="M-35 -362q61 80 96 135t58.5 112.5t41 132t46.5 193.5l227 950q25 102 63 182t96 134.5t138 83t191 28.5q59 0 112 -11t86 -28l-76 -151q-16 8 -49.5 17t-89.5 9q-68 0 -115 -19.5t-79.5 -54t-52 -83.5t-33.5 -111l-23 -92h356l-36 -160h-357l-164 -676 q-25 -104 -43 -186t-43.5 -153.5t-67.5 -138t-114 -138.5zM918 0l256 1065h190l-256 -1065h-190zM1219 1366q0 66 40.5 103.5t92.5 37.5q47 0 79.5 -29.5t32.5 -82.5q0 -61 -43 -99.5t-92 -38.5q-47 0 -78.5 29t-31.5 80z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1335" d="M-35 -362q61 80 96 135t58.5 112.5t41 132t46.5 193.5l227 950q25 102 63 182t96 134.5t138 83t191 28.5q59 0 112 -11t86 -28l-76 -151q-16 8 -49.5 17t-89.5 9q-68 0 -115 -19.5t-79.5 -54t-52 -83.5t-33.5 -111l-23 -92h356l-36 -160h-357l-164 -676 q-25 -104 -43 -186t-43.5 -153.5t-67.5 -138t-114 -138.5zM991 279q0 47 8.5 98t22.5 106l258 1073l199 33l-269 -1120q-10 -45 -20 -88t-10 -82q0 -70 33.5 -111t125.5 -51l-43 -155q-84 2 -142 25.5t-94 62.5t-52.5 92t-16.5 117z" />
<glyph unicode="&#xfb03;" horiz-adv-x="2033" d="M-35 -362q61 80 96 135t58.5 112.5t41 132t46.5 193.5l227 950q25 102 63 182t96 134.5t138 83t191 28.5q72 0 136 -14t97 -31l-76 -151q-16 8 -56 20t-118 12q-68 0 -115 -19.5t-79.5 -54t-52 -83.5t-33.5 -111l-23 -92h356l-36 -160h-357l-164 -676q-25 -104 -43 -186 t-43.5 -153.5t-67.5 -138t-114 -138.5zM727 -362q61 80 96 135t58.5 112.5t41 132t46.5 193.5l227 950q25 102 63 182t96 134.5t138 83t191 28.5q59 0 112 -11t86 -28l-76 -151q-16 8 -49.5 17t-89.5 9q-68 0 -115 -19.5t-79.5 -54t-52 -83.5t-33.5 -111l-23 -92h356 l-36 -160h-357l-164 -676q-25 -104 -43 -186t-43.5 -153.5t-67.5 -138t-114 -138.5zM1680 0l256 1065h190l-256 -1065h-190zM1981 1366q0 66 40.5 103.5t92.5 37.5q47 0 79.5 -29.5t32.5 -82.5q0 -61 -43 -99.5t-92 -38.5q-47 0 -78.5 29t-31.5 80z" />
<glyph unicode="&#xfb04;" horiz-adv-x="2097" d="M-35 -362q61 80 96 135t58.5 112.5t41 132t46.5 193.5l227 950q25 102 63 182t96 134.5t138 83t191 28.5q72 0 136 -14t97 -31l-76 -151q-16 8 -56 20t-118 12q-68 0 -115 -19.5t-79.5 -54t-52 -83.5t-33.5 -111l-23 -92h356l-36 -160h-357l-164 -676q-25 -104 -43 -186 t-43.5 -153.5t-67.5 -138t-114 -138.5zM727 -362q61 80 96 135t58.5 112.5t41 132t46.5 193.5l227 950q25 102 63 182t96 134.5t138 83t191 28.5q59 0 112 -11t86 -28l-76 -151q-16 8 -49.5 17t-89.5 9q-68 0 -115 -19.5t-79.5 -54t-52 -83.5t-33.5 -111l-23 -92h356 l-36 -160h-357l-164 -676q-25 -104 -43 -186t-43.5 -153.5t-67.5 -138t-114 -138.5zM1753 279q0 47 8.5 98t22.5 106l258 1073l199 33l-269 -1120q-10 -45 -20 -88t-10 -82q0 -70 33.5 -111t125.5 -51l-43 -155q-84 2 -142 25.5t-94 62.5t-52.5 92t-16.5 117z" />
<glyph unicode="&#xd;" horiz-adv-x="473" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode=" "  horiz-adv-x="473" />
<glyph unicode="&#x09;" horiz-adv-x="473" />
<glyph unicode="&#xa0;" horiz-adv-x="473" />
<glyph unicode="!" horiz-adv-x="512" d="M150 94q0 74 44 114t101 40t91 -32t34 -93q0 -35 -13.5 -62.5t-34 -47t-47 -30t-53.5 -10.5q-57 0 -89.5 31t-32.5 90zM299 442q16 139 37.5 279.5t62.5 312.5l93 385h200l-92 -387q-12 -49 -33.5 -128t-47 -163t-53.5 -164.5t-50 -134.5h-117z" />
<glyph unicode="&#x22;" horiz-adv-x="839" d="M432 1026q8 111 22.5 218.5t39.5 211.5l24 100h195l-25 -102q-25 -100 -62.5 -212t-82.5 -216h-111zM786 1026q8 111 22.5 218.5t39.5 211.5l24 100h195l-25 -102q-25 -100 -62.5 -212t-82.5 -216h-111z" />
<glyph unicode="#" horiz-adv-x="1339" d="M186 375l37 151h205l158 367h-275l37 151h301l162 375h172l-160 -375h322l160 375h172l-160 -375h172l-37 -151h-199l-159 -367h270l-37 -151h-297l-160 -375h-172l158 375h-319l-160 -375h-172l157 375h-176zM600 526h322l159 367h-321z" />
<glyph unicode="$" d="M172 137l88 146q37 -23 112.5 -54.5t194.5 -31.5q59 0 118.5 11t107 37.5t76 70.5t28.5 110q0 45 -17.5 78t-49 59.5t-75.5 48t-96 45.5q-45 20 -89 47t-78.5 65t-56 90t-21.5 126q0 147 99 246.5t296 124.5l55 233h166l-55 -229q88 -8 158.5 -36t107.5 -52l-86 -142 q-35 25 -101.5 47.5t-164.5 22.5q-59 0 -111.5 -11t-92.5 -36t-62.5 -62.5t-22.5 -89.5q0 -84 51 -129t156 -94q55 -25 106.5 -53.5t89 -67.5t60 -91t22.5 -124q0 -90 -30.5 -159.5t-88 -119.5t-136 -81t-173.5 -41l-61 -264h-166l64 266q-53 2 -102.5 12.5t-92.5 24.5 t-76 29.5t-51 27.5z" />
<glyph unicode="%" horiz-adv-x="1712" d="M324 975q0 86 27.5 171t77.5 152.5t124 109.5t166 42q123 0 191.5 -70.5t68.5 -208.5q0 -86 -27.5 -171t-78 -152.5t-124 -109.5t-165.5 -42q-123 0 -191.5 71t-68.5 208zM356 0l1125 1419h194l-1124 -1419h-195zM475 989q0 -70 23.5 -114t95.5 -44q59 0 103 33t73 81 t43 105.5t14 106.5q0 70 -23.5 114t-94.5 44q-59 0 -103.5 -33t-73 -81t-43 -105.5t-14.5 -106.5zM1053 248q0 86 27.5 171t77.5 152.5t124 109.5t166 42q123 0 191.5 -70.5t68.5 -208.5q0 -86 -27.5 -171t-78 -152.5t-124 -109.5t-165.5 -42q-123 0 -191.5 71t-68.5 208z M1204 262q0 -70 23.5 -114t95.5 -44q59 0 103 33t73 81t43 105.5t14 106.5q0 70 -23.5 114t-94.5 44q-59 0 -103.5 -33t-73 -81t-43 -105.5t-14.5 -106.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1310" d="M160 301q0 92 29.5 166t82 132t124 104.5t155.5 82.5q-35 57 -59.5 121t-24.5 146q0 92 30.5 165.5t87 126t137.5 80t179 27.5q57 0 110.5 -17.5t92.5 -52t62.5 -85t23.5 -117.5q0 -80 -28.5 -144.5t-81 -118t-124 -98.5t-157.5 -84l237 -329q98 125 158 288l166 -20 q-29 -86 -85 -191.5t-142 -206.5q51 -72 95 -141t68 -135h-196q-16 37 -40 75t-52 79q-92 -76 -206 -124t-261 -48q-102 0 -174 23.5t-118 66.5t-67.5 101t-21.5 128zM346 334q0 -86 46 -140.5t161 -54.5q98 0 192.5 36t169.5 106l-282 389q-53 -23 -104.5 -54.5 t-91.5 -72.5t-65.5 -93.5t-25.5 -115.5zM645 1065q0 -55 21.5 -108.5t54.5 -106.5q53 20 106.5 50t95.5 69t67.5 86t25.5 102q0 70 -38 106.5t-104 36.5q-92 0 -160.5 -58t-68.5 -177z" />
<glyph unicode="'" horiz-adv-x="483" d="M432 1026q8 111 22.5 218.5t39.5 211.5l24 100h195l-25 -102q-25 -100 -62.5 -212t-82.5 -216h-111z" />
<glyph unicode="(" horiz-adv-x="649" d="M258 254q0 389 156.5 725t464.5 625l127 -91q-129 -129 -233.5 -273t-177.5 -299t-113 -317.5t-40 -330.5q0 -174 25 -313.5t84 -268.5l-145 -82q-70 123 -109 285t-39 340z" />
<glyph unicode=")" horiz-adv-x="649" d="M-102 -289q129 129 233.5 273.5t177 299t112.5 317.5t40 331q0 174 -24.5 313t-84.5 268l146 82q70 -123 108.5 -284.5t38.5 -339.5q0 -389 -156.5 -725t-463.5 -625z" />
<glyph unicode="*" horiz-adv-x="962" d="M303 1055l57 172l13 -4q86 -29 156.5 -68t144.5 -80q-16 86 -31.5 164t-15.5 164v16h184v-16q0 -86 -15.5 -164t-31.5 -164q74 41 144.5 80t156.5 68l12 4l58 -172l-17 -6q-82 -31 -164 -40.5t-168 -15.5q68 -57 127.5 -111.5t110.5 -125.5l10 -15l-149 -104l-8 12 q-49 74 -85 146.5t-73 150.5q-37 -78 -73 -150.5t-85 -146.5l-8 -12l-150 104l11 15q51 72 110.5 126t126.5 111q-86 6 -168 15.5t-164 40.5z" />
<glyph unicode="+" d="M244 528l39 162h372l103 422h174l-103 -422h373l-39 -162h-372l-103 -424h-172l100 424h-372z" />
<glyph unicode="," horiz-adv-x="503" d="M6 -272q41 55 75 120.5t58.5 133t41 132t22.5 119.5h203q-10 -74 -35 -149.5t-61 -148t-81 -139t-94 -122.5z" />
<glyph unicode="-" horiz-adv-x="600" d="M178 524l41 178h496l-41 -178h-496z" />
<glyph unicode="." horiz-adv-x="503" d="M139 94q0 74 44 114t102 40q57 0 91 -32t34 -93q0 -35 -13.5 -62.5t-34 -47t-47 -30t-53.5 -10.5q-57 0 -90 31t-33 90z" />
<glyph unicode="/" horiz-adv-x="770" d="M-135 -379l1145 1983h190l-1145 -1983h-190z" />
<glyph unicode="0" d="M238 395q0 100 18 218t54 236t91.5 226.5t130 192.5t168 134t207.5 50q174 0 257 -107.5t83 -318.5q0 -100 -18.5 -218t-54 -235.5t-91 -226t-130 -192.5t-169 -134.5t-206.5 -50.5q-174 0 -257 107.5t-83 318.5zM432 420q0 -154 37 -219.5t131 -65.5q66 0 124 39 t108.5 104.5t91.5 152.5t69.5 183.5t44 195.5t15.5 191q0 154 -37 219.5t-131 65.5q-63 0 -123 -39t-110 -104.5t-91 -152.5t-69.5 -183t-44 -196.5t-15.5 -190.5z" />
<glyph unicode="1" d="M461 1126q123 45 251 116t244 177h138l-340 -1419h-191l273 1137q-29 -23 -69 -46.5t-86 -46t-95 -42t-94 -33.5z" />
<glyph unicode="2" d="M162 0q16 109 58 197t100.5 161.5t129 135t146.5 118.5q80 59 154.5 115.5t132 115t92.5 122t35 139.5q0 86 -46 134t-151 48q-57 0 -108.5 -14.5t-92.5 -34.5t-72.5 -44t-50.5 -42l-65 147q29 27 72 53.5t97 49t116.5 37t128.5 14.5q98 0 168.5 -27.5t114.5 -72.5 t64.5 -103t20.5 -119q0 -109 -34.5 -190.5t-94 -150t-137.5 -129t-166 -125.5q-51 -39 -108.5 -84t-108.5 -93.5t-90 -98.5t-55 -99h616l-37 -160h-829z" />
<glyph unicode="3" d="M141 53l72 162q14 -8 40 -21.5t62.5 -25.5t85 -21.5t109.5 -9.5q82 0 152.5 19.5t123 58.5t81 97.5t28.5 136.5q0 123 -89 176t-237 53h-43l37 156h64q68 0 132 17t116.5 53t84 88t31.5 122q0 86 -52 130t-151 44q-88 0 -158.5 -27.5t-121.5 -60.5l-45 148 q27 18 65.5 36.5t86 34t99.5 24.5t105 9q96 0 165 -24.5t114 -67.5t65.5 -101.5t20.5 -123.5q0 -135 -79 -230.5t-214 -148.5q35 -14 70.5 -40t63.5 -64t45 -88t17 -111q0 -109 -37.5 -198t-111.5 -152.5t-182.5 -98.5t-251.5 -35q-72 0 -132.5 10.5t-107.5 23.5t-78.5 27.5 t-44.5 22.5z" />
<glyph unicode="4" d="M178 362l31 130q57 96 150.5 219t207 249.5t238.5 246.5t248 212h186l-217 -901h166l-37 -156h-166l-86 -362h-184l86 362h-623zM410 518h430l159 664q-74 -61 -153.5 -138t-157.5 -163t-149.5 -178.5t-128.5 -184.5z" />
<glyph unicode="5" d="M150 49l73 160q29 -16 99.5 -44t183.5 -28q84 0 156.5 21.5t126 61.5t84 99.5t30.5 133.5q0 94 -51 148t-127 82t-164 36t-162 12q33 94 64 180t58.5 169t52 166t47.5 173h663l-37 -159h-493q-12 -41 -29.5 -94.5t-36 -107.5t-35 -103.5t-26.5 -82.5q233 -31 351 -127 t118 -274q0 -104 -35 -196.5t-108.5 -160t-187.5 -106.5t-269 -39q-66 0 -124.5 10.5t-104.5 23.5t-76.5 26.5t-40.5 19.5z" />
<glyph unicode="6" d="M254 379q0 215 72.5 405.5t205 333t319.5 225t419 82.5h26l-22 -161h-10q-145 0 -263 -38t-210.5 -100.5t-157 -146.5t-101.5 -176q66 33 142 50t145 17q92 0 158.5 -27.5t109.5 -73.5t62.5 -107.5t19.5 -129.5q0 -102 -31.5 -203.5t-97 -181t-169 -129t-246.5 -49.5 q-180 0 -275.5 110t-95.5 300zM442 395q0 -129 46.5 -199.5t162.5 -70.5q80 0 141.5 34t103.5 89t63.5 123.5t21.5 140.5q0 98 -48 149.5t-159 51.5q-72 0 -144.5 -13.5t-146.5 -48.5q-20 -66 -30.5 -134t-10.5 -122z" />
<glyph unicode="7" d="M324 0q31 109 81 226.5t113.5 236.5t136 233.5t148.5 218t152.5 190.5t146.5 148h-674l39 166h891l-39 -164q-88 -76 -203 -211t-227.5 -304t-210.5 -359.5t-154 -380.5h-200z" />
<glyph unicode="8" d="M207 289q0 147 87 262t241 190q-125 98 -125 254q0 92 33.5 174t97 145.5t154.5 100.5t206 37q94 0 163 -26.5t112 -71.5t63.5 -101.5t20.5 -115.5q0 -141 -79 -246.5t-220 -175.5q70 -47 117.5 -116t47.5 -177q0 -90 -33.5 -172t-101 -144.5t-170 -99.5t-237.5 -37 q-102 0 -174 29t-117 74t-65.5 102t-20.5 115zM395 322q0 -41 10.5 -76t35 -62.5t65.5 -43t102 -15.5q68 0 128.5 20.5t105.5 57.5t70.5 88t25.5 112q0 100 -69.5 162t-192.5 105q-55 -25 -106.5 -62t-90.5 -82t-61.5 -97t-22.5 -107zM596 1014q0 -86 51 -141.5t164 -92.5 q51 25 98 57.5t83 74.5t57.5 92.5t21.5 111.5q0 31 -8 63.5t-29.5 58.5t-59.5 42t-95 16q-61 0 -112.5 -21.5t-89.5 -58t-59.5 -89t-21.5 -113.5z" />
<glyph unicode="9" d="M174 -2l35 162q6 -2 19.5 -2h17.5q143 0 256.5 35.5t201 99t148.5 147.5t102 183q-66 -33 -141 -49.5t-145 -16.5q-92 0 -159 26.5t-110 72.5t-63.5 107.5t-20.5 129.5q0 102 32 202.5t98.5 179.5t167 127t235.5 48q190 0 286.5 -109.5t96.5 -300.5q0 -219 -68.5 -409.5 t-197.5 -331.5t-315.5 -222t-422.5 -81h-21t-32 2zM504 913q0 -98 49 -148t160 -50q72 0 144.5 13t146.5 48q18 61 28 130t10 136q0 113 -49 182.5t-159 69.5q-80 0 -141.5 -33.5t-103.5 -87t-63.5 -122t-21.5 -138.5z" />
<glyph unicode=":" horiz-adv-x="503" d="M139 94q0 74 44 114t102 40q57 0 91 -32t34 -93q0 -35 -13.5 -62.5t-34 -47t-47 -30t-53.5 -10.5q-57 0 -90 31t-33 90zM334 911q0 74 44 114t102 40q57 0 91 -32t34 -93q0 -35 -13.5 -62.5t-34 -47t-47 -30t-53.5 -10.5q-57 0 -90 31t-33 90z" />
<glyph unicode=";" horiz-adv-x="503" d="M6 -272q41 55 75 120.5t58.5 133t41 132t22.5 119.5h203q-10 -74 -35 -149.5t-61 -148t-81 -139t-94 -122.5zM334 911q0 74 44 114t102 40q57 0 91 -32t34 -93q0 -35 -13.5 -62.5t-34 -47t-47 -30t-53.5 -10.5q-57 0 -90 31t-33 90z" />
<glyph unicode="&#x3c;" d="M244 526l39 164l958 375l12 -168l-782 -299l637 -297l-86 -149z" />
<glyph unicode="=" d="M193 309l38 162h920l-39 -162h-919zM297 745l39 162h919l-38 -162h-920z" />
<glyph unicode="&#x3e;" d="M193 319l782 299l-637 297l86 150l778 -375l-39 -164l-958 -374z" />
<glyph unicode="?" horiz-adv-x="786" d="M238 94q0 74 44 114t101 40t91 -32t34 -93q0 -35 -13.5 -62.5t-34 -47t-47 -30t-53.5 -10.5q-57 0 -89.5 31t-32.5 90zM326 442q2 25 4 45.5t6 40.5q25 104 96.5 179t163.5 147q41 33 80 65.5t68.5 67.5t47 75t17.5 87q0 23 -6 49.5t-25.5 48t-54.5 35.5t-92 14 q-98 0 -164 -22.5t-102 -40.5l-21 147q49 29 134 50.5t177 21.5q96 0 162 -22.5t107 -61.5t58 -92.5t17 -114.5q0 -68 -24.5 -125t-64.5 -105t-89 -90t-100 -81q-76 -59 -138.5 -124t-84.5 -155q-4 -12 -5 -23.5t-1 -15.5h-166z" />
<glyph unicode="@" horiz-adv-x="1906" d="M227 344q0 205 69 404.5t202 355.5t325.5 252t442.5 96q334 0 514 -166t180 -489q0 -96 -21.5 -218t-81 -231.5t-165 -184.5t-271.5 -75q-129 0 -182 72q-47 -29 -110.5 -46.5t-135.5 -17.5q-154 0 -246 87t-92 261q0 117 40 226.5t116 192.5t184.5 133.5t247.5 50.5 q106 0 185 -18.5t120 -35.5l-149 -622q-10 -31 -10 -60q0 -25 13 -43t54 -18q86 0 147.5 50t102.5 129t60.5 173t19.5 182q0 113 -28.5 207t-93.5 162t-169 105.5t-256 37.5q-178 0 -331.5 -76.5t-266.5 -204.5t-176.5 -297t-63.5 -353q0 -137 40 -235.5t112 -162t172 -94.5 t221 -31q119 0 200 15.5t103 23.5l-14 -151q-18 -6 -53 -13.5t-79 -14.5t-94.5 -11t-99.5 -4q-147 0 -272 42t-216 125t-142.5 205.5t-51.5 284.5zM840 461q0 -111 46 -161t134 -50q111 0 188 41q0 35 4.5 58.5t10.5 45.5l116 482q-10 4 -44.5 8t-75.5 4q-88 0 -158 -35 t-119 -94.5t-75.5 -137t-26.5 -161.5z" />
<glyph unicode="A" horiz-adv-x="1302" d="M20 0q229 414 447.5 762t431.5 657h195q31 -154 56.5 -310.5t50 -328.5t47 -364.5t46.5 -415.5h-200q-10 92 -18.5 185.5t-18.5 179.5h-619l-200 -365h-218zM530 524h508q-16 166 -37.5 330t-50.5 338q-72 -104 -178 -269t-242 -399z" />
<glyph unicode="B" horiz-adv-x="1265" d="M172 23l330 1376q86 20 191.5 27.5t189.5 7.5q104 0 191 -17.5t149.5 -55.5t98.5 -99.5t36 -147.5q0 -125 -75 -220t-206 -142q88 -33 142.5 -100.5t54.5 -166.5q0 -141 -57.5 -237t-156.5 -154.5t-233 -83t-288 -24.5q-41 0 -89.5 2t-97.5 6t-96 11t-84 18zM401 160 q23 -4 68 -7t115 -3q76 0 161.5 11t157.5 45t120 95t48 164q0 100 -71.5 149.5t-231.5 49.5h-248zM559 819h195q68 0 138.5 11.5t128.5 41t96 81t38 130.5q0 104 -78 145.5t-237 41.5q-53 0 -104.5 -4t-69.5 -9z" />
<glyph unicode="C" horiz-adv-x="1228" d="M254 520q0 184 58.5 352t166 297t259 206t337.5 77q160 0 255 -33t136 -61l-82 -164q-47 31 -127.5 58.5t-191.5 27.5q-141 0 -254 -66.5t-190.5 -172t-118.5 -237.5t-41 -261q0 -399 346 -400q74 0 132 10.5t103 23.5t80 29.5t62 29.5l24 -173q-25 -14 -64.5 -30.5 t-93 -30.5t-118.5 -23.5t-139 -9.5q-133 0 -234.5 39t-169 110.5t-101.5 173t-34 228.5z" />
<glyph unicode="D" horiz-adv-x="1415" d="M172 23l330 1378q86 20 182 26.5t182 6.5q315 0 471 -148.5t156 -400.5q0 -176 -55.5 -339t-172 -288t-298 -198.5t-432.5 -73.5q-86 0 -186.5 8t-176.5 29zM403 164q16 -4 55.5 -7t127.5 -3q162 0 292 54t220 151.5t139 231.5t49 294q0 180 -104.5 280.5t-331.5 100.5 q-154 0 -184 -9z" />
<glyph unicode="E" horiz-adv-x="1140" d="M166 0l340 1419h837l-38 -166h-641l-103 -428h569l-38 -161h-568l-121 -498h689l-39 -166h-887z" />
<glyph unicode="F" horiz-adv-x="1073" d="M166 0l340 1419h829l-39 -166h-632l-105 -434h561l-39 -162h-559l-157 -657h-199z" />
<glyph unicode="G" horiz-adv-x="1333" d="M254 520q0 184 58.5 352t167 297t261 206t342.5 77q94 0 163 -12.5t118 -28.5t80 -33.5t49 -29.5l-88 -162q-16 10 -45 26.5t-70 32t-94 25.5t-121 10q-145 0 -260 -66.5t-193.5 -172t-119.5 -237.5t-41 -261q0 -203 86 -302.5t272 -99.5q76 0 137.5 10.5t87.5 20.5 l123 520h195l-156 -647q-25 -14 -67.5 -27.5t-100 -23.5t-124 -16.5t-136.5 -6.5q-258 0 -391 148.5t-133 400.5z" />
<glyph unicode="H" horiz-adv-x="1394" d="M166 0l340 1419h199l-144 -594h658l143 594h199l-340 -1419h-199l158 659h-658l-157 -659h-199z" />
<glyph unicode="I" horiz-adv-x="538" d="M166 0l340 1419h199l-340 -1419h-199z" />
<glyph unicode="J" horiz-adv-x="987" d="M49 98l98 150q33 -33 98.5 -70t166.5 -37q74 0 125 18.5t88 58.5t62.5 103.5t47.5 153.5l228 944h198l-229 -962q-25 -98 -55.5 -187.5t-87 -156t-147.5 -105.5t-234 -39q-72 0 -130.5 13.5t-103.5 34t-76.5 42t-48.5 39.5z" />
<glyph unicode="K" horiz-adv-x="1257" d="M166 0l340 1419h199l-150 -620q90 68 194.5 153.5t205 171.5t187.5 164t144 131h250q-78 -70 -175 -156.5t-202.5 -177t-212 -180.5t-203.5 -166q63 -59 134 -147t139.5 -188.5t131 -205t109.5 -198.5h-221q-53 94 -114.5 192.5t-126 188.5t-132 169t-135.5 134 l-163 -684h-199z" />
<glyph unicode="L" horiz-adv-x="1038" d="M166 0l340 1419h199l-302 -1253h629l-39 -166h-827z" />
<glyph unicode="M" horiz-adv-x="1734" d="M131 0q49 184 103.5 371.5t110.5 369t112.5 353.5t109.5 325h195q25 -90 52.5 -213t55 -257t51 -267t40.5 -242q74 111 162.5 244t179 267t175.5 256t152 212h191q-35 -346 -93.5 -703.5t-128.5 -715.5h-198q61 295 112.5 572.5t90.5 541.5q-33 -45 -82 -116.5 t-106.5 -157.5t-119 -179.5t-119 -180.5t-106.5 -161.5t-80 -121.5h-160q-8 49 -21 123.5t-29.5 161.5t-34 180.5t-35 179.5t-31.5 156.5t-25 115.5q-86 -264 -167.5 -544.5t-159.5 -569.5h-197z" />
<glyph unicode="N" horiz-adv-x="1429" d="M166 0l340 1419h170q66 -111 134.5 -251t131 -287.5t115.5 -288.5t88 -254l254 1081h194l-338 -1419h-174q-37 119 -88 270.5t-111.5 307t-124 302t-124.5 255.5l-273 -1135h-194z" />
<glyph unicode="O" horiz-adv-x="1513" d="M254 510q0 168 51 337t154.5 303t257 218t358.5 84q113 0 208 -34t164 -101.5t106.5 -168.5t37.5 -237q0 -170 -52 -337.5t-154.5 -302t-256 -218.5t-358.5 -84q-115 0 -209 34t-162.5 101.5t-106.5 169t-38 236.5zM461 535q0 -182 79 -288t251 -106q137 0 247.5 66.5 t187.5 173t117.5 240t40.5 266.5q0 182 -78.5 287.5t-250.5 105.5q-139 0 -249 -66.5t-186.5 -173t-117.5 -239.5t-41 -266z" />
<glyph unicode="P" horiz-adv-x="1208" d="M166 0l336 1401q86 20 182 26.5t182 6.5q248 0 374 -94.5t126 -274.5q0 -139 -52 -240.5t-148.5 -167t-231.5 -97.5t-301 -32h-141l-127 -528h-199zM530 694h129q86 0 176.5 15.5t162 54.5t117.5 107.5t46 177.5q0 117 -86 167t-233 50q-145 0 -176 -9z" />
<glyph unicode="Q" horiz-adv-x="1513" d="M254 510q0 168 51 337t154.5 303t257 218t358.5 84q113 0 208 -34t164 -101.5t106.5 -168.5t37.5 -237q0 -158 -44 -313.5t-133 -285.5t-221 -220t-306 -115q-2 -6 -2 -14v-12q0 -41 31.5 -69.5t84 -49t121 -34t142.5 -21.5l-70 -156q-72 8 -160 24.5t-164.5 52.5 t-128 95.5t-51.5 153.5v10t2 16q-197 20 -317.5 155.5t-120.5 381.5zM461 535q0 -182 79 -288t251 -106q137 0 247.5 66.5t187.5 173t117.5 240t40.5 266.5q0 182 -78.5 287.5t-250.5 105.5q-139 0 -249 -66.5t-186.5 -173t-117.5 -239.5t-41 -266z" />
<glyph unicode="R" horiz-adv-x="1247" d="M166 0l336 1401q86 20 182 26.5t182 6.5q227 0 360.5 -92.5t133.5 -270.5q0 -180 -105.5 -302t-296.5 -175q25 -43 58 -109.5t67.5 -147.5t67.5 -168t57 -169h-202q-29 78 -60 158t-62.5 153.5t-63.5 138t-58 113.5q-27 -2 -53.5 -3t-53.5 -1h-157l-133 -559h-199z M537 721h120q84 0 172.5 12t161 48t118.5 100.5t46 171.5q0 115 -87 164t-226 49q-145 0 -176 -9z" />
<glyph unicode="S" horiz-adv-x="1058" d="M76 74l92 162q39 -27 123 -61t213 -34q63 0 125.5 13.5t112 44t80 81t30.5 123.5q0 51 -19.5 89t-53.5 68t-80 55.5t-101 54.5q-47 25 -95 53.5t-86 70.5t-61.5 99t-23.5 137q0 92 35.5 170t105.5 134.5t171 87t233 30.5q61 0 116.5 -9t101.5 -23t80.5 -30t51.5 -28 l-92 -156q-35 29 -112 52.5t-181 23.5q-66 0 -122 -13.5t-97 -42t-66 -72.5t-25 -105q0 -53 16.5 -89t45.5 -64t71 -50.5t91 -46.5q61 -31 115.5 -62.5t95.5 -75t64.5 -100.5t23.5 -139q0 -113 -44 -198t-121 -141t-182.5 -85t-228.5 -29q-82 0 -148.5 11.5t-116.5 28 t-85 33.5t-53 32z" />
<glyph unicode="T" horiz-adv-x="1136" d="M338 1253l39 166h1055l-39 -166h-428l-301 -1253h-199l301 1253h-428z" />
<glyph unicode="U" horiz-adv-x="1363" d="M270 377q0 45 8.5 100t18.5 103l201 839h198l-204 -852q-10 -39 -16.5 -86t-6.5 -88q0 -53 12.5 -98t42 -79t76.5 -53.5t115 -19.5q174 0 270 97.5t145 310.5l209 868h199l-213 -891q-29 -117 -66.5 -219t-107.5 -177t-181.5 -119t-285.5 -44q-106 0 -184 32t-129.5 86 t-76 129t-24.5 161z" />
<glyph unicode="V" horiz-adv-x="1275" d="M340 1419h205q33 -365 62.5 -653t70.5 -530q86 123 175 264t178 293.5t174 310.5t163 315h219q-113 -213 -220 -404.5t-212.5 -366.5t-211 -335.5t-216.5 -312.5h-195q-31 154 -57 310.5t-49 328.5t-44.5 363.5t-41.5 416.5z" />
<glyph unicode="W" horiz-adv-x="1875" d="M362 676q0 178 6.5 359.5t18.5 383.5h197q-14 -215 -22.5 -425t-8.5 -427q0 -158 6 -313q141 217 281.5 469t281.5 524h183q16 -274 45.5 -532t58.5 -461q61 115 128 252t133.5 287.5t133 309.5t127.5 316h211q-164 -406 -326.5 -756t-342.5 -663h-201q-35 217 -68 447.5 t-53 502.5q-139 -254 -273.5 -484t-285.5 -466h-201q-16 166 -22.5 332t-6.5 344z" />
<glyph unicode="X" horiz-adv-x="1239" d="M27 0l653 758l-289 661h219l215 -522q111 129 217.5 260t198.5 262h227q-123 -168 -270 -341t-291 -333q78 -184 155 -374.5t136 -370.5h-207q-47 143 -103.5 291.5t-125.5 302.5l-502 -594h-233z" />
<glyph unicode="Y" horiz-adv-x="1171" d="M336 1419h215q39 -166 87 -338t107 -331q135 158 273.5 331.5t245.5 337.5h227q-154 -209 -322 -418.5t-360 -431.5l-135 -569h-199l135 569q-80 215 -148.5 431.5t-125.5 418.5z" />
<glyph unicode="Z" horiz-adv-x="1148" d="M72 0l30 133q59 80 137 175t166.5 198.5t182.5 208t186 203t178 184.5t158 151h-702l38 166h955l-37 -157q-57 -51 -135 -127t-168 -167t-185.5 -192.5t-189.5 -207t-180 -208t-156 -194.5h766l-39 -166h-1005z" />
<glyph unicode="[" horiz-adv-x="659" d="M98 -379l477 1983h453l-35 -150h-274l-406 -1683h275l-37 -150h-453z" />
<glyph unicode="\" horiz-adv-x="770" d="M338 1604h174l217 -1983h-178z" />
<glyph unicode="]" horiz-adv-x="659" d="M-84 -379l35 150h274l406 1683h-275l37 150h453l-477 -1983h-453z" />
<glyph unicode="^" d="M266 741l549 684h162l223 -702l-156 -68l-182 574l-456 -572z" />
<glyph unicode="_" horiz-adv-x="1015" d="M-94 -379l39 162h1024l-39 -162h-1024z" />
<glyph unicode="`" horiz-adv-x="770" d="M471 1479l139 116l260 -327l-100 -88z" />
<glyph unicode="a" horiz-adv-x="1114" d="M201 356q0 137 46 268.5t138 234t231.5 165.5t325.5 63q55 0 129 -12t127 -41l-158 -643q-10 -47 -17 -90t-7 -90t8 -95t29 -104l-168 -24q-10 23 -20.5 50.5t-16.5 57.5q-45 -41 -121 -82t-186 -41q-90 0 -154.5 29t-105.5 80t-60.5 120.5t-19.5 153.5zM391 377 q0 -43 8.5 -87t30 -78t57.5 -55.5t93 -21.5q86 0 147 37t107 86q2 35 7 73t13 77l123 507q-10 4 -33.5 6.5t-42.5 2.5q-117 0 -211 -45t-160.5 -120t-102.5 -174.5t-36 -207.5z" />
<glyph unicode="b" horiz-adv-x="1136" d="M166 41l362 1515l199 33l-135 -567q47 29 108.5 49.5t130.5 20.5q88 0 155 -29t110 -79t64.5 -119.5t21.5 -149.5q0 -141 -45 -273.5t-137.5 -236t-230.5 -166t-325 -62.5q-31 0 -67.5 3.5t-74.5 11.5t-73.5 20.5t-62.5 28.5zM383 154q20 -4 47 -8.5t53 -4.5 q117 0 211 46t160 123t101.5 176.5t35.5 207.5q0 45 -9 88t-32.5 76t-64 52.5t-101.5 19.5t-121.5 -21.5t-113.5 -60.5z" />
<glyph unicode="c" horiz-adv-x="923" d="M205 381q0 139 42 268t124 227.5t200.5 157t272.5 58.5q68 0 129 -9.5t117 -35.5l-72 -160q-33 18 -77 28.5t-114 10.5q-100 0 -180 -44t-136 -117t-86 -168t-30 -198q0 -53 11.5 -100t40 -83t76.5 -56.5t122 -20.5q37 0 78 7.5t76 17.5t62.5 21.5t39.5 21.5l17 -162 q-39 -25 -121 -48.5t-187 -23.5q-98 0 -174 30t-127 84t-77.5 129t-26.5 165z" />
<glyph unicode="d" horiz-adv-x="1120" d="M201 356q0 137 46 268.5t137 234t228.5 164.5t319.5 62q23 0 45 -2t47 -6l115 479l198 33l-288 -1198q-10 -47 -18.5 -90t-8.5 -90t8 -95t29 -104l-168 -24q-23 45 -35 108q-45 -41 -121 -82t-186 -41q-90 0 -155.5 28t-108.5 79t-63.5 121.5t-20.5 154.5zM391 377 q0 -47 10.5 -90t33 -77t60.5 -54.5t93 -20.5q86 0 146.5 36t105.5 83q2 33 7 70.5t15 83.5l123 512q-18 4 -40.5 5t-43.5 1q-117 0 -211 -45t-160.5 -121t-102.5 -175.5t-36 -207.5z" />
<glyph unicode="e" horiz-adv-x="1060" d="M205 354q0 143 43 276.5t123 236t194.5 163t259.5 60.5q84 0 144.5 -21.5t98.5 -58.5t56.5 -85t18.5 -100q0 -96 -35 -162.5t-92.5 -111.5t-134 -69.5t-159.5 -39t-168 -19.5t-161 -9q-2 -16 -2 -26.5v-16.5q0 -47 11.5 -89t42 -74t85 -50.5t142.5 -18.5q39 0 81 7.5 t79.5 18.5t67.5 22.5t42 21.5l16 -162q-41 -23 -132 -48.5t-214 -25.5q-104 0 -180 30t-127 82t-75.5 121.5t-24.5 147.5zM418 559q119 4 217 15.5t170 39t111.5 74.5t39.5 123q0 18 -7 38.5t-24.5 38t-48 29t-75.5 11.5q-72 0 -134.5 -31t-111.5 -81t-84 -116.5t-53 -140.5 z" />
<glyph unicode="f" horiz-adv-x="753" d="M-35 -362q61 80 96 135t58.5 112.5t41 132t46.5 193.5l227 950q25 102 63 182t96 134.5t138 83t191 28.5q72 0 136 -14t97 -31l-76 -151q-16 8 -56 20t-118 12q-68 0 -115 -19.5t-79.5 -54t-52 -83.5t-33.5 -111l-23 -92h356l-36 -160h-357l-164 -676q-25 -104 -43 -186 t-43.5 -153.5t-67.5 -138t-114 -138.5z" />
<glyph unicode="g" horiz-adv-x="1126" d="M53 -303l72 160q47 -35 125 -56.5t164 -21.5q84 0 147.5 20.5t109.5 59.5t74.5 92t45.5 121l8 39q-41 -23 -111 -45.5t-143 -22.5q-166 0 -255 83t-89 232q0 137 45 268.5t137 234t229.5 164.5t321.5 62q29 0 64.5 -4t73.5 -11t74 -19.5t62 -28.5l-223 -932 q-27 -111 -65.5 -199t-106.5 -149t-174.5 -95t-265.5 -34q-88 0 -174 23.5t-146 58.5zM391 412q0 -111 48.5 -160t146.5 -49q82 0 150.5 29.5t105.5 56.5l149 622q-20 4 -46.5 8.5t-51.5 4.5q-111 0 -203 -42t-158.5 -113t-103.5 -164t-37 -193z" />
<glyph unicode="h" horiz-adv-x="1130" d="M156 0l374 1556l199 33l-131 -538q47 16 99.5 26t105.5 10q92 0 158.5 -25.5t107.5 -69.5t60.5 -106.5t19.5 -135.5q0 -90 -29 -205l-131 -545h-190l121 504q14 57 27.5 116.5t13.5 112.5q0 82 -45.5 136.5t-167.5 54.5q-49 0 -101.5 -9.5t-87.5 -25.5l-213 -889h-190z " />
<glyph unicode="i" horiz-adv-x="509" d="M156 0l256 1065h190l-256 -1065h-190zM457 1366q0 66 41 103.5t92 37.5q47 0 79.5 -29.5t32.5 -82.5q0 -61 -43 -99.5t-92 -38.5q-47 0 -78.5 29t-31.5 80z" />
<glyph unicode="j" horiz-adv-x="509" d="M-227 -348l43 158q23 -14 57.5 -22.5t75.5 -8.5q92 0 140 71.5t77 188.5l246 1026h190l-246 -1024q-29 -115 -62.5 -196t-83.5 -132t-120 -74.5t-170 -23.5q-35 0 -79 10t-68 27zM459 1366q0 66 41 103.5t92 37.5q47 0 80 -29.5t33 -82.5q0 -61 -43.5 -99.5t-92.5 -38.5 q-47 0 -78.5 29t-31.5 80z" />
<glyph unicode="k" horiz-adv-x="1058" d="M156 0l374 1556l199 33l-229 -948q68 49 139.5 107.5t136 116t118.5 109.5t89 91h225q-43 -53 -109.5 -118.5t-140 -132t-148.5 -128t-134 -104.5q55 -51 111.5 -122t108.5 -150t95 -159.5t72 -150.5h-207q-72 166 -171 305t-210 227l-129 -532h-190z" />
<glyph unicode="l" horiz-adv-x="565" d="M221 279q0 47 8.5 98t22.5 106l258 1073l199 33l-269 -1120q-10 -45 -20 -88t-10 -82q0 -70 33.5 -111t125.5 -51l-43 -155q-84 2 -142 25.5t-94 62.5t-52.5 92t-16.5 117z" />
<glyph unicode="m" horiz-adv-x="1695" d="M156 0l245 1024q92 29 202 46t196 17t145.5 -29.5t93.5 -78.5q20 14 56 32.5t82.5 35t101 28.5t111.5 12q90 0 152.5 -27.5t100 -74.5t54 -109.5t16.5 -132.5q0 -45 -7 -97t-19 -101l-132 -545h-190l121 504q10 47 23.5 105.5t13.5 115.5q0 39 -9.5 76t-31 63.5t-60.5 43 t-96 16.5q-66 0 -127 -26t-102 -52q4 -29 7 -56.5t3 -54.5q0 -47 -7 -95t-18 -95l-131 -545h-190l121 504q10 47 22 106.5t12 118.5q0 39 -8 75t-27.5 62.5t-52 42t-84.5 15.5q-51 0 -99 -6.5t-81 -16.5l-217 -901h-190z" />
<glyph unicode="n" horiz-adv-x="1128" d="M156 0l245 1024q94 29 204 46t196 17q92 0 158.5 -25.5t107.5 -69.5t60.5 -106.5t19.5 -135.5q0 -47 -8.5 -99.5t-20.5 -105.5l-131 -545h-190l121 504q14 57 27 116.5t13 112.5q0 82 -45 136.5t-168 54.5q-51 0 -100 -6.5t-82 -16.5l-217 -901h-190z" />
<glyph unicode="o" horiz-adv-x="1136" d="M205 346q0 119 33.5 250t107.5 241.5t190.5 182.5t282.5 72q168 0 263.5 -99.5t95.5 -273.5q0 -119 -34 -250t-107.5 -241.5t-190.5 -182.5t-283 -72q-168 0 -263 99.5t-95 273.5zM395 362q0 -104 45 -165.5t156 -61.5q92 0 165 53.5t123 136.5t76.5 183t26.5 194 q0 104 -45 166t-156 62q-92 0 -164.5 -53.5t-122.5 -136.5t-77 -183t-27 -195z" />
<glyph unicode="p" horiz-adv-x="1118" d="M66 -379l335 1399q86 35 187.5 53.5t187.5 18.5q100 0 173 -29t120 -79t70.5 -118.5t23.5 -150.5q0 -141 -43 -273.5t-132 -235t-225 -163.5t-323 -61q-23 0 -49 2t-47 6l-88 -369h-190zM383 150q20 -4 44 -5.5t46 -1.5q117 0 209 45t156.5 122t99.5 176.5t35 207.5 q0 45 -10.5 88t-38 76t-74.5 52.5t-121 19.5q-51 0 -94 -9.5t-72 -17.5z" />
<glyph unicode="q" horiz-adv-x="1128" d="M201 356q0 137 46 268.5t138 234t231.5 165.5t325.5 63q57 0 132 -13t136 -46l-338 -1407h-190l98 412q-47 -25 -106.5 -42.5t-124.5 -17.5q-90 0 -155.5 28t-108.5 79t-63.5 121.5t-20.5 154.5zM391 377q0 -47 9.5 -90t32 -77t62.5 -54.5t101 -20.5q59 0 118.5 19.5 t108.5 56.5l170 702q-57 10 -92 11q-117 0 -211 -45t-160.5 -120t-102.5 -174.5t-36 -207.5z" />
<glyph unicode="r" horiz-adv-x="761" d="M156 0l245 1024q82 29 176.5 46t188.5 17q25 0 55.5 -2t60 -7t54.5 -10t37 -11l-55 -166q-41 16 -94.5 24.5t-102.5 8.5q-43 0 -85 -6.5t-73 -16.5l-217 -901h-190z" />
<glyph unicode="s" horiz-adv-x="843" d="M78 51l74 156q14 -8 39.5 -20.5t61.5 -24.5t79 -20.5t90 -8.5q43 0 87 8.5t78 26.5t55.5 50t21.5 79q0 31 -10.5 54.5t-33 44t-56.5 40t-81 43.5q-47 23 -86 45.5t-68.5 53t-46 72.5t-16.5 102q0 154 110.5 247t321.5 93q98 0 173 -19.5t104 -36.5l-72 -155 q-29 16 -92 34.5t-141 18.5q-39 0 -78 -8t-71 -27.5t-51 -51.5t-19 -77q0 -55 43 -91t129 -75q53 -25 95 -50.5t71.5 -57t45 -74.5t15.5 -103q0 -86 -33 -151.5t-91 -108.5t-138 -64.5t-174 -21.5q-70 0 -126.5 9.5t-98.5 21.5t-69.5 25.5t-41.5 21.5z" />
<glyph unicode="t" horiz-adv-x="796" d="M223 236q0 84 29 215l219 911l199 33l-80 -330h362l-39 -160h-362l-113 -471q-24 -92 -24 -162v-6q0 -68 36.5 -97.5t122.5 -29.5q59 0 117 19.5t88 36.5l15 -162q-31 -18 -105 -40t-168 -22q-154 0 -225.5 67.5t-71.5 197.5z" />
<glyph unicode="u" horiz-adv-x="1126" d="M225 309q0 53 9.5 105.5t21.5 105.5l131 545h191l-121 -504q-10 -47 -26.5 -110.5t-16.5 -126.5q0 -37 7 -71t26.5 -59.5t52 -41t82.5 -15.5q88 0 154.5 38t111.5 89q2 33 6 68t14 76l158 657h191l-162 -674q-10 -47 -17.5 -91t-7.5 -91t8.5 -94t27.5 -103l-167 -24 q-10 23 -20.5 51.5t-16.5 60.5q-23 -20 -53.5 -42.5t-70.5 -41t-88 -31t-105 -12.5q-90 0 -150.5 27t-98.5 72t-54.5 106.5t-16.5 130.5z" />
<glyph unicode="v" horiz-adv-x="985" d="M260 1065h193q2 -100 9 -214t17 -227.5t24.5 -219t31.5 -191.5q61 78 127.5 183.5t128 221t112.5 232.5t82 215h193q-45 -135 -115 -280.5t-151 -286t-170 -269.5t-173 -229h-174q-25 104 -47 229t-40.5 261.5t-31 282t-16.5 292.5z" />
<glyph unicode="w" horiz-adv-x="1611" d="M276 1065h191q2 -96 6 -209t10 -226.5t14.5 -220t20.5 -188.5q63 88 126 194.5t119 217t104 221.5t83 211h172q0 -92 3 -197.5t10.5 -216t17 -220.5t21.5 -210q51 63 114.5 167t125 221.5t114.5 237.5t82 218h194q-45 -141 -114.5 -290.5t-150.5 -290t-168 -265.5 t-167 -219h-170q-23 178 -40 358.5t-21 366.5q-41 -92 -92.5 -190.5t-108.5 -193.5t-114.5 -183t-110.5 -158h-170q-43 211 -69.5 480.5t-31.5 584.5z" />
<glyph unicode="x" horiz-adv-x="968" d="M25 0l491 549l-264 516h211l184 -383q86 104 159 200.5t126 182.5h207q-88 -129 -193.5 -261t-216.5 -261q66 -129 131.5 -267.5t112.5 -275.5h-199q-31 92 -79 198.5t-95 204.5l-354 -403h-221z" />
<glyph unicode="y" horiz-adv-x="978" d="M-115 -340l66 156q20 -16 55 -27.5t78 -11.5q119 0 202 57t156 152q-76 248 -124 522t-58 557h195q2 -92 10 -201.5t23.5 -227.5t36.5 -236.5t50 -227.5q78 123 141.5 255t112 253t81 222.5t46.5 162.5h199q-23 -78 -63.5 -193.5t-99 -250t-131.5 -274.5t-159 -269 q-66 -96 -131 -181t-140 -147.5t-165 -98.5t-203 -36q-57 0 -106 14.5t-72 30.5z" />
<glyph unicode="z" horiz-adv-x="929" d="M68 0l28 123q61 90 155.5 199.5t196 218t199.5 205t172 159.5h-510l37 160h760l-33 -137q-59 -53 -160.5 -150.5t-212 -209t-212 -221t-160.5 -187.5h571l-37 -160h-794z" />
<glyph unicode="{" horiz-adv-x="667" d="M209 539l37 147q45 0 84 17.5t68.5 45t49 63.5t27.5 73l88 368q23 94 56.5 161t88 109t133.5 61.5t189 19.5h8l-37 -150q-55 0 -98 -7t-74.5 -29.5t-54 -62.5t-37.5 -104l-75 -315q-33 -135 -79 -211t-132 -113q43 -35 60 -83t17 -101q0 -35 -6 -69.5t-14 -69.5l-76 -316 q-6 -29 -10 -51t-4 -41q0 -66 43 -86t135 -24l-39 -150h-8q-147 0 -227 52.5t-80 174.5q0 47 16 123l88 369q8 31 8 59q0 57 -33.5 96.5t-111.5 43.5z" />
<glyph unicode="|" horiz-adv-x="563" d="M98 -379l477 1983h175l-478 -1983h-174z" />
<glyph unicode="}" horiz-adv-x="667" d="M-86 -379l37 150q57 0 99 7t74 29.5t53.5 61.5t37.5 104l76 316q33 135 79 210.5t132 112.5q-43 35 -60.5 83t-17.5 102q0 35 6 69.5t14 69.5l76 315q6 29 10.5 51.5t4.5 40.5q0 66 -43 86.5t-136 24.5l39 150h8q147 0 227.5 -52.5t80.5 -175.5q0 -47 -17 -123l-88 -368 q-8 -31 -8 -60q0 -57 34 -96t111 -43l-36 -147q-47 0 -86 -17.5t-68 -45t-48.5 -63.5t-27.5 -73l-88 -369q-23 -94 -56.5 -160.5t-88 -108.5t-133 -61.5t-189.5 -19.5h-8z" />
<glyph unicode="~" d="M217 492q16 45 44 95t69 93t95 70.5t124 27.5q72 0 118 -27.5t85 -56.5q47 -33 83.5 -60.5t92.5 -27.5q33 0 60.5 16.5t48 40t37 52t28.5 53.5l131 -41q-16 -45 -44 -95t-69 -93t-95 -71t-124 -28q-72 0 -118 28t-85 56q-47 33 -83.5 60.5t-92.5 27.5q-35 0 -61.5 -16 t-48 -40t-37 -52.5t-27.5 -52.5z" />
<glyph unicode="&#xa1;" horiz-adv-x="512" d="M59 -379l93 385q12 49 33.5 128t47 163t53 165t50.5 134h117q-16 -139 -38 -279.5t-63 -312.5l-92 -383h-201zM332 911q0 74 44 114t101 40t91 -32t34 -93q0 -35 -13.5 -62.5t-33.5 -47t-46.5 -29.5t-53.5 -10q-57 0 -90 30.5t-33 89.5z" />
<glyph unicode="&#xa2;" d="M326 461q0 111 32.5 219t98 199.5t162 157t223.5 87.5l69 295h166l-67 -284q49 -4 96 -15.5t90 -34.5l-70 -151q-33 18 -76.5 28.5t-111.5 10.5q-96 0 -175 -44t-134.5 -116t-86 -162t-30.5 -184q0 -49 11.5 -93t39 -77t73.5 -52.5t118 -19.5q37 0 76.5 6.5t74.5 16.5 t61.5 21.5t39.5 21.5l14 -158q-33 -23 -105.5 -44t-164.5 -26l-68 -286h-166l70 297q-260 57 -260 387z" />
<glyph unicode="&#xa3;" d="M260 0q57 147 108.5 289.5t86.5 290.5l8 30h-191l37 154h191l41 170q37 154 86 253t115.5 156.5t150.5 80t188 22.5q106 0 166 -19.5t107 -46.5l-86 -153q-47 25 -97.5 39t-113.5 14q-59 0 -108.5 -16.5t-89.5 -56.5t-71.5 -105.5t-56.5 -163.5l-41 -174h387l-37 -154 h-387l-4 -16q-25 -104 -60.5 -215t-76.5 -215h594l-39 -164h-807z" />
<glyph unicode="&#xa4;" d="M248 342l149 150q-57 90 -57 219q0 127 57 217l-149 151l119 119l151 -154q96 59 217 60q119 0 215 -60l154 154l119 -119l-152 -151q59 -90 59 -217t-59 -217l152 -152l-119 -119l-154 152q-96 -57 -215 -58q-123 0 -217 58l-151 -152zM494 711q0 -59 19 -105.5t52 -80 t77 -51t93 -17.5t93 17.5t77 51t52.5 80t19.5 105.5q0 57 -19.5 104t-52.5 81t-77 51.5t-93 17.5t-93 -17.5t-77 -51.5t-52 -81t-19 -104z" />
<glyph unicode="&#xa5;" d="M203 217l35 145h331l49 209h-329l35 146h278q-145 360 -227 702h196q14 -72 35 -151.5t44.5 -161.5t51 -163t58.5 -152q57 68 120.5 146.5t126 160.5t121 163.5t105.5 157.5h215q-123 -178 -268.5 -361.5t-286.5 -340.5h278l-34 -146h-334l-49 -209h333l-34 -145h-334 l-51 -217h-185l52 217h-332z" />
<glyph unicode="&#xa6;" horiz-adv-x="563" d="M98 -379l189 785h174l-189 -785h-174zM387 819l188 785h175l-189 -785h-174z" />
<glyph unicode="&#xa7;" horiz-adv-x="995" d="M66 -152l73 150q39 -23 113 -45.5t172 -22.5q135 0 203.5 47.5t68.5 124.5q0 39 -13 66t-36.5 45t-58.5 32.5t-78 30.5q-57 20 -107.5 43t-88.5 57t-60.5 84t-22.5 124q0 111 73 202t177 148q-59 59 -59 166q0 84 32.5 149.5t92 110.5t142.5 68.5t181 23.5 q104 0 179 -21.5t122 -44.5l-71 -147q-35 16 -93.5 37.5t-150.5 21.5q-109 0 -178.5 -39.5t-69.5 -130.5q0 -59 39 -91.5t131 -65.5q57 -23 107.5 -45.5t87.5 -55t58.5 -82t21.5 -124.5q0 -117 -72 -204t-178 -147q29 -27 54.5 -70.5t25.5 -121.5q0 -74 -31 -137.5 t-91.5 -109.5t-148.5 -71.5t-202 -25.5q-111 0 -198 19.5t-146 51.5zM410 606q0 -41 19.5 -69.5t51 -51t74.5 -41t92 -36.5q12 -4 21.5 -8.5t17.5 -8.5q35 16 68.5 41t60.5 57.5t43 70.5t16 81q0 39 -18 68.5t-50 52t-76 41t-93 37.5q-27 10 -35 16q-35 -16 -68.5 -42 t-61.5 -57.5t-45 -69.5t-17 -81z" />
<glyph unicode="&#xa8;" horiz-adv-x="770" d="M403 1366q0 61 41 99t88 38q45 0 77 -29.5t32 -78.5q0 -59 -42 -96t-89 -37q-45 0 -76 28.5t-31 75.5zM793 1366q0 61 40.5 99t88.5 38q45 0 76.5 -29.5t31.5 -78.5q0 -59 -42 -96t-89 -37q-45 0 -75.5 28.5t-30.5 75.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1667" d="M291 711q0 174 57.5 312t153.5 233.5t223 145.5t266 50t266 -50t223.5 -145.5t154 -233.5t57.5 -312q0 -176 -57.5 -313.5t-154 -232.5t-223.5 -145.5t-266 -50.5t-266 50.5t-223 145.5t-153.5 232.5t-57.5 313.5zM440 711q0 -135 41 -247t114 -191.5t174 -123t222 -43.5 t222.5 43.5t174 123t113.5 191.5t41 247t-41 246.5t-113.5 191.5t-174 123t-222.5 43t-222 -43t-174 -123t-114 -191.5t-41 -246.5zM623 711q0 100 31.5 178t85 132t125 83t151.5 29q53 0 97 -8.5t77 -19.5t54.5 -22.5t29.5 -17.5l-47 -123q-25 14 -79 33.5t-126 19.5 q-104 0 -174 -71.5t-70 -212.5q0 -129 63.5 -209t186.5 -80q80 0 132 17.5t81 31.5l39 -123q-23 -12 -93.5 -36.5t-170.5 -24.5q-88 0 -160 28.5t-124 82t-80.5 132t-28.5 181.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="849" d="M324 946q0 94 32.5 184.5t98 161t164 114.5t231.5 44q37 0 91 -9t99 -28l-112 -416q-18 -66 -19 -127q0 -51 10.5 -86.5t26.5 -78.5l-141 -25q-8 10 -17.5 30.5t-13.5 41.5q-29 -29 -79 -52.5t-120 -23.5q-133 0 -192 74.5t-59 195.5zM473 961q0 -31 5 -60t20.5 -51.5 t42 -35.5t67.5 -13q45 0 84 22.5t70 55.5q0 31 7 66.5t13 62.5l82 311q-4 2 -15 4t-28 2q-78 0 -142.5 -29.5t-110.5 -81t-70.5 -117t-24.5 -136.5z" />
<glyph unicode="&#xab;" horiz-adv-x="1021" d="M197 594l415 440l119 -71l-321 -390l155 -385l-127 -59zM623 594l415 440l119 -71l-321 -390l155 -385l-127 -59z" />
<glyph unicode="&#xac;" d="M270 641l39 162h920l-168 -699h-172l129 537h-748z" />
<glyph unicode="&#xad;" horiz-adv-x="600" d="M178 524l41 178h496l-41 -178h-496z" />
<glyph unicode="&#xae;" horiz-adv-x="1667" d="M291 711q0 174 57.5 312t153.5 233.5t223 145.5t266 50t266 -50t223.5 -145.5t154 -233.5t57.5 -312q0 -176 -57.5 -313.5t-154 -232.5t-223.5 -145.5t-266 -50.5t-266 50.5t-223 145.5t-153.5 232.5t-57.5 313.5zM440 711q0 -135 41 -247t114 -191.5t174 -123t222 -43.5 t222.5 43.5t174 123t113.5 191.5t41 247t-41 246.5t-113.5 191.5t-174 123t-222.5 43t-222 -43t-174 -123t-114 -191.5t-41 -246.5zM705 303v795q55 12 116.5 18t112.5 6q178 0 269 -64.5t91 -199.5q0 -76 -40 -132t-113 -87q20 -25 46.5 -61.5t56.5 -80.5t57.5 -94.5 t50.5 -99.5h-158q-47 92 -105.5 175t-101.5 132h-137v-307h-145zM850 729h78q92 0 153.5 25.5t61.5 105.5q0 76 -58.5 105.5t-140.5 29.5q-23 0 -47.5 -1t-46.5 -3v-262z" />
<glyph unicode="&#xaf;" horiz-adv-x="770" d="M412 1309l34 147h574l-35 -147h-573z" />
<glyph unicode="&#xb0;" horiz-adv-x="679" d="M311 1305q0 66 23.5 119t62.5 89.5t91.5 56t109.5 19.5t109.5 -19.5t91.5 -56t62.5 -89.5t23.5 -119t-23.5 -119t-62.5 -90t-91.5 -56.5t-109.5 -19.5t-109.5 19.5t-91.5 56.5t-62.5 90t-23.5 119zM449 1305q0 -72 44 -114t105 -42t105.5 42t44.5 114t-44.5 113.5 t-105.5 41.5t-105 -42t-44 -113z" />
<glyph unicode="&#xb1;" d="M113 0l39 162h919l-39 -162h-919zM279 686l38 162h373l103 422h174l-103 -422h373l-39 -162h-373l-96 -401h-172l94 401h-372z" />
<glyph unicode="&#xb2;" horiz-adv-x="735" d="M254 645q8 74 32.5 128t61.5 97t84 78t100 68t93.5 59.5t67 51t39.5 52t13 62.5q0 43 -29.5 63.5t-70.5 20.5q-86 0 -136 -29.5t-77 -52.5l-49 100q41 39 112.5 74t166.5 35q117 0 173 -54.5t56 -146.5q0 -59 -27.5 -108t-70.5 -90t-94.5 -75t-98.5 -63 q-43 -29 -94 -61.5t-70 -79.5h387l-30 -129h-539z" />
<glyph unicode="&#xb3;" horiz-adv-x="735" d="M231 684l56 115q35 -16 85 -30.5t103 -14.5q43 0 83 6t70.5 23.5t50 46t19.5 71.5q0 57 -53 82t-141 25h-43l28 114h52q29 0 64.5 6.5t66 20.5t52 36.5t21.5 57.5q0 47 -27.5 65.5t-86.5 18.5q-55 0 -107.5 -16.5t-85.5 -32.5l-28 106q18 12 47.5 23.5t65.5 22t73 16.5 t70 6q121 0 171 -49t50 -119q0 -82 -42 -131t-116 -84q49 -20 81 -59t32 -105q0 -135 -102.5 -205.5t-268.5 -70.5q-82 0 -140.5 16.5t-99.5 38.5z" />
<glyph unicode="&#xb4;" horiz-adv-x="770" d="M537 1288l323 309l105 -147l-347 -262z" />
<glyph unicode="&#xb5;" horiz-adv-x="1171" d="M66 -379l346 1444h190l-121 -504q-10 -47 -26.5 -110.5t-16.5 -126.5q0 -37 7.5 -71t28 -59.5t53 -41t85.5 -15.5q78 0 142.5 26t119.5 73q0 53 5.5 104t19.5 109l148 616h190l-152 -633q-10 -47 -18 -88t-8 -76q0 -53 25.5 -88t91.5 -45l-43 -153q-86 2 -137.5 29.5 t-82.5 78.5q-74 -57 -164 -83.5t-190 -26.5q-63 0 -118.5 25.5t-84.5 72.5q-8 -53 -21 -117.5t-30 -128.5l-51 -211h-188z" />
<glyph unicode="&#xb6;" horiz-adv-x="1331" d="M313 868q0 154 70 261.5t181.5 175t250.5 98.5t279 31q86 0 182 -7.5t174 -27.5l-426 -1778h-160l396 1645q-52 10 -158 10h-35.5t-32.5 -2l-397 -1653h-160l219 916q-207 14 -295 100t-88 231z" />
<glyph unicode="&#xb7;" horiz-adv-x="503" d="M256 586q0 74 44 114t102 40q57 0 91 -32t34 -93q0 -35 -13.5 -62.5t-34 -47t-47 -30t-53.5 -10.5q-57 0 -90 31t-33 90z" />
<glyph unicode="&#xb8;" horiz-adv-x="770" d="M66 -356l47 112q16 -8 42.5 -15t65.5 -7q35 0 57.5 11t22.5 40q0 25 -16.5 37t-55.5 31l-12 6q12 20 30.5 47.5t37 54.5t35 48.5t24.5 31.5h131q-16 -23 -44 -58.5t-44 -60.5q41 -23 61.5 -49.5t20.5 -73.5q0 -92 -63.5 -139t-174.5 -47q-59 0 -99 9t-66 22z" />
<glyph unicode="&#xb9;" horiz-adv-x="735" d="M408 1272q94 29 177 70.5t146 93.5h103l-191 -791h-145l143 594q-53 -29 -113.5 -50.5t-101.5 -33.5z" />
<glyph unicode="&#xba;" horiz-adv-x="913" d="M334 934q0 82 24.5 173t79 168t141.5 127t212 50q123 0 193.5 -68.5t70.5 -193.5q0 -82 -24.5 -173t-79 -168t-141.5 -127t-212 -50q-123 0 -193.5 68.5t-70.5 193.5zM487 946q0 -66 33 -106.5t105 -40.5q66 0 117 35.5t86.5 91t54 123t18.5 129.5q0 66 -32.5 106.5 t-104.5 40.5q-66 0 -117 -36t-87 -91t-54.5 -122.5t-18.5 -129.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="1021" d="M143 201l322 389l-156 385l127 59l242 -465l-416 -440zM569 201l322 389l-156 385l127 59l242 -465l-416 -440z" />
<glyph unicode="&#xbc;" horiz-adv-x="1789" d="M401 0l1124 1419h195l-1124 -1419h-195zM408 1272q94 29 177 70.5t146 93.5h103l-191 -791h-145l143 594q-53 -29 -113.5 -50.5t-101.5 -33.5zM1163 195l23 94q33 55 89 121.5t125.5 136t145.5 134t148 114.5h133l-117 -486h102l-28 -114h-103l-45 -191h-139l45 191h-379 zM1329 309h242l78 324q-39 -33 -83 -72t-87 -82t-82 -86t-68 -84z" />
<glyph unicode="&#xbd;" horiz-adv-x="1789" d="M358 0l1124 1419h195l-1124 -1419h-195zM408 1272q94 29 177 70.5t146 93.5h103l-191 -791h-145l143 594q-53 -29 -113.5 -50.5t-101.5 -33.5zM1153 4q8 74 32.5 128t61.5 97t84 78t100 68t93.5 59.5t67 51t39.5 52t13 62.5q0 43 -29.5 63.5t-70.5 20.5q-86 0 -136 -29.5 t-77 -52.5l-49 100q41 39 112.5 74t166.5 35q117 0 173 -54.5t56 -146.5q0 -59 -27.5 -108t-70.5 -90t-94.5 -75t-98.5 -63q-43 -29 -94 -61.5t-70 -79.5h387l-30 -129h-539z" />
<glyph unicode="&#xbe;" horiz-adv-x="1789" d="M231 684l56 115q35 -16 85 -30.5t103 -14.5q43 0 83 6t70.5 23.5t50 46t19.5 71.5q0 57 -53 82t-141 25h-43l28 114h52q29 0 64.5 6.5t66 20.5t52 36.5t21.5 57.5q0 47 -27.5 65.5t-86.5 18.5q-55 0 -107.5 -16.5t-85.5 -32.5l-28 106q18 12 47.5 23.5t65.5 22t73 16.5 t70 6q121 0 171 -49t50 -119q0 -82 -42 -131t-116 -84q49 -20 81 -59t32 -105q0 -135 -102.5 -205.5t-268.5 -70.5q-82 0 -140.5 16.5t-99.5 38.5zM424 0l1124 1419h195l-1124 -1419h-195zM1163 195l23 94q33 55 89 121.5t125.5 136t145.5 134t148 114.5h133l-117 -486h102 l-28 -114h-103l-45 -191h-139l45 191h-379zM1329 309h242l78 324q-39 -33 -83 -72t-87 -82t-82 -86t-68 -84z" />
<glyph unicode="&#xbf;" horiz-adv-x="786" d="M80 -106q0 68 19.5 122t54 100t82 88t102.5 85q68 53 129 116.5t84 153.5q4 12 5 22.5t1 14.5h166q-2 -25 -4 -44.5t-6 -39.5q-25 -104 -95.5 -179t-148.5 -134q-37 -29 -72 -58.5t-63.5 -63.5t-46 -73t-17.5 -86q0 -23 6.5 -49.5t26 -48t54 -35.5t92.5 -14 q98 0 163.5 22.5t102.5 40.5l20 -147q-49 -29 -134 -50.5t-177 -21.5q-96 0 -161.5 22.5t-106.5 60.5t-58.5 89t-17.5 107zM539 911q0 74 44 114t101 40t91 -32t34 -93q0 -35 -13.5 -62.5t-34 -47t-46.5 -29.5t-53 -10q-57 0 -90 30.5t-33 89.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="1302" d="M20 0q229 414 447.5 762t431.5 657h195q31 -154 56.5 -310.5t50 -328.5t47 -364.5t46.5 -415.5h-200q-10 92 -18.5 185.5t-18.5 179.5h-619l-200 -365h-218zM530 524h508q-16 166 -37.5 330t-50.5 338q-72 -104 -178 -269t-242 -399zM799 1798l139 116l260 -327l-100 -88 z" />
<glyph unicode="&#xc1;" horiz-adv-x="1302" d="M20 0q229 414 447.5 762t431.5 657h195q31 -154 56.5 -310.5t50 -328.5t47 -364.5t46.5 -415.5h-200q-10 92 -18.5 185.5t-18.5 179.5h-619l-200 -365h-218zM530 524h508q-16 166 -37.5 330t-50.5 338q-72 -104 -178 -269t-242 -399zM863 1607l323 309l105 -147 l-347 -262z" />
<glyph unicode="&#xc2;" horiz-adv-x="1302" d="M20 0q229 414 447.5 762t431.5 657h195q31 -154 56.5 -310.5t50 -328.5t47 -364.5t46.5 -415.5h-200q-10 92 -18.5 185.5t-18.5 179.5h-619l-200 -365h-218zM530 524h508q-16 166 -37.5 330t-50.5 338q-72 -104 -178 -269t-242 -399zM758 1609l332 285l217 -303l-90 -84 l-162 178l-229 -168z" />
<glyph unicode="&#xc3;" horiz-adv-x="1302" d="M20 0q229 414 447.5 762t431.5 657h195q31 -154 56.5 -310.5t50 -328.5t47 -364.5t46.5 -415.5h-200q-10 92 -18.5 185.5t-18.5 179.5h-619l-200 -365h-218zM530 524h508q-16 166 -37.5 330t-50.5 338q-72 -104 -178 -269t-242 -399zM707 1634q41 86 97 132t148 46 q41 0 73 -14.5t61 -30.5q31 -16 55 -26.5t49 -10.5q43 0 71.5 32t51.5 66l96 -59q-41 -86 -97 -132t-149 -46q-41 0 -72.5 14t-60.5 31q-31 16 -55.5 26.5t-48.5 10.5q-43 0 -72 -32t-51 -67z" />
<glyph unicode="&#xc4;" horiz-adv-x="1302" d="M20 0q229 414 447.5 762t431.5 657h195q31 -154 56.5 -310.5t50 -328.5t47 -364.5t46.5 -415.5h-200q-10 92 -18.5 185.5t-18.5 179.5h-619l-200 -365h-218zM530 524h508q-16 166 -37.5 330t-50.5 338q-72 -104 -178 -269t-242 -399zM745 1685q0 61 41 99t88 38 q45 0 77 -29.5t32 -78.5q0 -59 -42 -96t-89 -37q-45 0 -76 28.5t-31 75.5zM1135 1685q0 61 40.5 99t88.5 38q45 0 76.5 -29.5t31.5 -78.5q0 -59 -42 -96t-89 -37q-45 0 -75.5 28.5t-30.5 75.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1302" d="M20 0q217 391 424 725t410 631q-29 27 -46 65.5t-17 87.5t17 87t46 66t66.5 42t80.5 14t81 -14t67 -42t46 -65.5t17 -87.5q0 -63 -27.5 -109t-72.5 -73q27 -143 49.5 -289.5t44 -308.5t43 -341t45.5 -388h-200q-10 92 -18.5 185.5t-18.5 179.5h-619l-200 -365h-218z M530 524h508q-16 166 -37.5 330t-50.5 338q-72 -104 -178 -269t-242 -399zM893 1509q0 -53 31.5 -82.5t76.5 -29.5t77 29.5t32 82.5t-31.5 83t-77.5 30q-45 0 -76.5 -29.5t-31.5 -83.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1900" d="M6 0q162 221 314.5 416.5t299 370t289 331t283.5 301.5h911l-39 -166h-610l-102 -428h538l-39 -161h-536l-121 -498h657l-39 -166h-854l86 365h-542q-70 -92 -136.5 -182.5t-132.5 -182.5h-227zM627 524h456l177 731q-160 -168 -320 -351t-313 -380z" />
<glyph unicode="&#xc7;" horiz-adv-x="1228" d="M254 520q0 184 58.5 352t166 297t259 206t337.5 77q160 0 255 -33t136 -61l-82 -164q-47 31 -127.5 58.5t-191.5 27.5q-141 0 -254 -66.5t-190.5 -172t-118.5 -237.5t-41 -261q0 -399 346 -400q74 0 132 10.5t103 23.5t80 29.5t62 29.5l24 -173q-25 -14 -64.5 -30.5 t-93 -30.5t-119 -23.5t-138.5 -9.5h-11q-10 -10 -17 -22.5t-15 -24.5q41 -23 61 -49.5t20 -73.5q0 -92 -63 -139t-174 -47q-59 0 -99 9t-67 22l47 112q16 -8 43 -15t66 -7q35 0 57.5 11t22.5 40q0 25 -16.5 37t-55.5 31l-12 6q14 25 36.5 59.5t45.5 63.5q-205 35 -306.5 175 t-101.5 363z" />
<glyph unicode="&#xc8;" horiz-adv-x="1140" d="M166 0l340 1419h837l-38 -166h-641l-103 -428h569l-38 -161h-568l-121 -498h689l-39 -166h-887zM733 1798l139 116l260 -327l-100 -88z" />
<glyph unicode="&#xc9;" horiz-adv-x="1140" d="M166 0l340 1419h837l-38 -166h-641l-103 -428h569l-38 -161h-568l-121 -498h689l-39 -166h-887zM797 1607l323 309l105 -147l-347 -262z" />
<glyph unicode="&#xca;" horiz-adv-x="1140" d="M166 0l340 1419h837l-38 -166h-641l-103 -428h569l-38 -161h-568l-121 -498h689l-39 -166h-887zM694 1609l332 285l217 -303l-90 -84l-162 178l-229 -168z" />
<glyph unicode="&#xcb;" horiz-adv-x="1140" d="M166 0l340 1419h837l-38 -166h-641l-103 -428h569l-38 -161h-568l-121 -498h689l-39 -166h-887zM679 1685q0 61 41 99t88 38q45 0 77 -29.5t32 -78.5q0 -59 -42 -96t-89 -37q-45 0 -76 28.5t-31 75.5zM1069 1685q0 61 40.5 99t88.5 38q45 0 76.5 -29.5t31.5 -78.5 q0 -59 -42 -96t-89 -37q-45 0 -75.5 28.5t-30.5 75.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="538" d="M166 0l340 1419h199l-340 -1419h-199zM418 1798l139 116l260 -327l-100 -88z" />
<glyph unicode="&#xcd;" horiz-adv-x="538" d="M166 0l340 1419h199l-340 -1419h-199zM482 1607l323 309l105 -147l-347 -262z" />
<glyph unicode="&#xce;" horiz-adv-x="538" d="M166 0l340 1419h199l-340 -1419h-199zM377 1609l332 285l217 -303l-90 -84l-162 178l-229 -168z" />
<glyph unicode="&#xcf;" horiz-adv-x="538" d="M166 0l340 1419h199l-340 -1419h-199zM364 1685q0 61 41 99t88 38q45 0 77 -29.5t32 -78.5q0 -59 -42 -96t-89 -37q-45 0 -76 28.5t-31 75.5zM754 1685q0 61 40.5 99t88.5 38q45 0 76.5 -29.5t31.5 -78.5q0 -59 -42 -96t-89 -37q-45 0 -75.5 28.5t-30.5 75.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1454" d="M199 664l37 153h165l140 584q86 20 182 26.5t182 6.5q315 0 471 -148.5t156 -400.5q0 -176 -55.5 -339t-172 -288t-298 -198.5t-433.5 -73.5q-86 0 -186 8t-176 29l154 641h-166zM442 164q16 -4 55.5 -7t127.5 -3q162 0 292 54t220 151.5t139 231.5t49 294 q0 180 -104.5 280.5t-331.5 100.5q-154 0 -184 -9l-107 -440h266l-37 -153h-266z" />
<glyph unicode="&#xd1;" horiz-adv-x="1429" d="M166 0l340 1419h170q66 -111 134.5 -251t131 -287.5t115.5 -288.5t88 -254l254 1081h194l-338 -1419h-174q-37 119 -88 270.5t-111.5 307t-124 302t-124.5 255.5l-273 -1135h-194zM768 1634q41 86 97 132t148 46q41 0 73 -14.5t61 -30.5q31 -16 55 -26.5t49 -10.5 q43 0 71.5 32t51.5 66l96 -59q-41 -86 -97 -132t-149 -46q-41 0 -72.5 14t-60.5 31q-31 16 -55.5 26.5t-48.5 10.5q-43 0 -72 -32t-51 -67z" />
<glyph unicode="&#xd2;" horiz-adv-x="1513" d="M254 510q0 168 51 337t154.5 303t257 218t358.5 84q113 0 208 -34t164 -101.5t106.5 -168.5t37.5 -237q0 -170 -52 -337.5t-154.5 -302t-256 -218.5t-358.5 -84q-115 0 -209 34t-162.5 101.5t-106.5 169t-38 236.5zM461 535q0 -182 79 -288t251 -106q137 0 247.5 66.5 t187.5 173t117.5 240t40.5 266.5q0 182 -78.5 287.5t-250.5 105.5q-139 0 -249 -66.5t-186.5 -173t-117.5 -239.5t-41 -266zM901 1798l139 116l260 -327l-100 -88z" />
<glyph unicode="&#xd3;" horiz-adv-x="1513" d="M254 510q0 168 51 337t154.5 303t257 218t358.5 84q113 0 208 -34t164 -101.5t106.5 -168.5t37.5 -237q0 -170 -52 -337.5t-154.5 -302t-256 -218.5t-358.5 -84q-115 0 -209 34t-162.5 101.5t-106.5 169t-38 236.5zM461 535q0 -182 79 -288t251 -106q137 0 247.5 66.5 t187.5 173t117.5 240t40.5 266.5q0 182 -78.5 287.5t-250.5 105.5q-139 0 -249 -66.5t-186.5 -173t-117.5 -239.5t-41 -266zM965 1607l323 309l105 -147l-347 -262z" />
<glyph unicode="&#xd4;" horiz-adv-x="1513" d="M254 510q0 168 51 337t154.5 303t257 218t358.5 84q113 0 208 -34t164 -101.5t106.5 -168.5t37.5 -237q0 -170 -52 -337.5t-154.5 -302t-256 -218.5t-358.5 -84q-115 0 -209 34t-162.5 101.5t-106.5 169t-38 236.5zM461 535q0 -182 79 -288t251 -106q137 0 247.5 66.5 t187.5 173t117.5 240t40.5 266.5q0 182 -78.5 287.5t-250.5 105.5q-139 0 -249 -66.5t-186.5 -173t-117.5 -239.5t-41 -266zM860 1609l332 285l217 -303l-90 -84l-162 178l-229 -168z" />
<glyph unicode="&#xd5;" horiz-adv-x="1513" d="M254 510q0 168 51 337t154.5 303t257 218t358.5 84q113 0 208 -34t164 -101.5t106.5 -168.5t37.5 -237q0 -170 -52 -337.5t-154.5 -302t-256 -218.5t-358.5 -84q-115 0 -209 34t-162.5 101.5t-106.5 169t-38 236.5zM461 535q0 -182 79 -288t251 -106q137 0 247.5 66.5 t187.5 173t117.5 240t40.5 266.5q0 182 -78.5 287.5t-250.5 105.5q-139 0 -249 -66.5t-186.5 -173t-117.5 -239.5t-41 -266zM809 1634q41 86 97 132t148 46q41 0 73 -14.5t61 -30.5q31 -16 55 -26.5t49 -10.5q43 0 71.5 32t51.5 66l96 -59q-41 -86 -97 -132t-149 -46 q-41 0 -72.5 14t-60.5 31q-31 16 -55.5 26.5t-48.5 10.5q-43 0 -72 -32t-51 -67z" />
<glyph unicode="&#xd6;" horiz-adv-x="1513" d="M254 510q0 168 51 337t154.5 303t257 218t358.5 84q113 0 208 -34t164 -101.5t106.5 -168.5t37.5 -237q0 -170 -52 -337.5t-154.5 -302t-256 -218.5t-358.5 -84q-115 0 -209 34t-162.5 101.5t-106.5 169t-38 236.5zM461 535q0 -182 79 -288t251 -106q137 0 247.5 66.5 t187.5 173t117.5 240t40.5 266.5q0 182 -78.5 287.5t-250.5 105.5q-139 0 -249 -66.5t-186.5 -173t-117.5 -239.5t-41 -266zM847 1685q0 61 41 99t88 38q45 0 77 -29.5t32 -78.5q0 -59 -42 -96t-89 -37q-45 0 -76 28.5t-31 75.5zM1237 1685q0 61 40.5 99t88.5 38 q45 0 76.5 -29.5t31.5 -78.5q0 -59 -42 -96t-89 -37q-45 0 -75.5 28.5t-30.5 75.5z" />
<glyph unicode="&#xd7;" d="M238 317l372 301l-223 295l129 105l223 -295l369 301l96 -123l-373 -301l224 -295l-129 -104l-224 295l-368 -301z" />
<glyph unicode="&#xd8;" horiz-adv-x="1513" d="M213 14l133 152q-92 131 -92 344q0 168 51 337t154.5 303t257 218t358.5 84q188 0 320 -94l121 137l114 -88l-133 -152q45 -66 69.5 -149.5t24.5 -194.5q0 -170 -52 -337.5t-154.5 -302t-256 -218.5t-358.5 -84q-193 0 -321 92l-119 -135zM461 535q0 -59 6 -112.5 t22 -94.5l779 886q-78 66 -213 66q-139 0 -249 -66.5t-186.5 -173t-117.5 -239.5t-41 -266zM575 207q39 -35 93.5 -50.5t122.5 -15.5q137 0 247.5 66.5t187.5 173t117.5 240t40.5 266.5q0 59 -6 110.5t-22 96.5z" />
<glyph unicode="&#xd9;" horiz-adv-x="1363" d="M270 377q0 45 8.5 100t18.5 103l201 839h198l-204 -852q-10 -39 -16.5 -86t-6.5 -88q0 -53 12.5 -98t42 -79t76.5 -53.5t115 -19.5q174 0 270 97.5t145 310.5l209 868h199l-213 -891q-29 -117 -66.5 -219t-107.5 -177t-181.5 -119t-285.5 -44q-106 0 -184 32t-129.5 86 t-76 129t-24.5 161zM817 1798l139 116l260 -327l-100 -88z" />
<glyph unicode="&#xda;" horiz-adv-x="1363" d="M270 377q0 45 8.5 100t18.5 103l201 839h198l-204 -852q-10 -39 -16.5 -86t-6.5 -88q0 -53 12.5 -98t42 -79t76.5 -53.5t115 -19.5q174 0 270 97.5t145 310.5l209 868h199l-213 -891q-29 -117 -66.5 -219t-107.5 -177t-181.5 -119t-285.5 -44q-106 0 -184 32t-129.5 86 t-76 129t-24.5 161zM881 1607l323 309l105 -147l-347 -262z" />
<glyph unicode="&#xdb;" horiz-adv-x="1363" d="M270 377q0 45 8.5 100t18.5 103l201 839h198l-204 -852q-10 -39 -16.5 -86t-6.5 -88q0 -53 12.5 -98t42 -79t76.5 -53.5t115 -19.5q174 0 270 97.5t145 310.5l209 868h199l-213 -891q-29 -117 -66.5 -219t-107.5 -177t-181.5 -119t-285.5 -44q-106 0 -184 32t-129.5 86 t-76 129t-24.5 161zM776 1609l332 285l217 -303l-90 -84l-162 178l-229 -168z" />
<glyph unicode="&#xdc;" horiz-adv-x="1363" d="M270 377q0 45 8.5 100t18.5 103l201 839h198l-204 -852q-10 -39 -16.5 -86t-6.5 -88q0 -53 12.5 -98t42 -79t76.5 -53.5t115 -19.5q174 0 270 97.5t145 310.5l209 868h199l-213 -891q-29 -117 -66.5 -219t-107.5 -177t-181.5 -119t-285.5 -44q-106 0 -184 32t-129.5 86 t-76 129t-24.5 161zM763 1685q0 61 41 99t88 38q45 0 77 -29.5t32 -78.5q0 -59 -42 -96t-89 -37q-45 0 -76 28.5t-31 75.5zM1153 1685q0 61 40.5 99t88.5 38q45 0 76.5 -29.5t31.5 -78.5q0 -59 -42 -96t-89 -37q-45 0 -75.5 28.5t-30.5 75.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1171" d="M336 1419h215q39 -166 87 -338t107 -331q135 158 273.5 331.5t245.5 337.5h227q-154 -209 -322 -418.5t-360 -431.5l-135 -569h-199l135 569q-80 215 -148.5 431.5t-125.5 418.5zM781 1607l323 309l105 -147l-347 -262z" />
<glyph unicode="&#xde;" horiz-adv-x="1216" d="M164 0l342 1419h199l-56 -233q39 4 81 5t81 1q248 0 372 -92t124 -273q0 -139 -52.5 -240.5t-148.5 -167t-231.5 -97t-301.5 -31.5h-141l-70 -291h-198zM473 457h127q86 0 176 15t162 54t118 107.5t46 177.5q0 117 -86.5 165t-233.5 48q-76 0 -118.5 -3t-57.5 -5z" />
<glyph unicode="&#xdf;" horiz-adv-x="1228" d="M-39 -362q61 80 96 135t58.5 112.5t41 132t46.5 193.5l221 924q57 242 190.5 348t350.5 106q82 0 143 -22.5t102 -60.5t61.5 -88t20.5 -105q0 -59 -13 -103.5t-41 -83.5t-68 -76.5t-93 -86.5q-35 -31 -60.5 -53.5t-42 -43t-24.5 -44t-8 -54.5q0 -43 34 -71.5t93 -69.5 q39 -27 72 -53.5t57.5 -59.5t38.5 -74t14 -96q0 -92 -33.5 -161.5t-92 -116t-140.5 -69t-176 -22.5q-117 0 -195.5 27t-99.5 43l72 154q23 -14 83 -39t154 -25q104 0 171 49t67 148q0 61 -34 103t-120 95q-53 33 -83.5 60.5t-47 54.5t-20.5 53.5t-4 57.5q0 88 56 155.5 t134 132.5q37 31 66.5 57.5t51 54.5t33 60.5t11.5 73.5q0 61 -41 98t-123 37q-256 0 -326 -295l-217 -901q-25 -104 -43 -186t-43.5 -153.5t-67.5 -138t-114 -138.5z" />
<glyph unicode="&#xe0;" horiz-adv-x="1114" d="M201 356q0 137 46 268.5t138 234t231.5 165.5t325.5 63q55 0 129 -12t127 -41l-158 -643q-10 -47 -17 -90t-7 -90t8 -95t29 -104l-168 -24q-10 23 -20.5 50.5t-16.5 57.5q-45 -41 -121 -82t-186 -41q-90 0 -154.5 29t-105.5 80t-60.5 120.5t-19.5 153.5zM391 377 q0 -43 8.5 -87t30 -78t57.5 -55.5t93 -21.5q86 0 147 37t107 86q2 35 7 73t13 77l123 507q-10 4 -33.5 6.5t-42.5 2.5q-117 0 -211 -45t-160.5 -120t-102.5 -174.5t-36 -207.5zM645 1479l139 116l260 -327l-100 -88z" />
<glyph unicode="&#xe1;" horiz-adv-x="1114" d="M201 356q0 137 46 268.5t138 234t231.5 165.5t325.5 63q55 0 129 -12t127 -41l-158 -643q-10 -47 -17 -90t-7 -90t8 -95t29 -104l-168 -24q-10 23 -20.5 50.5t-16.5 57.5q-45 -41 -121 -82t-186 -41q-90 0 -154.5 29t-105.5 80t-60.5 120.5t-19.5 153.5zM391 377 q0 -43 8.5 -87t30 -78t57.5 -55.5t93 -21.5q86 0 147 37t107 86q2 35 7 73t13 77l123 507q-10 4 -33.5 6.5t-42.5 2.5q-117 0 -211 -45t-160.5 -120t-102.5 -174.5t-36 -207.5zM707 1288l323 309l105 -147l-347 -262z" />
<glyph unicode="&#xe2;" horiz-adv-x="1114" d="M201 356q0 137 46 268.5t138 234t231.5 165.5t325.5 63q55 0 129 -12t127 -41l-158 -643q-10 -47 -17 -90t-7 -90t8 -95t29 -104l-168 -24q-10 23 -20.5 50.5t-16.5 57.5q-45 -41 -121 -82t-186 -41q-90 0 -154.5 29t-105.5 80t-60.5 120.5t-19.5 153.5zM391 377 q0 -43 8.5 -87t30 -78t57.5 -55.5t93 -21.5q86 0 147 37t107 86q2 35 7 73t13 77l123 507q-10 4 -33.5 6.5t-42.5 2.5q-117 0 -211 -45t-160.5 -120t-102.5 -174.5t-36 -207.5zM604 1290l332 285l217 -303l-90 -84l-162 178l-229 -168z" />
<glyph unicode="&#xe3;" horiz-adv-x="1114" d="M201 356q0 137 46 268.5t138 234t231.5 165.5t325.5 63q55 0 129 -12t127 -41l-158 -643q-10 -47 -17 -90t-7 -90t8 -95t29 -104l-168 -24q-10 23 -20.5 50.5t-16.5 57.5q-45 -41 -121 -82t-186 -41q-90 0 -154.5 29t-105.5 80t-60.5 120.5t-19.5 153.5zM391 377 q0 -43 8.5 -87t30 -78t57.5 -55.5t93 -21.5q86 0 147 37t107 86q2 35 7 73t13 77l123 507q-10 4 -33.5 6.5t-42.5 2.5q-117 0 -211 -45t-160.5 -120t-102.5 -174.5t-36 -207.5zM553 1315q41 86 97 132t148 46q41 0 73 -14.5t61 -30.5q31 -16 55 -26.5t49 -10.5q43 0 71.5 32 t51.5 66l96 -59q-41 -86 -97 -132t-149 -46q-41 0 -72.5 14t-60.5 31q-31 16 -55.5 26.5t-48.5 10.5q-43 0 -72 -32t-51 -67z" />
<glyph unicode="&#xe4;" horiz-adv-x="1114" d="M201 356q0 137 46 268.5t138 234t231.5 165.5t325.5 63q55 0 129 -12t127 -41l-158 -643q-10 -47 -17 -90t-7 -90t8 -95t29 -104l-168 -24q-10 23 -20.5 50.5t-16.5 57.5q-45 -41 -121 -82t-186 -41q-90 0 -154.5 29t-105.5 80t-60.5 120.5t-19.5 153.5zM391 377 q0 -43 8.5 -87t30 -78t57.5 -55.5t93 -21.5q86 0 147 37t107 86q2 35 7 73t13 77l123 507q-10 4 -33.5 6.5t-42.5 2.5q-117 0 -211 -45t-160.5 -120t-102.5 -174.5t-36 -207.5zM589 1366q0 61 41 99t88 38q45 0 77 -29.5t32 -78.5q0 -59 -42 -96t-89 -37q-45 0 -76 28.5 t-31 75.5zM979 1366q0 61 40.5 99t88.5 38q45 0 76.5 -29.5t31.5 -78.5q0 -59 -42 -96t-89 -37q-45 0 -75.5 28.5t-30.5 75.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1114" d="M201 356q0 137 46 268.5t138 234t231.5 165.5t325.5 63q55 0 129 -12t127 -41l-158 -643q-10 -47 -17 -90t-7 -90t8 -95t29 -104l-168 -24q-10 23 -20.5 50.5t-16.5 57.5q-45 -41 -121 -82t-186 -41q-90 0 -154.5 29t-105.5 80t-60.5 120.5t-19.5 153.5zM391 377 q0 -43 8.5 -87t30 -78t57.5 -55.5t93 -21.5q86 0 147 37t107 86q2 35 7 73t13 77l123 507q-10 4 -33.5 6.5t-42.5 2.5q-117 0 -211 -45t-160.5 -120t-102.5 -174.5t-36 -207.5zM688 1386q0 49 17.5 87t46 66t66.5 42t81 14t81 -14t66.5 -42t46 -66t17.5 -87t-17.5 -86.5 t-46 -65.5t-66.5 -42t-81 -14t-81 14t-66.5 42t-46 65.5t-17.5 86.5zM790 1386q0 -53 32 -82.5t77 -29.5t76.5 29.5t31.5 82.5t-31.5 83t-76.5 30t-77 -29.5t-32 -83.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1712" d="M201 233q0 143 70.5 224.5t178 120t234.5 50t242 17.5q2 16 3 31.5t1 32.5q0 111 -61.5 161t-196.5 50q-41 0 -82 -8.5t-78 -18.5t-66.5 -22.5t-44.5 -20.5l-16 162q47 25 136 49t188 24q53 0 106 -10t101.5 -32.5t88 -62.5t66.5 -97q39 51 88 89t104.5 63.5t113 37.5 t112.5 12q61 0 117.5 -14t97.5 -46t65.5 -81t24.5 -119q0 -96 -32.5 -162.5t-88 -111.5t-128 -69.5t-153.5 -39t-164 -19.5t-159 -9q-2 -16 -3 -31.5t-1 -30.5q0 -117 61.5 -165t196.5 -48q39 0 81 7.5t80 18.5t67.5 22.5t41.5 21.5l17 -162q-53 -29 -142.5 -51.5 t-187.5 -22.5q-55 0 -109.5 10.5t-101.5 34t-85 61.5t-60 95q-72 -94 -182.5 -147.5t-239.5 -53.5q-59 0 -114.5 15.5t-96.5 47.5t-65.5 81t-24.5 116zM387 260q0 -66 43 -97.5t113 -31.5q135 0 230 95.5t130 273.5q-145 -4 -243.5 -20.5t-159 -47.5t-87 -74t-26.5 -98z M1092 559q143 2 242.5 19.5t159.5 48t87 73.5t27 99q0 66 -43 97.5t-113 31.5q-135 0 -230 -95.5t-130 -273.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="923" d="M205 381q0 139 42 268t124 227.5t200.5 157t272.5 58.5q68 0 129 -9.5t117 -35.5l-72 -160q-33 18 -77 28.5t-114 10.5q-100 0 -180 -44t-136 -117t-86 -168t-30 -198q0 -53 11.5 -100t40 -83t76.5 -56.5t122 -20.5q37 0 78 7.5t76 17.5t62.5 21.5t39.5 21.5l17 -162 q-39 -25 -121 -47.5t-183 -24.5q-10 -14 -18 -26.5t-16 -24.5q41 -23 61.5 -49.5t20.5 -73.5q0 -92 -63.5 -139t-174.5 -47q-59 0 -99 9t-67 22l47 112q16 -8 43 -15t66 -7q35 0 57.5 11t22.5 40q0 25 -16.5 37t-55.5 31l-12 6q14 27 38.5 62.5t45.5 64.5q-141 33 -215 137 t-74 258z" />
<glyph unicode="&#xe8;" horiz-adv-x="1060" d="M205 354q0 143 43 276.5t123 236t194.5 163t259.5 60.5q84 0 144.5 -21.5t98.5 -58.5t56.5 -85t18.5 -100q0 -96 -35 -162.5t-92.5 -111.5t-134 -69.5t-159.5 -39t-168 -19.5t-161 -9q-2 -16 -2 -26.5v-16.5q0 -47 11.5 -89t42 -74t85 -50.5t142.5 -18.5q39 0 81 7.5 t79.5 18.5t67.5 22.5t42 21.5l16 -162q-41 -23 -132 -48.5t-214 -25.5q-104 0 -180 30t-127 82t-75.5 121.5t-24.5 147.5zM418 559q119 4 217 15.5t170 39t111.5 74.5t39.5 123q0 18 -7 38.5t-24.5 38t-48 29t-75.5 11.5q-72 0 -134.5 -31t-111.5 -81t-84 -116.5t-53 -140.5 zM627 1479l139 116l260 -327l-100 -88z" />
<glyph unicode="&#xe9;" horiz-adv-x="1060" d="M205 354q0 143 43 276.5t123 236t194.5 163t259.5 60.5q84 0 144.5 -21.5t98.5 -58.5t56.5 -85t18.5 -100q0 -96 -35 -162.5t-92.5 -111.5t-134 -69.5t-159.5 -39t-168 -19.5t-161 -9q-2 -16 -2 -26.5v-16.5q0 -47 11.5 -89t42 -74t85 -50.5t142.5 -18.5q39 0 81 7.5 t79.5 18.5t67.5 22.5t42 21.5l16 -162q-41 -23 -132 -48.5t-214 -25.5q-104 0 -180 30t-127 82t-75.5 121.5t-24.5 147.5zM418 559q119 4 217 15.5t170 39t111.5 74.5t39.5 123q0 18 -7 38.5t-24.5 38t-48 29t-75.5 11.5q-72 0 -134.5 -31t-111.5 -81t-84 -116.5t-53 -140.5 zM689 1288l323 309l105 -147l-347 -262z" />
<glyph unicode="&#xea;" horiz-adv-x="1060" d="M205 354q0 143 43 276.5t123 236t194.5 163t259.5 60.5q84 0 144.5 -21.5t98.5 -58.5t56.5 -85t18.5 -100q0 -96 -35 -162.5t-92.5 -111.5t-134 -69.5t-159.5 -39t-168 -19.5t-161 -9q-2 -16 -2 -26.5v-16.5q0 -47 11.5 -89t42 -74t85 -50.5t142.5 -18.5q39 0 81 7.5 t79.5 18.5t67.5 22.5t42 21.5l16 -162q-41 -23 -132 -48.5t-214 -25.5q-104 0 -180 30t-127 82t-75.5 121.5t-24.5 147.5zM418 559q119 4 217 15.5t170 39t111.5 74.5t39.5 123q0 18 -7 38.5t-24.5 38t-48 29t-75.5 11.5q-72 0 -134.5 -31t-111.5 -81t-84 -116.5t-53 -140.5 zM584 1290l332 285l217 -303l-90 -84l-162 178l-229 -168z" />
<glyph unicode="&#xeb;" horiz-adv-x="1060" d="M205 354q0 143 43 276.5t123 236t194.5 163t259.5 60.5q84 0 144.5 -21.5t98.5 -58.5t56.5 -85t18.5 -100q0 -96 -35 -162.5t-92.5 -111.5t-134 -69.5t-159.5 -39t-168 -19.5t-161 -9q-2 -16 -2 -26.5v-16.5q0 -47 11.5 -89t42 -74t85 -50.5t142.5 -18.5q39 0 81 7.5 t79.5 18.5t67.5 22.5t42 21.5l16 -162q-41 -23 -132 -48.5t-214 -25.5q-104 0 -180 30t-127 82t-75.5 121.5t-24.5 147.5zM418 559q119 4 217 15.5t170 39t111.5 74.5t39.5 123q0 18 -7 38.5t-24.5 38t-48 29t-75.5 11.5q-72 0 -134.5 -31t-111.5 -81t-84 -116.5t-53 -140.5 zM571 1366q0 61 41 99t88 38q45 0 77 -29.5t32 -78.5q0 -59 -42 -96t-89 -37q-45 0 -76 28.5t-31 75.5zM961 1366q0 61 40.5 99t88.5 38q45 0 76.5 -29.5t31.5 -78.5q0 -59 -42 -96t-89 -37q-45 0 -75.5 28.5t-30.5 75.5z" />
<glyph unicode="&#xec;" horiz-adv-x="509" d="M156 0l256 1065h190l-256 -1065h-190zM330 1479l139 116l260 -327l-100 -88z" />
<glyph unicode="&#xed;" horiz-adv-x="509" d="M156 0l256 1065h190l-256 -1065h-190zM394 1288l323 309l105 -147l-347 -262z" />
<glyph unicode="&#xee;" horiz-adv-x="509" d="M156 0l256 1065h190l-256 -1065h-190zM289 1290l332 285l217 -303l-90 -84l-162 178l-229 -168z" />
<glyph unicode="&#xef;" horiz-adv-x="509" d="M156 0l256 1065h190l-256 -1065h-190zM274 1366q0 61 41 99t88 38q45 0 77 -29.5t32 -78.5q0 -59 -42 -96t-89 -37q-45 0 -76 28.5t-31 75.5zM664 1366q0 61 40.5 99t88.5 38q45 0 76.5 -29.5t31.5 -78.5q0 -59 -42 -96t-89 -37q-45 0 -75.5 28.5t-30.5 75.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1165" d="M205 344q0 141 38 262t111.5 209t183 138.5t253.5 50.5q74 0 131 -19.5t94 -42.5q-4 66 -18.5 134.5t-43.5 135.5l-266 -84l-16 129l225 72q-33 53 -68.5 89t-76.5 75l143 96q39 -29 86 -80t92 -125l268 86l17 -129l-225 -71q33 -80 53 -178.5t20 -217.5q0 -61 -8 -152 t-32.5 -190.5t-68.5 -200t-114 -179t-169 -129t-236 -50.5q-80 0 -149 26t-118 73t-77.5 116.5t-28.5 155.5zM395 362q0 -111 54.5 -168t154.5 -57q94 0 166 54.5t122 141.5t79.5 198.5t40.5 226.5q-53 45 -114.5 62.5t-121.5 17.5q-186 0 -283.5 -126t-97.5 -350z" />
<glyph unicode="&#xf1;" horiz-adv-x="1128" d="M156 0l245 1024q94 29 204 46t196 17q92 0 158.5 -25.5t107.5 -69.5t60.5 -106.5t19.5 -135.5q0 -47 -8.5 -99.5t-20.5 -105.5l-131 -545h-190l121 504q14 57 27 116.5t13 112.5q0 82 -45 136.5t-168 54.5q-51 0 -100 -6.5t-82 -16.5l-217 -901h-190zM551 1315 q41 86 97 132t148 46q41 0 73 -14.5t61 -30.5q31 -16 55 -26.5t49 -10.5q43 0 71.5 32t51.5 66l96 -59q-41 -86 -97 -132t-149 -46q-41 0 -72.5 14t-60.5 31q-31 16 -55.5 26.5t-48.5 10.5q-43 0 -72 -32t-51 -67z" />
<glyph unicode="&#xf2;" horiz-adv-x="1136" d="M205 346q0 119 33.5 250t107.5 241.5t190.5 182.5t282.5 72q168 0 263.5 -99.5t95.5 -273.5q0 -119 -34 -250t-107.5 -241.5t-190.5 -182.5t-283 -72q-168 0 -263 99.5t-95 273.5zM395 362q0 -104 45 -165.5t156 -61.5q92 0 165 53.5t123 136.5t76.5 183t26.5 194 q0 104 -45 166t-156 62q-92 0 -164.5 -53.5t-122.5 -136.5t-77 -183t-27 -195zM637 1479l139 116l260 -327l-100 -88z" />
<glyph unicode="&#xf3;" horiz-adv-x="1136" d="M205 346q0 119 33.5 250t107.5 241.5t190.5 182.5t282.5 72q168 0 263.5 -99.5t95.5 -273.5q0 -119 -34 -250t-107.5 -241.5t-190.5 -182.5t-283 -72q-168 0 -263 99.5t-95 273.5zM395 362q0 -104 45 -165.5t156 -61.5q92 0 165 53.5t123 136.5t76.5 183t26.5 194 q0 104 -45 166t-156 62q-92 0 -164.5 -53.5t-122.5 -136.5t-77 -183t-27 -195zM701 1288l323 309l105 -147l-347 -262z" />
<glyph unicode="&#xf4;" horiz-adv-x="1136" d="M205 346q0 119 33.5 250t107.5 241.5t190.5 182.5t282.5 72q168 0 263.5 -99.5t95.5 -273.5q0 -119 -34 -250t-107.5 -241.5t-190.5 -182.5t-283 -72q-168 0 -263 99.5t-95 273.5zM395 362q0 -104 45 -165.5t156 -61.5q92 0 165 53.5t123 136.5t76.5 183t26.5 194 q0 104 -45 166t-156 62q-92 0 -164.5 -53.5t-122.5 -136.5t-77 -183t-27 -195zM594 1290l332 285l217 -303l-90 -84l-162 178l-229 -168z" />
<glyph unicode="&#xf5;" horiz-adv-x="1136" d="M205 346q0 119 33.5 250t107.5 241.5t190.5 182.5t282.5 72q168 0 263.5 -99.5t95.5 -273.5q0 -119 -34 -250t-107.5 -241.5t-190.5 -182.5t-283 -72q-168 0 -263 99.5t-95 273.5zM395 362q0 -104 45 -165.5t156 -61.5q92 0 165 53.5t123 136.5t76.5 183t26.5 194 q0 104 -45 166t-156 62q-92 0 -164.5 -53.5t-122.5 -136.5t-77 -183t-27 -195zM545 1315q41 86 97 132t148 46q41 0 73 -14.5t61 -30.5q31 -16 55 -26.5t49 -10.5q43 0 71.5 32t51.5 66l96 -59q-41 -86 -97 -132t-149 -46q-41 0 -72.5 14t-60.5 31q-31 16 -55.5 26.5 t-48.5 10.5q-43 0 -72 -32t-51 -67z" />
<glyph unicode="&#xf6;" horiz-adv-x="1136" d="M205 346q0 119 33.5 250t107.5 241.5t190.5 182.5t282.5 72q168 0 263.5 -99.5t95.5 -273.5q0 -119 -34 -250t-107.5 -241.5t-190.5 -182.5t-283 -72q-168 0 -263 99.5t-95 273.5zM395 362q0 -104 45 -165.5t156 -61.5q92 0 165 53.5t123 136.5t76.5 183t26.5 194 q0 104 -45 166t-156 62q-92 0 -164.5 -53.5t-122.5 -136.5t-77 -183t-27 -195zM581 1366q0 61 41 99t88 38q45 0 77 -29.5t32 -78.5q0 -59 -42 -96t-89 -37q-45 0 -76 28.5t-31 75.5zM971 1366q0 61 40.5 99t88.5 38q45 0 76.5 -29.5t31.5 -78.5q0 -59 -42 -96t-89 -37 q-45 0 -75.5 28.5t-30.5 75.5z" />
<glyph unicode="&#xf7;" d="M238 528l38 162h920l-39 -162h-919zM500 184q0 31 11 58.5t31.5 50t48 36t62.5 13.5q45 0 73 -26.5t28 -69.5q0 -31 -11.5 -58.5t-32 -50t-49 -36t-61.5 -13.5q-45 0 -72.5 26.5t-27.5 69.5zM688 971q0 31 11.5 58.5t32 50t48 35.5t62.5 13q45 0 72.5 -26.5t27.5 -69.5 q0 -31 -11 -58.5t-31.5 -50t-49.5 -36t-62 -13.5q-45 0 -72.5 27t-27.5 70z" />
<glyph unicode="&#xf8;" horiz-adv-x="1136" d="M141 2l113 129q-49 86 -49 215q0 119 33.5 250t108.5 241.5t191.5 182.5t282.5 72q133 0 221 -64l95 107l104 -76l-113 -129q23 -43 36.5 -95.5t13.5 -115.5q0 -119 -34 -250t-107.5 -241.5t-190.5 -182.5t-283 -72q-135 0 -223 64l-96 -111zM391 325.5q0 -16.5 4 -32.5 l527 598q-53 41 -136 41q-92 0 -165.5 -54.5t-125 -137.5t-78 -184.5t-26.5 -195.5v-34.5zM461 172q45 -39 135 -39q92 0 166 54.5t125 137.5t77.5 184t26.5 196v32.5t-4 32.5z" />
<glyph unicode="&#xf9;" horiz-adv-x="1126" d="M225 309q0 53 9.5 105.5t21.5 105.5l131 545h191l-121 -504q-10 -47 -26.5 -110.5t-16.5 -126.5q0 -37 7 -71t26.5 -59.5t52 -41t82.5 -15.5q88 0 154.5 38t111.5 89q2 33 6 68t14 76l158 657h191l-162 -674q-10 -47 -17.5 -91t-7.5 -91t8.5 -94t28.5 -103l-168 -24 q-10 23 -20.5 51.5t-16.5 60.5q-23 -20 -53.5 -42.5t-70.5 -41t-88 -31t-105 -12.5q-90 0 -150.5 27t-98.5 72t-54.5 106.5t-16.5 130.5zM621 1479l139 116l260 -327l-100 -88z" />
<glyph unicode="&#xfa;" horiz-adv-x="1126" d="M225 309q0 53 9.5 105.5t21.5 105.5l131 545h191l-121 -504q-10 -47 -26.5 -110.5t-16.5 -126.5q0 -37 7 -71t26.5 -59.5t52 -41t82.5 -15.5q88 0 154.5 38t111.5 89q2 33 6 68t14 76l158 657h191l-162 -674q-10 -47 -17.5 -91t-7.5 -91t8.5 -94t28.5 -103l-168 -24 q-10 23 -20.5 51.5t-16.5 60.5q-23 -20 -53.5 -42.5t-70.5 -41t-88 -31t-105 -12.5q-90 0 -150.5 27t-98.5 72t-54.5 106.5t-16.5 130.5zM684 1288l323 309l105 -147l-347 -262z" />
<glyph unicode="&#xfb;" horiz-adv-x="1126" d="M225 309q0 53 9.5 105.5t21.5 105.5l131 545h191l-121 -504q-10 -47 -26.5 -110.5t-16.5 -126.5q0 -37 7 -71t26.5 -59.5t52 -41t82.5 -15.5q88 0 154.5 38t111.5 89q2 33 6 68t14 76l158 657h191l-162 -674q-10 -47 -17.5 -91t-7.5 -91t8.5 -94t28.5 -103l-168 -24 q-10 23 -20.5 51.5t-16.5 60.5q-23 -20 -53.5 -42.5t-70.5 -41t-88 -31t-105 -12.5q-90 0 -150.5 27t-98.5 72t-54.5 106.5t-16.5 130.5zM577 1290l332 285l217 -303l-90 -84l-162 178l-229 -168z" />
<glyph unicode="&#xfc;" horiz-adv-x="1126" d="M225 309q0 53 9.5 105.5t21.5 105.5l131 545h191l-121 -504q-10 -47 -26.5 -110.5t-16.5 -126.5q0 -37 7 -71t26.5 -59.5t52 -41t82.5 -15.5q88 0 154.5 38t111.5 89q2 33 6 68t14 76l158 657h191l-162 -674q-10 -47 -17.5 -91t-7.5 -91t8.5 -94t28.5 -103l-168 -24 q-10 23 -20.5 51.5t-16.5 60.5q-23 -20 -53.5 -42.5t-70.5 -41t-88 -31t-105 -12.5q-90 0 -150.5 27t-98.5 72t-54.5 106.5t-16.5 130.5zM565 1366q0 61 41 99t88 38q45 0 77 -29.5t32 -78.5q0 -59 -42 -96t-89 -37q-45 0 -76 28.5t-31 75.5zM955 1366q0 61 40.5 99t88.5 38 q45 0 76.5 -29.5t31.5 -78.5q0 -59 -42 -96t-89 -37q-45 0 -75.5 28.5t-30.5 75.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="978" d="M-115 -340l66 156q20 -16 55 -27.5t78 -11.5q119 0 202 57t156 152q-76 248 -124 522t-58 557h195q2 -92 10 -201.5t23.5 -227.5t36.5 -236.5t50 -227.5q78 123 141.5 255t112 253t81 222.5t46.5 162.5h199q-23 -78 -63.5 -193.5t-99 -250t-131.5 -274.5t-159 -269 q-66 -96 -131 -181t-140 -147.5t-165 -98.5t-203 -36q-57 0 -106 14.5t-72 30.5zM584 1288l323 309l105 -147l-347 -262z" />
<glyph unicode="&#xfe;" horiz-adv-x="1118" d="M66 -379l464 1935l199 33l-125 -522q100 25 172 25q100 0 173 -29t120 -79t70.5 -118.5t23.5 -150.5q0 -141 -43 -273.5t-132 -235t-225 -163.5t-323 -61q-23 0 -49 2t-47 6l-88 -369h-190zM383 150q20 -4 44 -5.5t46 -1.5q117 0 209 45t156.5 122t99.5 176.5t35 207.5 q0 45 -10.5 88t-38 76t-74.5 52.5t-121 19.5q-51 0 -94 -9.5t-72 -17.5z" />
<glyph unicode="&#xff;" horiz-adv-x="978" d="M-115 -340l66 156q20 -16 55 -27.5t78 -11.5q119 0 202 57t156 152q-76 248 -124 522t-58 557h195q2 -92 10 -201.5t23.5 -227.5t36.5 -236.5t50 -227.5q78 123 141.5 255t112 253t81 222.5t46.5 162.5h199q-23 -78 -63.5 -193.5t-99 -250t-131.5 -274.5t-159 -269 q-66 -96 -131 -181t-140 -147.5t-165 -98.5t-203 -36q-57 0 -106 14.5t-72 30.5zM464 1366q0 61 41 99t88 38q45 0 77 -29.5t32 -78.5q0 -59 -42 -96t-89 -37q-45 0 -76 28.5t-31 75.5zM854 1366q0 61 40.5 99t88.5 38q45 0 76.5 -29.5t31.5 -78.5q0 -59 -42 -96t-89 -37 q-45 0 -75.5 28.5t-30.5 75.5z" />
<glyph unicode="&#x152;" horiz-adv-x="1966" d="M254 537q0 176 55.5 338.5t172 286.5t298 198t432.5 74q53 0 103.5 -4.5t91.5 -10.5h762l-39 -166h-610l-103 -428h539l-39 -161h-537l-120 -498h657l-39 -166h-784q-57 -6 -114.5 -10t-98.5 -4q-158 0 -276 38.5t-195.5 110.5t-116.5 173.5t-39 228.5zM461 537 q0 -180 104.5 -281.5t331.5 -101.5q49 0 93 3t73 7l260 1091q-23 4 -59.5 7.5t-102.5 3.5q-162 0 -292 -54.5t-220 -150.5t-139 -230.5t-49 -293.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1804" d="M205 346q0 119 33.5 250t107.5 241.5t190.5 182.5t282.5 72q129 0 211 -61.5t103 -166.5q72 98 180 162t256 64q84 0 144.5 -21.5t98 -58.5t56 -85t18.5 -100q0 -96 -34.5 -162.5t-92 -111.5t-134.5 -69.5t-160 -39t-168 -19.5t-160 -9q0 -10 -1 -18.5t-1 -18.5 q0 -72 20.5 -117t58 -72.5t92 -38t121.5 -10.5q39 0 79 8.5t76 18.5t63.5 22.5t40.5 20.5l16 -162q-18 -12 -53 -24.5t-80 -23.5t-96.5 -18.5t-106.5 -7.5q-141 0 -238.5 59.5t-128.5 166.5q-66 -92 -174 -159t-262 -67q-168 0 -263 99.5t-95 273.5zM395 362 q0 -104 45 -165.5t156 -61.5q92 0 165 53.5t123 136.5t76.5 183t26.5 194q0 104 -45 166t-156 62q-92 0 -164.5 -53.5t-122.5 -136.5t-77 -183t-27 -195zM1161 559q119 4 217.5 15.5t170 39t111.5 74.5t40 123q0 18 -7 38.5t-24.5 38t-48.5 29t-76 11.5q-72 0 -134 -31 t-111.5 -81t-84.5 -116.5t-53 -140.5z" />
<glyph unicode="&#x178;" horiz-adv-x="1171" d="M336 1419h215q39 -166 87 -338t107 -331q135 158 273.5 331.5t245.5 337.5h227q-154 -209 -322 -418.5t-360 -431.5l-135 -569h-199l135 569q-80 215 -148.5 431.5t-125.5 418.5zM663 1685q0 61 41 99t88 38q45 0 77 -29.5t32 -78.5q0 -59 -42 -96t-89 -37 q-45 0 -76 28.5t-31 75.5zM1053 1685q0 61 40.5 99t88.5 38q45 0 76.5 -29.5t31.5 -78.5q0 -59 -42 -96t-89 -37q-45 0 -75.5 28.5t-30.5 75.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="770" d="M430 1290l332 285l217 -303l-90 -84l-162 178l-229 -168z" />
<glyph unicode="&#x2dc;" horiz-adv-x="770" d="M369 1315q41 86 97 132t148 46q41 0 73 -14.5t61 -30.5q31 -16 55 -26.5t49 -10.5q43 0 71.5 32t51.5 66l96 -59q-41 -86 -97 -132t-149 -46q-41 0 -72.5 14t-60.5 31q-31 16 -55.5 26.5t-48.5 10.5q-43 0 -72 -32t-51 -67z" />
<glyph unicode="&#x2000;" horiz-adv-x="958" />
<glyph unicode="&#x2001;" horiz-adv-x="1916" />
<glyph unicode="&#x2002;" horiz-adv-x="958" />
<glyph unicode="&#x2003;" horiz-adv-x="1916" />
<glyph unicode="&#x2004;" horiz-adv-x="638" />
<glyph unicode="&#x2005;" horiz-adv-x="479" />
<glyph unicode="&#x2006;" horiz-adv-x="319" />
<glyph unicode="&#x2007;" horiz-adv-x="319" />
<glyph unicode="&#x2008;" horiz-adv-x="239" />
<glyph unicode="&#x2009;" horiz-adv-x="383" />
<glyph unicode="&#x200a;" horiz-adv-x="106" />
<glyph unicode="&#x2010;" horiz-adv-x="600" d="M178 524l41 178h496l-41 -178h-496z" />
<glyph unicode="&#x2011;" horiz-adv-x="600" d="M178 524l41 178h496l-41 -178h-496z" />
<glyph unicode="&#x2012;" horiz-adv-x="600" d="M178 524l41 178h496l-41 -178h-496z" />
<glyph unicode="&#x2013;" horiz-adv-x="1015" d="M125 532l39 162h1024l-39 -162h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2039" d="M125 532l39 162h2048l-39 -162h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="481" d="M373 1065q10 66 36.5 134.5t62.5 133t78 121.5t85 102l129 -53q-72 -111 -119 -215t-63 -223h-209z" />
<glyph unicode="&#x2019;" horiz-adv-x="481" d="M336 1098q72 111 119 215t63 223h209q-10 -66 -36.5 -134.5t-62.5 -133t-78 -121.5t-85 -103z" />
<glyph unicode="&#x201a;" horiz-adv-x="481" d="M23 -205q72 111 118.5 215.5t63.5 222.5h209q-10 -66 -37 -134t-62.5 -132.5t-77.5 -122t-85 -102.5z" />
<glyph unicode="&#x201c;" horiz-adv-x="854" d="M373 1065q10 66 36.5 134.5t62.5 133t78 121.5t85 102l129 -53q-72 -111 -119 -215t-63 -223h-209zM746 1065q10 66 36.5 134.5t62.5 133t78 121.5t85 102l129 -53q-72 -111 -119 -215t-63 -223h-209z" />
<glyph unicode="&#x201d;" horiz-adv-x="854" d="M336 1098q72 111 118.5 215.5t63.5 222.5h209q-10 -66 -37 -134t-62.5 -132.5t-77.5 -122t-85 -102.5zM709 1098q72 111 118.5 215.5t63.5 222.5h209q-10 -66 -37 -134t-62.5 -132.5t-77.5 -122t-85 -102.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="854" d="M23 -205q72 111 118.5 215.5t63.5 222.5h209q-10 -66 -37 -134t-62.5 -132.5t-77.5 -122t-85 -102.5zM396 -205q72 111 118.5 215.5t63.5 222.5h209q-10 -66 -37 -134t-62.5 -132.5t-77.5 -122t-85 -102.5z" />
<glyph unicode="&#x2022;" horiz-adv-x="737" d="M274 723q0 51 18.5 99t52.5 83t82 56.5t110 21.5q61 0 109 -21.5t82 -56.5t52.5 -83t18.5 -99q0 -53 -18.5 -100.5t-52.5 -82t-82 -56t-109 -21.5t-109.5 21.5t-82.5 56t-52.5 82t-18.5 100.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="2039" d="M225 94q0 74 44 114t102 40q57 0 91 -32t34 -93q0 -35 -13.5 -62.5t-34 -47t-47 -30t-53.5 -10.5q-57 0 -90 31t-33 90zM907 94q0 74 44 114t102 40q57 0 91 -32t34 -93q0 -35 -13.5 -62.5t-34 -47t-47 -30t-53.5 -10.5q-57 0 -90 31t-33 90zM1589 94q0 74 44 114t102 40 q57 0 91 -32t34 -93q0 -35 -13.5 -62.5t-34 -47t-47 -30t-53.5 -10.5q-57 0 -90 31t-33 90z" />
<glyph unicode="&#x202f;" horiz-adv-x="383" />
<glyph unicode="&#x2039;" horiz-adv-x="595" d="M197 594l415 440l119 -71l-321 -390l155 -385l-127 -59z" />
<glyph unicode="&#x203a;" horiz-adv-x="595" d="M143 201l322 389l-156 385l127 59l242 -465l-416 -440z" />
<glyph unicode="&#x205f;" horiz-adv-x="479" />
<glyph unicode="&#x20ac;" d="M213 465l35 147h164q6 51 16 102.5t25 104.5h-158l35 148h172q41 100 100.5 187t141 151.5t185 102.5t230.5 38q80 0 136.5 -12.5t109.5 -34.5l-76 -154q-33 16 -75 27.5t-119 11.5q-80 0 -145.5 -24.5t-120 -66.5t-96.5 -100.5t-73 -125.5h486l-62 -148h-479 q-16 -51 -26.5 -103t-16.5 -104h432l-61 -147h-383v-37q0 -86 21.5 -142.5t58.5 -89t87 -46t107 -13.5q92 0 157.5 17.5t115.5 40.5l6 -162q-14 -8 -45 -18.5t-73 -19.5t-89 -15.5t-94 -6.5q-215 0 -328 112t-113 333v47h-188z" />
<glyph unicode="&#x2122;" horiz-adv-x="1554" d="M270 1294v125h572v-125h-215v-557h-142v557h-215zM870 737q16 252 28.5 415t29.5 267h135q16 -35 40.5 -85t52.5 -106.5t58.5 -115.5t59.5 -115q27 55 57.5 114.5t59 117t53 106.5t41.5 84h135q16 -104 28.5 -267t28.5 -415h-143q-4 104 -10 226t-15 223 q-23 -39 -50 -90.5t-53 -103.5l-48.5 -97t-34.5 -72h-100q-12 27 -35 72t-49.5 96t-54 102.5t-48.5 92.5q-8 -100 -14 -222t-10 -227h-142z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1064" d="M0 0v1065h1065v-1065h-1065z" />
<hkern u1="K" u2="&#xef;" k="-63" />
<hkern u1="T" u2="&#xef;" k="-86" />
<hkern u1="T" u2="&#xee;" k="-23" />
<hkern u1="V" u2="&#xef;" k="-86" />
<hkern u1="V" u2="&#xec;" k="-23" />
<hkern u1="W" u2="&#xef;" k="-33" />
<hkern u1="Y" u2="&#xef;" k="-86" />
<hkern u1="Y" u2="&#xec;" k="-23" />
<hkern u1="&#xdd;" u2="&#xef;" k="-86" />
<hkern u1="&#xdd;" u2="&#xec;" k="-23" />
<hkern u1="&#x178;" u2="&#xef;" k="-86" />
<hkern u1="&#x178;" u2="&#xec;" k="-23" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="backslash" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="x" 	k="-25" />
<hkern g1="b" 	g2="backslash" 	k="41" />
<hkern g1="b" 	g2="x" 	k="6" />
<hkern g1="b" 	g2="v" 	k="16" />
<hkern g1="b" 	g2="w" 	k="8" />
<hkern g1="b" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="b" 	g2="z" 	k="14" />
<hkern g1="b" 	g2="quoteright,quotedblright" 	k="92" />
<hkern g1="b" 	g2="quoteleft,quotedblleft" 	k="92" />
<hkern g1="b" 	g2="hyphen,uni00AD,endash,emdash" 	k="-16" />
<hkern g1="b" 	g2="asterisk" 	k="92" />
<hkern g1="b" 	g2="quotedbl,quotesingle" 	k="92" />
<hkern g1="c,ccedilla" 	g2="x" 	k="-25" />
<hkern g1="c,ccedilla" 	g2="v" 	k="-6" />
<hkern g1="c,ccedilla" 	g2="w" 	k="-16" />
<hkern g1="c,ccedilla" 	g2="y,yacute,ydieresis" 	k="-27" />
<hkern g1="c,ccedilla" 	g2="z" 	k="-27" />
<hkern g1="c,ccedilla" 	g2="hyphen,uni00AD,endash,emdash" 	k="20" />
<hkern g1="c,ccedilla" 	g2="parenright" 	k="-25" />
<hkern g1="c,ccedilla" 	g2="comma,period,ellipsis" 	k="-25" />
<hkern g1="c,ccedilla" 	g2="slash" 	k="-31" />
<hkern g1="c,ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="18" />
<hkern g1="c,ccedilla" 	g2="c,ccedilla" 	k="20" />
<hkern g1="c,ccedilla" 	g2="d" 	k="18" />
<hkern g1="c,ccedilla" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="20" />
<hkern g1="c,ccedilla" 	g2="g" 	k="18" />
<hkern g1="c,ccedilla" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="c,ccedilla" 	g2="q" 	k="18" />
<hkern g1="c,ccedilla" 	g2="s" 	k="-23" />
<hkern g1="c,ccedilla" 	g2="braceright" 	k="-16" />
<hkern g1="c,ccedilla" 	g2="eth" 	k="20" />
<hkern g1="d" 	g2="backslash" 	k="-10" />
<hkern g1="d" 	g2="x" 	k="-27" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="backslash" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="asterisk" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="g" 	g2="backslash" 	k="20" />
<hkern g1="g" 	g2="j" 	k="-45" />
<hkern g1="h" 	g2="backslash" 	k="35" />
<hkern g1="h" 	g2="v" 	k="16" />
<hkern g1="h" 	g2="w" 	k="16" />
<hkern g1="h" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="h" 	g2="quoteright,quotedblright" 	k="82" />
<hkern g1="h" 	g2="quoteleft,quotedblleft" 	k="61" />
<hkern g1="h" 	g2="asterisk" 	k="51" />
<hkern g1="h" 	g2="quotedbl,quotesingle" 	k="82" />
<hkern g1="j" 	g2="j" 	k="-53" />
<hkern g1="k" 	g2="backslash" 	k="20" />
<hkern g1="k" 	g2="z" 	k="-37" />
<hkern g1="k" 	g2="hyphen,uni00AD,endash,emdash" 	k="45" />
<hkern g1="k" 	g2="slash" 	k="-25" />
<hkern g1="k" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="43" />
<hkern g1="k" 	g2="c,ccedilla" 	k="47" />
<hkern g1="k" 	g2="d" 	k="43" />
<hkern g1="k" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="47" />
<hkern g1="k" 	g2="g" 	k="43" />
<hkern g1="k" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="47" />
<hkern g1="k" 	g2="q" 	k="43" />
<hkern g1="k" 	g2="s" 	k="-27" />
<hkern g1="k" 	g2="eth" 	k="47" />
<hkern g1="k" 	g2="l" 	k="29" />
<hkern g1="k" 	g2="t" 	k="25" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="25" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="l,uniFB02,uniFB04" 	g2="x" 	k="-45" />
<hkern g1="l,uniFB02,uniFB04" 	g2="v" 	k="25" />
<hkern g1="l,uniFB02,uniFB04" 	g2="w" 	k="20" />
<hkern g1="l,uniFB02,uniFB04" 	g2="y,yacute,ydieresis" 	k="25" />
<hkern g1="l,uniFB02,uniFB04" 	g2="z" 	k="-27" />
<hkern g1="l,uniFB02,uniFB04" 	g2="hyphen,uni00AD,endash,emdash" 	k="10" />
<hkern g1="l,uniFB02,uniFB04" 	g2="asterisk" 	k="10" />
<hkern g1="l,uniFB02,uniFB04" 	g2="comma,period,ellipsis" 	k="-10" />
<hkern g1="l,uniFB02,uniFB04" 	g2="slash" 	k="-25" />
<hkern g1="l,uniFB02,uniFB04" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="25" />
<hkern g1="l,uniFB02,uniFB04" 	g2="c,ccedilla" 	k="18" />
<hkern g1="l,uniFB02,uniFB04" 	g2="d" 	k="25" />
<hkern g1="l,uniFB02,uniFB04" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="18" />
<hkern g1="l,uniFB02,uniFB04" 	g2="g" 	k="25" />
<hkern g1="l,uniFB02,uniFB04" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="l,uniFB02,uniFB04" 	g2="q" 	k="25" />
<hkern g1="l,uniFB02,uniFB04" 	g2="eth" 	k="18" />
<hkern g1="l,uniFB02,uniFB04" 	g2="l" 	k="10" />
<hkern g1="l,uniFB02,uniFB04" 	g2="t" 	k="10" />
<hkern g1="l,uniFB02,uniFB04" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="l,uniFB02,uniFB04" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="m,n,ntilde" 	g2="backslash" 	k="35" />
<hkern g1="m,n,ntilde" 	g2="v" 	k="16" />
<hkern g1="m,n,ntilde" 	g2="w" 	k="16" />
<hkern g1="m,n,ntilde" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="m,n,ntilde" 	g2="quoteright,quotedblright" 	k="82" />
<hkern g1="m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="61" />
<hkern g1="m,n,ntilde" 	g2="asterisk" 	k="51" />
<hkern g1="m,n,ntilde" 	g2="quotedbl,quotesingle" 	k="82" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="backslash" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="x" 	k="8" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="v" 	k="16" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="w" 	k="8" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="z" 	k="6" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteright,quotedblright" 	k="92" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteleft,quotedblleft" 	k="92" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="hyphen,uni00AD,endash,emdash" 	k="-16" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="asterisk" 	k="98" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotedbl,quotesingle" 	k="92" />
<hkern g1="p" 	g2="backslash" 	k="41" />
<hkern g1="p" 	g2="x" 	k="6" />
<hkern g1="p" 	g2="v" 	k="16" />
<hkern g1="p" 	g2="w" 	k="8" />
<hkern g1="p" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="p" 	g2="z" 	k="14" />
<hkern g1="p" 	g2="quoteright,quotedblright" 	k="92" />
<hkern g1="p" 	g2="quoteleft,quotedblleft" 	k="92" />
<hkern g1="p" 	g2="hyphen,uni00AD,endash,emdash" 	k="-16" />
<hkern g1="p" 	g2="asterisk" 	k="92" />
<hkern g1="p" 	g2="quotedbl,quotesingle" 	k="92" />
<hkern g1="q" 	g2="j" 	k="-31" />
<hkern g1="r" 	g2="x" 	k="-10" />
<hkern g1="r" 	g2="v" 	k="-14" />
<hkern g1="r" 	g2="w" 	k="-37" />
<hkern g1="r" 	g2="y,yacute,ydieresis" 	k="-12" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="r" 	g2="hyphen,uni00AD,endash,emdash" 	k="20" />
<hkern g1="r" 	g2="asterisk" 	k="-20" />
<hkern g1="r" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="r" 	g2="comma,period,ellipsis" 	k="92" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="14" />
<hkern g1="r" 	g2="c,ccedilla" 	k="10" />
<hkern g1="r" 	g2="d" 	k="14" />
<hkern g1="r" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="10" />
<hkern g1="r" 	g2="g" 	k="14" />
<hkern g1="r" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="r" 	g2="q" 	k="14" />
<hkern g1="r" 	g2="eth" 	k="10" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="8" />
<hkern g1="s" 	g2="slash" 	k="-20" />
<hkern g1="t" 	g2="x" 	k="-55" />
<hkern g1="t" 	g2="z" 	k="-33" />
<hkern g1="t" 	g2="hyphen,uni00AD,endash,emdash" 	k="20" />
<hkern g1="t" 	g2="parenright" 	k="-25" />
<hkern g1="t" 	g2="comma,period,ellipsis" 	k="-25" />
<hkern g1="t" 	g2="slash" 	k="-31" />
<hkern g1="t" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="t" 	g2="c,ccedilla" 	k="10" />
<hkern g1="t" 	g2="d" 	k="10" />
<hkern g1="t" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="10" />
<hkern g1="t" 	g2="g" 	k="10" />
<hkern g1="t" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="t" 	g2="q" 	k="10" />
<hkern g1="t" 	g2="s" 	k="-29" />
<hkern g1="t" 	g2="braceright" 	k="-10" />
<hkern g1="t" 	g2="eth" 	k="10" />
<hkern g1="t" 	g2="l" 	k="8" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="31" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="backslash" 	k="20" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="x" 	k="-27" />
<hkern g1="v" 	g2="x" 	k="-39" />
<hkern g1="v" 	g2="v" 	k="-35" />
<hkern g1="v" 	g2="w" 	k="-27" />
<hkern g1="v" 	g2="y,yacute,ydieresis" 	k="-35" />
<hkern g1="v" 	g2="comma,period,ellipsis" 	k="37" />
<hkern g1="v" 	g2="slash" 	k="41" />
<hkern g1="w" 	g2="x" 	k="-39" />
<hkern g1="w" 	g2="v" 	k="-35" />
<hkern g1="w" 	g2="w" 	k="-27" />
<hkern g1="w" 	g2="y,yacute,ydieresis" 	k="-35" />
<hkern g1="w" 	g2="comma,period,ellipsis" 	k="37" />
<hkern g1="w" 	g2="slash" 	k="31" />
<hkern g1="x" 	g2="x" 	k="-59" />
<hkern g1="x" 	g2="y,yacute,ydieresis" 	k="-23" />
<hkern g1="x" 	g2="z" 	k="-37" />
<hkern g1="x" 	g2="hyphen,uni00AD,endash,emdash" 	k="27" />
<hkern g1="x" 	g2="comma,period,ellipsis" 	k="-31" />
<hkern g1="x" 	g2="slash" 	k="-31" />
<hkern g1="x" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="18" />
<hkern g1="x" 	g2="c,ccedilla" 	k="12" />
<hkern g1="x" 	g2="d" 	k="18" />
<hkern g1="x" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="12" />
<hkern g1="x" 	g2="g" 	k="18" />
<hkern g1="x" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="12" />
<hkern g1="x" 	g2="q" 	k="18" />
<hkern g1="x" 	g2="s" 	k="-29" />
<hkern g1="x" 	g2="eth" 	k="12" />
<hkern g1="x" 	g2="guillemotleft,guilsinglleft" 	k="41" />
<hkern g1="y,yacute,ydieresis" 	g2="x" 	k="-31" />
<hkern g1="y,yacute,ydieresis" 	g2="v" 	k="-27" />
<hkern g1="y,yacute,ydieresis" 	g2="w" 	k="-18" />
<hkern g1="y,yacute,ydieresis" 	g2="y,yacute,ydieresis" 	k="-27" />
<hkern g1="y,yacute,ydieresis" 	g2="asterisk" 	k="-10" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,ellipsis" 	k="37" />
<hkern g1="z" 	g2="x" 	k="-31" />
<hkern g1="z" 	g2="v" 	k="-27" />
<hkern g1="z" 	g2="w" 	k="-18" />
<hkern g1="z" 	g2="y,yacute,ydieresis" 	k="-27" />
<hkern g1="z" 	g2="hyphen,uni00AD,endash,emdash" 	k="10" />
<hkern g1="z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="z" 	g2="c,ccedilla" 	k="10" />
<hkern g1="z" 	g2="d" 	k="10" />
<hkern g1="z" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="10" />
<hkern g1="z" 	g2="g" 	k="10" />
<hkern g1="z" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="z" 	g2="q" 	k="10" />
<hkern g1="z" 	g2="eth" 	k="10" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="thorn" 	g2="backslash" 	k="41" />
<hkern g1="thorn" 	g2="x" 	k="6" />
<hkern g1="thorn" 	g2="v" 	k="16" />
<hkern g1="thorn" 	g2="w" 	k="8" />
<hkern g1="thorn" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="thorn" 	g2="z" 	k="14" />
<hkern g1="thorn" 	g2="quoteright,quotedblright" 	k="92" />
<hkern g1="thorn" 	g2="quoteleft,quotedblleft" 	k="92" />
<hkern g1="thorn" 	g2="hyphen,uni00AD,endash,emdash" 	k="-16" />
<hkern g1="thorn" 	g2="asterisk" 	k="92" />
<hkern g1="thorn" 	g2="quotedbl,quotesingle" 	k="92" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="102" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v" 	k="68" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="39" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="y,yacute,ydieresis" 	k="33" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="125" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="143" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="hyphen,uni00AD,endash,emdash" 	k="33" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk" 	k="176" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle" 	k="125" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="parenright" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="comma,period,ellipsis" 	k="-31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="slash" 	k="-31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="l" 	k="23" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guillemotleft,guilsinglleft" 	k="25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="colon,semicolon" 	k="-31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="57" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="184" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="106" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="W" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="160" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guillemotright,guilsinglright" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="exclamdown" 	k="-18" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-59" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="J" 	k="-41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="S" 	k="-29" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="X" 	k="-53" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Z" 	k="-33" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="AE" 	k="-68" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,Ccedilla" 	k="57" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="G" 	k="57" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Q" 	k="57" />
<hkern g1="B" 	g2="backslash" 	k="12" />
<hkern g1="B" 	g2="asterisk" 	k="20" />
<hkern g1="B" 	g2="T" 	k="45" />
<hkern g1="B" 	g2="V" 	k="18" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="hyphen,uni00AD,endash,emdash" 	k="127" />
<hkern g1="C,Ccedilla" 	g2="parenright" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="c,ccedilla" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="d" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="q" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="eth" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="84" />
<hkern g1="C,Ccedilla" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="53" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="-23" />
<hkern g1="C,Ccedilla" 	g2="V" 	k="-25" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="-25" />
<hkern g1="C,Ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-37" />
<hkern g1="C,Ccedilla" 	g2="J" 	k="-27" />
<hkern g1="C,Ccedilla" 	g2="X" 	k="-31" />
<hkern g1="C,Ccedilla" 	g2="AE" 	k="-43" />
<hkern g1="C,Ccedilla" 	g2="C,Ccedilla" 	k="53" />
<hkern g1="C,Ccedilla" 	g2="G" 	k="53" />
<hkern g1="C,Ccedilla" 	g2="Q" 	k="53" />
<hkern g1="C,Ccedilla" 	g2="ampersand" 	k="10" />
<hkern g1="D,Eth" 	g2="backslash" 	k="10" />
<hkern g1="D,Eth" 	g2="quoteright,quotedblright" 	k="45" />
<hkern g1="D,Eth" 	g2="quoteleft,quotedblleft" 	k="59" />
<hkern g1="D,Eth" 	g2="hyphen,uni00AD,endash,emdash" 	k="-25" />
<hkern g1="D,Eth" 	g2="asterisk" 	k="31" />
<hkern g1="D,Eth" 	g2="quotedbl,quotesingle" 	k="45" />
<hkern g1="D,Eth" 	g2="comma,period,ellipsis" 	k="25" />
<hkern g1="D,Eth" 	g2="slash" 	k="57" />
<hkern g1="D,Eth" 	g2="T" 	k="98" />
<hkern g1="D,Eth" 	g2="V" 	k="43" />
<hkern g1="D,Eth" 	g2="W" 	k="27" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="D,Eth" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="D,Eth" 	g2="J" 	k="25" />
<hkern g1="D,Eth" 	g2="X" 	k="61" />
<hkern g1="D,Eth" 	g2="Z" 	k="12" />
<hkern g1="D,Eth" 	g2="AE" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="hyphen,uni00AD,endash,emdash" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,ccedilla" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="d" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="g" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="q" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="eth" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-23" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="J" 	k="-20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="X" 	k="-18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="AE" 	k="-31" />
<hkern g1="F" 	g2="z" 	k="20" />
<hkern g1="F" 	g2="comma,period,ellipsis" 	k="102" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="25" />
<hkern g1="F" 	g2="c,ccedilla" 	k="25" />
<hkern g1="F" 	g2="d" 	k="25" />
<hkern g1="F" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="25" />
<hkern g1="F" 	g2="g" 	k="25" />
<hkern g1="F" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="F" 	g2="q" 	k="25" />
<hkern g1="F" 	g2="s" 	k="16" />
<hkern g1="F" 	g2="eth" 	k="25" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="F" 	g2="m,n,ntilde" 	k="10" />
<hkern g1="F" 	g2="p" 	k="10" />
<hkern g1="F" 	g2="r" 	k="10" />
<hkern g1="F" 	g2="colon,semicolon" 	k="31" />
<hkern g1="F" 	g2="T" 	k="-20" />
<hkern g1="F" 	g2="V" 	k="-35" />
<hkern g1="F" 	g2="Y,Yacute,Ydieresis" 	k="-37" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="88" />
<hkern g1="F" 	g2="J" 	k="164" />
<hkern g1="F" 	g2="AE" 	k="133" />
<hkern g1="F" 	g2="ampersand" 	k="51" />
<hkern g1="J" 	g2="J" 	k="39" />
<hkern g1="K" 	g2="v" 	k="57" />
<hkern g1="K" 	g2="w" 	k="51" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="37" />
<hkern g1="K" 	g2="hyphen,uni00AD,endash,emdash" 	k="106" />
<hkern g1="K" 	g2="asterisk" 	k="20" />
<hkern g1="K" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="66" />
<hkern g1="K" 	g2="c,ccedilla" 	k="66" />
<hkern g1="K" 	g2="d" 	k="66" />
<hkern g1="K" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="66" />
<hkern g1="K" 	g2="g" 	k="66" />
<hkern g1="K" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="66" />
<hkern g1="K" 	g2="q" 	k="66" />
<hkern g1="K" 	g2="eth" 	k="66" />
<hkern g1="K" 	g2="t" 	k="20" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="66" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="111" />
<hkern g1="K" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="96" />
<hkern g1="K" 	g2="T" 	k="-29" />
<hkern g1="K" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="45" />
<hkern g1="K" 	g2="V" 	k="-43" />
<hkern g1="K" 	g2="W" 	k="-20" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="-45" />
<hkern g1="K" 	g2="guillemotright,guilsinglright" 	k="47" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-66" />
<hkern g1="K" 	g2="J" 	k="-33" />
<hkern g1="K" 	g2="S" 	k="-25" />
<hkern g1="K" 	g2="X" 	k="-59" />
<hkern g1="K" 	g2="Z" 	k="-23" />
<hkern g1="K" 	g2="AE" 	k="-72" />
<hkern g1="K" 	g2="C,Ccedilla" 	k="96" />
<hkern g1="K" 	g2="G" 	k="96" />
<hkern g1="K" 	g2="Q" 	k="96" />
<hkern g1="K" 	g2="ampersand" 	k="45" />
<hkern g1="K" 	g2="at" 	k="31" />
<hkern g1="L" 	g2="backslash" 	k="182" />
<hkern g1="L" 	g2="v" 	k="127" />
<hkern g1="L" 	g2="w" 	k="109" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="78" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="223" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="238" />
<hkern g1="L" 	g2="hyphen,uni00AD,endash,emdash" 	k="164" />
<hkern g1="L" 	g2="asterisk" 	k="242" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="223" />
<hkern g1="L" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="86" />
<hkern g1="L" 	g2="c,ccedilla" 	k="86" />
<hkern g1="L" 	g2="d" 	k="86" />
<hkern g1="L" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="86" />
<hkern g1="L" 	g2="g" 	k="86" />
<hkern g1="L" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="86" />
<hkern g1="L" 	g2="q" 	k="86" />
<hkern g1="L" 	g2="eth" 	k="86" />
<hkern g1="L" 	g2="t" 	k="51" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="59" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="143" />
<hkern g1="L" 	g2="question" 	k="59" />
<hkern g1="L" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="121" />
<hkern g1="L" 	g2="T" 	k="256" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="104" />
<hkern g1="L" 	g2="V" 	k="219" />
<hkern g1="L" 	g2="W" 	k="137" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="258" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="23" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-45" />
<hkern g1="L" 	g2="J" 	k="-43" />
<hkern g1="L" 	g2="S" 	k="-25" />
<hkern g1="L" 	g2="X" 	k="-41" />
<hkern g1="L" 	g2="Z" 	k="-20" />
<hkern g1="L" 	g2="AE" 	k="-53" />
<hkern g1="L" 	g2="C,Ccedilla" 	k="121" />
<hkern g1="L" 	g2="G" 	k="121" />
<hkern g1="L" 	g2="Q" 	k="121" />
<hkern g1="L" 	g2="ampersand" 	k="31" />
<hkern g1="L" 	g2="at" 	k="72" />
<hkern g1="M" 	g2="t" 	k="10" />
<hkern g1="M" 	g2="T" 	k="20" />
<hkern g1="M" 	g2="V" 	k="18" />
<hkern g1="M" 	g2="Y,Yacute,Ydieresis" 	k="35" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="backslash" 	k="10" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="45" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="59" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="hyphen,uni00AD,endash,emdash" 	k="-25" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="asterisk" 	k="31" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="45" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,ellipsis" 	k="25" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="57" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="98" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="43" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="27" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="25" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="61" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="12" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="41" />
<hkern g1="P" 	g2="x" 	k="-33" />
<hkern g1="P" 	g2="v" 	k="-41" />
<hkern g1="P" 	g2="w" 	k="-31" />
<hkern g1="P" 	g2="y,yacute,ydieresis" 	k="-49" />
<hkern g1="P" 	g2="comma,period,ellipsis" 	k="113" />
<hkern g1="P" 	g2="slash" 	k="84" />
<hkern g1="P" 	g2="T" 	k="33" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="98" />
<hkern g1="P" 	g2="J" 	k="174" />
<hkern g1="P" 	g2="X" 	k="70" />
<hkern g1="P" 	g2="AE" 	k="115" />
<hkern g1="P" 	g2="ampersand" 	k="20" />
<hkern g1="Q" 	g2="backslash" 	k="10" />
<hkern g1="Q" 	g2="quoteright,quotedblright" 	k="45" />
<hkern g1="Q" 	g2="quoteleft,quotedblleft" 	k="59" />
<hkern g1="Q" 	g2="hyphen,uni00AD,endash,emdash" 	k="-25" />
<hkern g1="Q" 	g2="asterisk" 	k="31" />
<hkern g1="Q" 	g2="quotedbl,quotesingle" 	k="45" />
<hkern g1="Q" 	g2="comma,period,ellipsis" 	k="25" />
<hkern g1="Q" 	g2="slash" 	k="57" />
<hkern g1="Q" 	g2="T" 	k="98" />
<hkern g1="Q" 	g2="V" 	k="43" />
<hkern g1="Q" 	g2="W" 	k="27" />
<hkern g1="Q" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="Q" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="Q" 	g2="J" 	k="25" />
<hkern g1="Q" 	g2="X" 	k="61" />
<hkern g1="Q" 	g2="Z" 	k="12" />
<hkern g1="Q" 	g2="AE" 	k="41" />
<hkern g1="R" 	g2="backslash" 	k="12" />
<hkern g1="R" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="R" 	g2="c,ccedilla" 	k="20" />
<hkern g1="R" 	g2="d" 	k="20" />
<hkern g1="R" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="20" />
<hkern g1="R" 	g2="g" 	k="20" />
<hkern g1="R" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="R" 	g2="q" 	k="20" />
<hkern g1="R" 	g2="eth" 	k="20" />
<hkern g1="R" 	g2="T" 	k="57" />
<hkern g1="R" 	g2="V" 	k="25" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="45" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-43" />
<hkern g1="R" 	g2="J" 	k="-18" />
<hkern g1="R" 	g2="X" 	k="-39" />
<hkern g1="R" 	g2="Z" 	k="-16" />
<hkern g1="R" 	g2="AE" 	k="-51" />
<hkern g1="R" 	g2="ampersand" 	k="20" />
<hkern g1="S" 	g2="v" 	k="47" />
<hkern g1="S" 	g2="w" 	k="45" />
<hkern g1="S" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="S" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="T" 	g2="backslash" 	k="-51" />
<hkern g1="T" 	g2="x" 	k="33" />
<hkern g1="T" 	g2="v" 	k="37" />
<hkern g1="T" 	g2="w" 	k="31" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="37" />
<hkern g1="T" 	g2="z" 	k="74" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="T" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="T" 	g2="hyphen,uni00AD,endash,emdash" 	k="106" />
<hkern g1="T" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="T" 	g2="parenright" 	k="-20" />
<hkern g1="T" 	g2="comma,period,ellipsis" 	k="113" />
<hkern g1="T" 	g2="slash" 	k="109" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="127" />
<hkern g1="T" 	g2="c,ccedilla" 	k="123" />
<hkern g1="T" 	g2="d" 	k="127" />
<hkern g1="T" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="123" />
<hkern g1="T" 	g2="g" 	k="127" />
<hkern g1="T" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="123" />
<hkern g1="T" 	g2="q" 	k="127" />
<hkern g1="T" 	g2="s" 	k="100" />
<hkern g1="T" 	g2="eth" 	k="123" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="98" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="115" />
<hkern g1="T" 	g2="m,n,ntilde" 	k="100" />
<hkern g1="T" 	g2="p" 	k="100" />
<hkern g1="T" 	g2="r" 	k="100" />
<hkern g1="T" 	g2="colon,semicolon" 	k="68" />
<hkern g1="T" 	g2="question" 	k="-33" />
<hkern g1="T" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="49" />
<hkern g1="T" 	g2="T" 	k="-37" />
<hkern g1="T" 	g2="V" 	k="-51" />
<hkern g1="T" 	g2="W" 	k="-29" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-53" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="82" />
<hkern g1="T" 	g2="exclamdown" 	k="68" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="139" />
<hkern g1="T" 	g2="J" 	k="197" />
<hkern g1="T" 	g2="X" 	k="-23" />
<hkern g1="T" 	g2="AE" 	k="145" />
<hkern g1="T" 	g2="C,Ccedilla" 	k="49" />
<hkern g1="T" 	g2="G" 	k="49" />
<hkern g1="T" 	g2="Q" 	k="49" />
<hkern g1="T" 	g2="ampersand" 	k="45" />
<hkern g1="T" 	g2="at" 	k="80" />
<hkern g1="T" 	g2="M" 	k="14" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="10" />
<hkern g1="V" 	g2="backslash" 	k="-37" />
<hkern g1="V" 	g2="z" 	k="25" />
<hkern g1="V" 	g2="quoteright,quotedblright" 	k="-10" />
<hkern g1="V" 	g2="quoteleft,quotedblleft" 	k="-14" />
<hkern g1="V" 	g2="hyphen,uni00AD,endash,emdash" 	k="25" />
<hkern g1="V" 	g2="quotedbl,quotesingle" 	k="-10" />
<hkern g1="V" 	g2="comma,period,ellipsis" 	k="82" />
<hkern g1="V" 	g2="slash" 	k="61" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="76" />
<hkern g1="V" 	g2="c,ccedilla" 	k="57" />
<hkern g1="V" 	g2="d" 	k="76" />
<hkern g1="V" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="57" />
<hkern g1="V" 	g2="g" 	k="76" />
<hkern g1="V" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="57" />
<hkern g1="V" 	g2="q" 	k="76" />
<hkern g1="V" 	g2="eth" 	k="57" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="35" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="V" 	g2="m,n,ntilde" 	k="39" />
<hkern g1="V" 	g2="p" 	k="39" />
<hkern g1="V" 	g2="r" 	k="39" />
<hkern g1="V" 	g2="colon,semicolon" 	k="10" />
<hkern g1="V" 	g2="question" 	k="-45" />
<hkern g1="V" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="6" />
<hkern g1="V" 	g2="T" 	k="-45" />
<hkern g1="V" 	g2="V" 	k="-59" />
<hkern g1="V" 	g2="W" 	k="-35" />
<hkern g1="V" 	g2="Y,Yacute,Ydieresis" 	k="-61" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="V" 	g2="exclamdown" 	k="10" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="86" />
<hkern g1="V" 	g2="J" 	k="133" />
<hkern g1="V" 	g2="X" 	k="-31" />
<hkern g1="V" 	g2="AE" 	k="113" />
<hkern g1="V" 	g2="C,Ccedilla" 	k="6" />
<hkern g1="V" 	g2="G" 	k="6" />
<hkern g1="V" 	g2="Q" 	k="6" />
<hkern g1="V" 	g2="ampersand" 	k="10" />
<hkern g1="W" 	g2="backslash" 	k="-16" />
<hkern g1="W" 	g2="quoteright,quotedblright" 	k="-10" />
<hkern g1="W" 	g2="quotedbl,quotesingle" 	k="-10" />
<hkern g1="W" 	g2="slash" 	k="33" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="W" 	g2="c,ccedilla" 	k="14" />
<hkern g1="W" 	g2="d" 	k="20" />
<hkern g1="W" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="14" />
<hkern g1="W" 	g2="g" 	k="20" />
<hkern g1="W" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="W" 	g2="q" 	k="20" />
<hkern g1="W" 	g2="eth" 	k="14" />
<hkern g1="W" 	g2="m,n,ntilde" 	k="12" />
<hkern g1="W" 	g2="p" 	k="12" />
<hkern g1="W" 	g2="r" 	k="12" />
<hkern g1="W" 	g2="T" 	k="-23" />
<hkern g1="W" 	g2="V" 	k="-37" />
<hkern g1="W" 	g2="Y,Yacute,Ydieresis" 	k="-39" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="35" />
<hkern g1="W" 	g2="J" 	k="66" />
<hkern g1="W" 	g2="AE" 	k="49" />
<hkern g1="X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="47" />
<hkern g1="X" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="47" />
<hkern g1="X" 	g2="t" 	k="25" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="49" />
<hkern g1="X" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="68" />
<hkern g1="X" 	g2="v" 	k="37" />
<hkern g1="X" 	g2="w" 	k="31" />
<hkern g1="X" 	g2="y,yacute,ydieresis" 	k="25" />
<hkern g1="X" 	g2="comma,period,ellipsis" 	k="-31" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="X" 	g2="guillemotleft,guilsinglleft" 	k="57" />
<hkern g1="X" 	g2="guillemotright,guilsinglright" 	k="29" />
<hkern g1="X" 	g2="hyphen,uni00AD,endash,emdash" 	k="45" />
<hkern g1="X" 	g2="slash" 	k="-25" />
<hkern g1="X" 	g2="ampersand" 	k="16" />
<hkern g1="X" 	g2="asterisk" 	k="10" />
<hkern g1="X" 	g2="at" 	k="27" />
<hkern g1="X" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-47" />
<hkern g1="X" 	g2="J" 	k="-20" />
<hkern g1="X" 	g2="V" 	k="-23" />
<hkern g1="X" 	g2="X" 	k="-41" />
<hkern g1="X" 	g2="Y,Yacute,Ydieresis" 	k="-25" />
<hkern g1="X" 	g2="Z" 	k="-20" />
<hkern g1="X" 	g2="AE" 	k="-53" />
<hkern g1="X" 	g2="C,Ccedilla" 	k="68" />
<hkern g1="X" 	g2="G" 	k="68" />
<hkern g1="X" 	g2="Q" 	k="68" />
<hkern g1="X" 	g2="c,ccedilla" 	k="47" />
<hkern g1="X" 	g2="d" 	k="47" />
<hkern g1="X" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="47" />
<hkern g1="X" 	g2="g" 	k="47" />
<hkern g1="X" 	g2="q" 	k="47" />
<hkern g1="X" 	g2="eth" 	k="47" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,ellipsis" 	k="113" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteleft,quotedblleft" 	k="-8" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="86" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="ampersand" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="at" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="133" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="180" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="V" 	k="-63" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="X" 	k="-35" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="-66" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,Ccedilla" 	k="43" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="G" 	k="43" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Q" 	k="43" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,ccedilla" 	k="119" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="119" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="q" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="eth" 	k="119" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="43" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="M" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="119" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,ntilde" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="53" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="16" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="49" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-8" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="backslash" 	k="-43" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="question" 	k="-45" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="exclamdown" 	k="37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-49" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="W" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotedbl,quotesingle" 	k="-8" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="p" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="r" 	k="61" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="45" />
<hkern g1="Z" 	g2="guillemotright,guilsinglright" 	k="18" />
<hkern g1="Z" 	g2="hyphen,uni00AD,endash,emdash" 	k="96" />
<hkern g1="Z" 	g2="ampersand" 	k="31" />
<hkern g1="Z" 	g2="at" 	k="20" />
<hkern g1="Z" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-31" />
<hkern g1="Z" 	g2="J" 	k="-29" />
<hkern g1="Z" 	g2="V" 	k="-29" />
<hkern g1="Z" 	g2="X" 	k="-27" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="-31" />
<hkern g1="Z" 	g2="AE" 	k="-39" />
<hkern g1="Z" 	g2="C,Ccedilla" 	k="31" />
<hkern g1="Z" 	g2="G" 	k="31" />
<hkern g1="Z" 	g2="Q" 	k="31" />
<hkern g1="Z" 	g2="c,ccedilla" 	k="51" />
<hkern g1="Z" 	g2="d" 	k="51" />
<hkern g1="Z" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="51" />
<hkern g1="Z" 	g2="g" 	k="51" />
<hkern g1="Z" 	g2="q" 	k="51" />
<hkern g1="Z" 	g2="eth" 	k="51" />
<hkern g1="Z" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="Z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="51" />
<hkern g1="Z" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="25" />
<hkern g1="Z" 	g2="backslash" 	k="-10" />
<hkern g1="Z" 	g2="T" 	k="-16" />
<hkern g1="Thorn" 	g2="comma,period,ellipsis" 	k="51" />
<hkern g1="Thorn" 	g2="quoteleft,quotedblleft" 	k="76" />
<hkern g1="Thorn" 	g2="hyphen,uni00AD,endash,emdash" 	k="-33" />
<hkern g1="Thorn" 	g2="asterisk" 	k="41" />
<hkern g1="Thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="Thorn" 	g2="J" 	k="90" />
<hkern g1="Thorn" 	g2="V" 	k="53" />
<hkern g1="Thorn" 	g2="X" 	k="80" />
<hkern g1="Thorn" 	g2="Y,Yacute,Ydieresis" 	k="96" />
<hkern g1="Thorn" 	g2="Z" 	k="45" />
<hkern g1="Thorn" 	g2="quoteright,quotedblright" 	k="68" />
<hkern g1="Thorn" 	g2="backslash" 	k="47" />
<hkern g1="Thorn" 	g2="T" 	k="133" />
<hkern g1="Thorn" 	g2="W" 	k="10" />
<hkern g1="Thorn" 	g2="quotedbl,quotesingle" 	k="68" />
<hkern g1="parenleft" 	g2="y,yacute,ydieresis" 	k="-41" />
<hkern g1="parenleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="parenleft" 	g2="V" 	k="-31" />
<hkern g1="parenleft" 	g2="Y,Yacute,Ydieresis" 	k="-31" />
<hkern g1="parenleft" 	g2="AE" 	k="43" />
<hkern g1="parenleft" 	g2="C,Ccedilla" 	k="20" />
<hkern g1="parenleft" 	g2="G" 	k="20" />
<hkern g1="parenleft" 	g2="Q" 	k="20" />
<hkern g1="parenleft" 	g2="c,ccedilla" 	k="31" />
<hkern g1="parenleft" 	g2="d" 	k="37" />
<hkern g1="parenleft" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="31" />
<hkern g1="parenleft" 	g2="g" 	k="37" />
<hkern g1="parenleft" 	g2="q" 	k="37" />
<hkern g1="parenleft" 	g2="eth" 	k="31" />
<hkern g1="parenleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="parenleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="37" />
<hkern g1="parenleft" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="parenleft" 	g2="T" 	k="-20" />
<hkern g1="parenleft" 	g2="W" 	k="-10" />
<hkern g1="parenleft" 	g2="j" 	k="-184" />
<hkern g1="parenleft" 	g2="parenright" 	k="-164" />
<hkern g1="bracketleft" 	g2="y,yacute,ydieresis" 	k="-78" />
<hkern g1="bracketleft" 	g2="V" 	k="-10" />
<hkern g1="bracketleft" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="bracketleft" 	g2="c,ccedilla" 	k="10" />
<hkern g1="bracketleft" 	g2="d" 	k="10" />
<hkern g1="bracketleft" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="10" />
<hkern g1="bracketleft" 	g2="g" 	k="10" />
<hkern g1="bracketleft" 	g2="q" 	k="10" />
<hkern g1="bracketleft" 	g2="eth" 	k="10" />
<hkern g1="bracketleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="bracketleft" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="bracketleft" 	g2="j" 	k="-195" />
<hkern g1="bracketleft" 	g2="bracketright" 	k="-164" />
<hkern g1="braceleft" 	g2="y,yacute,ydieresis" 	k="-78" />
<hkern g1="braceleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="braceleft" 	g2="AE" 	k="51" />
<hkern g1="braceleft" 	g2="C,Ccedilla" 	k="8" />
<hkern g1="braceleft" 	g2="G" 	k="8" />
<hkern g1="braceleft" 	g2="Q" 	k="8" />
<hkern g1="braceleft" 	g2="c,ccedilla" 	k="10" />
<hkern g1="braceleft" 	g2="d" 	k="10" />
<hkern g1="braceleft" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="10" />
<hkern g1="braceleft" 	g2="g" 	k="10" />
<hkern g1="braceleft" 	g2="q" 	k="10" />
<hkern g1="braceleft" 	g2="eth" 	k="10" />
<hkern g1="braceleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="8" />
<hkern g1="braceleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="braceleft" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="braceleft" 	g2="j" 	k="-186" />
<hkern g1="braceleft" 	g2="braceright" 	k="-164" />
<hkern g1="asterisk" 	g2="v" 	k="-61" />
<hkern g1="asterisk" 	g2="w" 	k="-41" />
<hkern g1="asterisk" 	g2="y,yacute,ydieresis" 	k="-61" />
<hkern g1="asterisk" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="asterisk" 	g2="J" 	k="229" />
<hkern g1="asterisk" 	g2="X" 	k="20" />
<hkern g1="asterisk" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="asterisk" 	g2="AE" 	k="123" />
<hkern g1="asterisk" 	g2="x" 	k="-61" />
<hkern g1="asterisk" 	g2="T" 	k="20" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="104" />
<hkern g1="quotedbl,quotesingle" 	g2="V" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="AE" 	k="221" />
<hkern g1="quotedbl,quotesingle" 	g2="C,Ccedilla" 	k="14" />
<hkern g1="quotedbl,quotesingle" 	g2="G" 	k="14" />
<hkern g1="quotedbl,quotesingle" 	g2="Q" 	k="14" />
<hkern g1="quotedbl,quotesingle" 	g2="c,ccedilla" 	k="61" />
<hkern g1="quotedbl,quotesingle" 	g2="d" 	k="72" />
<hkern g1="quotedbl,quotesingle" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="61" />
<hkern g1="quotedbl,quotesingle" 	g2="g" 	k="72" />
<hkern g1="quotedbl,quotesingle" 	g2="q" 	k="72" />
<hkern g1="quotedbl,quotesingle" 	g2="eth" 	k="61" />
<hkern g1="quotedbl,quotesingle" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="quotedbl,quotesingle" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="72" />
<hkern g1="quotedbl,quotesingle" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="quotedbl,quotesingle" 	g2="T" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="104" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="180" />
<hkern g1="quoteleft,quotedblleft" 	g2="V" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="221" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,Ccedilla" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="G" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="Q" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,ccedilla" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="d" 	k="51" />
<hkern g1="quoteleft,quotedblleft" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="g" 	k="51" />
<hkern g1="quoteleft,quotedblleft" 	g2="q" 	k="51" />
<hkern g1="quoteleft,quotedblleft" 	g2="eth" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="51" />
<hkern g1="quoteleft,quotedblleft" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="T" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="W" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="104" />
<hkern g1="quoteright,quotedblright" 	g2="V" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="221" />
<hkern g1="quoteright,quotedblright" 	g2="C,Ccedilla" 	k="14" />
<hkern g1="quoteright,quotedblright" 	g2="G" 	k="14" />
<hkern g1="quoteright,quotedblright" 	g2="Q" 	k="14" />
<hkern g1="quoteright,quotedblright" 	g2="c,ccedilla" 	k="61" />
<hkern g1="quoteright,quotedblright" 	g2="d" 	k="72" />
<hkern g1="quoteright,quotedblright" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="61" />
<hkern g1="quoteright,quotedblright" 	g2="g" 	k="72" />
<hkern g1="quoteright,quotedblright" 	g2="q" 	k="72" />
<hkern g1="quoteright,quotedblright" 	g2="eth" 	k="61" />
<hkern g1="quoteright,quotedblright" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="72" />
<hkern g1="quoteright,quotedblright" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="quoteright,quotedblright" 	g2="T" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="v" 	k="51" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="w" 	k="41" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="V" 	k="164" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="184" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="AE" 	k="-10" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,Ccedilla" 	k="61" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="G" 	k="61" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Q" 	k="61" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="s" 	k="-20" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="184" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="82" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="j" 	k="-92" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-10" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="31" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="X" 	k="16" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="102" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="82" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="49" />
<hkern g1="guillemotright,guilsinglright" 	g2="X" 	k="33" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="117" />
<hkern g1="guillemotright,guilsinglright" 	g2="AE" 	k="61" />
<hkern g1="guillemotright,guilsinglright" 	g2="x" 	k="31" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="125" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="20" />
<hkern g1="guillemotright,guilsinglright" 	g2="S" 	k="31" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="J" 	k="147" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="V" 	k="53" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="X" 	k="45" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="84" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="AE" 	k="55" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="C,Ccedilla" 	k="-25" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="G" 	k="-25" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Q" 	k="-25" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="c,ccedilla" 	k="-16" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="d" 	k="-16" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="-16" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="g" 	k="-16" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="q" 	k="-16" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="eth" 	k="-16" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-25" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-16" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-16" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="x" 	k="27" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="T" 	k="123" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="S" 	k="51" />
<hkern g1="comma,period,ellipsis" 	g2="v" 	k="109" />
<hkern g1="comma,period,ellipsis" 	g2="w" 	k="82" />
<hkern g1="comma,period,ellipsis" 	g2="y,yacute,ydieresis" 	k="45" />
<hkern g1="comma,period,ellipsis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-31" />
<hkern g1="comma,period,ellipsis" 	g2="V" 	k="164" />
<hkern g1="comma,period,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="174" />
<hkern g1="comma,period,ellipsis" 	g2="AE" 	k="-51" />
<hkern g1="comma,period,ellipsis" 	g2="C,Ccedilla" 	k="92" />
<hkern g1="comma,period,ellipsis" 	g2="G" 	k="92" />
<hkern g1="comma,period,ellipsis" 	g2="Q" 	k="92" />
<hkern g1="comma,period,ellipsis" 	g2="c,ccedilla" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="d" 	k="57" />
<hkern g1="comma,period,ellipsis" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="g" 	k="57" />
<hkern g1="comma,period,ellipsis" 	g2="q" 	k="57" />
<hkern g1="comma,period,ellipsis" 	g2="eth" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="92" />
<hkern g1="comma,period,ellipsis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="57" />
<hkern g1="comma,period,ellipsis" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="comma,period,ellipsis" 	g2="x" 	k="-10" />
<hkern g1="comma,period,ellipsis" 	g2="T" 	k="174" />
<hkern g1="comma,period,ellipsis" 	g2="W" 	k="82" />
<hkern g1="comma,period,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="51" />
<hkern g1="comma,period,ellipsis" 	g2="l" 	k="20" />
<hkern g1="comma,period,ellipsis" 	g2="t" 	k="31" />
<hkern g1="comma,period,ellipsis" 	g2="zero" 	k="31" />
<hkern g1="colon,semicolon" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-25" />
<hkern g1="colon,semicolon" 	g2="V" 	k="37" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="76" />
<hkern g1="colon,semicolon" 	g2="AE" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="T" 	k="90" />
<hkern g1="colon,semicolon" 	g2="W" 	k="12" />
<hkern g1="backslash" 	g2="v" 	k="31" />
<hkern g1="backslash" 	g2="w" 	k="31" />
<hkern g1="backslash" 	g2="y,yacute,ydieresis" 	k="-127" />
<hkern g1="backslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-31" />
<hkern g1="backslash" 	g2="V" 	k="102" />
<hkern g1="backslash" 	g2="Y,Yacute,Ydieresis" 	k="113" />
<hkern g1="backslash" 	g2="AE" 	k="-55" />
<hkern g1="backslash" 	g2="C,Ccedilla" 	k="70" />
<hkern g1="backslash" 	g2="G" 	k="70" />
<hkern g1="backslash" 	g2="Q" 	k="70" />
<hkern g1="backslash" 	g2="d" 	k="-20" />
<hkern g1="backslash" 	g2="g" 	k="-20" />
<hkern g1="backslash" 	g2="q" 	k="-20" />
<hkern g1="backslash" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="70" />
<hkern g1="backslash" 	g2="M" 	k="16" />
<hkern g1="backslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-20" />
<hkern g1="backslash" 	g2="m,n,ntilde" 	k="-27" />
<hkern g1="backslash" 	g2="x" 	k="-20" />
<hkern g1="backslash" 	g2="T" 	k="164" />
<hkern g1="backslash" 	g2="W" 	k="61" />
<hkern g1="backslash" 	g2="p" 	k="-27" />
<hkern g1="backslash" 	g2="r" 	k="-27" />
<hkern g1="backslash" 	g2="j" 	k="-256" />
<hkern g1="backslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="66" />
<hkern g1="backslash" 	g2="germandbls" 	k="-27" />
<hkern g1="backslash" 	g2="thorn" 	k="-27" />
<hkern g1="backslash" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="-27" />
<hkern g1="backslash" 	g2="H" 	k="37" />
<hkern g1="backslash" 	g2="B" 	k="37" />
<hkern g1="backslash" 	g2="D,Eth" 	k="37" />
<hkern g1="backslash" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="37" />
<hkern g1="backslash" 	g2="F" 	k="37" />
<hkern g1="backslash" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="37" />
<hkern g1="backslash" 	g2="L" 	k="37" />
<hkern g1="backslash" 	g2="N,Ntilde" 	k="37" />
<hkern g1="backslash" 	g2="P" 	k="37" />
<hkern g1="backslash" 	g2="R" 	k="37" />
<hkern g1="backslash" 	g2="Thorn" 	k="37" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="66" />
<hkern g1="slash" 	g2="J" 	k="94" />
<hkern g1="slash" 	g2="V" 	k="-92" />
<hkern g1="slash" 	g2="X" 	k="-41" />
<hkern g1="slash" 	g2="Y,Yacute,Ydieresis" 	k="-86" />
<hkern g1="slash" 	g2="Z" 	k="-12" />
<hkern g1="slash" 	g2="AE" 	k="182" />
<hkern g1="slash" 	g2="c,ccedilla" 	k="37" />
<hkern g1="slash" 	g2="d" 	k="41" />
<hkern g1="slash" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="37" />
<hkern g1="slash" 	g2="g" 	k="41" />
<hkern g1="slash" 	g2="q" 	k="41" />
<hkern g1="slash" 	g2="eth" 	k="37" />
<hkern g1="slash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="slash" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="slash" 	g2="T" 	k="-78" />
<hkern g1="slash" 	g2="W" 	k="-66" />
<hkern g1="slash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-25" />
<hkern g1="slash" 	g2="l" 	k="-31" />
<hkern g1="slash" 	g2="thorn" 	k="-10" />
<hkern g1="slash" 	g2="H" 	k="-25" />
<hkern g1="slash" 	g2="B" 	k="-25" />
<hkern g1="slash" 	g2="D,Eth" 	k="-25" />
<hkern g1="slash" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="-25" />
<hkern g1="slash" 	g2="F" 	k="-25" />
<hkern g1="slash" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="-25" />
<hkern g1="slash" 	g2="L" 	k="-25" />
<hkern g1="slash" 	g2="N,Ntilde" 	k="-25" />
<hkern g1="slash" 	g2="P" 	k="-25" />
<hkern g1="slash" 	g2="R" 	k="-25" />
<hkern g1="slash" 	g2="Thorn" 	k="-25" />
<hkern g1="slash" 	g2="b" 	k="-31" />
<hkern g1="slash" 	g2="k" 	k="-10" />
<hkern g1="slash" 	g2="h" 	k="-31" />
<hkern g1="slash" 	g2="K" 	k="-25" />
<hkern g1="ampersand" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-25" />
<hkern g1="ampersand" 	g2="V" 	k="82" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ydieresis" 	k="117" />
<hkern g1="ampersand" 	g2="AE" 	k="-33" />
<hkern g1="ampersand" 	g2="T" 	k="174" />
<hkern g1="ampersand" 	g2="W" 	k="31" />
<hkern g1="ampersand" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="66" />
<hkern g1="exclamdown" 	g2="V" 	k="37" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="76" />
<hkern g1="exclamdown" 	g2="T" 	k="90" />
<hkern g1="exclamdown" 	g2="W" 	k="12" />
<hkern g1="exclamdown" 	g2="j" 	k="-92" />
<hkern g1="questiondown" 	g2="y,yacute,ydieresis" 	k="-94" />
<hkern g1="questiondown" 	g2="V" 	k="70" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="139" />
<hkern g1="questiondown" 	g2="T" 	k="164" />
<hkern g1="questiondown" 	g2="W" 	k="27" />
<hkern g1="questiondown" 	g2="j" 	k="-213" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="35" />
<hkern g1="at" 	g2="J" 	k="41" />
<hkern g1="at" 	g2="X" 	k="20" />
<hkern g1="at" 	g2="Y,Yacute,Ydieresis" 	k="72" />
<hkern g1="at" 	g2="T" 	k="102" />
</font>
</defs></svg> 