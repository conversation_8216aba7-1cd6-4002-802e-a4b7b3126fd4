import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ActivatedRoute, Router } from '@angular/router';
import { LoanRequestService } from '../../service/loan-request.service';
import { forkJoin } from 'rxjs';
import { FileStackPickerConfig } from '../../../loan-request-origin/models/file-stack-picker-config';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { DOCUMENTS_INFO } from './documents-category.file';
import { DocumentsService } from '../../service/documents.service';
import { BusinessService } from '../../service/business.service';
import { RequiredDocumentsModel } from '../../model/required-document.model';

@Component({
  selector: 'app-documents-page',
  templateUrl: './documents-page.component.html',
  styleUrls: ['./documents-page.component.scss'],
})
export class DocumentsPageComponent implements OnInit {
  instruction: string;
  id: number;
  documentsArray: any[];
  fileStackPickerConfigurations: FileStackPickerConfig;
  parentFilesForm: UntypedFormGroup;
  checkedFiles: Array<any> = [];
  isSave = true;
  documentsCategory = DOCUMENTS_INFO;
  storeCheckboxs: number[];
  flattenedArray: any[];
  filestackWorkflowUUID: string;
  owners: any;
  loanRequest: any;
  requeredOwnerDoc: any[];

  constructor(
    private translateService: TranslateService,
    private route: ActivatedRoute,
    private router: Router,
    private loanRequestService: LoanRequestService,
    private documentsService: DocumentsService,
    private businessService: BusinessService,
    private fb: UntypedFormBuilder
  ) {}

  ngOnInit() {
    this.storeCheckboxs = JSON.parse(localStorage.getItem('checkedFiles'));
    this.getFilestackConfig();

    this.route.parent.parent.parent.children[0].data.subscribe((data) => {
      this.loanRequest = data.loanRequest.business.id;
      this.owners = data.loanRequest.business.owners;
      this.id = data.loanRequest.id;
    });
    this.translateService
      .get('loan-request.documents.instruction')
      .subscribe((result) => {
        this.instruction = result;
      });
    this.getDocumentsArray();
  }

  getDocumentsByCategory(category: string) {
    return this.loanRequestService.getRequiredDocuments(this.id, category);
  }

  getDocumentsArray() {
    const observables = this.documentsCategory.map((url) =>
      this.getDocumentsByCategory(url.category)
    );
    forkJoin(observables).subscribe((val) => {
      this.filestackWorkflowUUID = val[0].filestackWorkflowUUID;
      this.documentsArray = val
        .map((item) => {
          return item.requiredDocuments;
        })
        .filter((item) => {
          return item.length !== 0;
        });

      this.createForm();
      if (this.owners.length !== 0) {
        this.getOwnersDocuments();
      }
    });
  }

  getFilestackConfig() {
    this.documentsService
      .getFileStackPickerConfigurations()
      .subscribe((data) => (this.fileStackPickerConfigurations = data));
  }

  createForm() {
    this.parentFilesForm = this.fb.group({
      checkedFile: [''],
    });
  }

  onCheckedFiles(event) {
    if (event.check) {
      this.checkedFiles.push(event.info);
    } else {
      const index = this.checkedFiles.findIndex(
        (item) => item.id === event.info.id
      );
      this.checkedFiles.splice(index, 1);
    }

    this.buttonState() ? (this.isSave = true) : (this.isSave = false);
  }

  buttonState() {
    const requiredFilesLength = this.flattenedArray.filter(
      (item) => item.type.isDocOptional === false
    ).length;

    const requiredSelectedFilesLength = this.checkedFiles.filter(
      (item) => item.isDocOptional === false
    ).length;

    return requiredFilesLength !== requiredSelectedFilesLength;
  }

  checkFilesfromStore() {
    this.flattenedArray.forEach((item) => {
      this.storeCheckboxs.forEach((id) => {
        if (item.type.id === id) {
          this.checkedFiles.push(item.type);
        }
      });
    });
  }

  getOwnersDocuments() {
    this.businessService
      .getRequiredBusinessOwnerDocuments(this.loanRequest)
      .subscribe((data) => {
        const ownerDocs = Array.from(data.values()).map(
          (ownerDocuments, index) => {
            ownerDocuments.forEach((document) => {
              const ownerID: number = Array.from(data.keys())[index];
              const docID: number = document.type.id;
              const uniqIDDoc = String(ownerID) + String(docID);
              document.type.id = +uniqIDDoc;
            });
            return ownerDocuments;
          }
        );

        this.documentsArray = this.documentsArray.concat(ownerDocs);
        this.flattenedArray = [].concat(...this.documentsArray);
        if (this.storeCheckboxs) {
          this.checkFilesfromStore();
        }
      });
  }

  submit() {
    const arrayId = JSON.stringify(this.checkedFiles.map((item) => item.id));
    localStorage.setItem('checkedFiles', arrayId);

    if (!this.buttonState()) {
      this.documentsService.completedUploadFiles(this.buttonState());
      this.router.navigate(['../loan-request/' + this.id]);
    }
  }
}
