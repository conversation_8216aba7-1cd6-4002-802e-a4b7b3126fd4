import { Agent } from './agent';
import { DebtPaymentInfo } from './debt-payment-info';

export interface Loan {
  id: number;
  loanName: string;
  loanAmount: number;
  fundedDate: string;
  organizationName: string;
  paidPercent: number;
  agent?: Agent;
  organizationId: number;
  status: string;
  statusCode: string;
  purpose?: string;
  createdAt?: string;
  requestDate?: string;
  creditCardExpiry?: string;
  creditCardStatus?: string;
  isLoanRealEstate?: boolean;
  debtPaymentInfo?: DebtPaymentInfo;
}
