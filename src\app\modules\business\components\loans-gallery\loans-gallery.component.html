<div class="loans-gallery-wrap">
  <div class="loans-gallery-options">
    <div class="filter-options">
      <form action="#" [formGroup]="loanOptionsForm">
        <div class="control-wrap filter-control">
          <button type="button" class="btn-search">
            <i class="icon icon-search" aria-hidden="true"></i>
          </button>
          <input
            type="search"
            formControlName="filterSearch"
            class="filter-search"
            (input)="searchFilter(loanOptionsForm.controls['filterSearch'])"
          />
        </div>
      </form>
    </div>
    <div class="btn-holder">
      <a [routerLink]="['/' + newLoanPath]" class="btn btn-primary">{{
        'loan-gallery.new-loan-btn' | translate
      }}</a>
    </div>
  </div>
  <div class="gallery-section">
    <div class="gallery-holder" *ngIf="loanInfoData; else newLoan_content">
      <loan-gallery-box
        class="loan-gallery-box"
        *ngFor="let loanItem of loanInfoData | filter: searchText"
        [loanItem]="loanItem"
        (loanClicked)="redirectToLoan($event)"
      >
      </loan-gallery-box>
    </div>

    <ng-template #newLoan_content class="top-section">
      <div class="new-loan-box">
        <div>
          <span *ngIf="newLoanName$ | async as newLoanName">
            {{ newLoanName.firstName }},</span
          >
          <p [innerHtml]="newLoanInfo"></p>
        </div>
        <img src="/assets/img/biz/no-loan.png" alt="no-loan-img" />
      </div>
    </ng-template>
  </div>
</div>
