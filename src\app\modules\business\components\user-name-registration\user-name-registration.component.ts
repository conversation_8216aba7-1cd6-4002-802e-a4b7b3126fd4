import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import * as HttpStatus from 'http-status-codes';
import { Subscription } from 'rxjs';
import { DomainDataDto } from '../../../../layouts/app-component/domain-data';
import { DomainService } from '../../../../layouts/app-component/domain.service';
import { ErrorHandling } from '../../../shared/models/error-handling';
import { AppConstants } from '../../../shared/utils/app-constans';
import { matchingPasswords } from '../../../shared/validators/matching-passwords';
import { AppValidators } from '../../../shared/validators/validators';
import { REGISTRATION_STEPS } from '../../layouts/register/registration-steps';
import { RegistrationAllowedRequest } from '../../model/registration-allowed-equest';
import { TypeEnum } from '../../model/type-enum';
import { UserRegistration } from '../../model/user-registration.class';
import { HelperService } from '../../service/helper.service';
import { RegistrationService } from '../../service/registration.service';
import { UtilityService } from '../../service/utility.service';

@Component({
  selector: 'app-user-name-registration',
  templateUrl: './user-name-registration.component.html',
  styleUrls: ['./user-name-registration.component.scss'],
})
export class UserNameRegistrationComponent implements OnInit, OnDestroy {
  filteredCitiesSingle: any[];
  filteredStreetsSingle: any[];

  tzMask = AppConstants.TZ_AND_REGISTRATION_NUMBER_MASK;
  cellPhoneMask = AppConstants.CELL_PHONE_MASK;

  userRegistrationInfo: UserRegistration;
  userRegistrationForm: UntypedFormGroup;
  registrationMode: boolean;
  disabledBtn: boolean;
  maxPasswordLength: any;
  minPasswordLength: any;
  creditBureauTranslation: string;

  userTypes: any[];

  hideTerms = false;

  private routeDataSubscription: Subscription;
  private accountTypes: TypeEnum[];
  private onLanguageChangeSubscription: Subscription;

  constructor(
    private registrationService: RegistrationService,
    private utilityService: UtilityService,
    private translateService: TranslateService,
    private route: ActivatedRoute,
    private fb: UntypedFormBuilder,
    private router: Router,
    private helperService: HelperService,
    private domainService: DomainService
  ) {}

  ngOnInit() {
    this.domainService.getDomainData().subscribe((response: DomainDataDto) => {
      this.maxPasswordLength = response.maxPasswordLength;
      this.minPasswordLength = response.minPasswordLength;
    });
    this.userRegistrationInfo =
      this.registrationService.getUserRegistrationInfo();
    this.routeDataSubscription = this.route.data.subscribe((res) => {
      this.registrationMode = res.registrationMode;
      this.accountTypes = res.accountTypes;
      this.setUserTypes();
      if (!this.userRegistrationInfo.type) {
        this.userRegistrationInfo.type = this.userTypes[0].id;
      }
    });
    this.createForm();

    // fix for the p-autocomplete bug - to set initial values
    this.userRegistrationForm.get('city').patchValue({
      name: this.userRegistrationInfo.city,
    });
    this.userRegistrationForm.get('street').patchValue({
      name: this.userRegistrationInfo.street,
    });

    this.onLanguageChangeSubscription =
      this.translateService.onLangChange.subscribe(() => {
        this.setUserTypes();
      });

    this.translateService
      .get('register.user-information.credit-bureau')
      .subscribe((result) => {
        this.creditBureauTranslation = result;
      });
  }

  ngAfterViewInit() {
    setTimeout(() => {
      for (const key in this.userRegistrationForm.controls) {
        if (this.userRegistrationForm.controls[key]) {
          this.userRegistrationForm.controls[key].markAsPristine();
        }
      }
    }, 50);
  }

  ngOnDestroy() {
    this.routeDataSubscription.unsubscribe();
    this.onLanguageChangeSubscription.unsubscribe();
  }

  async filterCitiesSingle(event: any) {
    this.filteredCitiesSingle = await this.utilityService.filterCities(
      event.query
    );
  }

  async filterStreetSingle(event: any) {
    const city =
      this.userRegistrationForm.get('city').value.name ||
      this.userRegistrationForm.get('city').value;

    this.filteredStreetsSingle = await this.utilityService.filterStreets(
      city,
      event.query
    );
  }

  createForm() {
    this.userRegistrationForm = this.fb.group(
      {
        type: [this.userRegistrationInfo.type, [Validators.required]],
        firstName: [this.userRegistrationInfo.firstName, [Validators.required]],
        lastName: [this.userRegistrationInfo.lastName, [Validators.required]],
        identity: [
          this.userRegistrationInfo.identity,
          [Validators.required, AppValidators.tzValidator],
        ],
        cellPhone: [
          this.userRegistrationInfo.cellPhone,
          [Validators.required, AppValidators.getValidator('cellPhone')],
        ],
        city: [{ name: this.userRegistrationInfo.city }, [Validators.required]],
        street: [
          { name: this.userRegistrationInfo.street },
          [Validators.required],
        ],
        streetNumber: [
          this.userRegistrationInfo.streetNumber,
          [Validators.required],
        ],
        zipcode: [
          this.userRegistrationInfo.zipcode,
          [AppValidators.getValidator('zip-code')],
        ],
        businessEmail: [
          this.userRegistrationInfo.businessEmail,
          [Validators.required, AppValidators.getValidator('email')],
        ],
        personalEmail: [
          this.userRegistrationInfo.personalEmail,
          [AppValidators.getValidator('email')],
        ],
        password: [
          '',
          [
            Validators.required,
            AppValidators.getValidator('containsSpace'),
            AppValidators.getValidator('containsForbiddenChars'),
            AppValidators.getValidator('containsNoLetters'),
            AppValidators.getValidator('containsHebrewLetters'),
            AppValidators.getValidator('tooShort', this.minPasswordLength),
            AppValidators.getValidator('tooLong', this.maxPasswordLength),
            AppValidators.getValidator('containConsecutiveCharacters'),
            AppValidators.getValidator('containsNoNumbersSymbols'),
            AppValidators.getValidator('containsDot'),
          ],
        ],
        passwordConfirmation: ['', [Validators.required]],
        termsOfUse: [null, [Validators.requiredTrue]],
        legalTerms: [true],
      },
      { validator: matchingPasswords('password', 'passwordConfirmation') }
    );
  }

  setUserTypes() {
    this.userTypes = this.helperService.fromTypeEnumToPRadioButton(
      this.accountTypes
    );
  }

  collapseTerms(event) {
    event === true ? (this.hideTerms = true) : (this.hideTerms = false);
  }

  submit() {
    const success = () => {
      this.disabledBtn = true;
      this.registrationService.saveUserRegistrationInfo(
        this.userRegistrationForm
      );
      this.router
        .navigate(['/register/' + REGISTRATION_STEPS[1]])
        .then(() => window.scrollTo(0, 0));
    };

    const fail = (res: any) => {
      if (res.status === HttpStatus.UNPROCESSABLE_ENTITY) {
        ErrorHandling.formSetErrors(
          JSON.parse(res.error),
          this.userRegistrationForm
        );
      } else {
        throw Error(res);
      }
    };

    const request = {} as RegistrationAllowedRequest;
    request.identity = this.userRegistrationForm.value.identity;
    request.businessEmail = this.userRegistrationForm.value.businessEmail;

    this.registrationService
      .checkIfRegistrationAllowed(request)
      .subscribe(success, fail);
  }
}
