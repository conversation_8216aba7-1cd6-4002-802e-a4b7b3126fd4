import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { LoanRequestService } from '../../service/loan-request.service';
import { ActivatedRoute, Router } from '@angular/router';
import { LoanRequest } from '../../model/loan-request';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { LOAN_PAGES } from '../../layouts/loan/loan-pages';
import { Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'request-details',
  templateUrl: './request-details.component.html',
  styleUrls: ['./request-details.component.scss'],
})
export class RequestDetailsComponent implements OnInit, OnDestroy {
  loanRequest: LoanRequest;
  requestDetailsForm: UntypedFormGroup;

  currentLang: string;

  loanApproved: boolean;
  formLocked: boolean;

  private loanRequestSubscription: Subscription;

  constructor(
    private route: ActivatedRoute,
    private loanRequestService: LoanRequestService,
    private fb: UntypedFormBuilder,
    private router: Router,
    private translateService: TranslateService
  ) {}

  ngOnInit() {
    this.currentLang = this.translateService.currentLang;
    this.loanRequestSubscription = this.route.parent.parent.data.subscribe(
      ({ loanRequest }) => {
        this.loanRequest = loanRequest;
        this.loanApproved = !!this.loanRequest.loan;
        this.formLocked = this.loanRequestService.getFormLockedStatus(
          this.loanRequest
        );
      }
    );
    this.createForm();
  }

  ngOnDestroy() {
    this.loanRequestSubscription.unsubscribe();
  }

  private createForm() {
    this.requestDetailsForm = this.fb.group({
      name: [this.loanRequest.name, [Validators.required]],
    });
    this.requestDetailsForm.get('name').markAsDirty();

    if (this.formLocked) {
      this.requestDetailsForm.disable();
    }
  }

  submitForm(event: Event) {
    event.preventDefault();
    if (this.formLocked) {
      this.router.navigate([LOAN_PAGES[2]], {
        relativeTo: this.route.parent.parent,
      });
      return;
    }
    this.loanRequest.name = this.requestDetailsForm.value.name;
    const loanRequestToSave = new LoanRequest();
    loanRequestToSave.id = this.loanRequest.id;
    loanRequestToSave.name = this.requestDetailsForm.value.name;
    this.loanRequestService.updateLoanRequest(loanRequestToSave).subscribe(() =>
      this.router.navigate([LOAN_PAGES[2]], {
        relativeTo: this.route.parent.parent,
      })
    );
  }
}
