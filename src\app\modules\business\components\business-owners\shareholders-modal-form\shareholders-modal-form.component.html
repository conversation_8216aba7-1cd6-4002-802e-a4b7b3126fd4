<div class="shareholder-form-wrap bz-form-wrap">
  <div class="col-lg-11 m-auto">
    <div class="loan-info-title">
      <h2>פרטי שותף</h2>
    </div>
    <div class="form-holder">
      <form
        action="#"
        class="business-owners-form"
        [formGroup]="businessOwnersForm"
      >
        <strong class="heading">פרטים אישיים</strong>
        <div class="form-group-wrap">
          <div class="form-group">
            <div class="label-holder">
              <label>שם פרטי</label>
            </div>
            <div formGroupName="person">
              <div class="input-holder">
                <input
                  type="text"
                  class="form-control"
                  formControlName="firstName"
                />
              </div>
              <control-validation-errors
                [control]="businessOwnersForm.get('person.firstName')"
                translateTag="error"
              >
              </control-validation-errors>
            </div>
          </div>
          <div class="form-group">
            <div formGroupName="person">
              <div class="label-holder">
                <label>שם משפחה</label>
              </div>
              <div class="input-holder">
                <input
                  type="text"
                  class="form-control"
                  formControlName="lastName"
                />
              </div>
              <control-validation-errors
                [control]="businessOwnersForm.get('person.lastName')"
                translateTag="error"
              >
              </control-validation-errors>
            </div>
          </div>
        </div>
        <div class="form-group-wrap">
          <div class="form-group" style="height: 60px">
            <div class="label-holder">
              <label>מגדר</label>
            </div>
            <div formGroupName="person">
              <div class="radio-box">
                <p-radioButton
                  class="bz-tarya-radio"
                  name="userGender"
                  value="{{ maleGenderOption.value }}"
                  formControlName="gender"
                  label="{{ maleGenderOption.label }}"
                ></p-radioButton>
                <p-radioButton
                  class="bz-tarya-radio"
                  name="userGender"
                  value="{{ femaleGenderOption.value }}"
                  formControlName="gender"
                  label="{{ femaleGenderOption.label }}"
                ></p-radioButton>
                <control-validation-errors
                  [control]="businessOwnersForm.get('person.gender')"
                  translateTag="error"
                >
                </control-validation-errors>
              </div>
            </div>
          </div>

          <div class="form-group col-lg-6">
            <div class="label-holder">
              <label>תאריך לידה</label>
            </div>
            <div formGroupName="person">
              <p-calendar
                formControlName="birthDate"
                class="corporate-datepicker"
                placeholder="DD / MM / YYYY"
                [yearNavigator]="true"
                dateFormat="yy/mm/dd"
                [locale]="calendarLanguage"
                yearRange="{{ minYear + ':' + currentYear }}"
                [required]="true"
              >
              </p-calendar>
              <control-validation-errors
                [control]="businessOwnersForm.get('person.birthDate')"
                translateTag="error"
              >
              </control-validation-errors>
            </div>
          </div>
        </div>
        <div class="form-group-wrap">
          <input
            type="text"
            class="form-control"
            formControlName="id"
            [hidden]="hiddenId"
          />
          <input
            type="text"
            class="form-control"
            formControlName="documents"
            [hidden]="hiddenId"
          />
          <div class="form-group" formGroupName="person">
            <input
              type="text"
              class="form-control"
              formControlName="id"
              [hidden]="hiddenId"
            />
            <div class="label-holder">
              <label>מסי תעודת זהות</label>
            </div>
            <div class="input-holder">
              <input
                type="text"
                class="form-control"
                formControlName="identity"
                [textMask]="{ mask: registrationNumberMask }"
              />
            </div>
            <control-validation-errors
              [control]="businessOwnersForm.get('person.identity')"
              translateTag="error"
            >
            </control-validation-errors>
          </div>
          <div class="form-group" formGroupName="person">
            <div class="label-holder">
              <label>טלפון</label>
            </div>
            <div class="input-holder">
              <input
                type="text"
                class="form-control"
                [textMask]="{ mask: cellPhoneMask }"
                formControlName="cellPhone"
              />
            </div>
            <control-validation-errors
              [control]="businessOwnersForm.get('person.cellPhone')"
              translateTag="error"
            >
            </control-validation-errors>
          </div>
        </div>
        <div class="form-group-wrap">
          <div class="form-group" formGroupName="person">
            <div class="label-holder">
              <label>תפקיד</label>
            </div>
            <div class="input-holder">
              <input
                type="text"
                class="form-control"
                formControlName="jobTitle"
              />
            </div>
            <control-validation-errors
              [control]="businessOwnersForm.get('person.jobTitle')"
              translateTag="error"
            >
            </control-validation-errors>
          </div>
          <div class="form-group">
            <div class="label-holder">
              <label>אחוז החזקה</label>
            </div>
            <div class="input-holder">
              <input
                type="number"
                (input)="
                  setCorrectPercent(
                    businessOwnersForm.controls['ownershipPercentage']
                  )
                "
                class="form-control"
                formControlName="ownershipPercentage"
              />
            </div>
            <control-validation-errors
              [control]="businessOwnersForm.get('ownershipPercentage')"
              translateTag="error"
            >
            </control-validation-errors>
          </div>
        </div>
        <strong class="heading">יצירת קשר</strong>
        <div class="form-group-wrap" formGroupName="person">
          <div class="form-group">
            <div class="label-holder">
              <label>כתובת אימייל עסקית</label>
            </div>
            <div class="input-holder">
              <input
                type="email"
                class="form-control"
                formControlName="businessEmail"
              />
            </div>
            <control-validation-errors
              [control]="businessOwnersForm.get('person.businessEmail')"
              translateTag="error"
            >
            </control-validation-errors>
          </div>
          <div class="form-group">
            <div class="label-holder">
              <label>כתובת אימייל פרטית</label>
            </div>
            <div class="input-holder">
              <input
                type="email"
                class="form-control"
                formControlName="personalEmail"
              />
              <span class="msg-box">אופציונלי</span>
            </div>
          </div>
        </div>
        <div class="form-group-wrap" formGroupName="person">
          <div class="form-group">
            <div class="label-holder">
              <label>כתובת מגורים פרטית</label>
            </div>
            <div class="input-wrap" formGroupName="address">
              <div class="input-holder">
                <input
                  type="text"
                  class="form-control"
                  formControlName="id"
                  [hidden]="hiddenId"
                />
                <input
                  type="text"
                  class="form-control"
                  formControlName="countryCode"
                  [hidden]="hiddenId"
                />
                <div class="bz-autocomplete">
                  <p-autoComplete
                    formControlName="city"
                    [suggestions]="filteredCitiesSingle"
                    (completeMethod)="filterCitiesSingle($event)"
                    field="name"
                    [forceSelection]="true"
                    placeholder="{{ 'register.city' | translate }}"
                    [minLength]="1"
                    [style]="{ width: '100%' }"
                    [dropdown]="true"
                  ></p-autoComplete>
                </div>
                <control-validation-errors
                  [control]="businessOwnersForm.get('person.address.city')"
                  translateTag="error"
                >
                </control-validation-errors>
              </div>
              <div class="input-holder">
                <div class="bz-autocomplete">
                  <p-autoComplete
                    formControlName="street"
                    [suggestions]="filteredStreetsSingle"
                    (completeMethod)="filterStreetSingle($event)"
                    field="name"
                    [forceSelection]="true"
                    placeholder="{{ 'register.street' | translate }}"
                    [minLength]="1"
                    [style]="{ width: '100%' }"
                    [dropdown]="true"
                  ></p-autoComplete>
                </div>
                <control-validation-errors
                  [control]="businessOwnersForm.get('person.address.street')"
                  translateTag="error"
                >
                </control-validation-errors>
              </div>
              <div class="input-holder">
                <input
                  type="text"
                  class="form-control"
                  placeholder="מספר"
                  formControlName="streetNumber"
                />
                <control-validation-errors
                  [control]="
                    businessOwnersForm.get('person.address.streetNumber')
                  "
                  translateTag="error"
                >
                </control-validation-errors>
              </div>
              <div class="input-holder">
                <input
                  type="number"
                  class="form-control"
                  placeholder="{{ 'register.zip-code' | translate }}"
                  formControlName="zipcode"
                />
                <control-validation-errors
                  [control]="businessOwnersForm.get('person.address.zipcode')"
                  translateTag="error"
                >
                </control-validation-errors>
              </div>
            </div>
          </div>
        </div>
        <div class="btn-holder">
          <button type="button" class="btn btn-default" (click)="cancelData()">
            ביטול
          </button>
          <button
            type="button"
            class="btn btn-primary"
            [disabled]="!businessOwnersForm.valid"
            (click)="saveData(businessOwnersForm.value.id)"
          >
            שמור
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
