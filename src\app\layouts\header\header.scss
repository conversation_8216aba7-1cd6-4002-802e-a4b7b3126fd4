@import 'styles/variables';
@import 'styles/vendors/bootstrap/share';

.top-row {
  height: 30px;
  background: #2f4858;
  color: #f1f2f2;
  display: flex;
  .hiyou {
    border-right: 0.5px solid #bbbaba;
  }
}

.nav {
  white-space: nowrap;

  span {
    padding: 5px 10px;
    line-height: 20px;
    display: inline-block;
    vertical-align: top;
  }

  ul {
    display: contents;
    vertical-align: top;
    margin: 0;
    list-style: none;
    padding: 0;
  }
  li {
    display: inline-block;
    vertical-align: top;
    span {
      cursor: pointer;

      &:hover,
      &:focus {
        background-color: #002437;
        text-decoration: none;
      }
    }
  }
}

.contact-us {
  a {
    display: inline-block;
    vertical-align: top;
    padding: 7px 3px;
  }
}

a {
  color: #f1f2f2;
  padding: 0 20px;
  min-height: 30px;
  display: inline-block;
  vertical-align: top;
  font-size: 14px;
  line-height: 16px;

  &:hover,
  &:focus {
    background-color: #002437;
    text-decoration: none;
  }

  + a {
    &:after {
      height: 100%;
      content: '';
      display: block;
      background-color: #8fa4af;
      width: 1px;
      margin: 0 -20px;
      top: 0;
      position: absolute;
    }
  }

  &.logo {
    padding: 0 5px;
    @include media-breakpoint-down(md) {
      width: 120px;
    }

    &:hover,
    &:focus {
      background-color: transparent;
    }

    img {
      max-width: 100%;
      height: auto;
      display: inline-block;
      vertical-align: top;
    }
  }
}

.flags {
  min-width: 70px;
  display: flex;
  justify-content: space-around;

  > span {
    height: 16px;
    width: 25px;
    background-size: auto 100%;
    background-repeat: no-repeat;
    display: inline-block;
    cursor: pointer;
  }
}

.he {
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAQCAMAAAAlM38UAAAAwFBMVEUAAAAGR5sGR5wISJwSS5YSUKETTJcVUqEXTpkXT5k/cLFCcrNCdLNGdrRLf7lMgLpQg7tRg7xVhb1bisBei8Bij8NnjsNok8VrlsZumMdxlsZylsZyl8dymshym8h0ncl2ncp3msl/pM6BosyCo82Cps+GqNCMrNKOr9OWtNeat9ifu9u5zeS/0ebS3u7V4e7f6PPl7PTl7Pbp7/ft7e3u7u7x8fHz8/P09PT29vb4+Pj5+fn7+/v8/Pz9/f7///8kxWRlAAAAlklEQVR42nWN1w6DMBAEN4WQ3it2EkjvnQ7x//9VAKN7gYzX8uxJJ0Pk8m/c3HCNM8Y5i94kGlu3UDfGGYwGQlq830hDBOT7HWkAX8pbX6y2c/0pmw9PSM6zr1ie0uLBTe04EGJ0SIsLR8qr15lM292HbA5s+mfYJ7VhkV8vpBaqaimDWkNZKcYU4kQ3OUoFpsjBxCeXH+NaQJNuwqzbAAAAAElFTkSuQmCC);
}

.en {
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAQCAMAAAAlM38UAAAA7VBMVEUAAAAvL4IxMYMzM4Q7O4k9PYVBQYhBQYxFRYxKSo9NTZNPT5JSUpVWVpdZWZtaWpxdXZxeXp5gYJ9hYaBmZqNoaKVycqp9fbGAgLOFhbaGhraUlL6amsKensqfn8ukpM6rq9GxsdWystW3t9i9vdu+vtvCwt3GxuDIyODOCh/OCx/Q0ObVIzTVJDXWJjjWKDnaMULbNkfgP1DhGS3hGy/iHzPiITXiJDfiJzriRFTkLD/kL0HlQFDlTV3mUF/nTl7oTV3oVWTpV2bqXGvraHXsc3/td4PugYzu7u7xkpzx8fH29vb7+/v+/v7///8AWoy4AAAAlElEQVR4AW3EhZKCUABG4X+XDRtDUbG7O8BuRdT7/o+jzg2cge/MHIQiH3Ycwq201ZXZI5iqJV+p76uEeSBQj1dFJ+qyhj9RVnjKkTqv4KtESyLC3OGNFWSevKUOM7iLnrxoSW2GcP1nJZ5EmBv+ct8Z0ZRaDPD782WZUPM+TOLARFcf2+g9tLWRjdaBQRwYaDSdPAEdWz/BQNzGNgAAAABJRU5ErkJggg==);
}

.top-menu {
  height: 72px;
  background: #eff5f9;
  align-items: center;
  display: flex;
  position: relative;
  box-shadow: 0 2px 4px 0 #b0bcc4;
  @include media-breakpoint-down(md) {
    height: 75px;
  }
}
.header-logo {
  align-items: center;
  display: flex;
  height: 92px;
  background-color: #007b8e;
  @include media-breakpoint-down(md) {
    height: 65px;
  }
}

:host ::ng-deep .contact-us a {
  color: #f1f2f2;
}

:host ::ng-deep a:hover,
a:focus {
  text-decoration: none;
}

.btn {
  display: none;
}
.top-menu-item {
  justify-content: flex-end;
}
:host-context(.rtl) {
  .hiyou {
    border-right: 0.5px solid transparent;
  }
  .border-left {
    border-left: 0.5px solid #bbbaba;
  }
}

@media screen and (max-width: 760px) {
  .contact-us {
    display: none;
  }
  .top-row {
    height: 90px;
    flex-direction: column-reverse;
    .container {
      height: 45px;
      width: 100%;
    }
    .mobile-info {
      width: 27%;
      ::before {
        border-left: 1px solid grey;
      }
    }
  }
  .nav {
    span {
      padding: 5px 7px;
    }
  }
  .btn {
    background-color: #00b28d;
    font-size: 16px;
    display: inline-block;
    margin-left: auto;
    .rtl & {
      margin-right: auto;
      margin-left: 0;
    }
  }
  .flags {
    position: absolute;
    top: -65px;
  }
}
