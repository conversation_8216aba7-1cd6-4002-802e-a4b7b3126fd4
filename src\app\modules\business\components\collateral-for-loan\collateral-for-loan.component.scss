@import 'styles/vendors/bootstrap/share';
@import 'styles/variables';

.add-btn-wrap {
  padding: 25px 0;
}

.add-btn {
  cursor: pointer;
  height: 28px;
  width: 28px;
  border-radius: 50%;
  background: #00bec7;
  position: relative;
  display: block;

  &:after,
  &:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1px;
    height: 13px;
    background: #fff;
  }

  &:after {
    width: 13px;
    height: 1px;
  }

  &.disabled {
    cursor: not-allowed;
  }
}

.input-code {
  border: none;
  // outline: none;
  width: 127px;
  border-bottom: 1px solid #ccc;
  display: inline-block;
  vertical-align: bottom;
  padding: 1px 10px;
  @include media-breakpoint-up(md) {
    width: 147px;
  }

  &[disabled] {
    background: transparent;
  }
}

.loan-type-box {
  font-size: 16px;
  margin-bottom: 30px;
  color: #000;

  .type,
  span {
    display: inline-block;
    vertical-align: top;
    padding: 0 3px;
  }
}

:host ::ng-deep .heading-wrap {
  margin-bottom: 15px;

  .heading {
    margin: 0;
    display: inline-block;
    vertical-align: top;
    padding: 0 30px 0 0;
    position: relative;

    .rtl & {
      padding: 0 0 0 30px;
    }
  }
}

:host ::ng-deep .bz-info-tooltip {
  position: absolute;
  right: 0;
  top: 2px;

  .rtl & {
    right: auto;
    left: 0;
  }
}

.checkbox-list {
  margin: 0 -10px;

  .checkbox-row {
    margin-bottom: 20px;
    padding: 0 10px;
    display: inline-block;
    vertical-align: bottom;
  }
}

.content-row {
  max-width: 100%;
  margin: 0 0 30px;
}

:host ::ng-deep .switch-hold {
  margin-bottom: 20px;

  .bz-switch {
    display: inline-block;
    vertical-align: middle;
  }

  .label-switch {
    display: inline-block;
    vertical-align: middle;
    color: #000;
    font-size: 16px;
    padding: 0 10px 0 0;

    .rtl & {
      padding: 0 0 0 10px;
    }
  }
}

:host ::ng-deep .additional-liabilities {
  margin-bottom: 35px;

  .checkbox-row {
    margin-bottom: 15px;
  }

  .input-code {
    border-bottom-color: #737373;
  }
}

:host ::ng-deep .add-guarantor {
  .radio-box {
    display: inline-block;
    vertical-align: middle;
  }

  .heading {
    margin-bottom: 15px;
  }

  .add-guarantor-form-wrap {
    max-width: 350px;
  }

  .add-guarantor-form {
    font-size: 14px;
    margin-bottom: 30px;

    .radio-row {
      margin-bottom: 30px;
    }

    .form-group-wrap {
      margin: 0 -15px;
    }

    .label-holder {
      margin-bottom: 5px;
    }

    .form-group {
      padding: 0 15px;
    }
  }

  .btn-group-wrap {
    margin: 0 -5px;
    display: flex;
    flex-wrap: wrap;
  }

  .btn-col {
    padding: 0 5px;
    width: 50%;
  }

  .btn {
    padding: 7px;
    border-radius: 24px;
    font-size: 14px;
    width: 100%;
  }
}
