import { BalanceRate } from './balance-rate';
import { AdditionalBorrowers } from './additional-borrowers';

export interface LoanDetails {
  name: string;
  lenders: number;
  loanDetails: {
    unpaidPrincipal: number;
    monthlyPayment: number;
    rate: string;
    duration: number;
    paidPeriod: number;
  };
  generalInformation: {
    fundingDate: string;
    originatedOn: string;
    paidOn: number;
    annualInterest: number;
    annualInterestEffective: number;
    paymentMethod: string;
    fees: number;
    reason: string;
  };
  repaymentStatus: {
    currentStatus: string;
    nextDueDate: string;
    paidPercent: number;
    paidAmount: number;
    unpaidAmount: number;
  };
  additionalBorrowers: Array<AdditionalBorrowers>;
  balanceRate: Array<BalanceRate>;
}
