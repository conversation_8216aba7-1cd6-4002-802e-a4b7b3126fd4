import {
  Component,
  OnInit,
  Input,
  OnChanges,
  SimpleChanges,
  Output,
  EventEmitter,
  ViewChild,
  TemplateRef,
} from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { AccountDetailsService } from '../../services/account-details.service';
import { takeUntil } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';
import { Base } from '~tarya/modules/table-generator/shared/base';
import { Subject } from 'rxjs/internal/Subject';
import { template } from 'lodash';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-account-details-confirm-mobile-dialog',
  templateUrl: './account-details-confirm-mobile-dialog.component.html',
  styleUrls: ['./account-details-confirm-mobile-dialog.component.scss'],
})
export class AccountDetailsConfirmMobileDialogComponent
  extends Base
  implements OnInit, OnChanges
{
  @Input() display: boolean;
  @Input() phone: UntypedFormControl;
  @Input() firstName: UntypedFormControl;

  @Output() dialogClosed: EventEmitter<boolean> = new EventEmitter();
  @Output() successfulVerification: EventEmitter<boolean> = new EventEmitter();
  @ViewChild('mobileConfirmationDialog')
  mobileConfirmationDialog: TemplateRef<any>;

  form = this.accountDetailsService.generateMobileVerificationForm();

  verificationCode = '';
  errors = {
    verificationCode: {
      required: 'account.form-errors.required',
      invalidCode: 'account.form-errors.invalid-code',
    },
  };

  private isNewPhoneVerified: boolean;
  private newPhone: string;
  private mobileConfirmationDialogRef: MatDialogRef<any>;

  constructor(
    private accountDetailsService: AccountDetailsService,
    private translateService: TranslateService,
    private matDialog: MatDialog
  ) {
    super();
  }

  ngOnInit() {
    this.phone.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe((phoneNumber) => {
        if (this.isNewPhoneVerified) {
          this.successfulVerificationValue(false);
        }
        if (this.newPhone === phoneNumber) {
          //Next line hides verification button; see isPhoneVerificationNeeded() in parent component;
          this.phone.markAsPristine();

          this.successfulVerificationValue(true);
        }
        this.form.get('phone').setValue(phoneNumber);
      });
  }

  ngOnChanges({ display }: SimpleChanges): void {
    if (display && display.currentValue) {
      this.phoneConfirmationDialogState(true);
    }
  }

  sendVerificationCode() {
    this.accountDetailsService
      .sendPhoneVerificationCode(
        this.firstName.value,
        this.phone.value,
        this.translateService.currentLang.toUpperCase()
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe();
  }

  phoneConfirmationDialogState(state: boolean): void {
    if (state) {
      if (this.accountDetailsService.verifiedNewNumber === true) {
        this.accountDetailsService.verifiedNewNumber = undefined;
        this.successfulVerificationValue(false);
        this.newPhone = null;
      }
      this.sendVerificationCode();
      this.mobileConfirmationDialogRef = this.matDialog.open(
        this.mobileConfirmationDialog
      );
    } else {
      this.form.get('code').reset();
      if (this.accountDetailsService.verifiedNewNumber === undefined) {
        this.accountDetailsService.verifiedNewNumber = false;
      }
      this.phone.updateValueAndValidity();
    }

    this.dialogClosed.emit(state);
  }

  verifyMobileNumber() {
    this.accountDetailsService.verifiedNewNumber = undefined;
    this.newPhone = null;
    this.form.get('code').updateValueAndValidity();
    this.form.markAllAsTouched();

    this.accountDetailsService
      .verifyPhoneCode(this.form.value)
      .pipe(takeUntil(this.destroy$))
      .subscribe(({ valid }) => {
        if (valid) {
          this.accountDetailsService.verifiedNewNumber = true;
          this.accountDetailsService.verificationCode =
            this.form.get('code').value;
          this.newPhone = this.form.value.phone;
          this.mobileConfirmationDialogRef.close();
          //Next line hides verification button; see isPhoneVerificationNeeded() in parent component;
          this.phone.markAsPristine();

          this.successfulVerificationValue(true);
        } else {
          this.accountDetailsService.verifiedNewNumber = false;
        }
        this.form.get('code').updateValueAndValidity();
      });
  }

  private successfulVerificationValue(value: boolean): void {
    this.isNewPhoneVerified = value;
    this.successfulVerification.emit(value);
  }
}
