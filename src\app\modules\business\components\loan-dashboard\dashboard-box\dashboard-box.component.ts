import { Component, Input, OnInit } from '@angular/core';
import {
  LoanDashboardBoxData,
  LoansDashboardData,
} from '../loan-dashboard.component';
import { LOAN_PAGES } from '../../../layouts/loan/loan-pages';
import { Router, UrlTree } from '@angular/router';

@Component({
  selector: 'dashboard-box',
  templateUrl: './dashboard-box.component.html',
  styleUrls: ['./dashboard-box.component.scss'],
})
export class DashboardBoxComponent implements OnInit {
  @Input('loanDashboardData')
  loanDashboardData: LoansDashboardData;

  @Input('boxItem')
  boxItem: LoanDashboardBoxData;

  @Input() routerSidebar: boolean;

  pages: any = LOAN_PAGES;
  activePage: boolean;

  constructor(private router: Router) {}

  ngOnInit() {
    const urlTree: UrlTree = this.router.parseUrl(this.router.url);
    const urlSegment = urlTree.root.children.primary.segments;
    const last = urlSegment[urlSegment.length - 1];

    if (this.boxItem.routeLink === last.path) {
      this.activePage = true;
    }
  }
}
