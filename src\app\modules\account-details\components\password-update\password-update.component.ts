import { Component, OnInit } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { catchError, takeUntil } from 'rxjs/operators';
import { throwError, Subject, Observable } from 'rxjs';
import { Base } from '~tarya/modules/table-generator/shared/base';
import { AppNotificationsService } from '~tarya-shared/services/app-notifications.service';
import { MessageType } from '~tarya-shared/services/models/popup-message-type.enum';
import { PasswordUpdateService } from '../../../lender/services/password-update.service';
import { Router } from '@angular/router';
import { cloneDeep, isEqual } from 'lodash';
import { AppValidators } from '~tarya/modules/shared/validators/validators';
import { PasswordFormService } from '~tarya/modules/shared/services/generate-password-form.service';
import { InterpolateParams } from '~tarya/modules/shared/models/ngx-translate-params.interface';
import { PasswordFormScenarios } from '~tarya/modules/shared/enums/password-form-scenarios.enum';
import { PasswordFormControlNames } from '~tarya/modules/shared/enums/password-form-control-names.enum';
@Component({
  selector: 'app-password-update',
  templateUrl: './password-update.component.html',
  styleUrls: ['./password-update.component.scss'],
})
export class PasswordUpdateComponent extends Base implements OnInit {
  form: UntypedFormGroup;
  confirmationDialog$ = new Subject<boolean>();
  leaveWithoutSaving: boolean = false;
  initialValues: any;
  dashboardRoute: string;
  minMaxPasswordLength: InterpolateParams;
  passwordFormControlNames = PasswordFormControlNames;

  constructor(
    private passwordUpdateService: PasswordUpdateService,
    private appNotificationsService: AppNotificationsService,
    private router: Router,
    private passwordFormService: PasswordFormService
  ) {
    super();
  }

  ngOnInit() {
    this.form = this.passwordFormService.generatePasswordForm(
      PasswordFormScenarios.UPDATE_PASSWORD
    );
    this.minMaxPasswordLength = this.passwordFormService.minMaxPasswordLength;
    this.passwordFormService
      .checkForMoreThanOneErrValidationForPassword(
        this.form,
        PasswordFormControlNames.NEW
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe((validationErr) => {
        if (validationErr) {
          this.form.get(PasswordFormControlNames.NEW).setErrors(validationErr);
        }
      });
    this.dashboardRoute = localStorage.getItem('dashboardRoute');
    this.initialValues = cloneDeep(this.form.value);
  }

  update() {
    this.form.markAllAsTouched();

    if (this.form.valid) {
      this.passwordUpdateService
        .update({
          password: this.form.get(PasswordFormControlNames.OLD).value,
          newPassword: this.form.get(PasswordFormControlNames.NEW).value,
        })
        .pipe(
          catchError((data) => {
            const passwordError = data.error.fieldErrors.find(
              (error) => error.field === 'password'
            );
            if (passwordError) {
              this.form.get(PasswordFormControlNames.OLD).setErrors({
                invalidPassword: true,
              });
            } else {
              this.appNotificationsService.open({
                description: 'password-update.update-error',
                delay: 10000,
                type: MessageType.Error,
              });
            }
            return throwError(data);
          })
        )
        .subscribe(() => {
          this.appNotificationsService.open({
            description: 'password-update.update-success',
            delay: 10000,
            type: MessageType.Success,
          });
          this.form.reset(this.initialValues);

          Object.keys(this.form.controls).forEach((key) => {
            this.form.get(key).setErrors(null);
          });
          this.router.navigateByUrl(this.dashboardRoute);
        });
    }
  }

  cancelSave(cancelSave?: boolean): void {
    const currentDetails = cloneDeep(this.form.value);

    if (cancelSave) {
      this.leaveWithoutSaving = true;
      this.confirmationDialog$.next(false);
      this.router.navigateByUrl(this.dashboardRoute);
    } else if (cancelSave === false) {
      this.confirmationDialog$.next(false);
    } else if (!isEqual(this.initialValues, currentDetails)) {
      this.confirmationDialog$.next(true);
    } else {
      this.router.navigateByUrl(this.dashboardRoute);
    }
  }

  canDeactivate(): Observable<boolean> | boolean {
    const currentDetails = cloneDeep(this.form.value);

    if (
      !this.leaveWithoutSaving &&
      !isEqual(this.initialValues, currentDetails)
    ) {
      this.confirmationDialog$.next(true);
      return false;
    }
    return true;
  }
}
