import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { ManagerDetail } from '~tarya-agents/model/managerDetail';
import { AgentsDataService } from '~tarya-agents/services/agents-data.service';
import { AgentDetail } from '~tarya-agents/model/agentDetail';
import { TableConfig } from '~tarya-agents/model/tableConfig';

@Component({
  selector: 'app-agency',
  templateUrl: './agency.component.html',
  styleUrls: ['./agency.component.scss'],
})
export class AgencyComponent implements OnChanges {
  @Input() config: TableConfig;
  @Input() numberOfLenders: number;
  managerData: ManagerDetail;
  agentDetail: AgentDetail;
  loading: boolean;

  constructor(private agentService: AgentsDataService) {}

  ngOnChanges(changes: SimpleChanges) {
    if ('numberOfLenders' in changes) {
      this.numberOfLenders = changes.numberOfLenders.currentValue;
    }
    this.managerData = {
      ...this.managerData,
      numberOfLenders: this.numberOfLenders,
      agentSummaryList: [],
    };
    this.agentService.getManagerDetail().subscribe((data) => {
      this.managerData = {
        ...data,
        agentSummaryList: !!data.agentSummaryList
          ? data.agentSummaryList.map((item) => {
              const fullName = item.agentName.split(' ');
              return {
                ...item,
                agentFirstName: fullName[0],
                agentLastName: fullName[fullName.length - 1],
              };
            })
          : [],
      };
    });
  }

  selectedRowData(event: { agentId: string }) {
    this.loading = true;
    this.agentDetail = null;
    this.agentService.getManagerAgentDetail(event.agentId).subscribe((data) => {
      this.agentDetail = data;
      this.loading = false;
    });
  }
}
