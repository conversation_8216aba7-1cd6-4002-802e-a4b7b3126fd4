import { Component, OnInit, TemplateRef } from '@angular/core';
import { TemplateReferenceService } from '~tarya/modules/login/services/template-reference.service';

@Component({
  selector: 'app-account-details-layout',
  templateUrl: './account-details-layout.component.html',
  styleUrls: ['./account-details-layout.component.scss'],
})
export class AccountDetailsLayoutComponent {
  estimateTemplate: TemplateRef<any>;
  getTemplate$ = this.templateRef.change$;

  constructor(private templateRef: TemplateReferenceService) {}
}
