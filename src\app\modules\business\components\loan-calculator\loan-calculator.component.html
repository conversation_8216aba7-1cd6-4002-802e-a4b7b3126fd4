<div class="visual-options-box">
  <div class="title">
    <strong>מחשבון הלוואה</strong>
  </div>
  <form action="#" class="calculate-form" [formGroup]="loanCalculatorForm">
    <div class="form-group">
      <div class="label-holder">
        <label>אני מעוניין ללוות</label>
      </div>
      <div class="control-holder">
        <div class="control-wrap">
          <span class="currency">₪</span>
          <p-spinner
            class="bz-spinner"
            [max]="initialRegistrationParams.maxLoanAmount"
            [step]="initialRegistrationParams.stepLoanAmount"
            [min]="0"
            (onBlur)="setMinLoanAmount()"
            (onChange)="setMonthlyPayment()"
            formControlName="amount"
          >
          </p-spinner>
        </div>
      </div>
    </div>
    <div class="form-group">
      <div class="label-holder">
        <label>אני אהיה מסוגל להחזיר כל חודש</label>
      </div>
      <div class="control-holder">
        <div class="control-wrap">
          <span class="currency">₪</span>
          <div class="bz-spinner">
            <div class="p-spinner">
              <input
                type="text"
                class="form-control monthly-payment"
                [options]="{
                  prefix: '',
                  thousands: ',',
                  decimal: '.',
                  precision: 2,
                  align: 'center'
                }"
                currencyMask
                formControlName="monthlyPayment"
                readonly
              />
              <button
                type="button"
                class="p-spinner-button p-spinner-up"
                (click)="incrementMonthPayment()"
              ></button>
              <button
                type="button"
                class="p-spinner-button p-spinner-down"
                (click)="decrementMonthPayment()"
              ></button>
            </div>
          </div>
        </div>
        <p-slider
          class="bz-slider"
          [min]="0"
          [max]="
            initialRegistrationParams.maxPeriod -
            initialRegistrationParams.minPeriod
          "
          [step]="initialRegistrationParams.stepPeriod"
          [style]="{ width: '100%' }"
          (onChange)="monthlyPaymentSliderChange($event)"
          formControlName="monthlyPaymentSlider"
        >
        </p-slider>
      </div>
    </div>
    <div class="form-group">
      <div class="label-holder">
        <label>לפרק זמן של</label>
      </div>
      <div class="control-holder">
        <div class="control-wrap period-control">
          <span class="month-label">חודשים</span>
          <p-spinner
            class="bz-spinner"
            [max]="initialRegistrationParams.maxPeriod"
            [step]="initialRegistrationParams.stepPeriod"
            [min]="minLoanDuration"
            (onBlur)="setMinPeriod()"
            (onChange)="setMonthlyPayment()"
            formControlName="period"
          >
          </p-spinner>
        </div>
      </div>
    </div>
    <div class="form-group">
      <button
        type="button"
        (submit)="onSubmit($event)"
        class="btn btn-primary"
        [disabled]="!loanCalculatorForm.valid"
        [routerLink]="['/register']"
      >
        המשך לקבלת תנאים
      </button>
    </div>
  </form>
</div>
