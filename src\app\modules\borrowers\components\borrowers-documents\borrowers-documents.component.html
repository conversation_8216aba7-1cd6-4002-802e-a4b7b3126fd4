<div
  class="documents"
  matSort
  [matSortDisableClear]="true"
  (matSortChange)="sortData($event)"
>
  <div
    class="d-md-flex align-items-center justify-content-between px-3 px-sm-0 mb-1"
  >
    <h2 class="documents-header mb-md-0">
      {{ 'documents.header' | translate }}
    </h2>
    <form [formGroup]="filterForm">
      <div class="mb-3 d-flex position-relative">
        <div
          class="documents-search d-flex align-items-center position-relative mx-md-2"
        >
          <label for="documents-search" class="mb-1 mx-1">
            <img
              src="/assets/images/borrowers/search-icon.png"
              alt="search-icon"
              width="18"
            />
          </label>
          <input
            formControlName="searchInput"
            label="'documents.header'"
            type="text"
            class="documents-search-input"
            name="documents-search"
            id="documents-search"
            required
          />
          <span class="documents-search-label">{{
            'documents.search' | translate
          }}</span>
        </div>
        <button
          type="button"
          cdkOverlayOrigin
          #trigger="cdkOverlayOrigin"
          #filterBtn
          (click)="isFilterMenuOpened = !isFilterMenuOpened"
          class="documents-filter-btn btn btn-tar-outline-primary mx-1 ms-3 ms-md-1"
        >
          <i class="fas fa-filter fa-lg" aria-hidden="true"></i>
          {{ 'documents.filter-options.filter' | translate }}
        </button>
        <ng-template
          #filterOverlay
          cdkConnectedOverlay
          [cdkConnectedOverlayOrigin]="trigger"
          [cdkConnectedOverlayOpen]="isFilterMenuOpened"
          [cdkConnectedOverlayHasBackdrop]="true"
          (backdropClick)="isFilterMenuOpened = false"
        >
          <div #filterMenu class="documents-filter p-4">
            <div class="d-flex align-items-baseline mb-3">
              <span class="me-auto font-weight-500 fz-17">{{
                'documents.filter-options.header' | translate
              }}</span>
              <button (click)="resetForm()" class="btn btn-tar-outline-primary">
                {{ 'documents.filter-options.reset' | translate }}
              </button>
            </div>
            <app-tar-select
              #actionTypeSelect
              [attr.data-qa]="'actionType'"
              [label]="'documents.filter-options.action-type'"
              [options]="actionTypes"
              [multiple]="false"
              [panelClass]="'select-panel'"
              formControlName="actionType"
            >
            </app-tar-select>
            <mat-form-field appearance="fill" class="w-100">
              <mat-label>{{
                'documents.filter-options.start-date' | translate
              }}</mat-label>
              <input
                matInput
                [matDatepicker]="startDatePicker"
                formControlName="startDate"
              />
              <mat-datepicker-toggle
                matSuffix
                [for]="startDatePicker"
              ></mat-datepicker-toggle>
              <mat-datepicker #startDatePicker></mat-datepicker>
            </mat-form-field>
            <mat-form-field appearance="fill" class="w-100">
              <mat-label>{{
                'documents.filter-options.end-date' | translate
              }}</mat-label>
              <input
                matInput
                [matDatepicker]="endDatePicker"
                formControlName="endDate"
              />
              <mat-datepicker-toggle
                matSuffix
                [for]="endDatePicker"
              ></mat-datepicker-toggle>
              <mat-datepicker #endDatePicker></mat-datepicker>
            </mat-form-field>
          </div>
        </ng-template>
      </div>
    </form>
  </div>
  <div class="documents-table-header py-1 pt-2 fz-13 text-uppercase fw-bold">
    <div class="row">
      <div class="col-3 d-none d-md-block">
        {{ 'documents.table.header.type' | translate }}
      </div>
      <div class="col-4 col-md-3">
        {{ 'documents.table.header.topic' | translate }}
      </div>
      <div mat-sort-header="loanId" class="col-3 col-md-2">
        {{ 'documents.table.header.loan-id' | translate }}
      </div>
      <div mat-sort-header="date" class="col-3 col-md-2 p-0">
        {{ 'documents.table.header.date' | translate }}
      </div>
    </div>
  </div>
  <p-accordion #accordion *ngIf="sortedDocumentsAfterPagination">
    <p-accordionTab
      [disabled]="true"
      *ngFor="
        let document of sortedDocumentsAfterPagination.data;
        let i = index
      "
      [selected]="this.reviewedDocIndex === i"
    >
      <p-header class="w-100">
        <div
          class="document-header"
          [ngClass]="{
            'not-read': !document.isFileRead
          }"
        >
          <div class="row align-items-center">
            <div class="d-none d-md-block col-md-3 fw-bold">
              <img
                src="assets/images/history-reports/pdf-icon.svg"
                width="24"
                alt="pdf-icon"
                class="pe-4"
              />
              <span class="d-none d-sm-inline">
                {{
                  'documents.table.content.fileType.' + document.fileType
                    | translate
                }}
              </span>
            </div>
            <div class="col-4 col-md-3">
              {{
                'documents.table.content.fileTopic.' + document.fileTopic
                  | translate
              }}
            </div>
            <div class="col-3 col-md-2">{{ document.externalLoanId }}</div>
            <div class="col-3 col-md-2 p-0">
              {{ document.createDate | date: 'dd-MM-yyyy' }}
            </div>
            <div class="col-2">
              <button
                (click)="setLetterPdf(document.fileHashId, i)"
                class="btn btn-tar-outline-primary w-100 mb-2"
              >
                <span class="d-none d-md-inline">
                  {{
                    (this.reviewedDocIndex === i
                      ? 'documents.table.buttons.hide'
                      : 'documents.table.buttons.preview'
                    ) | translate
                  }}
                </span>
                <i
                  class="fas d-md-none"
                  [ngClass]="
                    this.reviewedDocIndex === i ? 'fa-eye-slash' : 'fa-eye'
                  "
                  aria-hidden="true"
                ></i>
              </button>
              <button
                (click)="downloadDocPdf(document.fileHashId, document.fileType)"
                class="btn btn-tar-outline-primary w-100"
              >
                <span class="d-none d-md-inline">
                  {{ 'documents.table.buttons.download' | translate }}
                </span>
                <!-- <i class="fa fa-download d-sm-none" aria-hidden="true"></i> -->
                <img
                  class="d-md-none"
                  src="assets/images/borrowers/download-file.png"
                  alt="download-file-icon"
                />
              </button>
            </div>
          </div>
        </div>
      </p-header>
      <p-content>
        <iframe
          *ngIf="letter"
          [src]="letter"
          class="w-100 pdf-file-box"
          title="file box"
        ></iframe>
      </p-content>
    </p-accordionTab>
  </p-accordion>
  <mat-paginator
    #paginator
    [length]="documents?.length"
    [pageSize]="rowsPerPage"
    aria-label="Select page"
    hidePageSize="true"
    class="bg-transparent"
  >
  </mat-paginator>
</div>
