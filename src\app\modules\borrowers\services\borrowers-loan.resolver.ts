import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import { LoanDetails } from '../models/loan-details';
import { BorrowersService } from '../services/borrowers.service';

@Injectable({ providedIn: 'root' })
export class BorrowersLoanResolver implements Resolve<LoanDetails> {
  constructor(
    private borrowersService: BorrowersService,
    private translate: TranslateService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<LoanDetails> {
    const language = this.translate.currentLang.toUpperCase();
    return this.borrowersService.getLoanDetails(route.params.id, language);
  }
}
