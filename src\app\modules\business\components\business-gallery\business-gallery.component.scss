@import 'styles/vendors/bootstrap/share';
@import 'styles/variables';

.bz-gallery-options {
  padding: 20px 10px;

  @include media-breakpoint-up(sm) {
    display: flex;
    padding: 20px 10px 70px;
    align-items: center;
    justify-content: space-between;
  }

  .btn-holder {
    padding: 5px;
  }

  .control-wrap {
    display: inline-block;
    vertical-align: middle;
    padding: 5px;
  }

  .filter-control {
    font-size: 12px;
    .btn-search {
      color: #737373;
    }

    .filter-search {
      height: 45px;
    }
  }
}

.gallery-holder {
  width: 100%;
  position: relative;
  @include media-breakpoint-up(md) {
    display: flex;
    flex-wrap: wrap;
  }

  .no-results {
    display: none;
    opacity: 0.8;

    &:only-child {
      display: block;
      width: 100%;
      text-align: center;
      padding: 40px 10px;
    }
  }
}

.bz-gallery-box {
  padding: 0 15px;
  margin-bottom: 30px;
  display: flex;
  flex-wrap: wrap;
  min-height: 335px;
  @include media-breakpoint-up(md) {
    width: 50%;
    margin-bottom: 44px;
  }
  @include media-breakpoint-up(lg) {
    width: 33.33%;
  }
}
