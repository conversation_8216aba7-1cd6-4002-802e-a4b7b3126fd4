import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { Observable } from 'rxjs';
import { LoanList } from '../models/loan-list';
import { BorrowersService } from './borrowers.service';

@Injectable({
  providedIn: 'root',
})
export class OrganizationWithLoansResolver implements Resolve<LoanList> {
  constructor(private borrowersService: BorrowersService) {}

  resolve(): Observable<LoanList> {
    return this.borrowersService.getAllLoans(
      localStorage.getItem('organization_id')
    );
  }
}
