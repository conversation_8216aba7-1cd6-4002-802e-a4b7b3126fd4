import { formatDate } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Inject, Injectable, LOCALE_ID } from '@angular/core';
import {
  AbstractControl,
  UntypedFormBuilder,
  UntypedFormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { SelectItem } from 'primeng/api';
import { from, Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { AppNotificationsService } from '~tarya-shared/services/app-notifications.service';
import { MessageType } from '~tarya-shared/services/models/popup-message-type.enum';
import { AutoCompleteService } from '~tarya/modules/shared/services/autocomplete-service';
import { AccountDetails } from '../../lender/models/account-details';
import { Country } from '../../lender/models/country';
import { Lender } from '~tarya/modules/lender/models/lender';

const CUSTOMER_ACCOUNT_PATH = '/rest/api/my/customer/account';
const PHONE_VERIFICATION_PATH = '/rest/api/phones/verification-codes';
@Injectable({
  providedIn: 'root',
})
export class AccountDetailsService {
  verificationCode: string;

  private initialPhone: string;
  private verifiedNumber: boolean;

  constructor(
    private fb: UntypedFormBuilder,
    private http: HttpClient,
    private appNotificationsService: AppNotificationsService,
    private autoCompleteService: AutoCompleteService,
    @Inject(LOCALE_ID) private localeId: string
  ) {}

  set verifiedNewNumber(status: boolean) {
    this.verifiedNumber = status;
  }

  get verifiedNewNumber(): boolean {
    return this.verifiedNumber;
  }

  set initialMobilePhone(phone: string) {
    this.initialPhone = phone;
  }

  get initialMobilePhone(): string {
    return this.initialPhone;
  }

  getCountries(lang: string): Observable<SelectItem[]> {
    lang = `${lang.charAt(0).toUpperCase()}${lang.slice(1)}`;
    return this.http.post('/rest/api/countries/list', {}).pipe(
      map((countries: Country[]) => {
        return countries.map((country) => ({
          label: country[`name${lang}`],
          value: country.alpha2,
        }));
      })
    );
  }

  getAccountData(): Observable<AccountDetails> {
    return this.http
      .post<AccountDetails>(`${CUSTOMER_ACCOUNT_PATH}/read`, {})
      .pipe(catchError((error) => of(error)));
  }

  updateAccountData(data: AccountDetails): Observable<AccountDetails> {
    return this.http.post<AccountDetails>(
      `${CUSTOMER_ACCOUNT_PATH}/update`,
      data
    );
  }

  sendPhoneVerificationCode(
    firstName: string,
    phone: string,
    language: string
  ): Observable<unknown> {
    return this.http.post<unknown>(`${PHONE_VERIFICATION_PATH}/create`, {
      firstName,
      phone,
      language,
    });
  }

  verifyPhoneCode(data: { phone: string; code: string }): Observable<unknown> {
    return this.http.post<unknown>(`${PHONE_VERIFICATION_PATH}/validate`, data);
  }

  loadCities(): Observable<SelectItem[]> {
    return from(this.autoCompleteService.getCities()).pipe(
      map((cities) => cities.map(({ name }) => ({ value: name, label: name })))
    );
  }

  getStreets(city: string): Observable<SelectItem[]> {
    return from(this.autoCompleteService.getStreets(city)).pipe(
      map((streets) =>
        streets.map(({ name }) => ({ value: name, label: name }))
      )
    );
  }

  showToast(
    translation: string,
    type: MessageType = MessageType.Success
  ): void {
    this.appNotificationsService.open({
      description: translation,
      delay: 10000,
      type,
    });
  }

  generateForm(): UntypedFormGroup {
    const emptyDisabledControl = {
      value: '',
      disabled: true,
    };

    return this.fb.group({
      firstName: emptyDisabledControl,
      lastName: emptyDisabledControl,
      dateOfBirth: emptyDisabledControl,
      socialId: emptyDisabledControl,
      address: this.fb.group({
        country: ['', Validators.required],
        city: ['', Validators.required],
        streetName: ['', Validators.required],
        streetNumber: ['', Validators.required],
        postalCode: '',
      }),
      email: emptyDisabledControl,
      telephone: ['', this.isPhoneValid()],
      mobilePhone: [
        '',
        [Validators.required, this.isPhoneValid(), this.isPhoneVerified()],
      ],
      preferredLanguage: ['', Validators.required],
      taxInvoiceRecipientName: ['', Validators.required],
    });
  }

  generateMobileVerificationForm(): UntypedFormGroup {
    return this.fb.group({
      phone: '',
      code: ['', [Validators.required, this.isValidCode()]],
    });
  }

  fillAccountDetailsForm(
    form: UntypedFormGroup,
    {
      firstName,
      lastName,
      dateOfBirth,
      socialId,
      address,
      email,
      telephone,
      mobilePhone,
      preferredLanguage,
      taxInvoiceRecipientName,
    }: AccountDetails,
    { isCorporateLender, companyName }: Lender
  ): void {
    const invoiceName = isCorporateLender
      ? companyName
      : `${firstName} ${lastName}`;
    form.setValue({
      firstName,
      lastName,
      dateOfBirth: formatDate(dateOfBirth, 'dd.MM.yyyy', this.localeId),
      socialId,
      address: {
        country: address.country,
        city: address.city,
        streetName: address.streetName,
        streetNumber: address.streetNumber,
        postalCode: address.postalCode,
      },
      email,
      telephone,
      mobilePhone,
      preferredLanguage,
      taxInvoiceRecipientName: taxInvoiceRecipientName
        ? taxInvoiceRecipientName
        : invoiceName,
    });
  }

  private isPhoneVerified(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (
        control.value !== this.initialMobilePhone &&
        this.verifiedNewNumber === false
      ) {
        return { phoneVerificationError: true };
      }
      return null;
    };
  }

  private isPhoneValid(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (
        !control.value.match(/^05\d([-]{0,1})\d{7}$/) &&
        control.value !== ''
      ) {
        return { invalidPhone: true };
      }

      return null;
    };
  }

  private isValidCode(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (control.value !== '' && this.verifiedNewNumber === false) {
        return { invalidCode: true };
      }
      return null;
    };
  }
}
