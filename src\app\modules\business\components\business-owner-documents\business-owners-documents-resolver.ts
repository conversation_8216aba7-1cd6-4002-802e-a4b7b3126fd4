import {
  ActivatedRouteSnapshot,
  Resolve,
  RouterStateSnapshot,
} from '@angular/router';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { RequiredDocumentResponse } from '../../model/required-documents-response';
import { BusinessService } from '../../service/business.service';
import { RequiredDocumentsModel } from '../../model/required-document.model';

@Injectable()
export class BusinessOwnersRequiredDocumentsResolver
  implements
    Resolve<Map<number, RequiredDocumentResponse | RequiredDocumentsModel[]>> {
  constructor(private businessService: BusinessService) {}

  resolve(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ):
    | Observable<
        Map<number, RequiredDocumentResponse | RequiredDocumentsModel[]>
      >
    | Promise<Map<number, RequiredDocumentResponse | RequiredDocumentsModel[]>>
    | Map<number, RequiredDocumentResponse | RequiredDocumentsModel[]> {
    return this.businessService.getRequiredBusinessOwnerDocuments(
      route.parent.parent.data.loanRequest.business.id
    );
  }
}
