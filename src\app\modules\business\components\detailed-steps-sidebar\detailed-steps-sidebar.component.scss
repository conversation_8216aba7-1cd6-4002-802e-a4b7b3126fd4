@import 'styles/vendors/bootstrap/share';
@import 'styles/variables';

.btn-holder {
  margin: 0 20px 30px 0;
}

:host ::ng-deep .btn-back {
  display: inline-block;
  height: 40px;
  width: 110%;
  border: 1px solid #dedede;
  background: #fff;
  color: #737373;
  position: relative;
  text-align: center;
  padding-top: 7px;

  &:after {
    content: '';
    position: absolute;
    top: 50%;
    width: 10px;
    height: 10px;
    margin: 0 2px 0 2px;
    right: 10%;
    border-top: 1px solid #969696;
    border-left: 1px solid #969696;
    transform: translate(-50%, -50%) rotate(135deg);

    .rtl & {
      margin: 0 0 0 -2px;
      right: 87%;
      transform: translate(-50%, -50%) rotate(-45deg);
    }
  }
}

:host-context(.rtl) {
  .btn-holder {
    margin: 0 0 30px 20px;
  }
}

@media only screen and (max-width: 700px) {
  :host ::ng-deep .btn-back {
    width: 100%;
    &::after {
      right: 2%;
      .rtl & {
        right: 90%;
      }
    }
  }
  .btn-holder {
    margin: 0 0 20px 0 !important;
  }
}
