import { Component, OnInit, Renderer2, Inject } from '@angular/core';
import { DomainService } from '~tarya/layouts/app-component/domain.service';
import { DOCUMENT } from '@angular/common';

@Component({
  selector: 'body',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit {
  domainId: string;
  homePage: string;
  cloudCssStylesheetLink: HTMLElement;

  constructor(private domainService: DomainService) {}

  ngOnInit() {
    this.getDomainData();
  }

  getDomainData() {
    this.domainService.getDomainData().subscribe((response) => {
      this.domainService.domainData = response;
      this.domainId = response.domainSlug;
      this.homePage = response.homePage;
      if (response.communityName.toLowerCase() === 'ofek') {
        this.domainService.loadDomainCss(response.communityName);
      }
    });
  }
}
