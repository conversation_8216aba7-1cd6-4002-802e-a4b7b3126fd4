<div class="upload-box">
  <div class="upload-inner">
    <div class="upload-col">
      <div class="upload-content">
        <div class="info-box">
          <span class="bz-info-tooltip"
            ><i class="icon-info" aria-hidden="true"></i
          ></span>
        </div>
        <div class="content-wrap">
          <h5>
            {{ fileUploadInfo.fileTitle
            }}<span
              class="required-document"
              *ngIf="!fileUploadInfo.isDocOptional"
            >
              *
            </span>
          </h5>
          <p>
            {{ 'loan-request.files.' + fileUploadInfo.fileStatus | translate }}
          </p>
        </div>
      </div>
    </div>
    <div class="upload-col">
      <ul class="upload-options list-unstyled">
        <li>
          <i class="icon icon-share-document d-none" aria-hidden="true"></i>
        </li>
        <li>
          <p-fileUpload
            class="bz-tarya-fileupload"
            name="file"
            accept=".pdf"
            [url]="fileUploadInfo.uploadUrl"
            [maxFileSize]="maxSize"
            [chooseLabel]="btnLabel"
            invalidFileSizeMessageDetail="{{ 'error.FILE_TO_BIG' | translate }}"
            invalidFileTypeMessageDetail="{{
              'error.WRONG_FILE_FORMAT' | translate
            }}"
            (onUpload)="onUpload()"
            (onError)="onError()"
            (onBeforeUpload)="onBeforeUpload($event)"
            auto="auto"
          >
          </p-fileUpload>
        </li>
      </ul>
    </div>
  </div>
  <div
    class="radio-wrap"
    [formGroup]="fileUploadInfo.uploadFileForm"
    class="d-none"
  >
    <p-radioButton
      class="bz-tarya-radio"
      name="uploadFile"
      [value]="fileUploadInfo.typeEnumModelId"
      [(ngModel)]="selectedValue"
      id="uploadFileItem.fileId"
      formControlName="uploadFileRadio"
    ></p-radioButton>
  </div>
</div>
