import { Component, OnInit } from '@angular/core';
import { LoanRequestService } from '../../service/loan-request.service';
import { LoanRequest } from '../../model/loan-request';
import { BusinessService } from '../../service/business.service';
import { Router } from '@angular/router';
import { LOAN_ROUTE } from '../../layouts/loan/loan-pages';
import {
  AbstractControl,
  UntypedFormBuilder,
  UntypedFormGroup,
} from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { LoanRequestStatus } from '../../enums/loan-request-status.enum';
import { LoginService } from '~tarya/modules/core/services/login.service';
import { EMPTY, Observable, of } from 'rxjs';
import { User } from '~tarya/modules/core/models/user';
import { Base } from '~tarya/modules/table-generator/shared/base';
import { switchMap, takeUntil, tap } from 'rxjs/operators';

export class LoanInfoData {
  businessLogoResource?: string;
  businessName?: string;
  loanName?: string;
  loanNumber?: number;
  loanAmount?: number;
  loanPeriod?: any;
  loanStatus?: LoanRequestStatus;
  loanDescription?: string;
  additionalInfo?: string;
  loanProgress?: number;
}

@Component({
  selector: 'app-loans-gallery',
  templateUrl: './loans-gallery.component.html',
  styleUrls: ['./loans-gallery.component.scss'],
})
export class LoansGalleryComponent extends Base implements OnInit {
  searchText: any;

  loanOptionsForm: UntypedFormGroup;

  loanInfoData: LoanInfoData[];
  loanRequests: LoanRequest[];

  newLoanPath: string;
  newLoanName$: Observable<User>;
  newLoanInfo: string;

  constructor(
    private loanRequestService: LoanRequestService,
    private businessService: BusinessService,
    private router: Router,
    private fb: UntypedFormBuilder,
    private translateService: TranslateService,
    private loginService: LoginService
  ) {
    super();
  }

  ngOnInit() {
    this.createForm();
    this.newLoanPath = '/loans/new';

    this.businessService
      .getAllBusinessesBe()
      .pipe(
        switchMap((business) => {
          if (business.length === 0) {
            this.newLoanName$ = this.loginService.observableUserDto;
            return of(EMPTY);
          }
          return this.loanRequestService.getAllLoanRequestsBe().pipe(
            tap((loans) => {
              this.loanRequests = loans;
              this.getLoanInfo();
            })
          );
        })
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe();

    this.translateService.get('new-loan-text').subscribe((result) => {
      this.newLoanInfo = result;
    });
  }

  createForm() {
    this.loanOptionsForm = this.fb.group({
      filterSearch: [null],
    });
  }

  searchFilter(control: AbstractControl) {
    this.searchText = control.value;
  }

  redirectToLoan(event: LoanInfoData) {
    this.router.navigate([LOAN_ROUTE + '/' + event.loanNumber], {
      relativeTo: this.router.routerState.root,
    });
  }

  getLoanInfo() {
    this.loanRequests.forEach((loanRequest: LoanRequest) => {
      loanRequest.progress =
        this.loanRequestService.getLoanProgress(loanRequest);

      this.loanInfoData = [];
      this.loanRequestService
        .getDocumentsProgress(loanRequest.id)
        .pipe(takeUntil(this.destroy$))
        .subscribe((docsProgress) => {
          loanRequest.progress =
            this.loanRequestService.getLoanProgress(loanRequest);
          loanRequest.progress.docsProgress = docsProgress;
          this.loanInfoData.push({
            businessLogoResource: loanRequest.business.logoExists
              ? this.businessService.getBusinessLogoResource(
                  loanRequest.business.id
                )
              : null,
            loanName: loanRequest.name,
            businessName: loanRequest.business.name,
            loanNumber: loanRequest.id,
            loanAmount: loanRequest.loan
              ? loanRequest.loan.amount
              : loanRequest.amount,
            loanPeriod: loanRequest.loan
              ? loanRequest.loan.period
              : loanRequest.period,
            loanStatus: loanRequest.status,
            loanDescription: loanRequest.purposeFreeText,
            loanProgress:
              this.loanRequestService.calculateAndGetLoanProgressValue(
                loanRequest
              ),
          });
        });
    });
  }
}
