<div class="white-box-wrap">
    <div class="col-12 col-md-6 no-padding">
        <form action="#" [formGroup]="newLoanBusinessDetailsForm">
            <div class="heading-secondary">
                <h3>פרטי העסק</h3>
            </div>
            <div class="form-group-wrap">
                <div class="form-group">
                    <div class="radio-row">
                        <div class="radio-box">
                            <p-radioButton class="bz-tarya-radio" name="businessState" value="exist" label="עסק קיים במערכת" formControlName="businessState"></p-radioButton>
                        </div>
                        <div class="radio-box">
                            <p-radioButton class="bz-tarya-radio" [(ngModel)]="checked" binary="true" name="businessState" value="new" label=" עסק חדש" formControlName="businessState"></p-radioButton>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group-wrap" *ngIf="newLoanBusinessDetailsForm.get('businessState').value === 'exist'">
                <div class="form-group">
                    <div class="input-holder">
                        <div class="bz-select-holder">
                            <p-dropdown [options]="businessData"  [style]="{'width':'100%'}"
                                        formControlName="businessSelect"
                            ></p-dropdown>
                        </div>
                    </div>
                </div>
            </div>
            <div class="btn-holder">
                <button type="button" class="btn btn-primary" [disabled]="!newLoanBusinessDetailsForm.valid" (click)="submit()">המשך</button>
            </div>
        </form>
    </div>
</div>