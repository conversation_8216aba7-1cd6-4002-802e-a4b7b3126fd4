import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { CreditCardDetails } from '../models/credit-card-details';
import { ExpiredCreditCardsServiceResponse } from '../models/expire-credit-cards-service-response';

@Injectable({
  providedIn: 'root',
})
export class BorrowersCreditCardService {
  private expiredCreditCardsApi =
    '/rest/api/borrowers/dashboard/credit-card/expired-creditcard/list';
  private getCreditCardIframeApi =
    '/rest/api/borrowers/dashboard/credit-card/iframe/create';
  private onUpdateSuccessApi =
    '/rest/api/borrowers/dashboard/credit-card/onsuccess';
  private getCreditCardIframeForPaymentApi =
    '/rest/api/borrowers/dashboard/credit-card/self-service/iframe/';

  constructor(private http: HttpClient) {}

  getExpiredCreditCards(): Observable<ExpiredCreditCardsServiceResponse> {
    return this.http.post<ExpiredCreditCardsServiceResponse>(
      this.expiredCreditCardsApi,
      {}
    );
  }

  getCreditCardIframe(details: CreditCardDetails): Observable<{ url: string }> {
    return this.http.post<{ url: string }>(
      this.getCreditCardIframeApi,
      details
    );
  }

  onUpdateSuccess(loanId: number | string): Observable<void> {
    return this.http.post<void>(
      this.onUpdateSuccessApi + `?loanId=${loanId}`,
      {}
    );
  }

  getCreditCardIframeForPayment(
    details: CreditCardDetails
  ): Observable<{ url: string }> {
    return this.http.post<{ url: string }>(
      this.getCreditCardIframeForPaymentApi,
      details
    );
  }
}
