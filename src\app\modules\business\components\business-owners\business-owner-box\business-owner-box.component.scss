@import 'styles/vendors/bootstrap/share';
@import 'styles/variables';

:host ::ng-deep .owner-box-wrap {
  position: relative;

  & + .owner-box-wrap {
    border-top: 1px solid #d8d8d8;
  }

  &:only-child {
    border-bottom: 1px solid #d8d8d8;
  }

  .owner-box-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 0;
    @include media-breakpoint-up(md) {
      padding: 35px 0;
    }
  }

  .dlt-owner {
    padding: 0 10px;
    font-size: 18px;
    line-height: 1;
    color: #7d7d7d;
    @include media-breakpoint-up(md) {
      padding: 0 25px;
    }

    i {
      padding: 3px;
      cursor: pointer;
      display: inline-block;
      vertical-align: top;
    }
  }

  .owner-box-content {
    font-size: 15px;
    @include media-breakpoint-up(md) {
      padding: 0 10px;
    }

    span {
      display: block;
    }

    strong {
      margin-bottom: 10px;
      display: block;
      font-size: 16px;
    }
  }
}
