import { ActivatedRouteSnapshot } from '@angular/router';
import { getSelectors, RouterReducerState } from '@ngrx/router-store';
import { createFeatureSelector, createSelector } from '@ngrx/store';

export const selectRouter = createFeatureSelector<RouterReducerState>('router');

const getRouteParams = (route: ActivatedRouteSnapshot) => {
  if (route.children.length === 0) {
    return route.params;
  }

  const combinedChildParams = route.children.reduce(
    (prev, childRoute) => ({ ...prev, ...getRouteParams(childRoute) }),
    {}
  );

  return {
    ...route.params,
    ...combinedChildParams,
  };
};

// This will get and merge all router params from the parent routers
export const selectRouterParams = createSelector(
  selectRouter,
  (routerState) => {
    if (!routerState?.state?.root) {
      return {};
    }

    return getRouteParams(routerState.state.root);
  }
);

export const {
  selectCurrentRoute, // select the current route
  selectFragment, // select the current route fragment
  selectQueryParams, // select the current route query params
  selectQueryParam, // factory function to select a query param
  selectRouteParams, // select the current route params
  selectRouteParam, // factory function to select a route param
  selectRouteData, // select the current route data
  selectUrl, // select the current url
} = getSelectors(selectRouter);
