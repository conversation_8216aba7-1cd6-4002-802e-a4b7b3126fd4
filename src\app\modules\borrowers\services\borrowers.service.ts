import { HttpClient, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, ReplaySubject, Subject } from 'rxjs';
import { ActiveLoansServiceResponse } from '../models/active-loans-service-response';
import { LoanDetails } from '../models/loan-details';
import { LoanList } from '../models/loan-list';
import { PaymentMethods } from '../models/payment-methods';
import { LoanRequest } from '../models/loan-request';
import { MenuNavigationResponse } from '../models/menu-navigation';
import { SidebarInfo } from '../models/sidebar-info';
import { AvailablePages } from '../models/available-pages';
import { WarningLetter } from '../models/warning-letter';
import { GetFileBody } from '../models/get-file-body';

@Injectable({
  providedIn: 'root',
})
export class BorrowersService {
  loansForSidebar = new ReplaySubject<SidebarInfo>(null);
  onLoanChange$ = this.loansForSidebar.asObservable();
  allDocuments = new ReplaySubject<WarningLetter[]>(null);
  onAllDocumentsChange$ = this.allDocuments.asObservable();

  deleteOrContinueRequest$: Subject<{
    loan: LoanRequest;
    action: string;
  }> = new Subject();
  selectedLoanId: BehaviorSubject<number> = new BehaviorSubject(null);
  onSelectedLoanIdChange$ = this.selectedLoanId.asObservable();
  private loansInfoApi = '/rest/api/borrowers/dashboard/summary/read';
  private loanDetailsApi = '/rest/api/borrowers/dashboard/loan/read';
  private loanListApi = '/rest/api/borrowers/dashboard/organization/read';
  private loanNavigationApi = '/rest/api/borrowers/dashboard/menu/list';
  private deleteLoanRequestApi = '/rest/api/borrowers/dashboard/request/cancel';
  private getPaymentMethodsApi =
    '/rest/api/borrowers/dashboard/credit-card/credit-and-debit/list';
  private getAvailablePagesApi =
    '/rest/api/borrowers/dashboard/control-flags/read';
  private getAllDocumentsApi = '/rest/api/borrowers/dashboard/documents/list';
  private getLetterApi = '/rest/api/borrowers/dashboard/documents/download';

  constructor(private http: HttpClient) {}

  getActiveLoansInfo(): Observable<ActiveLoansServiceResponse> {
    return this.http.post<ActiveLoansServiceResponse>(this.loansInfoApi, {});
  }

  getNavigation(locale: string): Observable<MenuNavigationResponse[]> {
    return this.http.post<MenuNavigationResponse[]>(this.loanNavigationApi, {
      locale,
    });
  }

  getLoanDetails(id: number, locale: string): Observable<LoanDetails> {
    return this.http.post<LoanDetails>(this.loanDetailsApi, {
      id,
      locale,
    });
  }

  getAllLoans(organizationId: string): Observable<LoanList> {
    return this.http.post<LoanList>(this.loanListApi, { organizationId });
  }

  deleteLoanRequest(requestId: string): Observable<void> {
    return this.http.post<void>(this.deleteLoanRequestApi, { requestId });
  }

  getPaymentMethods(): Observable<PaymentMethods> {
    return this.http.post<PaymentMethods>(this.getPaymentMethodsApi, {});
  }

  getAvailablePages(): Observable<AvailablePages> {
    return this.http.post<AvailablePages>(this.getAvailablePagesApi, {});
  }

  getAllDocuments(): Observable<WarningLetter[]> {
    return this.http.post<WarningLetter[]>(this.getAllDocumentsApi, {});
  }

  getFile(info: GetFileBody): Observable<HttpResponse<Blob>> {
    return this.http.post(this.getLetterApi, info, {
      responseType: 'blob',
      observe: 'response',
    });
  }
}
