<header class="page-header">
  <h1 class="page-header-title">
    {{ 'account.header' | translate }}
  </h1>
</header>
<form class="" [formGroup]="form" (submit)="save()">
  <section class="mb-2">
    <header>
      <h3 class="section-title mb-4">
        {{ 'account.personal-details' | translate }}
      </h3>
    </header>
    <div class="row">
      <div
        class="col-md-6 mb-3"
        tabindex="0"
        [attr.aria-label]="
          generateAriaLabelsForPrefilledFields('account.firstName', 'firstName')
        "
      >
        <app-tar-inputtext
          formControlName="firstName"
          appearance="standard"
          label="account.firstName"
        >
        </app-tar-inputtext>
      </div>
      <div
        class="col-md-6 mb-3"
        tabindex="0"
        [attr.aria-label]="
          generateAriaLabelsForPrefilledFields('account.lastName', 'lastName')
        "
      >
        <app-tar-inputtext
          formControlName="lastName"
          appearance="standard"
          label="account.lastName"
        >
        </app-tar-inputtext>
      </div>
      <div
        class="col-md-6 mb-3"
        tabindex="0"
        [attr.aria-label]="
          generateAriaLabelsForPrefilledFields(
            'account.dateOfBirth',
            'dateOfBirth'
          )
        "
      >
        <app-tar-inputtext
          formControlName="dateOfBirth"
          appearance="standard"
          label="account.dateOfBirth"
        >
        </app-tar-inputtext>
      </div>
      <div
        class="col-md-6 mb-3"
        tabindex="0"
        [attr.aria-label]="
          generateAriaLabelsForPrefilledFields('account.localId', 'socialId')
        "
      >
        <app-tar-input-id
          formControlName="socialId"
          label="account.localId"
          appearance="standard"
        >
        </app-tar-input-id>
      </div>
      <div class="col-md-6 mb-3" [formGroup]="getSubFormGroup('address')">
        <app-tar-select
          formControlName="country"
          label="account.country"
          [options]="countries$ | async"
          [errors]="errors.onlyRequired"
          [optionsTranslation]="false"
        >
        </app-tar-select>
      </div>
      <div class="col-md-6 mb-3" [formGroup]="getSubFormGroup('address')">
        <app-tar-select
          formControlName="city"
          label="account.city"
          [options]="cities$ | async"
          [errors]="errors.onlyRequired"
          [optionsTranslation]="false"
        >
        </app-tar-select>
      </div>
      <div class="col-md-6 mb-3" [formGroup]="getSubFormGroup('address')">
        <app-tar-select
          formControlName="streetName"
          label="account.street"
          [options]="streets$ | async"
          [errors]="errors.onlyRequired"
          [optionsTranslation]="false"
        >
        </app-tar-select>
      </div>
      <div class="col-6 col-md-3 mb-3" [formGroup]="getSubFormGroup('address')">
        <app-tar-inputtext
          formControlName="streetNumber"
          label="account.streetNumber"
          [errors]="errors.onlyRequired"
        >
        </app-tar-inputtext>
      </div>
      <div class="col-6 col-md-3 mb-3" [formGroup]="getSubFormGroup('address')">
        <app-tar-inputtext
          formControlName="postalCode"
          label="account.postCode"
        >
        </app-tar-inputtext>
      </div>
    </div>
  </section>

  <hr class="line mb-4" />
  <section class="mb-2">
    <header>
      <h3 class="section-title mb-4">
        {{ 'account.contact-info' | translate }}
      </h3>
    </header>
    <div class="row">
      <div class="col-md-6 mb-3">
        <app-tar-inputtext
          formControlName="email"
          appearance="standard"
          label="account.email"
        >
        </app-tar-inputtext>
      </div>
      <div class="col-md-6 mb-3">
        <app-tar-inputtext
          formControlName="telephone"
          label="account.telephone"
          [errors]="errors.mobilePhone"
        >
        </app-tar-inputtext>
      </div>
      <div class="col-md-6 mb-3">
        <app-tar-inputtext
          formControlName="mobilePhone"
          label="account.mobile-number"
          [errors]="errors.mobilePhone"
        >
        </app-tar-inputtext>
        <span
          *ngIf="isSuccessfulPhoneVerification"
          class="successful-verification-message fz-12"
          [innerHTML]="
            'account.mobile-verification.verified-but-not-saved' | translate
          "
        >
        </span>
      </div>
      <div class="col-md-6 mb-3" *ngIf="isPhoneVerificationNeeded()">
        <span class="d-inline-block mb-1">{{
          'account.mobile-verification.text' | translate
        }}</span>
        <button
          class="btn btn-tar-outline-primary btn-tar-sm"
          type="button"
          (click)="showMobileConfirmation(true)"
        >
          {{ 'account.mobile-verification.btn' | translate }}
        </button>
      </div>
    </div>
  </section>
  <hr class="line mb-4" />
  <section class="mb-2">
    <header>
      <h3 class="section-title mb-1 mb-md-4">
        {{ 'account.notification-preference-title' | translate }}
      </h3>
    </header>
    <div class="mb-3 row flex-column-reverse flex-md-row">
      <div class="col-12 col-md-6">
        <app-tar-select
          formControlName="preferredLanguage"
          label="account.select-language"
          [options]="languages"
          [errors]="errors.onlyRequired"
          [optionsTranslation]="true"
        >
        </app-tar-select>
      </div>
      <div class="col-12 col-md-6 mb-3 mb-md-0">
        {{ 'account.preferredLanguage' | translate }}
      </div>
    </div>
  </section>
  <hr class="line mb-4" />
  <section class="mb-2">
    <header>
      <h3 class="section-title mb-4">
        {{ 'account.invoice-title' | translate }}
      </h3>
    </header>
    <div class="mb-3">
      <app-tar-inputtext
        formControlName="taxInvoiceRecipientName"
        label="account.invoiceFullName"
      >
      </app-tar-inputtext>
    </div>
  </section>
  <footer>
    <button
      class="btn btn-tar-mw btn-tar-primary btn-block btn-md-block-none mb-1 mb-md-0"
      type="submit"
      [disabled]="disableSave()"
    >
      {{ 'account.save' | translate }}
    </button>
    <button
      type="button"
      class="btn btn-tar-mw btn-tar-basic-primary btn-block btn-md-block-none mx-md-2"
      (click)="cancelSave()"
    >
      {{ 'account.cancel-btn' | translate }}
    </button>
  </footer>
</form>
<ng-template #layoutSideImage>
  <img class="img-fluid" src="/assets/img/login/Tarya_Logo.png" alt="img" />
</ng-template>

<app-account-details-confirm-mobile-dialog
  [phone]="getFormControl('mobilePhone')"
  [firstName]="getFormControl('firstName')"
  [display]="showMobileConfirmationDialog"
  (dialogClosed)="showMobileConfirmation($event)"
  (successfulVerification)="showPhoneVerified($event)"
>
</app-account-details-confirm-mobile-dialog>

<app-tar-dialog-material
  [display]="confirmationDialog"
  [toggle]="false"
  [modal]="true"
  [closeIcon]="false"
  dialogTitle="{{ 'account.confirm-changes.text' | translate }}"
  dialogOkButtonName="{{ 'account.confirm-changes.yes' | translate }}"
  dialogCancelButtonName="{{ 'account.confirm-changes.no' | translate }}"
  (onConfirmed)="cancelSave(true)"
  (onClose)="cancelSave(false)"
>
</app-tar-dialog-material>
