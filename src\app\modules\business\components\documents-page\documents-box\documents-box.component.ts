import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { LanguageService } from '../../../../language/language.service';
import { FileStackPickerConfig } from '../../../../loan-request-origin/models/file-stack-picker-config';
import { ClientOptions, PickerOptions } from 'filestack-js';
import { UntypedFormGroup } from '@angular/forms';
import { LroUploadFileMetadata } from '../../../../loan-request-origin/models/lro-upload-file-metadata';
import { LoginService } from '../../../../core/services/login.service';
import { DOCUMENTS_INFO } from '../documents-category.file';
import { DocumentsService } from '../../../service/documents.service';
import { RequiredDocumentsModel } from '../../../model/required-document.model';

@Component({
  selector: 'app-documents-box',
  templateUrl: './documents-box.component.html',
  styleUrls: ['./documents-box.component.scss'],
})
export class DocumentsBoxComponent implements OnInit {
  @Input() documentsInfo: RequiredDocumentsModel[];
  @Input() fileStackConfig: FileStackPickerConfig;
  @Input() parentFilesForm: UntypedFormGroup;
  @Input() storeCheckboxs: number[];
  @Input() filestackWorkflowUUID: string;
  @Input() ownersInfo: any;

  @Output() checkedFiles: EventEmitter<any> = new EventEmitter();

  currentLang: string;
  isUploaded = false;
  userId: string;
  documents = DOCUMENTS_INFO;
  documentTitle: string;
  entityType = 'loan';
  clientOptions: ClientOptions;
  uploadError = false;

  pickerOptions: PickerOptions;

  ownerName: string;

  constructor(
    private languageService: LanguageService,
    private userService: LoginService,
    private documentsService: DocumentsService
  ) {}

  ngOnInit() {
    this.clientOptions = {
      security: {
        policy: this.fileStackConfig.fileStackSecurityPolicy,
        signature: this.fileStackConfig.fileStackSecuritySignature,
      },
    };
    this.pickerOptions = {
      maxFiles: 50,
      storeTo: {
        workflows: [this.filestackWorkflowUUID],
        // path: '/test'
      },
    };

    this.currentLang = this.languageService.currentLang;

    this.documents.forEach((item) => {
      if (item.category === this.documentsInfo[0].type.category) {
        this.documentTitle = item.translationKey;
      } else {
        this.documentTitle = 'BUSINESS_OWNERS_DOCUMENTS';
      }
    });
    this.setCheckedFiles();
  }

  onUploadIsDone(files) {
    let file: LroUploadFileMetadata;
    if (files.length > 0) {
      for (const item of files) {
        file = {
          entityType: this.entityType,
          entityId: this.userId,
          customData: {
            jobid: item.workflows[Object.keys(item.workflows)[0]].jobid,
            filestackWorkflow: Object.keys(item.workflows)[0],
          },
        };

        this.uploadFilestoBiz(file);
      }
    } else {
      this.showError();
    }
  }

  showError() {
    this.uploadError = true;
  }

  onChange(info, event) {
    const check = event.target.checked;
    this.checkedFiles.emit({ info, check });
  }

  uploadFilestoBiz(files) {
    this.documentsService.uploadFile(files).subscribe(
      (data) => (this.isUploaded = true),
      (err) => this.showError()
    );
  }

  setCheckedFiles() {
    if (this.storeCheckboxs) {
      this.documentsInfo.forEach((item) => {
        this.storeCheckboxs.forEach((id) => {
          if (item.type.id === id) {
            item.type.checked = true;
          }
        });
      });
    }
  }
}
