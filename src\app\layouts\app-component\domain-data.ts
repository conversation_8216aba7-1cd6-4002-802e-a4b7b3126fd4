import { HeadScript } from '~tarya-shared/components/head-scripts/head-script.class';

export enum ApplicationType {
  DEFAULT = 'DEFAULT',
  BUSINESS = 'BUSINESS',
}

export class DomainDataDto {
  domain: string;
  domainSlug: string;
  communityName: string;
  homePage: string;
  copyrightText: string;
  copyrightTextEn: string;
  contactUsText: string;
  contactUsTextEn: string;
  maxAge: number;
  termsOfServicePageLink: string;
  scripts: HeadScript[];
  heapAnalyticsId: string;
  supportPhoneNumber: string;
  appType: ApplicationType;
  maxCarDiscountPercentage?: number;
  testMode?: boolean;
  preApprovedSphere?: boolean;
  maxPasswordLength?: number;
  minPasswordLength?: number;
  protocol?: string;
  region?: string;
}
