import { LiveAnnouncer } from '@angular/cdk/a11y';
import { HttpResponse } from '@angular/common/http';
import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { Sort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { saveAs } from 'file-saver';
import { Accordion } from 'primeng/accordion';
import { Observable } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { Base } from '~tarya/modules/table-generator/shared/base';
import { MenuItems } from '../../enums/menu-items';
import { Loan } from '../../models/loan';
import { WarningLetter } from '../../models/warning-letter';
import { WarningLetterFileInfo } from '../../models/warning-letter-file-info';
import { BorrowersService } from '../../services/borrowers.service';

@Component({
  selector: 'app-borrowers-documents',
  templateUrl: './borrowers-documents.component.html',
  styleUrls: ['./borrowers-documents.component.scss'],
})
export class BorrowersDocumentsComponent extends Base implements OnInit {
  @ViewChild('accordion', { static: true }) accordion: Accordion;
  @ViewChild('paginator', { static: true }) paginator: MatPaginator;

  reviewedDocIndex: number;
  documents: WarningLetter[];
  sortedDocuments: MatTableDataSource<WarningLetter>;
  filteredDocuments: MatTableDataSource<WarningLetter>;
  sortedDocumentsAfterPagination: MatTableDataSource<WarningLetter>;
  loans: Loan[] = this.route.snapshot.data.loansList.loans;
  letter: SafeResourceUrl;
  lettersHashIdInfo: WarningLetterFileInfo[] = [];
  rowsPerPage = 5;
  actionTypes: { value: string; label: string }[] = [];
  actionTypeLabel = 'Action type';
  filterForm: UntypedFormGroup;
  sort: Sort;
  isFilterMenuOpened = false;

  constructor(
    private _liveAnnouncer: LiveAnnouncer,
    private borrowersService: BorrowersService,
    public sanitizer: DomSanitizer,
    private route: ActivatedRoute,
    private fb: UntypedFormBuilder,
    private cd: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit() {
    this.borrowersService.loansForSidebar.next({
      activeMenuItem: MenuItems.Documents,
    });
    this.filterForm = this.createForm();
    this.filterForm.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        let documentsAfterFilter = this.documents;
        const searchInput = this.filterForm.get('searchInput').value;
        const dataTypeValue = this.filterForm.get('actionType').value;
        const startDateValue = this.filterForm.get('startDate').value;
        const endDateValue = this.filterForm.get('endDate').value;
        if (searchInput) {
          const searchValue = searchInput.trim().toLowerCase();
          documentsAfterFilter = documentsAfterFilter.filter((document) =>
            document.fileType.toLowerCase().includes(searchValue)
          );
        }
        if (startDateValue) {
          documentsAfterFilter = documentsAfterFilter.filter((document) => {
            const documentDateTime = new Date(document.createDate).setHours(0);
            const startDateTimeValue = new Date(startDateValue).setHours(0);
            if (documentDateTime >= startDateTimeValue) {
              return document;
            }
          });
        }
        if (endDateValue) {
          documentsAfterFilter = documentsAfterFilter.filter((document) => {
            const documentDateTime = new Date(document.createDate).setHours(0);
            const endDateTimeValue = new Date(endDateValue).setHours(0);
            if (documentDateTime <= endDateTimeValue) {
              return document;
            }
          });
        }
        if (dataTypeValue) {
          documentsAfterFilter = documentsAfterFilter.filter(
            (document) =>
              dataTypeValue.toLowerCase() === document.fileType.toLowerCase()
          );
        }
        if (this.sort) {
          this.sortedDocuments = new MatTableDataSource(documentsAfterFilter);
          this.sortData(this.sort);
        } else {
          this.assignTableDataSource(documentsAfterFilter);
        }
        this.paginator.firstPage();
      });
    this.borrowersService
      .getAllDocuments()
      .pipe(takeUntil(this.destroy$))
      .subscribe((documents: WarningLetter[]) => {
        this.documents = documents;
        this.documents.forEach((document) => {
          const isDocTypeInActionTypes = this.actionTypes.find(
            (actionType) => actionType.value === document.fileType
          );
          if (!isDocTypeInActionTypes) {
            this.actionTypes.push({
              value: document.fileType,
              label: document.fileType,
            });
          }
        });
        if (this.documents.length && this.documents.length < this.rowsPerPage) {
          this.rowsPerPage = this.documents.length;
        }
        this.assignTableDataSource(this.documents.slice());
        this.paginator.firstPage();
      });
    this.paginator.page.pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.reviewedDocIndex = null;
      this.assignTableDataSource(this.sortedDocuments.data);
      this.cd.detectChanges();
    });
  }

  setLetterPdf(selectedFileHashId: string, index: number) {
    if (this.reviewedDocIndex === index) {
      this.reviewedDocIndex = null;
      this.letter = null;
    } else {
      this.reviewedDocIndex = index;
      this.documents[this.reviewedDocIndex].isFileRead = true;
      this.borrowersService.allDocuments.next(this.documents);
      this.setDocument(selectedFileHashId).subscribe((letter) => {
        const file = new Blob([letter.body], {
          type: 'application/pdf',
        });
        this.letter = this.sanitizer.bypassSecurityTrustResourceUrl(
          window.URL.createObjectURL(file)
        );
      });
    }
  }

  downloadDocPdf(fileHashId: string, fileType: string) {
    this.setDocument(fileHashId).subscribe((letter) => {
      const file = new Blob([letter.body], {
        type: 'application/pdf',
      });
      saveAs(file, `${fileType}.pdf`);
    });
  }

  resetForm() {
    this.assignTableDataSource(this.documents);
    this.filterForm.reset();
  }

  sortData(sort: Sort) {
    this.sort = sort;
    const tempSortedFiles = this.sortedDocuments.data.slice();
    const sortedFiles = tempSortedFiles.sort((a, b) => {
      const isAsc = this.sort.direction === 'asc';
      switch (this.sort.active) {
        case 'loanId':
          return this.compare(a.externalLoanId, b.externalLoanId, isAsc);
        case 'date':
          return this.compare(a.createDate, b.createDate, isAsc);
        default:
          return 0;
      }
    });
    this.assignTableDataSource(sortedFiles);
    this._liveAnnouncer.announce(`Sorted ${sort.direction}ending`);
  }

  private compare(a: number | string, b: number | string, isAsc: boolean) {
    return (a < b ? -1 : 1) * (isAsc ? 1 : -1);
  }

  private createForm() {
    return this.fb.group({
      searchInput: [''],
      actionType: [''],
      startDate: [''],
      endDate: [''],
    });
  }

  private setDocument(fileHashId: string): Observable<HttpResponse<Blob>> {
    return this.borrowersService
      .getFile({
        fileHashId,
        isFileRead: true,
      })
      .pipe(takeUntil(this.destroy$));
  }

  private assignTableDataSource(arr: WarningLetter[]): void {
    this.sortedDocuments = new MatTableDataSource(arr);
    this.sortedDocumentsAfterPagination =
      this.getSortedDocsAfterPagination(arr);
    this.sortedDocuments.paginator = this.paginator;
    this.filteredDocuments = new MatTableDataSource(arr);
  }

  private getSortedDocsAfterPagination(
    arr: WarningLetter[]
  ): MatTableDataSource<WarningLetter> {
    const firstDocumentToShowIndex =
      this.paginator.pageIndex * this.rowsPerPage;
    return new MatTableDataSource(
      arr.slice(
        firstDocumentToShowIndex,
        firstDocumentToShowIndex + this.rowsPerPage
      )
    );
  }
}
