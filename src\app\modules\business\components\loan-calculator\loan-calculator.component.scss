@import 'styles/vendors/bootstrap/share';
@import 'styles/variables';

.visual-options-box {
  border-radius: 4px;
  border: 1px solid #d8d8d8;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.5);
  width: 100%;
  margin: 0 auto;
  max-width: 368px;
  background: #fff;
  color: #000;
  padding: 35px;
  font-size: 14px;
  @include media-breakpoint-up(md) {
    padding: 35px;
  }
  @include media-breakpoint-up(md) {
    padding: 35px 55px;
  }
}

.title {
  font-size: 20px;
  text-align: center;
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-control {
  text-align: center;
  border: 1px solid #979797;
  border-radius: 2px;
  padding: 14px 70px;
  font-size: 16px;
  line-height: 1;
  color: #000;
}

:host ::ng-deep .btn.btn-primary {
  border-radius: 2px;
  display: block;
  width: 100%;
  border: none;
  // outline: none;
  padding: 19px 11px;
  font-size: 20px;
  background: #00b28d;
}

:host ::ng-deep .bz-spinner {
  .p-spinner-input {
    font-size: 16px;
    line-height: 1;
    padding: 14px 70px;
    color: #000;
  }
}

:host ::ng-deep .period-control {
  .p-spinner-input {
    padding-right: 125px;

    .rtl & {
      padding-right: 70px;
      padding-left: 125px;
    }
  }
}

/* Select styles*/
:host ::ng-deep .bz-select-holder {
  color: inherit;
  font-weight: 400;
  font-size: 16px;
  line-height: 50px;
  height: 50px;

  .p-dropdown,
  .p-multiselect {
    &.p-state-focus {
      .p-dropdown-label {
        background: transparent;
      }
    }
  }

  .p-dropdown-trigger {
    color: #000;
    right: 60px;

    .rtl & {
      right: auto;
      left: 60px;
    }
  }

  .p-dropdown-label,
  .p-multiselect-label {
    border: 1px solid #979797;
    border-radius: 2px;
    text-align: center;
    padding: 0 80px;
  }
}

:host ::ng-deep .control-wrap {
  position: relative;

  .currency {
    right: 70px;
    z-index: 3;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);

    .rtl & {
      right: auto;
      left: 70px;
    }
  }
}
