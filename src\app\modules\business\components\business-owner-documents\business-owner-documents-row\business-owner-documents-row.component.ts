import { Component, Input, OnInit } from '@angular/core';
import { FileUploadInfo } from '../../documents-general/general-files-info.class';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { Subscription } from 'rxjs';
import { EnumService } from '../../../service/enum.service';
import { LoanRequestService } from '../../../service/loan-request.service';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { FileUploadStatus } from '../../documents-general/document-uploader/file-upload-status';
import { BusinessOwnerData } from '../business-owner-documents.component';

@Component({
  selector: 'business-owner-documents-row',
  templateUrl: './business-owner-documents-row.component.html',
  styleUrls: ['./business-owner-documents-row.component.scss'],
})
export class BusinessOwnerDocumentsRowComponent implements OnInit {
  @Input('businessOwnerData')
  businessOwnerData: BusinessOwnerData;

  @Input('fileUploadResource')
  fileUploadResource: string;

  numberOfDocuments: number;

  uploadFilesInfo: FileUploadInfo[] = [];
  uploadFileForm: UntypedFormGroup;
  uploadFileRadio: any;

  private id: number;
  private onTranslationChangeSubscription: Subscription;

  private byOptionalDocuments(document) {
    return !document.isDocOptional && document.requiredDocumentStatus;
  }

  constructor(
    private fb: UntypedFormBuilder,
    private enumService: EnumService,
    private loanRequestService: LoanRequestService,
    private route: ActivatedRoute,
    private translateService: TranslateService
  ) {}

  ngOnInit() {
    this.createForm();
    this.fillFilesUploadInfo();
    this.onTranslationChangeSubscription =
      this.translateService.onTranslationChange.subscribe(() =>
        this.fillFilesUploadInfo()
      );
    this.updateDocumentsCounter();
  }

  ngOnDestroy() {
    this.onTranslationChangeSubscription.unsubscribe();
  }

  fillFilesUploadInfo() {
    const currentLang = this.translateService.currentLang;
    this.numberOfDocuments =
      this.businessOwnerData.requiredDocuments.requiredDocuments.filter(
        (item) => {
          return !item.type.isDocOptional;
        }
      ).length;

    for (const requiredDocument of this.businessOwnerData.requiredDocuments
      .requiredDocuments) {
      this.uploadFilesInfo.push({
        fileTitle: requiredDocument.type[currentLang],
        isDocOptional: requiredDocument.type.isDocOptional,
        fileStatus: requiredDocument.isUploaded
          ? FileUploadStatus.FILE_IS_BEING_CHECKED
          : FileUploadStatus.FILE_WAS_NOT_UPLOADED,
        typeEnumModelId: requiredDocument.type.id,
        uploadFileForm: this.uploadFileForm,
        uploadUrl: this.fileUploadResource,
        id: this.businessOwnerData.businessOwnerId,
        requiredDocumentStatus: requiredDocument.isUploaded,
      });
    }
  }

  updateDocumentsCounter() {
    this.businessOwnerData.numberOfUploadedDocuments =
      this.uploadFilesInfo.filter(this.byOptionalDocuments).length;
  }

  createForm() {
    this.uploadFileForm = this.fb.group({
      uploadFileRadio: [this.uploadFileRadio, [Validators.required]],
    });
  }
}
