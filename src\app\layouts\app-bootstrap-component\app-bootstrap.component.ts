import {
  Component,
  OnInit,
  Inject,
  AfterViewInit,
  Renderer2,
} from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { DomainService } from '../app-component/domain.service';

@Component({
  selector: 'body',
  templateUrl: './app-bootstrap.component.html',
  styleUrls: ['./app-bootstrap.component.scss'],
})
export class AppBootstrapComponent implements OnInit, AfterViewInit {
  inIframe: boolean;

  constructor(
    @Inject(DOCUMENT) private document: Document,
    private domainService: DomainService,
    private renderer: Renderer2
  ) {
    // This is important when someone try to download the report
    localStorage.setItem('redirectToMonthlyLenderReport', window.location.href);
    this.getDomainData();
  }

  ngOnInit() {
    this.inIframe = self !== top;
  }
  ngAfterViewInit() {}

  getDomainData(): void {
    this.domainService.getDomainData().subscribe(({ communityName }) => {
      if (communityName.toLowerCase() === 'ofek') {
        this.domainService.loadDomainCss(communityName);
      }
    });
  }
}
