.content-wrap {
  margin: 15px auto;
  column-count: 2;
  -moz-column-count: 2;
  -webkit-column-count: 2;
  column-gap: 5em;
  column-width: 90px;
  -moz-column-width: 90px;
  -moz-column-gap: 5em;
  -webkit-column-width: 90px;
  -webkit-column-gap: 5em;
}
.upload-inner {
  border: 1px solid #212529;
  border-radius: 2px;
  margin: 15px auto;
  padding: 20px;
}
.required-document {
  color: red;
  font-size: 16px;
  position: relative;
  top: 7px;
}
.uploaded {
  color: #00b28d;
}
.uploadError {
  color: #ff0000;
  text-align: left;
}
.icon-cloud {
  font-size: 20px;
  text-align: left;
  cursor: pointer;
  &::before {
    position: absolute;
    left: 10px;
    z-index: 0;
  }
}
:host ::ng-deep .btn {
  &.btn-default {
    width: 50px;
    background: transparent;
    position: relative;
    z-index: 1;
  }
  &:focus {
    box-shadow: none;
  }
}
.checkbox {
  margin: 0 5px;
}

:host ::ng-deep app-file-picker {
  button {
    background-color: transparent;
    border: none;
    width: 100px;
    margin: -20px 0 0;
    &:hover {
      background-color: transparent;
    }
  }
}

@media only screen and (max-width: 700px) {
  .content-wrap {
    column-count: auto;
  }
  .upload-inner {
    padding: 5px;
  }
}
