<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="ubuntubold_italic" horiz-adv-x="1163" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="491" />
<glyph unicode="&#xfb01;" horiz-adv-x="1437" d="M-74 -348q53 49 98.5 120.5t81 151.5t61.5 163t42 155l213 893q51 217 184 335.5t375 118.5q92 0 149.5 -14t92.5 -31l-109 -237q-37 10 -70.5 17t-74.5 7q-113 0 -167 -57t-75 -144l-12 -53h360l-61 -250h-359l-125 -522q-70 -293 -151.5 -455.5t-179.5 -236.5zM1001 0 l256 1077h306l-258 -1077h-304zM1286 1362q0 43 17.5 81t46 65.5t66.5 44t81 16.5q63 0 114.5 -38t51.5 -116q0 -43 -17.5 -81t-46 -65.5t-66.5 -44t-81 -16.5q-63 0 -114.5 38t-51.5 116z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1462" d="M-74 -348q53 49 98.5 120.5t81 151.5t61.5 163t42 155l213 893q51 217 184 335.5t375 118.5q92 0 149.5 -14t92.5 -31l-109 -237q-37 10 -70.5 17t-74.5 7q-113 0 -167 -57t-75 -144l-12 -53h360l-61 -250h-359l-125 -522q-70 -293 -151.5 -455.5t-179.5 -236.5z M1025 271q1 85 24 182l260 1087l317 49l-285 -1179q-8 -37 -9 -68t11.5 -54.5t42 -37.5t82.5 -19l-61 -251q-145 0 -228 37.5t-119 103t-35 150.5z" />
<glyph unicode="&#xfb03;" horiz-adv-x="2293" d="M-78 -348q53 49 98.5 120.5t81 151.5t61.5 163t42 155l213 893q51 217 184 335.5t375 118.5q92 0 160.5 -19.5t103.5 -35.5l-102 -240q-37 18 -85 27.5t-89 9.5q-113 0 -167 -57t-75 -144l-12 -53h360l-61 -250h-359l-125 -522q-70 -293 -151.5 -455.5t-179.5 -236.5z M782 -348q53 49 98.5 120.5t81 151.5t61.5 163t42 155l213 893q51 217 184 335.5t375 118.5q92 0 149.5 -14t92.5 -31l-109 -237q-37 10 -70.5 17t-74.5 7q-113 0 -167 -57t-75 -144l-12 -53h360l-61 -250h-359l-125 -522q-70 -293 -151.5 -455.5t-179.5 -236.5zM1857 0 l256 1077h306l-258 -1077h-304zM2142 1362q0 43 17.5 81t46 65.5t66.5 44t81 16.5q63 0 114.5 -38t51.5 -116q0 -43 -17.5 -81t-46 -65.5t-66.5 -44t-81 -16.5q-63 0 -114.5 38t-51.5 116z" />
<glyph unicode="&#xfb04;" horiz-adv-x="2361" d="M-78 -348q53 49 98.5 120.5t81 151.5t61.5 163t42 155l213 893q51 217 184 335.5t375 118.5q92 0 160.5 -19.5t103.5 -35.5l-102 -240q-37 18 -85 27.5t-89 9.5q-113 0 -167 -57t-75 -144l-12 -53h360l-61 -250h-359l-125 -522q-70 -293 -151.5 -455.5t-179.5 -236.5z M784 -348q53 49 98.5 120.5t81 151.5t61.5 163t42 155l213 893q51 217 184 335.5t375 118.5q92 0 149.5 -14t92.5 -31l-109 -237q-37 10 -70.5 17t-74.5 7q-113 0 -167 -57t-75 -144l-12 -53h360l-61 -250h-359l-125 -522q-70 -293 -151.5 -455.5t-179.5 -236.5zM1883 271 q1 85 24 182l260 1087l317 49l-285 -1179q-8 -37 -9 -68t11.5 -54.5t42 -37.5t82.5 -19l-61 -251q-145 0 -228 37.5t-119 103t-35 150.5z" />
<glyph unicode="&#xd;" horiz-adv-x="491" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode=" "  horiz-adv-x="491" />
<glyph unicode="&#x09;" horiz-adv-x="491" />
<glyph unicode="&#xa0;" horiz-adv-x="491" />
<glyph unicode="!" horiz-adv-x="604" d="M164 139q0 47 17.5 86t46 66.5t65.5 43t76 15.5q72 0 120 -41t48 -125q0 -47 -17.5 -86t-46.5 -66.5t-65.5 -43t-75.5 -15.5q-72 0 -120 41t-48 125zM291 453q16 147 36.5 290.5t59.5 309.5l88 366h326l-88 -366q-41 -166 -87 -309.5t-102 -290.5h-233z" />
<glyph unicode="&#x22;" horiz-adv-x="1007" d="M369 954q8 119 22.5 236t36.5 217l37 149h301l-37 -151q-23 -98 -67 -215t-91 -236h-202zM815 954q8 119 22.5 236t36.5 217l37 149h301l-37 -151q-23 -98 -67 -215t-91 -236h-202z" />
<glyph unicode="#" horiz-adv-x="1351" d="M141 0l146 348h-142l58 236h182l104 252h-225l58 235h266l147 348h262l-147 -348h227l148 348h262l-148 -348h144l-58 -235h-184l-104 -252h227l-57 -236h-269l-145 -348h-262l145 348h-227l-146 -348h-262zM647 584h227l105 252h-227z" />
<glyph unicode="$" d="M139 139l125 226q23 -16 55.5 -34t73.5 -32t88.5 -24.5t96.5 -10.5q43 0 87 5t78.5 20.5t56 45.5t21.5 79q0 33 -14 57.5t-39 45t-56.5 37.5t-64.5 34l-53 26q-131 66 -189.5 164.5t-58.5 204.5q0 68 24.5 132.5t75 117.5t129 89t187.5 48l55 219h258l-57 -225 q78 -16 142.5 -44t105.5 -54l-123 -217q-53 41 -129 62.5t-148 21.5q-88 0 -151.5 -33t-63.5 -103q0 -29 8.5 -51t28 -42.5t52 -42t83.5 -46.5q59 -31 113.5 -64.5t96.5 -78.5t67.5 -102.5t25.5 -135.5q0 -96 -30.5 -167.5t-87 -123t-135 -81t-175.5 -39.5l-59 -246h-258 l61 250q-111 16 -188.5 51t-114.5 61z" />
<glyph unicode="%" horiz-adv-x="1781" d="M246 958q0 117 34.5 208t93 154.5t134.5 97.5t156 34q68 0 124 -18.5t97 -56.5t63.5 -94t22.5 -132q0 -117 -35 -208t-93.5 -154.5t-134 -97.5t-155.5 -34q-68 0 -124 18.5t-97 56.5t-63.5 94.5t-22.5 131.5zM315 0l1143 1419h295l-1143 -1419h-295zM471 967 q0 -55 30.5 -84t73.5 -29q33 0 64 19.5t54.5 57.5t37.5 91t14 121q0 55 -27.5 84.5t-70.5 29.5q-33 0 -64.5 -20t-56 -57t-40 -91.5t-15.5 -121.5zM1098 268q0 117 34.5 208t93 154.5t134.5 97.5t156 34q68 0 124 -18.5t97 -56.5t63.5 -94t22.5 -132q0 -117 -35 -208 t-93.5 -154.5t-134 -97.5t-155.5 -34q-68 0 -124 18.5t-97 56.5t-63.5 94.5t-22.5 131.5zM1323 276q0 -55 30.5 -83.5t73.5 -28.5q33 0 64 19.5t54.5 57.5t37.5 91t14 121q0 55 -27.5 84.5t-70.5 29.5q-33 0 -64.5 -20.5t-56 -57t-40 -91t-15.5 -122.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1357" d="M164 322q0 154 80 265t258 191l35 17q-41 68 -60.5 134t-19.5 130q0 82 31.5 154.5t92 125t145.5 83t192 30.5q88 0 155.5 -23.5t111.5 -62.5t66.5 -91t22.5 -110q0 -51 -18.5 -106t-54.5 -108.5t-91 -100.5t-129 -80l-76 -35l172 -229q51 84 94 207l269 -35 q-88 -223 -207 -377q53 -76 95 -151.5t77 -149.5h-309q-12 27 -27.5 55.5t-32.5 59.5q-195 -133 -444 -133q-109 0 -189.5 28.5t-134 76.5t-79 108.5t-25.5 126.5zM467 381q0 -66 42 -114t122 -48q53 0 122.5 17.5t147.5 70.5l-239 316l-31 -15q-86 -39 -125 -102.5 t-39 -124.5zM727 1051q0 -57 53 -146l25 10q98 45 152.5 97.5t54.5 124.5q0 39 -29 68.5t-90 29.5t-113.5 -46t-52.5 -138z" />
<glyph unicode="'" horiz-adv-x="561" d="M369 954q8 119 22.5 236t36.5 217l37 149h301l-37 -151q-23 -98 -67 -215t-91 -236h-202z" />
<glyph unicode="(" horiz-adv-x="729" d="M201 276q0 184 46 366.5t129 353.5t198.5 326t254.5 282l197 -138q-117 -127 -213 -267t-165.5 -291.5t-108.5 -312.5t-39 -327q0 -129 24.5 -255t77.5 -255l-225 -137q-92 154 -134 317.5t-42 337.5z" />
<glyph unicode=")" horiz-adv-x="729" d="M-49 -240q117 125 214 265.5t165.5 293t107.5 313.5t39 326q0 129 -24.5 255t-77.5 255l225 138q92 -154 134 -318t42 -338q0 -184 -46 -366.5t-129 -353.5t-198.5 -325.5t-255.5 -281.5z" />
<glyph unicode="*" horiz-adv-x="1028" d="M266 1008l82 254l49 -17q25 -8 57.5 -25.5t64.5 -39t61.5 -43t52.5 -39.5q-10 27 -21.5 62.5t-21.5 72.5t-17.5 72.5t-7.5 62.5v51h266v-51q0 -27 -7 -62.5t-17 -72.5t-21.5 -72.5t-21.5 -62.5q20 18 51 39.5t62.5 43t64.5 39t57 25.5l50 17l81 -254l-49 -17 q-27 -8 -61.5 -12t-73.5 -5t-75.5 -1h-65.5q25 -16 54.5 -38t59 -45.5t56 -48t43.5 -46.5l33 -43l-217 -154l-31 41q-16 20 -31.5 53t-30 69t-24.5 70.5t-19 63.5q-8 -29 -18 -63.5t-24.5 -70.5t-30 -69t-31.5 -53l-31 -41l-217 154l33 43q16 23 43 47t56.5 47.5t59 45 t54.5 38.5h-66t-75.5 1t-73.5 5t-62 12z" />
<glyph unicode="+" d="M195 483l63 256h342l90 379h279l-90 -379h342l-64 -256h-342l-90 -379h-279l91 379h-342z" />
<glyph unicode="," horiz-adv-x="514" d="M-20 -274q78 135 131 280.5t63 304.5h320q-6 -178 -89 -337.5t-202 -307.5z" />
<glyph unicode="-" horiz-adv-x="702" d="M150 473l67 277h594l-68 -277h-593z" />
<glyph unicode="." horiz-adv-x="516" d="M123 139q0 47 17.5 86t46 66.5t65.5 43t76 15.5q72 0 120 -40t48 -126q0 -47 -17.5 -86t-46.5 -66.5t-65.5 -43t-75.5 -15.5q-72 0 -120 40t-48 126z" />
<glyph unicode="/" horiz-adv-x="892" d="M-184 -379l1147 1983h309l-1145 -1983h-311z" />
<glyph unicode="0" d="M197 391q0 233 52 428t145 336t224.5 219t290.5 78q100 0 170 -36t114 -95t63.5 -134t19.5 -155q0 -233 -52.5 -427.5t-145.5 -336t-224 -219.5t-291 -78q-100 0 -169.5 36t-113.5 95.5t-63.5 134t-19.5 154.5zM477 434q0 -106 32.5 -153.5t100.5 -47.5q66 0 121 39 t97 102.5t74 144.5t52.5 165t30.5 164t10 141q0 104 -31.5 153.5t-97.5 49.5q-68 0 -123 -39t-98 -102.5t-74.5 -145.5t-52 -166t-31 -163.5t-10.5 -141.5z" />
<glyph unicode="1" d="M410 1124q66 25 137 56.5t142 69.5t138.5 80t122.5 89h213l-342 -1419h-305l246 1022q-72 -41 -156 -75t-160 -58z" />
<glyph unicode="2" d="M123 0q18 129 63 223t112 171t150.5 143.5t182.5 142.5q92 72 156.5 124t104.5 96t58.5 83t18.5 82q0 53 -38 89t-97 36q-84 0 -164 -36t-174 -107l-105 223q123 100 246 141t254 41q82 0 152.5 -26.5t123 -72.5t81 -107.5t28.5 -131.5q0 -78 -16.5 -139.5t-55.5 -118.5 t-103.5 -116.5t-158.5 -133.5q-55 -43 -111.5 -82t-110.5 -80t-105.5 -87t-96.5 -103h610l-61 -254h-944z" />
<glyph unicode="3" d="M113 68l110 241q59 -41 147.5 -66.5t180.5 -25.5q57 0 111.5 12.5t95.5 41t65.5 73.5t24.5 111q0 84 -68.5 127t-183.5 43h-131l59 241h148q43 0 90 8.5t86 30t64.5 57t25.5 93.5q0 72 -49 105.5t-129 33.5q-63 0 -140 -22.5t-159 -65.5l-78 229q100 57 206.5 87 t221.5 30q109 0 190.5 -29.5t136 -79t81 -112t26.5 -127.5q0 -53 -17.5 -105.5t-51 -98.5t-85.5 -83.5t-120 -62.5q86 -41 133 -126t47 -186q0 -84 -35 -168t-110.5 -151.5t-192.5 -109.5t-281 -42q-233 0 -419 97z" />
<glyph unicode="4" d="M137 317l51 226q66 90 160 203.5t205 232.5t229.5 233.5t233.5 206.5h291l-207 -856h149l-59 -246h-152l-77 -317h-291l75 317h-608zM481 563h332l119 496q-53 -51 -111.5 -110.5t-118 -124t-116 -131t-105.5 -130.5z" />
<glyph unicode="5" d="M129 68l113 247q72 -43 153.5 -65.5t157.5 -22.5q147 0 225 55.5t78 172.5q0 49 -26.5 89t-86 67.5t-159 43t-242.5 15.5q68 190 129 383.5t109 365.5h757l-61 -254h-506q-20 -74 -44 -148.5t-42 -125.5q123 -10 212 -46t147.5 -91.5t87 -126t28.5 -150.5 q0 -115 -38 -209t-113.5 -159.5t-188 -101.5t-260.5 -36q-43 0 -98 6.5t-113.5 17.5t-115 29.5t-103.5 43.5z" />
<glyph unicode="6" d="M215 471q0 180 61.5 353t188.5 308.5t321.5 218.5t460.5 83h38t44 -2l-37 -242q-127 -4 -236.5 -18.5t-200.5 -53.5t-159.5 -104.5t-113.5 -169.5q53 29 123.5 42t132.5 13q76 0 142.5 -23.5t116.5 -71.5t78.5 -120t28.5 -166q0 -133 -43 -234.5t-116.5 -171t-172 -105.5 t-206.5 -36q-96 0 -178 33t-142.5 95.5t-95.5 155.5t-35 216zM506 465q0 -98 45 -169t139 -71q98 0 160.5 72t62.5 201q0 98 -53 135t-127 37q-51 0 -103 -13.5t-98 -31.5q-27 -78 -26 -160z" />
<glyph unicode="7" d="M276 0q39 152 113 314.5t166 317t194.5 292t198.5 237.5h-616l63 258h994l-54 -221q-78 -66 -185.5 -190.5t-216 -285.5t-202.5 -346.5t-145 -375.5h-310z" />
<glyph unicode="8" d="M190 322q0 61 17.5 117.5t55.5 109.5t97.5 103t143.5 102q-49 47 -83 107.5t-34 146.5q0 80 29.5 159.5t92 143t157 102.5t223.5 39q86 0 159.5 -24.5t127 -68.5t84 -107.5t30.5 -139.5q0 -45 -10 -95t-41 -100.5t-87 -101.5t-148 -96q35 -25 65.5 -53.5t53 -65.5 t35.5 -82t13 -102q0 -92 -35.5 -173t-104 -141.5t-171 -95.5t-233.5 -35q-76 0 -154 17.5t-140.5 59.5t-102.5 109t-40 165zM483 358q0 -72 50.5 -107.5t121.5 -35.5q43 0 83 13.5t71 38t49.5 60.5t18.5 81q0 41 -14.5 72.5t-40 58t-59.5 48t-73 42.5q-63 -31 -103 -65 t-63.5 -68.5t-32 -69.5t-8.5 -68zM668 1026q0 -66 41 -111t98 -79q119 55 159 115.5t40 117.5q0 66 -41 102.5t-113 36.5q-84 0 -134 -53t-50 -129z" />
<glyph unicode="9" d="M158 -6l37 242q127 2 236.5 17t200.5 54t159.5 104.5t113.5 170.5q-53 -29 -123.5 -42.5t-132.5 -13.5q-76 0 -142.5 23.5t-116.5 72t-78.5 119t-28.5 166.5q0 131 43 233.5t116.5 172t172 105.5t206.5 36q94 0 177 -32.5t143.5 -95t95.5 -156t35 -216.5 q0 -180 -61.5 -353t-188.5 -308t-321.5 -218t-460.5 -83h-38t-44 2zM573 928q0 -98 53.5 -135t127.5 -37q51 0 103 13t97 32q27 78 27 160q0 98 -45 168.5t-139 70.5q-98 0 -161 -71.5t-63 -200.5z" />
<glyph unicode=":" horiz-adv-x="516" d="M123 139q0 47 17.5 86t46 66.5t65.5 43t76 15.5q72 0 120 -40t48 -126q0 -47 -17.5 -86t-46.5 -66.5t-65.5 -43t-75.5 -15.5q-72 0 -120 40t-48 126zM277 866q0 47 17.5 86t46 66.5t65.5 43t76 15.5q72 0 120 -40t48 -126q0 -47 -17.5 -86t-46.5 -66.5t-65.5 -43 t-75.5 -15.5q-72 0 -120 40t-48 126z" />
<glyph unicode=";" horiz-adv-x="516" d="M-12 -274q78 135 131 280.5t63 304.5h320q-6 -178 -89 -337.5t-202 -307.5zM277 866q0 47 17.5 86t46 66.5t65.5 43t76 15.5q72 0 120 -40t48 -126q0 -47 -17.5 -86t-46.5 -66.5t-65.5 -43t-75.5 -15.5q-72 0 -120 40t-48 126z" />
<glyph unicode="&#x3c;" d="M193 500l55 239l971 371l20 -254l-709 -246l582 -268l-129 -227z" />
<glyph unicode="=" d="M152 258l61 256h963l-62 -256h-962zM260 707l62 256h962l-61 -256h-963z" />
<glyph unicode="&#x3e;" d="M139 371l709 245l-582 269l129 225l791 -385l-56 -240l-970 -370z" />
<glyph unicode="?" horiz-adv-x="880" d="M252 139q0 47 17.5 86t46 66.5t65.5 43t76 15.5q72 0 120 -40t48 -126q0 -47 -17.5 -86t-46.5 -66.5t-65.5 -43t-75.5 -15.5q-72 0 -120 40t-48 126zM350 453q0 63 19.5 116.5t53.5 98.5t79 86t96 80q61 47 104 81.5t71 64.5t41 56.5t13 55.5q0 53 -37.5 77.5t-95.5 24.5 q-37 0 -78 -7t-79.5 -19.5t-73.5 -27t-62 -28.5l-39 240q80 45 183.5 72.5t208.5 27.5q74 0 141.5 -18.5t118.5 -56.5t81.5 -94t30.5 -130q0 -88 -27.5 -153.5t-70.5 -117t-97 -93t-108 -82.5q-78 -59 -137 -119t-65 -135h-271z" />
<glyph unicode="@" horiz-adv-x="1927" d="M205 365q0 266 91 472.5t242.5 347t346 213t403.5 72.5q141 0 270.5 -39.5t228.5 -121.5t158.5 -207t59.5 -297q0 -137 -34 -271.5t-105.5 -240t-182 -171t-262.5 -65.5q-53 0 -100 20.5t-78 59.5q-72 -41 -140.5 -60.5t-146.5 -19.5q-162 0 -252 106.5t-90 280.5 q0 100 41 209t121 198t200 146.5t279 57.5q123 0 203 -21.5t135 -44.5l-145 -606q-4 -10 -4 -18.5v-16.5q0 -35 22.5 -50t57.5 -15q53 0 98 37.5t78 104.5t51 158t18 197q0 129 -39.5 222.5t-109.5 151.5t-162 86t-198 28q-147 0 -294 -56.5t-262.5 -168t-187.5 -280.5 t-72 -392q0 -145 46.5 -241.5t124 -154t181 -82t220.5 -24.5q72 0 160 10t161 25l-12 -203q-86 -16 -177 -26.5t-169 -10.5q-180 0 -323.5 49t-244 140.5t-153.5 221.5t-53 290zM885 469q0 -92 32.5 -140t110.5 -48q61 0 143 41q2 16 6.5 36.5t10.5 49.5l100 421 q-35 8 -84 9q-84 0 -143.5 -35t-99 -89.5t-58 -120t-18.5 -124.5z" />
<glyph unicode="A" horiz-adv-x="1462" d="M31 0q133 229 248.5 423.5t223 367t210 327t209.5 301.5h284q33 -147 62.5 -326.5t55.5 -366.5t47.5 -374.5t39.5 -351.5h-321q-4 80 -9.5 156.5t-11.5 152.5h-532q-41 -76 -86 -152.5t-86 -156.5h-334zM666 559h383q-10 125 -22 266.5t-32 282.5q-86 -137 -169 -272.5 t-160 -276.5z" />
<glyph unicode="B" horiz-adv-x="1347" d="M154 25l327 1372q117 23 245 31t235 8q123 0 212 -26t145 -69t84 -101t28 -124q0 -45 -9.5 -94t-37 -97t-75.5 -91t-126 -78q92 -41 135 -112t43 -157q0 -98 -39 -189t-125 -161.5t-223 -112.5t-334 -42q-123 0 -252 10t-233 33zM522 252q33 -6 83 -9t93 -3q61 0 122 9 t109 33.5t78.5 67.5t30.5 111q0 29 -11 57.5t-35.5 51t-64.5 37t-96 14.5h-221zM670 860h200q123 0 183.5 53.5t60.5 126.5q0 39 -17.5 67t-46 44t-65.5 23.5t-76 7.5q-43 0 -92 -3t-74 -8z" />
<glyph unicode="C" horiz-adv-x="1306" d="M252 573q0 160 56.5 318t166 281.5t268 201.5t365.5 78q98 0 193.5 -22.5t199.5 -79.5l-115 -248q-86 45 -153.5 61.5t-145.5 16.5q-117 0 -210 -47.5t-159.5 -128t-101 -187t-34.5 -225.5q0 -180 78.5 -262t226.5 -82q98 0 181 20.5t155 53.5l34 -261 q-84 -41 -186 -65.5t-250 -24.5q-127 0 -232.5 42t-180 120t-115.5 189.5t-41 250.5z" />
<glyph unicode="D" horiz-adv-x="1470" d="M154 25l329 1370q117 23 224.5 32t193.5 9q152 0 271.5 -41t202.5 -117t126 -180.5t43 -231.5q0 -207 -67.5 -370.5t-193.5 -277.5t-306 -175t-408 -61q-86 0 -198.5 10t-216.5 33zM526 256q23 -2 46.5 -3t64.5 -1q133 0 240.5 41t182.5 117.5t116 188.5t41 251 q0 72 -18.5 130t-60.5 100t-108.5 64.5t-161.5 22.5q-61 0 -125 -6z" />
<glyph unicode="E" horiz-adv-x="1216" d="M154 0l340 1419h927l-65 -262h-608l-66 -287h537l-64 -258h-537l-81 -348h655l-66 -264h-972z" />
<glyph unicode="F" horiz-adv-x="1177" d="M154 0l340 1419h919l-65 -264h-600l-72 -303h530l-63 -264h-531l-141 -588h-317z" />
<glyph unicode="G" horiz-adv-x="1409" d="M252 573q0 160 56.5 318t167 281.5t274 201.5t376.5 78q98 0 208 -28.5t198 -86.5l-115 -247q-72 45 -152.5 67.5t-158.5 22.5q-125 0 -222.5 -47.5t-165 -128t-102 -187t-34.5 -225.5q0 -180 75 -263t222 -83q41 0 73.5 3t59.5 7l118 494h320l-172 -711 q-57 -23 -165.5 -44.5t-276.5 -21.5q-143 0 -253 42t-183.5 120t-110.5 188.5t-37 249.5z" />
<glyph unicode="H" horiz-adv-x="1462" d="M154 0l340 1419h319l-131 -545h504l131 545h319l-342 -1419h-319l145 604h-504l-145 -604h-317z" />
<glyph unicode="I" horiz-adv-x="638" d="M154 0l340 1419h319l-342 -1419h-317z" />
<glyph unicode="J" horiz-adv-x="1056" d="M41 96l141 238q49 -35 107.5 -61.5t138.5 -26.5q57 0 99 13t73 43t52.5 77t37.5 115l221 925h320l-238 -989q-25 -104 -65.5 -189t-106 -145.5t-162 -92.5t-233.5 -32q-111 0 -210 34t-175 91z" />
<glyph unicode="K" horiz-adv-x="1355" d="M154 0l340 1419h319l-125 -512q160 137 301 263t264 249h379q-186 -174 -369.5 -345t-385.5 -333q129 -141 247.5 -323t212.5 -418h-348q-29 70 -68.5 149.5t-88 159.5t-103.5 154t-113 131l-145 -594h-317z" />
<glyph unicode="L" horiz-adv-x="1155" d="M154 0l340 1419h319l-276 -1149h628l-65 -270h-946z" />
<glyph unicode="M" horiz-adv-x="1798" d="M113 0q47 168 103 358.5t116.5 380t120 365.5t112.5 315h287q12 -55 31.5 -156.5t41 -223t44 -249.5t41.5 -235q70 106 151.5 229t161.5 242t151.5 222.5t122.5 170.5h283q-14 -141 -37.5 -317t-53.5 -364.5t-62.5 -379t-65.5 -358.5h-311q43 209 91 451.5t89 490.5 q-53 -84 -118.5 -187.5t-131.5 -209t-127 -201.5t-106 -164h-234q-10 61 -24.5 155.5t-32 200t-36 213t-34.5 193.5q-80 -248 -144.5 -490.5t-119.5 -451.5h-309z" />
<glyph unicode="N" horiz-adv-x="1519" d="M154 0l340 1419h243q47 -72 101.5 -169t112 -209.5t114.5 -234.5t109 -241l202 854h318l-342 -1419h-240q-86 242 -191.5 483.5t-228.5 456.5l-225 -940h-313z" />
<glyph unicode="O" horiz-adv-x="1550" d="M252 567q0 160 55 317.5t158.5 283.5t253 205t338.5 79q125 0 229.5 -41t180 -117.5t117.5 -187.5t42 -250q0 -160 -54 -318.5t-157.5 -284.5t-253.5 -204t-340 -78q-125 0 -229.5 41t-180 118t-117.5 187.5t-42 249.5zM580 590q0 -154 64.5 -249t209.5 -95q94 0 175 49 t141.5 131t94 187.5t33.5 220.5q0 154 -64.5 249t-209.5 95q-94 0 -175 -49.5t-141.5 -131.5t-94 -188.5t-33.5 -218.5z" />
<glyph unicode="P" horiz-adv-x="1308" d="M154 0l333 1399q111 20 210.5 28.5t187.5 8.5q129 0 232.5 -28t175 -80t110.5 -128t39 -170q0 -154 -59.5 -260t-164 -172t-246 -94.5t-306.5 -28.5h-80l-115 -475h-317zM651 743h80q86 0 156.5 13.5t121 44t78 79t27.5 117.5q0 92 -66.5 131t-189.5 39q-35 0 -59.5 -2 t-48.5 -4z" />
<glyph unicode="Q" horiz-adv-x="1548" d="M252 567q0 160 55 317.5t158.5 283.5t253 205t338.5 79q125 0 229.5 -41t180 -117.5t117.5 -187.5t42 -250q0 -141 -43 -283.5t-126 -262.5t-202.5 -206t-273.5 -116v-6q0 -45 29.5 -72t80 -41t116 -20.5t136.5 -10.5l-94 -221q-162 6 -270.5 32.5t-175 70.5t-95 107.5 t-28.5 143.5v15q-197 41 -312.5 190.5t-115.5 390.5zM580 590q0 -154 64.5 -249t209.5 -95q94 0 175 49t141.5 131t94 187.5t33.5 220.5q0 154 -64.5 249t-209.5 95q-94 0 -175 -49.5t-141.5 -131.5t-94 -188.5t-33.5 -218.5z" />
<glyph unicode="R" horiz-adv-x="1343" d="M154 0l333 1399q111 20 215.5 28.5t190.5 8.5q137 0 239.5 -33t170 -88t100.5 -130t33 -157q0 -68 -15.5 -133.5t-57.5 -124.5t-112.5 -109.5t-181.5 -85.5q57 -106 117.5 -257.5t113.5 -317.5h-342q-47 141 -95 279.5t-107 238.5h-160l-125 -518h-317zM657 770h109 q98 0 165.5 18.5t108.5 49t58.5 70.5t17.5 81q0 35 -10 66.5t-37 56.5t-71.5 39t-114.5 14q-35 0 -72 -2t-61 -4z" />
<glyph unicode="S" horiz-adv-x="1144" d="M82 102l135 244q25 -16 59.5 -34.5t78.5 -35t93.5 -27.5t102.5 -11q47 0 94 7t84 25.5t60.5 52t23.5 87.5q0 41 -22.5 70.5t-57.5 54t-78 45t-86 45.5q-55 33 -104 69.5t-84 82.5t-55.5 102.5t-20.5 130.5q0 94 36 175t106.5 140t177 93t249.5 34q66 0 127.5 -11 t114.5 -27.5t97 -38t75 -42.5l-133 -235q-51 39 -125 65.5t-160 26.5q-47 0 -90 -8t-75.5 -27.5t-52 -50.5t-19.5 -76q0 -41 15.5 -68.5t40 -48t57 -37t69.5 -34.5q63 -33 122.5 -70t104.5 -85t72 -109.5t27 -145.5q0 -117 -43 -203t-121 -143t-185.5 -85t-238.5 -28 q-86 0 -161 14.5t-135 34t-104 42t-71 40.5z" />
<glyph unicode="T" horiz-adv-x="1216" d="M297 1151l65 268h1151l-65 -268h-416l-276 -1151h-320l277 1151h-416z" />
<glyph unicode="U" horiz-adv-x="1398" d="M246 397q0 106 30 228l191 794h319l-200 -839q-20 -74 -21 -150q0 -41 9.5 -73.5t33 -57.5t63.5 -38t99 -13q123 0 186.5 76.5t98.5 218.5l209 876h319l-215 -903q-70 -289 -226.5 -417t-414.5 -128q-133 0 -224 32t-148.5 88t-83 135t-25.5 171z" />
<glyph unicode="V" horiz-adv-x="1409" d="M348 1419h342q6 -117 15.5 -251t21.5 -270t25.5 -270t27.5 -251q63 106 136 233t148 264.5t147.5 276.5t134.5 268h340q-78 -147 -170.5 -317t-198 -352.5t-223 -372t-246.5 -377.5h-299q-33 174 -62.5 360.5t-56.5 370.5t-47.5 359.5t-34.5 328.5z" />
<glyph unicode="W" horiz-adv-x="1916" d="M328 756q0 166 6 330.5t16 332.5h322q-10 -180 -19.5 -353t-9.5 -359v-141.5t4 -143.5q57 100 120.5 213t125 225.5t117 218t94.5 189.5h262q4 -84 11 -190.5t16.5 -220.5t19.5 -226.5t21 -208.5q53 104 108 225t110.5 250t110 262t103.5 260h338q-160 -389 -326 -740 t-356 -679h-299q-16 113 -29.5 216t-25 203.5t-20.5 203t-18 213.5q-117 -217 -230.5 -425t-242.5 -411h-299q-16 190 -23 376.5t-7 379.5z" />
<glyph unicode="X" horiz-adv-x="1370" d="M31 0l645 772l-289 647h352l170 -438q92 111 178.5 224.5t157.5 213.5h350q-59 -84 -125.5 -173t-137 -178t-142.5 -174t-139 -161q43 -96 80.5 -182t72.5 -173t68 -179.5t67 -198.5h-342q-29 88 -52 155.5t-44.5 126t-44 110.5t-49.5 110l-412 -502h-364z" />
<glyph unicode="Y" horiz-adv-x="1335" d="M348 1419h340q33 -147 72 -288t86 -295q59 76 113.5 146.5t105.5 141t101.5 143t103.5 152.5h352q-86 -121 -166 -229.5t-159.5 -212t-163.5 -205.5t-179 -211l-135 -561h-319l135 561q-86 217 -155.5 424t-131.5 434z" />
<glyph unicode="Z" horiz-adv-x="1306" d="M92 0l47 190q82 109 192.5 237t230.5 257t241 250t223 217h-627l66 268h1046l-53 -221q-82 -70 -196.5 -177.5t-239.5 -233t-250 -261t-227 -258.5h712l-65 -268h-1100z" />
<glyph unicode="[" horiz-adv-x="763" d="M61 -379l476 1983h555l-60 -244h-264l-358 -1495h264l-60 -244h-553z" />
<glyph unicode="\" horiz-adv-x="831" d="M307 1604h293l211 -1983h-293z" />
<glyph unicode="]" horiz-adv-x="763" d="M-78 -379l60 244h262l358 1495h-264l59 244h555l-477 -1983h-553z" />
<glyph unicode="^" d="M213 739l575 688h242l240 -716l-266 -113l-160 506l-414 -498z" />
<glyph unicode="_" horiz-adv-x="1030" d="M-61 -379l57 254h1024l-59 -254h-1022z" />
<glyph unicode="`" horiz-adv-x="505" d="M315 1475l189 161l276 -338l-131 -118z" />
<glyph unicode="a" horiz-adv-x="1204" d="M205 418q0 141 50 266t139 218t210 146.5t262 53.5q18 0 64.5 -2t106 -9.5t125 -23.5t124.5 -45l-133 -557q-20 -82 -20 -164q-1 -35 3 -71q12 -118 48 -210l-273 -38q-12 25 -22 47t-21 51q-57 -47 -126.5 -78t-155.5 -31q-102 0 -175 36t-119 96.5t-66.5 141.5 t-20.5 173zM508 440q0 -98 32.5 -155.5t121.5 -57.5q47 0 84.5 18.5t80.5 61.5q4 51 13.5 107.5t19.5 103.5l80 324q-27 4 -48.5 6t-51.5 2q-70 0 -130.5 -35t-105.5 -91t-70.5 -130t-25.5 -154z" />
<glyph unicode="b" horiz-adv-x="1177" d="M143 51l355 1489l317 49l-127 -528q41 18 82 28.5t88 10.5q98 0 170 -35t119 -94.5t69.5 -139t22.5 -172.5q0 -141 -52 -267t-142.5 -219t-213 -147.5t-266.5 -54.5q-18 0 -64 1t-105.5 9.5t-126 25t-126.5 44.5zM489 231q12 -2 21.5 -4t20 -3t24.5 -1h37q72 0 135.5 35 t109.5 93.5t72.5 132t26.5 153.5q0 98 -33 153.5t-121 55.5q-29 0 -74.5 -10.5t-82.5 -42.5z" />
<glyph unicode="c" horiz-adv-x="983" d="M205 430q0 139 45 262t129 215t203.5 145.5t267.5 53.5q92 0 164.5 -17.5t132.5 -46.5l-105 -237q-41 16 -85 29.5t-107 13.5q-154 0 -242 -103.5t-88 -281.5q0 -104 45 -169t166 -65q59 0 114.5 12.5t98.5 30.5l23 -243q-57 -23 -126 -40.5t-167 -17.5q-127 0 -215 37 t-145.5 99.5t-83 146.5t-25.5 176z" />
<glyph unicode="d" horiz-adv-x="1208" d="M205 416q0 139 50 263t138 216t209 145.5t264 53.5q37 0 69 -4.5t64 -12.5l111 463l317 49l-270 -1124q-20 -82 -20 -164q-1 -35 3 -71q12 -118 48 -210l-273 -38q-12 25 -22 47t-21 51q-57 -47 -127.5 -78t-156.5 -31q-102 0 -175 35t-119 95.5t-67.5 141.5t-21.5 173z M508 438q0 -98 33.5 -154.5t122.5 -56.5q47 0 85.5 18.5t81.5 61.5q4 51 13.5 107.5t19.5 103.5l76 316q-12 2 -21.5 4t-18.5 3t-23.5 1h-36.5q-72 0 -133.5 -34t-105.5 -91.5t-68.5 -130t-24.5 -148.5z" />
<glyph unicode="e" horiz-adv-x="1085" d="M205 422q0 131 46 254t130 218t204 153.5t265 58.5q72 0 133 -20.5t107.5 -59.5t72 -95.5t25.5 -127.5q0 -115 -51.5 -195t-142.5 -130t-216 -72.5t-272 -22.5q14 -86 62 -122t153 -36q66 0 133.5 11.5t126.5 33.5l23 -239q-57 -23 -146.5 -41.5t-193.5 -18.5 q-127 0 -214.5 36t-141.5 97.5t-78.5 143.5t-24.5 174zM514 592q127 4 205 21.5t121 42t57 53t14 59.5q0 96 -110 96q-104 0 -183 -74.5t-104 -197.5z" />
<glyph unicode="f" horiz-adv-x="897" d="M-74 -348q53 49 98.5 120.5t81 151.5t61.5 163t42 155l213 893q51 217 184 335.5t375 118.5q92 0 160.5 -19.5t103.5 -35.5l-102 -240q-37 18 -85 27.5t-89 9.5q-113 0 -167 -57t-75 -144l-12 -53h360l-61 -250h-359l-125 -522q-70 -293 -151.5 -455.5t-179.5 -236.5z " />
<glyph unicode="g" horiz-adv-x="1161" d="M68 -303l102 242q63 -29 132 -49.5t169 -20.5q131 0 197.5 58.5t87.5 150.5l8 39q-41 -20 -87 -32.5t-97 -12.5q-96 0 -166 29.5t-114 83t-65.5 126t-21.5 158.5q0 156 57.5 275.5t152.5 199.5t219 121t259 41q98 0 194.5 -20.5t188.5 -63.5l-211 -887 q-31 -133 -81 -230.5t-124.5 -162t-179 -96t-244.5 -31.5q-129 0 -217 23.5t-159 58.5zM516 492q0 -37 6 -69t22.5 -55.5t45 -38t72.5 -14.5q29 0 76.5 10.5t86.5 47.5l113 469q-35 8 -86 8q-78 0 -139.5 -27.5t-105.5 -75t-67.5 -114t-23.5 -141.5z" />
<glyph unicode="h" horiz-adv-x="1157" d="M143 0l369 1540l317 49l-124 -514q41 8 82.5 14.5t82.5 6.5q88 0 151.5 -26t104.5 -72t61.5 -109.5t20.5 -136.5q0 -86 -20 -170l-139 -582h-306l136 569q8 35 15 74t7 74q0 53 -27.5 91t-105.5 38q-33 0 -62.5 -4t-58.5 -11l-198 -831h-306z" />
<glyph unicode="i" horiz-adv-x="579" d="M143 0l256 1077h306l-259 -1077h-303zM428 1362q0 43 17.5 81t46 65.5t66.5 44t81 16.5q63 0 114.5 -38t51.5 -116q0 -43 -17.5 -81t-46 -65.5t-66.5 -44t-81 -16.5q-63 0 -114.5 38t-51.5 116z" />
<glyph unicode="j" horiz-adv-x="593" d="M-184 -340l78 242q29 -10 56 -16.5t62 -6.5q72 0 111 43t55 125l248 1030h305l-250 -1038q-49 -213 -153 -315.5t-297 -102.5q-57 0 -113.5 11.5t-101.5 27.5zM455 1362q0 43 17 81t46 65.5t67 44t81 16.5q63 0 114 -38t51 -116q0 -43 -17 -81t-46 -65.5t-66.5 -44 t-80.5 -16.5q-63 0 -114.5 38t-51.5 116z" />
<glyph unicode="k" horiz-adv-x="1148" d="M143 0l369 1540l317 49l-217 -901q123 98 231.5 200.5t186.5 188.5h344q-115 -127 -238.5 -248.5t-283.5 -259.5q43 -53 87 -122.5t84 -145.5t73.5 -153.5t56.5 -147.5h-340q-18 53 -46 116.5t-61.5 126t-70.5 122t-74 106.5l-115 -471h-303z" />
<glyph unicode="l" horiz-adv-x="645" d="M231 453l261 1087l317 49l-285 -1179q-8 -37 -9 -68t11.5 -54.5t42 -37.5t82.5 -19l-61 -251q-145 0 -228 38q-83 36 -119 102q-35 64 -35 146q0 85 23 187z" />
<glyph unicode="m" horiz-adv-x="1732" d="M143 0l248 1028q35 10 75 23.5t90 24.5t112.5 18.5t144.5 7.5q98 0 162.5 -24.5t118.5 -73.5q76 43 150.5 70.5t174.5 27.5q96 0 165 -25.5t114 -74t65.5 -115t20.5 -146.5q0 -37 -5 -77.5t-16 -81.5l-139 -582h-305l135 569q6 29 13.5 65t7.5 71q0 57 -27 99t-105 42 q-43 0 -81.5 -15.5t-69.5 -31.5q4 -16 4 -30.5v-27.5q0 -37 -5 -77.5t-15 -81.5l-140 -582h-305l135 569q6 29 13.5 65t7.5 71q0 57 -26.5 99t-104.5 42q-33 0 -55.5 -3t-53.5 -9l-198 -834h-306z" />
<glyph unicode="n" horiz-adv-x="1161" d="M143 0l248 1028q35 10 77 23.5t94 24.5t116.5 18.5t146.5 7.5q242 0 332 -140q54 -84 54 -205q0 -80 -23 -175l-139 -582h-306l136 569q12 53 19 103q3 24 3 45q0 24 -4 44q-8 38 -38 61.5t-91 23.5q-59 0 -121 -12l-198 -834h-306z" />
<glyph unicode="o" horiz-adv-x="1175" d="M205 416q0 123 40 245.5t117.5 221t190.5 161t258 62.5q106 0 187 -33t133.5 -92t79 -140t26.5 -179q0 -123 -39 -246t-114.5 -221.5t-189 -161t-263.5 -62.5q-109 0 -189 33t-132 92.5t-78.5 140t-26.5 179.5zM508 446q0 -104 32.5 -160.5t118.5 -56.5q68 0 119 39 t86 98.5t52.5 130t17.5 134.5q0 104 -33 160.5t-119 56.5q-68 0 -119 -39t-85.5 -98.5t-52 -130t-17.5 -134.5z" />
<glyph unicode="p" horiz-adv-x="1183" d="M51 -379l340 1417q82 25 187.5 44.5t228.5 19.5q115 0 197.5 -35t136 -95.5t79 -141.5t25.5 -173q0 -150 -49 -273.5t-136 -213.5t-209 -140t-265 -50q-70 0 -140 12l-92 -371h-303zM502 238q35 -8 86 -9q80 0 145.5 30t111.5 83t71.5 128t25.5 165q0 88 -39 149.5 t-135 61.5q-66 0 -123 -12z" />
<glyph unicode="q" horiz-adv-x="1173" d="M205 418q0 160 54 286.5t149.5 215t222.5 135.5t274 47q190 0 391 -80l-333 -1401h-306l95 397q-88 -38 -180 -38h-5q-78 0 -144.5 29.5t-114.5 86t-75.5 138.5t-27.5 184zM508 440q0 -117 39 -162t106 -45q39 0 77 11.5t85 40.5l135 557q-20 4 -42.5 6t-45.5 2 q-84 0 -149.5 -33t-111.5 -89t-69.5 -131t-23.5 -157z" />
<glyph unicode="r" horiz-adv-x="872" d="M143 0l246 1020q88 29 197.5 54.5t247.5 25.5q51 0 113.5 -9.5t109.5 -29.5l-90 -246q-41 10 -81 19.5t-118 9.5q-29 0 -63.5 -5t-57.5 -12l-198 -827h-306z" />
<glyph unicode="s" horiz-adv-x="989" d="M102 66l105 233q35 -20 109 -51t178 -31t143 29.5t39 64.5q0 23 -7 39.5t-23.5 30.5t-45.5 29.5t-74 35.5q-51 23 -96 48.5t-79 61.5t-53 82t-19 110q0 166 121.5 262t355.5 96q111 0 196.5 -24.5t139.5 -53.5l-105 -227q-43 23 -110.5 43t-133.5 20q-29 0 -56.5 -3 t-51 -13t-37.5 -28.5t-14 -49.5q0 -41 31.5 -61.5t89.5 -49.5q80 -39 133 -71.5t84.5 -69.5t46 -84t14.5 -110q0 -66 -27.5 -129.5t-87 -112.5t-151.5 -80t-221 -31q-80 0 -144.5 10.5t-114 26t-83 31.5t-52.5 27z" />
<glyph unicode="t" horiz-adv-x="851" d="M207 299q4 94 29 199l200 848l318 49l-78 -318h340l-62 -250h-338l-90 -376q-12 -47 -15 -88q-1 -7 0 -15q1 -32 11 -56q14 -30 46 -46.5t89 -16.5q49 0 95.5 9.5t93.5 25.5l22 -233q-61 -23 -132.5 -39.5t-170.5 -16.5q-141 0 -219 42t-110 115q-29 65 -29 147v20z" />
<glyph unicode="u" horiz-adv-x="1189" d="M206 303v21q0 84 21 172l140 581h305l-137 -577q-9 -44 -17 -92q-4 -26 -4 -51q0 -19 2 -37q6 -40 31.5 -65.5t81.5 -25.5q47 0 87 17.5t85 60.5q4 51 13 107.5t20 103.5l133 559h305l-146 -612q-20 -82 -20 -164q0 -35 4 -71q12 -118 47 -210l-272 -38q-23 45 -43 100 q-57 -45 -129 -76t-160 -31q-121 0 -195.5 44t-111.5 117t-40 167z" />
<glyph unicode="v" horiz-adv-x="1118" d="M256 1077h315q2 -80 6.5 -175t10.5 -191.5t14 -187.5t19 -165q45 61 101 151.5t109.5 189t98.5 197.5t69 181h324q-53 -145 -132 -298.5t-169 -297t-183.5 -268.5t-172.5 -213h-254q-53 205 -96.5 478.5t-59.5 598.5z" />
<glyph unicode="w" horiz-adv-x="1671" d="M266 1077h299q0 -74 1 -160t4 -178t9.5 -188t16.5 -191q51 86 101.5 179.5t96.5 186.5t86 183t70 168h254q0 -147 5 -329.5t26 -387.5q55 92 102 189.5t87 192t72 180.5t56 155h324q-41 -102 -97.5 -230t-130 -269.5t-165.5 -289t-205 -288.5h-238q-25 168 -37 321.5 t-16 309.5q-39 -84 -87 -171t-98 -170t-97.5 -158t-83.5 -132h-238q-16 86 -33.5 205t-34 259t-30 297t-19.5 316z" />
<glyph unicode="x" horiz-adv-x="1124" d="M41 0l489 549l-264 528h309l156 -336q70 86 132.5 168t119.5 168h305q-86 -123 -188.5 -255t-237.5 -275q82 -154 139.5 -284t102.5 -263h-301q-37 100 -74 184t-72 156l-297 -340h-319z" />
<glyph unicode="y" horiz-adv-x="1089" d="M-51 -330l92 240q41 -16 73.5 -25.5t80.5 -9.5q72 0 136 46t107 124q-53 205 -97 459t-60 573h313q2 -80 8 -176t14.5 -193.5t18.5 -189.5t22 -166q98 147 173 327.5t141 397.5h323q-57 -156 -115.5 -291t-122 -256.5t-131 -232.5t-143.5 -219q-55 -80 -114.5 -162 t-132 -147.5t-161.5 -106.5t-206 -41q-72 0 -123 13.5t-96 35.5z" />
<glyph unicode="z" horiz-adv-x="1077" d="M123 0l43 178q59 74 136 160t160 174t166 169t154 146h-438l62 250h835l-49 -203q-47 -41 -123 -111.5t-164 -155.5t-180 -178t-172 -179h500l-62 -250h-868z" />
<glyph unicode="{" horiz-adv-x="759" d="M164 492l59 241q63 0 108.5 14.5t76 40t48 60.5t27.5 78l74 309q23 92 61.5 161.5t102 116t157 69t222.5 22.5h41l-60 -244h-51q-49 0 -83 -11.5t-56.5 -33t-35.5 -52t-21 -71.5l-64 -272q-12 -53 -28.5 -98.5t-45 -84.5t-72.5 -69.5t-112 -55.5q72 -33 98.5 -79 t26.5 -105q0 -47 -16 -123l-64 -272q-12 -49 -12 -82q0 -47 28.5 -66.5t98.5 -19.5h51l-59 -244h-41q-180 0 -275.5 56.5t-95.5 205.5q0 61 22 162l60 254q20 98 -16.5 145.5t-153.5 47.5z" />
<glyph unicode="|" horiz-adv-x="622" d="M61 -379l476 1983h290l-477 -1983h-289z" />
<glyph unicode="}" horiz-adv-x="759" d="M-104 -379l59 244h51q51 0 84 10t54.5 31.5t35 53.5t23.5 73l63 272q12 53 28.5 98.5t45.5 84t73 69.5t111 55q-72 33 -98.5 79t-26.5 106q0 47 17 123l63 272q12 49 13 82q0 47 -29 66.5t-98 19.5h-52l60 244h41q180 0 275 -56.5t95 -206.5q0 -61 -22 -161l-60 -254 q-20 -98 17 -145.5t153 -47.5l-59 -241q-63 0 -108.5 -14.5t-76 -40t-48 -60.5t-27.5 -78l-74 -309q-23 -92 -61.5 -162t-102 -116t-156.5 -68.5t-222 -22.5h-41z" />
<glyph unicode="~" d="M168 465q23 70 56.5 135.5t81.5 115.5t110.5 79.5t142.5 29.5q68 0 118 -24.5t91 -53t79 -53t83 -24.5q18 0 33.5 4t32 19.5t35 45t42.5 80.5l195 -57q-29 -78 -64 -143.5t-81 -113.5t-106 -76t-140 -28q-68 0 -118 25t-91 53.5t-79 53t-83 24.5q-18 0 -33.5 -4 t-32 -19.5t-35 -45t-43.5 -80.5z" />
<glyph unicode="&#xa1;" horiz-adv-x="604" d="M66 -358l88 366q41 166 87 309.5t101 290.5h233q-16 -147 -36.5 -290.5t-59.5 -309.5l-88 -366h-325zM330 877q0 47 17.5 85.5t46 66.5t65.5 43t76 15q72 0 119.5 -39.5t47.5 -125.5q0 -47 -17 -86t-46 -67t-65.5 -43t-75.5 -15q-72 0 -120 40t-48 126z" />
<glyph unicode="&#xa2;" d="M299 479q0 131 37 238.5t101.5 189.5t150.5 137.5t184 79.5l72 295h291l-70 -286q90 -18 170 -64l-102 -235q-49 29 -100.5 41t-98.5 12q-70 0 -128 -26.5t-101 -77t-67 -125t-24 -169.5q0 -102 52.5 -140t126.5 -38q59 0 124.5 16.5t118.5 37.5l23 -244 q-59 -25 -123 -40t-125 -22l-68 -282h-290l75 315q-104 43 -166.5 135t-62.5 252z" />
<glyph unicode="&#xa3;" d="M213 0q74 166 120 303t77 254h-179l60 250h178l51 197q33 123 89.5 207.5t128 137t154.5 75t173 22.5q92 0 171 -20.5t144 -55.5l-123 -225q-104 47 -194 47q-43 0 -83 -10.5t-74 -38t-61.5 -76.5t-45.5 -127l-33 -133h360l-59 -250h-360q-18 -72 -45 -152.5t-54 -148.5 h555l-61 -256h-889z" />
<glyph unicode="&#xa4;" d="M195 385l147 141q-43 80 -43 185q0 104 43 184l-147 141l192 189l152 -148q43 20 88 28.5t98 8.5q51 0 97 -8t91 -29l152 146l190 -187l-147 -141q43 -80 43 -184t-43 -185l147 -141l-192 -188l-152 147q-43 -20 -88 -28.5t-98 -8.5q-51 0 -97 8.5t-91 28.5l-152 -145z M557 711q0 -88 49 -133.5t119 -45.5t119 45.5t49 133.5t-49 133t-119 45t-119 -45t-49 -133z" />
<glyph unicode="&#xa5;" d="M172 227l47 195h297l33 141h-297l47 195h236q-33 84 -65 171t-60.5 173t-52 167t-40.5 150h320q14 -74 30.5 -138t35 -127.5t39.5 -130t46 -140.5q104 135 196.5 259t191.5 277h331q-115 -172 -237.5 -331.5t-261.5 -329.5h206l-47 -195h-299l-32 -141h299l-48 -195h-299 l-55 -227h-319l55 227h-297z" />
<glyph unicode="&#xa6;" horiz-adv-x="618" d="M49 -379l195 813h291l-197 -813h-289zM328 791l196 813h291l-197 -813h-290z" />
<glyph unicode="&#xa7;" horiz-adv-x="1054" d="M74 -135l104 217q125 -70 252 -70q117 0 167 39t50 86q0 33 -19.5 62.5t-54.5 46.5l-153 78q-104 51 -148.5 119.5t-44.5 144.5q0 98 66.5 195.5t177.5 162.5q-57 70 -57 166q0 63 30.5 124.5t90 109t149.5 77t209 29.5q92 0 179 -20.5t157 -61.5l-105 -217 q-63 33 -122.5 47t-112.5 14q-86 0 -136.5 -30.5t-50.5 -77.5q0 -23 16.5 -44.5t57.5 -41.5l154 -78q104 -53 148 -121.5t44 -144.5q0 -98 -61.5 -190.5t-159.5 -151.5l-27 -17q61 -84 62 -172q0 -68 -29.5 -134t-91 -118.5t-157 -85t-224.5 -32.5q-98 0 -196.5 22.5 t-163.5 67.5zM498 637q0 -41 29.5 -81t101.5 -75l88 -43q61 41 98 88t37 99q0 45 -35 88t-112 80l-72 32q-59 -39 -97 -88t-38 -100z" />
<glyph unicode="&#xa8;" horiz-adv-x="1097" d="M473 1368q0 39 14.5 72t38 55.5t53 35.5t60.5 13q57 0 98 -38t41 -101q0 -37 -15 -68.5t-40 -55.5t-54.5 -36t-58.5 -12q-59 0 -98 37t-39 98zM928 1368q0 39 14 72t38 55.5t53.5 35.5t60.5 13q57 0 98 -38t41 -101q0 -37 -15.5 -68.5t-40 -55.5t-54 -36t-58.5 -12 q-59 0 -98 37t-39 98z" />
<glyph unicode="&#xa9;" horiz-adv-x="1630" d="M246 711q0 174 58 312t156.5 233.5t227.5 145.5t270 50t270.5 -50t227.5 -145.5t156.5 -233.5t58.5 -312q0 -176 -58.5 -313.5t-156.5 -232.5t-227 -145.5t-271 -50.5q-141 0 -270 50.5t-227.5 145.5t-156.5 232.5t-58 313.5zM455 711q0 -123 36.5 -223.5t103.5 -172 t160 -110.5t203 -39q111 0 204 39t160 110.5t103.5 172t36.5 223.5t-36.5 223t-103.5 172t-160 110.5t-204 38.5t-203.5 -38.5t-159.5 -110.5t-103.5 -172t-36.5 -223zM616 715q0 186 101.5 285.5t267.5 99.5q98 0 158.5 -26.5t83.5 -39.5l-66 -180q-29 16 -64.5 27.5 t-92.5 11.5q-68 0 -109 -43t-41 -125q0 -37 6 -70.5t22.5 -59.5t45 -42t76.5 -16q61 0 102 14t74 27l57 -187q-27 -14 -90.5 -38.5t-155.5 -24.5q-184 0 -279.5 100t-95.5 287z" />
<glyph unicode="&#xaa;" horiz-adv-x="966" d="M307 956q0 86 33 174.5t99.5 159t168 115.5t240.5 45q59 0 124.5 -8t137.5 -29l-90 -381q-20 -70 -26.5 -163t38.5 -210l-192 -26l-27 63q-109 -72 -215 -71q-72 0 -126 26.5t-91 72.5t-55.5 106.5t-18.5 125.5zM524 979q0 -61 32 -105.5t97 -44.5q29 0 59.5 10.5 t63.5 34.5q-2 6 -2 29q0 31 6 63.5t15 63.5l55 213q-14 2 -27.5 3t-25.5 1q-55 0 -104.5 -18.5t-87.5 -54t-59.5 -85t-21.5 -110.5z" />
<glyph unicode="&#xab;" horiz-adv-x="1255" d="M205 594l448 479l193 -115l-322 -403l148 -371l-203 -94zM758 594l448 479l193 -115l-322 -403l148 -371l-203 -94z" />
<glyph unicode="&#xac;" d="M207 584l61 256h963l-178 -736h-279l117 480h-684z" />
<glyph unicode="&#xad;" horiz-adv-x="702" d="M150 473l67 277h594l-68 -277h-593z" />
<glyph unicode="&#xae;" horiz-adv-x="1630" d="M246 711q0 174 58 312t156.5 233.5t227.5 145.5t270 50t270.5 -50t227.5 -145.5t156.5 -233.5t58.5 -312q0 -176 -58.5 -313.5t-156.5 -232.5t-227 -145.5t-271 -50.5q-141 0 -270 50.5t-227.5 145.5t-156.5 232.5t-58 313.5zM455 711q0 -123 36.5 -223.5t103.5 -172 t160 -110.5t203 -39q111 0 204 39t160 110.5t103.5 172t36.5 223.5t-36.5 223t-103.5 172t-160 110.5t-204 38.5t-203.5 -38.5t-159.5 -110.5t-103.5 -172t-36.5 -223zM688 362v701q70 16 130.5 23.5t105.5 7.5q330 0 329 -248q0 -127 -110 -187q23 -35 40 -66.5t34.5 -65 t35 -73.5t39.5 -92h-209q-35 78 -59.5 136.5t-54.5 105.5h-82v-242h-199zM887 764h37q66 0 98.5 17.5t32.5 66.5q0 41 -29 57.5t-84 16.5q-14 0 -29.5 -1t-25.5 -3v-154z" />
<glyph unicode="&#xaf;" horiz-adv-x="770" d="M350 1276l51 209h660l-51 -209h-660z" />
<glyph unicode="&#xb0;" horiz-adv-x="782" d="M328 1282q0 74 26.5 132.5t71.5 100t105 63t126 21.5t126.5 -21.5t105.5 -63t71.5 -100t26.5 -132.5t-26.5 -132t-71.5 -100t-105.5 -63.5t-126.5 -21.5t-126 21.5t-105 63.5t-71.5 100t-26.5 132zM545 1282q0 -53 34.5 -85t77.5 -32t78 32t35 85t-35 85t-78 32 t-77.5 -32t-34.5 -85z" />
<glyph unicode="&#xb1;" d="M90 0l60 256h962l-61 -256h-961zM252 676l61 256h342l84 348h279l-84 -348h342l-62 -256h-342l-84 -348h-278l84 348h-342z" />
<glyph unicode="&#xb2;" horiz-adv-x="743" d="M213 618q0 63 12.5 118t43 104t85 95t140.5 93q59 33 94 56.5t53 43t23.5 37t5.5 37.5q0 27 -20.5 43.5t-55.5 16.5q-39 0 -91 -22.5t-118 -76.5l-76 162q139 127 326 127q51 0 97 -12.5t81 -38t55.5 -66.5t20.5 -96q0 -94 -49 -164.5t-160 -140.5q-29 -18 -57.5 -35.5 t-54 -35t-44 -32t-24.5 -26.5h342l-41 -187h-588z" />
<glyph unicode="&#xb3;" horiz-adv-x="743" d="M188 668l82 172q39 -25 98.5 -40.5t118.5 -15.5q66 0 115 22.5t49 76.5q0 35 -30.5 54t-98.5 19h-104l41 158h92q66 0 101.5 31t35.5 65q0 25 -22.5 43.5t-71.5 18.5q-51 0 -104.5 -16.5t-94.5 -36.5l-43 163q51 27 132 48.5t163 21.5q49 0 93 -9t78 -33t53.5 -61.5 t19.5 -93.5q0 -59 -35 -116.5t-100 -85.5q45 -23 71.5 -69t26.5 -101q0 -78 -33 -131.5t-86 -87t-121.5 -49t-138.5 -15.5q-82 0 -157.5 17.5t-129.5 50.5z" />
<glyph unicode="&#xb4;" horiz-adv-x="585" d="M362 1307l328 329l158 -178l-363 -278z" />
<glyph unicode="&#xb5;" horiz-adv-x="1294" d="M72 -340l338 1417h305l-137 -577q-8 -35 -15.5 -73t-9.5 -73q0 -53 27.5 -89t111.5 -36q27 0 70 13.5t102 66.5q4 51 13.5 107.5t19.5 101.5l133 559h305l-145 -612q-10 -41 -17.5 -75t-7.5 -60q0 -47 27 -70.5t98 -28.5l-100 -254q-129 2 -194.5 32t-94.5 73 q-68 -49 -142.5 -78t-146.5 -29q-45 0 -88 13.5t-80 42.5q0 -47 -6 -92t-24 -121l-39 -158h-303z" />
<glyph unicode="&#xb6;" horiz-adv-x="1454" d="M317 895q0 131 61.5 232.5t173.5 169t270.5 102.5t355.5 35q94 0 192 -8.5t209 -26.5l-426 -1778h-291l377 1571q-29 4 -70 6t-86 2l-378 -1579h-291l219 907q-147 23 -231.5 115t-84.5 252z" />
<glyph unicode="&#xb7;" horiz-adv-x="495" d="M201 581q0 47 17.5 86t46 66.5t65.5 43t76 15.5q72 0 120 -40t48 -126q0 -47 -17.5 -86t-46.5 -66.5t-65.5 -43t-75.5 -15.5q-72 0 -120 40t-48 126z" />
<glyph unicode="&#xb8;" horiz-adv-x="667" d="M-2 -406l74 168q27 -12 60.5 -20t64.5 -8q82 0 82 49q0 23 -14.5 37t-47.5 28l-41 19q12 20 30.5 46t36 51.5t33 46t21.5 30.5h192q-8 -10 -20 -27.5t-25.5 -36t-26 -37t-20.5 -30.5q45 -23 67.5 -59.5t22.5 -77.5q0 -59 -23.5 -102.5t-63.5 -71t-91 -40t-104 -12.5 q-55 0 -109.5 12.5t-97.5 34.5z" />
<glyph unicode="&#xb9;" horiz-adv-x="743" d="M319 1266q98 35 187.5 79t161.5 91h178l-195 -818h-233l131 553q-45 -23 -93 -42t-114 -42z" />
<glyph unicode="&#xba;" horiz-adv-x="899" d="M287 918q0 102 29.5 198t88 171t146.5 120t203 45q160 0 234.5 -78t74.5 -213q0 -102 -29.5 -198.5t-88 -171t-146.5 -119.5t-203 -45q-160 0 -234.5 78t-74.5 213zM500 934q0 -39 10 -62.5t26.5 -37t38 -18.5t43.5 -5q45 0 87.5 25.5t74 70.5t51 105.5t19.5 132.5 q0 37 -10.5 61.5t-26.5 37.5t-37.5 18.5t-44.5 5.5q-45 0 -87 -25.5t-73.5 -71t-51 -106.5t-19.5 -131z" />
<glyph unicode="&#xbb;" horiz-adv-x="1255" d="M125 205l321 403l-147 371l203 94l264 -504l-449 -479zM676 205l321 403l-147 371l203 94l264 -504l-449 -479z" />
<glyph unicode="&#xbc;" horiz-adv-x="1798" d="M280 1266q98 35 187.5 79t161.5 91h178l-195 -818h-233l131 553q-45 -23 -93 -42t-114 -42zM352 0l1071 1419h278l-1067 -1419h-282zM1054 185l31 141q113 145 236.5 267t277.5 229h191l-115 -473h94l-39 -164h-94l-45 -183h-193l45 183h-389zM1296 349h186l64 260 q-63 -55 -130 -122t-120 -138z" />
<glyph unicode="&#xbd;" horiz-adv-x="1798" d="M301 1266q98 35 187.5 79t161.5 91h178l-195 -818h-233l131 553q-45 -23 -93 -42t-114 -42zM316 0l1071 1419h278l-1067 -1419h-282zM1163 4q0 63 12.5 118t43 104t85 95t140.5 93q59 33 94 56.5t53 43t23.5 37t5.5 37.5q0 27 -20.5 43.5t-55.5 16.5q-39 0 -91 -22.5 t-118 -76.5l-76 162q139 127 326 127q51 0 97 -12.5t81 -38t55.5 -66.5t20.5 -96q0 -94 -49 -164.5t-160 -140.5q-29 -18 -57.5 -35.5t-54 -35t-44 -32t-24.5 -26.5h342l-41 -187h-588z" />
<glyph unicode="&#xbe;" horiz-adv-x="1798" d="M170 668l82 172q39 -25 98.5 -40.5t118.5 -15.5q66 0 115 22.5t49 76.5q0 35 -30.5 54t-98.5 19h-104l41 158h92q66 0 101.5 31t35.5 65q0 25 -22.5 43.5t-71.5 18.5q-51 0 -104.5 -16.5t-94.5 -36.5l-43 163q51 27 132 48.5t163 21.5q49 0 93 -9t78 -33t53.5 -61.5 t19.5 -93.5q0 -59 -35 -116.5t-100 -85.5q45 -23 71.5 -69t26.5 -101q0 -78 -33 -131.5t-86 -87t-121.5 -49t-138.5 -15.5q-82 0 -157.5 17.5t-129.5 50.5zM393 0l1071 1419h278l-1067 -1419h-282zM1095 185l31 141q113 145 236.5 267t277.5 229h191l-115 -473h94l-39 -164 h-94l-45 -183h-193l45 183h-389zM1337 349h186l64 260q-63 -55 -130 -122t-120 -138z" />
<glyph unicode="&#xbf;" horiz-adv-x="880" d="M82 -102q0 88 27.5 153.5t70.5 116.5t97.5 93t107.5 83q78 57 136.5 116.5t66.5 137.5h270q0 -66 -19.5 -118t-53 -98t-79 -86t-96.5 -79q-61 -47 -105 -82t-71.5 -64.5t-40 -55t-12.5 -56.5q0 -53 38 -77.5t95 -24.5q37 0 78 7t80 18.5t73.5 25.5t61.5 31l39 -240 q-80 -45 -183.5 -72.5t-207.5 -27.5q-74 0 -141.5 18t-119 56t-82 94.5t-30.5 130.5zM584 866q0 47 17 86t46 67t66 43t75 15q72 0 120 -40t48 -126q0 -47 -17 -86t-46 -66.5t-65.5 -43t-75.5 -15.5q-72 0 -120 40t-48 126z" />
<glyph unicode="&#xc0;" horiz-adv-x="1462" d="M31 0q133 229 248.5 423.5t223 367t210 327t209.5 301.5h284q33 -147 62.5 -326.5t55.5 -366.5t47.5 -374.5t39.5 -351.5h-321q-4 80 -9.5 156.5t-11.5 152.5h-532q-41 -76 -86 -152.5t-86 -156.5h-334zM666 559h383q-10 125 -22 266.5t-32 282.5q-86 -137 -169 -272.5 t-160 -276.5zM761 1803l189 161l276 -338l-131 -118z" />
<glyph unicode="&#xc1;" horiz-adv-x="1462" d="M31 0q133 229 248.5 423.5t223 367t210 327t209.5 301.5h284q33 -147 62.5 -326.5t55.5 -366.5t47.5 -374.5t39.5 -351.5h-321q-4 80 -9.5 156.5t-11.5 152.5h-532q-41 -76 -86 -152.5t-86 -156.5h-334zM666 559h383q-10 125 -22 266.5t-32 282.5q-86 -137 -169 -272.5 t-160 -276.5zM851 1635l328 329l158 -178l-363 -278z" />
<glyph unicode="&#xc2;" horiz-adv-x="1462" d="M31 0q133 229 248.5 423.5t223 367t210 327t209.5 301.5h284q33 -147 62.5 -326.5t55.5 -366.5t47.5 -374.5t39.5 -351.5h-321q-4 80 -9.5 156.5t-11.5 152.5h-532q-41 -76 -86 -152.5t-86 -156.5h-334zM666 559h383q-10 125 -22 266.5t-32 282.5q-86 -137 -169 -272.5 t-160 -276.5zM792 1649l367 297l252 -318l-123 -108l-170 159l-231 -155z" />
<glyph unicode="&#xc3;" horiz-adv-x="1462" d="M31 0q133 229 248.5 423.5t223 367t210 327t209.5 301.5h284q33 -147 62.5 -326.5t55.5 -366.5t47.5 -374.5t39.5 -351.5h-321q-4 80 -9.5 156.5t-11.5 152.5h-532q-41 -76 -86 -152.5t-86 -156.5h-334zM666 559h383q-10 125 -22 266.5t-32 282.5q-86 -137 -169 -272.5 t-160 -276.5zM746 1649q47 111 112.5 165t159.5 54q39 0 70 -11.5t57.5 -24.5t51 -24.5t49.5 -11.5q29 0 52 16.5t58 67.5l137 -84q-47 -111 -112.5 -165t-159.5 -54q-39 0 -69.5 11.5t-57.5 24.5t-51.5 24.5t-48.5 11.5q-29 0 -52.5 -16.5t-58.5 -67.5z" />
<glyph unicode="&#xc4;" horiz-adv-x="1462" d="M31 0q133 229 248.5 423.5t223 367t210 327t209.5 301.5h284q33 -147 62.5 -326.5t55.5 -366.5t47.5 -374.5t39.5 -351.5h-321q-4 80 -9.5 156.5t-11.5 152.5h-532q-41 -76 -86 -152.5t-86 -156.5h-334zM666 559h383q-10 125 -22 266.5t-32 282.5q-86 -137 -169 -272.5 t-160 -276.5zM747 1696q0 39 14.5 72t38 55.5t53 35.5t60.5 13q57 0 98 -38t41 -101q0 -37 -15 -68.5t-40 -55.5t-54.5 -36t-58.5 -12q-59 0 -98 37t-39 98zM1202 1696q0 39 14 72t38 55.5t53.5 35.5t60.5 13q57 0 98 -38t41 -101q0 -37 -15.5 -68.5t-40 -55.5t-54 -36 t-58.5 -12q-59 0 -98 37t-39 98z" />
<glyph unicode="&#xc5;" horiz-adv-x="1462" d="M31 0q131 225 245.5 417.5t220 363.5t207 323.5t205.5 298.5q-29 31 -46 73t-17 95q0 57 19.5 101t53 74t76.5 45t90 15q49 0 93.5 -15t78 -45t53 -74t19.5 -101q0 -72 -31.5 -123t-80.5 -80q31 -147 59.5 -320.5t53 -353.5t45 -358t36.5 -336h-321q-4 80 -9.5 156.5 t-11.5 152.5h-532q-41 -76 -86 -152.5t-86 -156.5h-334zM666 559h383q-10 125 -22 266.5t-32 282.5q-86 -137 -169 -272.5t-160 -276.5zM993 1571q0 -47 29 -71.5t63 -24.5q37 0 66 24.5t29 71.5t-29 71.5t-66 24.5q-35 0 -63.5 -24.5t-28.5 -71.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="2000" d="M0 0q172 221 319.5 409.5t289 357.5t283.5 328.5t300 323.5h1014l-66 -268h-606l-66 -279h535l-64 -262h-534l-82 -342h653l-65 -268h-953l82 336h-458q-63 -78 -131 -168t-123 -168h-328zM770 590h330l127 528q-43 -47 -101.5 -113.5t-121 -139t-124 -145.5t-110.5 -130 z" />
<glyph unicode="&#xc7;" horiz-adv-x="1306" d="M252 573q0 160 56.5 318t166 281.5t268 201.5t365.5 78q98 0 193.5 -22.5t199.5 -79.5l-115 -248q-86 45 -153.5 61.5t-145.5 16.5q-117 0 -210 -47.5t-159.5 -128t-101 -187t-34.5 -225.5q0 -180 78.5 -262t226.5 -82q98 0 181 20.5t155 53.5l34 -261 q-84 -41 -183 -64.5t-245 -25.5q-12 -16 -22 -32.5t-19 -28.5q45 -23 68 -59.5t23 -77.5q0 -59 -24 -102.5t-63.5 -71t-91 -40t-104.5 -12.5q-55 0 -109.5 12.5t-97.5 34.5l74 168q27 -12 60.5 -20t64.5 -8q82 0 82 49q0 23 -14.5 37t-47.5 28l-41 19l84 125 q-186 47 -292.5 199.5t-106.5 381.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="1216" d="M154 0l340 1419h927l-65 -262h-608l-66 -287h537l-64 -258h-537l-81 -348h655l-66 -264h-972zM700 1803l189 161l276 -338l-131 -118z" />
<glyph unicode="&#xc9;" horiz-adv-x="1216" d="M154 0l340 1419h927l-65 -262h-608l-66 -287h537l-64 -258h-537l-81 -348h655l-66 -264h-972zM835 1635l328 329l158 -178l-363 -278z" />
<glyph unicode="&#xca;" horiz-adv-x="1216" d="M154 0l340 1419h927l-65 -262h-608l-66 -287h537l-64 -258h-537l-81 -348h655l-66 -264h-972zM720 1649l367 297l252 -318l-123 -108l-170 159l-231 -155z" />
<glyph unicode="&#xcb;" horiz-adv-x="1216" d="M154 0l340 1419h927l-65 -262h-608l-66 -287h537l-64 -258h-537l-81 -348h655l-66 -264h-972zM625 1696q0 39 14.5 72t38 55.5t53 35.5t60.5 13q57 0 98 -38t41 -101q0 -37 -15 -68.5t-40 -55.5t-54.5 -36t-58.5 -12q-59 0 -98 37t-39 98zM1080 1696q0 39 14 72t38 55.5 t53.5 35.5t60.5 13q57 0 98 -38t41 -101q0 -37 -15.5 -68.5t-40 -55.5t-54 -36t-58.5 -12q-59 0 -98 37t-39 98z" />
<glyph unicode="&#xcc;" horiz-adv-x="638" d="M154 0l340 1419h319l-342 -1419h-317zM348 1803l189 161l276 -338l-131 -118z" />
<glyph unicode="&#xcd;" horiz-adv-x="638" d="M154 0l340 1419h319l-342 -1419h-317zM514 1635l328 329l158 -178l-363 -278z" />
<glyph unicode="&#xce;" horiz-adv-x="638" d="M154 0l340 1419h319l-342 -1419h-317zM405 1649l367 297l252 -318l-123 -108l-170 159l-231 -155z" />
<glyph unicode="&#xcf;" horiz-adv-x="638" d="M154 0l340 1419h319l-342 -1419h-317zM342 1696q0 39 14.5 72t38 55.5t53 35.5t60.5 13q57 0 98 -38t41 -101q0 -37 -15 -68.5t-40 -55.5t-54.5 -36t-58.5 -12q-59 0 -98 37t-39 98zM797 1696q0 39 14 72t38 55.5t53.5 35.5t60.5 13q57 0 98 -38t41 -101 q0 -37 -15.5 -68.5t-40 -55.5t-54 -36t-58.5 -12q-59 0 -98 37t-39 98z" />
<glyph unicode="&#xd0;" horiz-adv-x="1470" d="M133 618l53 236h166l131 541q117 23 224.5 32t193.5 9q152 0 271.5 -41t202.5 -117t126 -180.5t43 -231.5q0 -207 -67.5 -370.5t-193.5 -277.5t-306 -175t-408 -61q-86 0 -198.5 10t-216.5 33l143 593h-164zM526 256q23 -2 46.5 -3t64.5 -1q133 0 240.5 41t182.5 117.5 t116 188.5t41 251q0 72 -18.5 130t-60.5 100t-108.5 64.5t-161.5 22.5q-61 0 -125 -6l-73 -307h260l-56 -236h-262z" />
<glyph unicode="&#xd1;" horiz-adv-x="1519" d="M154 0l340 1419h243q47 -72 101.5 -169t112 -209.5t114.5 -234.5t109 -241l202 854h318l-342 -1419h-240q-86 242 -191.5 483.5t-228.5 456.5l-225 -940h-313zM776 1649q47 111 112.5 165t159.5 54q39 0 70 -11.5t57.5 -24.5t51 -24.5t49.5 -11.5q29 0 52 16.5t58 67.5 l137 -84q-47 -111 -112.5 -165t-159.5 -54q-39 0 -69.5 11.5t-57.5 24.5t-51.5 24.5t-48.5 11.5q-29 0 -52.5 -16.5t-58.5 -67.5z" />
<glyph unicode="&#xd2;" horiz-adv-x="1550" d="M252 567q0 160 55 317.5t158.5 283.5t253 205t338.5 79q125 0 229.5 -41t180 -117.5t117.5 -187.5t42 -250q0 -160 -54 -318.5t-157.5 -284.5t-253.5 -204t-340 -78q-125 0 -229.5 41t-180 118t-117.5 187.5t-42 249.5zM580 590q0 -154 64.5 -249t209.5 -95q94 0 175 49 t141.5 131t94 187.5t33.5 220.5q0 154 -64.5 249t-209.5 95q-94 0 -175 -49.5t-141.5 -131.5t-94 -188.5t-33.5 -218.5zM897 1803l189 161l276 -338l-131 -118z" />
<glyph unicode="&#xd3;" horiz-adv-x="1550" d="M252 567q0 160 55 317.5t158.5 283.5t253 205t338.5 79q125 0 229.5 -41t180 -117.5t117.5 -187.5t42 -250q0 -160 -54 -318.5t-157.5 -284.5t-253.5 -204t-340 -78q-125 0 -229.5 41t-180 118t-117.5 187.5t-42 249.5zM580 590q0 -154 64.5 -249t209.5 -95q94 0 175 49 t141.5 131t94 187.5t33.5 220.5q0 154 -64.5 249t-209.5 95q-94 0 -175 -49.5t-141.5 -131.5t-94 -188.5t-33.5 -218.5zM849 1635l328 329l158 -178l-363 -278z" />
<glyph unicode="&#xd4;" horiz-adv-x="1550" d="M252 567q0 160 55 317.5t158.5 283.5t253 205t338.5 79q125 0 229.5 -41t180 -117.5t117.5 -187.5t42 -250q0 -160 -54 -318.5t-157.5 -284.5t-253.5 -204t-340 -78q-125 0 -229.5 41t-180 118t-117.5 187.5t-42 249.5zM580 590q0 -154 64.5 -249t209.5 -95q94 0 175 49 t141.5 131t94 187.5t33.5 220.5q0 154 -64.5 249t-209.5 95q-94 0 -175 -49.5t-141.5 -131.5t-94 -188.5t-33.5 -218.5zM790 1649l367 297l252 -318l-123 -108l-170 159l-231 -155z" />
<glyph unicode="&#xd5;" horiz-adv-x="1550" d="M252 567q0 160 55 317.5t158.5 283.5t253 205t338.5 79q125 0 229.5 -41t180 -117.5t117.5 -187.5t42 -250q0 -160 -54 -318.5t-157.5 -284.5t-253.5 -204t-340 -78q-125 0 -229.5 41t-180 118t-117.5 187.5t-42 249.5zM580 590q0 -154 64.5 -249t209.5 -95q94 0 175 49 t141.5 131t94 187.5t33.5 220.5q0 154 -64.5 249t-209.5 95q-94 0 -175 -49.5t-141.5 -131.5t-94 -188.5t-33.5 -218.5zM733 1649q47 111 112.5 165t159.5 54q39 0 70 -11.5t57.5 -24.5t51 -24.5t49.5 -11.5q29 0 52 16.5t58 67.5l137 -84q-47 -111 -112.5 -165t-159.5 -54 q-39 0 -69.5 11.5t-57.5 24.5t-51.5 24.5t-48.5 11.5q-29 0 -52.5 -16.5t-58.5 -67.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="1550" d="M252 567q0 160 55 317.5t158.5 283.5t253 205t338.5 79q125 0 229.5 -41t180 -117.5t117.5 -187.5t42 -250q0 -160 -54 -318.5t-157.5 -284.5t-253.5 -204t-340 -78q-125 0 -229.5 41t-180 118t-117.5 187.5t-42 249.5zM580 590q0 -154 64.5 -249t209.5 -95q94 0 175 49 t141.5 131t94 187.5t33.5 220.5q0 154 -64.5 249t-209.5 95q-94 0 -175 -49.5t-141.5 -131.5t-94 -188.5t-33.5 -218.5zM735 1696q0 39 14.5 72t38 55.5t53 35.5t60.5 13q57 0 98 -38t41 -101q0 -37 -15 -68.5t-40 -55.5t-54.5 -36t-58.5 -12q-59 0 -98 37t-39 98z M1190 1696q0 39 14 72t38 55.5t53.5 35.5t60.5 13q57 0 98 -38t41 -101q0 -37 -15.5 -68.5t-40 -55.5t-54 -36t-58.5 -12q-59 0 -98 37t-39 98z" />
<glyph unicode="&#xd7;" d="M182 340l350 283l-208 276l208 168l207 -276l353 284l143 -188l-350 -283l209 -276l-209 -168l-207 276l-352 -284z" />
<glyph unicode="&#xd8;" horiz-adv-x="1550" d="M207 37l145 166q-100 143 -100 364q0 160 55 317.5t158.5 283.5t253 205t338.5 79q184 0 319 -86l127 145l160 -129l-139 -159q49 -72 75.5 -164t26.5 -203q0 -160 -54 -318.5t-157.5 -284.5t-253.5 -204t-340 -78q-188 0 -323 86l-131 -149zM569 578q0 -33 2 -62t9 -55 l604 686q-60 41 -150 41q-96 0 -180 -53.5t-147.5 -139.5t-100.5 -195.5t-37 -221.5zM688 276q59 -41 154 -40q94 0 179 52t148.5 138t101.5 194.5t38 223.5q0 66 -13 121z" />
<glyph unicode="&#xd9;" horiz-adv-x="1398" d="M246 397q0 106 30 228l191 794h319l-200 -839q-20 -74 -21 -150q0 -41 9.5 -73.5t33 -57.5t63.5 -38t99 -13q123 0 186.5 76.5t98.5 218.5l209 876h319l-215 -903q-70 -289 -226.5 -417t-414.5 -128q-133 0 -224 32t-148.5 88t-83 135t-25.5 171zM718 1803l189 161 l276 -338l-131 -118z" />
<glyph unicode="&#xda;" horiz-adv-x="1398" d="M246 397q0 106 30 228l191 794h319l-200 -839q-20 -74 -21 -150q0 -41 9.5 -73.5t33 -57.5t63.5 -38t99 -13q123 0 186.5 76.5t98.5 218.5l209 876h319l-215 -903q-70 -289 -226.5 -417t-414.5 -128q-133 0 -224 32t-148.5 88t-83 135t-25.5 171zM827 1635l328 329 l158 -178l-363 -278z" />
<glyph unicode="&#xdb;" horiz-adv-x="1398" d="M246 397q0 106 30 228l191 794h319l-200 -839q-20 -74 -21 -150q0 -41 9.5 -73.5t33 -57.5t63.5 -38t99 -13q123 0 186.5 76.5t98.5 218.5l209 876h319l-215 -903q-70 -289 -226.5 -417t-414.5 -128q-133 0 -224 32t-148.5 88t-83 135t-25.5 171zM755 1649l367 297 l252 -318l-123 -108l-170 159l-231 -155z" />
<glyph unicode="&#xdc;" horiz-adv-x="1398" d="M246 397q0 106 30 228l191 794h319l-200 -839q-20 -74 -21 -150q0 -41 9.5 -73.5t33 -57.5t63.5 -38t99 -13q123 0 186.5 76.5t98.5 218.5l209 876h319l-215 -903q-70 -289 -226.5 -417t-414.5 -128q-133 0 -224 32t-148.5 88t-83 135t-25.5 171zM686 1696q0 39 14.5 72 t38 55.5t53 35.5t60.5 13q57 0 98 -38t41 -101q0 -37 -15 -68.5t-40 -55.5t-54.5 -36t-58.5 -12q-59 0 -98 37t-39 98zM1141 1696q0 39 14 72t38 55.5t53.5 35.5t60.5 13q57 0 98 -38t41 -101q0 -37 -15.5 -68.5t-40 -55.5t-54 -36t-58.5 -12q-59 0 -98 37t-39 98z" />
<glyph unicode="&#xdd;" horiz-adv-x="1335" d="M348 1419h340q33 -147 72 -288t86 -295q59 76 113.5 146.5t105.5 141t101.5 143t103.5 152.5h352q-86 -121 -166 -229.5t-159.5 -212t-163.5 -205.5t-179 -211l-135 -561h-319l135 561q-86 217 -155.5 424t-131.5 434zM792 1635l328 329l158 -178l-363 -278z" />
<glyph unicode="&#xde;" horiz-adv-x="1316" d="M152 0l340 1419h319l-49 -205q14 2 37.5 3.5t48 2.5t46.5 1h34q129 0 221 -33t152.5 -88.5t89 -127t28.5 -149.5q0 -94 -40 -193.5t-129 -181t-231 -134t-345 -52.5h-142l-63 -262h-317zM598 535h100q86 0 158 13t124 44t81 82t29 129q0 84 -72 115.5t-193 31.5 q-35 0 -69.5 -2t-59.5 -4z" />
<glyph unicode="&#xdf;" horiz-adv-x="1304" d="M-74 -348q53 49 98.5 120.5t81 151.5t61.5 163t42 155l219 921q27 117 81 198t126 132t157 73.5t173 22.5q82 0 158.5 -21.5t135 -60.5t93 -95t34.5 -126t-31.5 -139.5t-99.5 -132.5q-31 -29 -60.5 -55.5t-53 -53t-39 -55.5t-15.5 -59q0 -43 39 -75t88 -69l41 -31 q55 -41 84 -104t29 -135q0 -102 -41 -178t-108.5 -125t-154.5 -74t-179 -25q-82 0 -161 18.5t-146 55.5l108 240q59 -35 112.5 -49.5t98.5 -14.5q76 0 121 34t45 85q0 27 -16.5 54.5t-51.5 54.5l-92 67q-80 59 -104.5 114.5t-24.5 115.5q0 61 23.5 110t59.5 90t78 75 t78 67.5t59.5 68.5t23.5 78q0 47 -45 69.5t-105 22.5q-74 0 -137.5 -47t-91.5 -162l-195 -817q-70 -293 -151.5 -455.5t-180.5 -236.5z" />
<glyph unicode="&#xe0;" horiz-adv-x="1204" d="M205 418q0 141 50 266t139 218t210 146.5t262 53.5q18 0 64.5 -2t106 -9.5t125 -23.5t124.5 -45l-133 -557q-29 -117 -16.5 -234.5t47.5 -210.5l-273 -38q-12 25 -22 47t-21 51q-57 -47 -126.5 -78t-155.5 -31q-102 0 -175 36t-119 96.5t-66.5 141.5t-20.5 173zM508 440 q0 -98 32.5 -155.5t121.5 -57.5q47 0 84.5 18.5t80.5 61.5q4 51 13.5 107.5t19.5 103.5l80 324q-27 4 -48.5 6t-51.5 2q-70 0 -130.5 -35t-105.5 -91t-70.5 -130t-25.5 -154zM723 1475l189 161l276 -338l-131 -118z" />
<glyph unicode="&#xe1;" horiz-adv-x="1204" d="M205 418q0 141 50 266t139 218t210 146.5t262 53.5q18 0 64.5 -2t106 -9.5t125 -23.5t124.5 -45l-133 -557q-29 -117 -16.5 -234.5t47.5 -210.5l-273 -38q-12 25 -22 47t-21 51q-57 -47 -126.5 -78t-155.5 -31q-102 0 -175 36t-119 96.5t-66.5 141.5t-20.5 173zM508 440 q0 -98 32.5 -155.5t121.5 -57.5q47 0 84.5 18.5t80.5 61.5q4 51 13.5 107.5t19.5 103.5l80 324q-27 4 -48.5 6t-51.5 2q-70 0 -130.5 -35t-105.5 -91t-70.5 -130t-25.5 -154zM727 1307l328 329l158 -178l-363 -278z" />
<glyph unicode="&#xe2;" horiz-adv-x="1204" d="M205 418q0 141 50 266t139 218t210 146.5t262 53.5q18 0 64.5 -2t106 -9.5t125 -23.5t124.5 -45l-133 -557q-29 -117 -16.5 -234.5t47.5 -210.5l-273 -38q-12 25 -22 47t-21 51q-57 -47 -126.5 -78t-155.5 -31q-102 0 -175 36t-119 96.5t-66.5 141.5t-20.5 173zM508 440 q0 -98 32.5 -155.5t121.5 -57.5q47 0 84.5 18.5t80.5 61.5q4 51 13.5 107.5t19.5 103.5l80 324q-27 4 -48.5 6t-51.5 2q-70 0 -130.5 -35t-105.5 -91t-70.5 -130t-25.5 -154zM596 1309l367 297l252 -318l-123 -108l-170 159l-231 -155z" />
<glyph unicode="&#xe3;" horiz-adv-x="1204" d="M205 418q0 141 50 266t139 218t210 146.5t262 53.5q18 0 64.5 -2t106 -9.5t125 -23.5t124.5 -45l-133 -557q-29 -117 -16.5 -234.5t47.5 -210.5l-273 -38q-12 25 -22 47t-21 51q-57 -47 -126.5 -78t-155.5 -31q-102 0 -175 36t-119 96.5t-66.5 141.5t-20.5 173zM508 440 q0 -98 32.5 -155.5t121.5 -57.5q47 0 84.5 18.5t80.5 61.5q4 51 13.5 107.5t19.5 103.5l80 324q-27 4 -48.5 6t-51.5 2q-70 0 -130.5 -35t-105.5 -91t-70.5 -130t-25.5 -154zM569 1315q47 111 112.5 165t159.5 54q39 0 70 -11.5t57.5 -24.5t51 -24.5t49.5 -11.5 q29 0 52 16.5t58 67.5l137 -84q-47 -111 -112.5 -165t-159.5 -54q-39 0 -69.5 11.5t-57.5 24.5t-51.5 24.5t-48.5 11.5q-29 0 -52.5 -16.5t-58.5 -67.5z" />
<glyph unicode="&#xe4;" horiz-adv-x="1204" d="M205 418q0 141 50 266t139 218t210 146.5t262 53.5q18 0 64.5 -2t106 -9.5t125 -23.5t124.5 -45l-133 -557q-29 -117 -16.5 -234.5t47.5 -210.5l-273 -38q-12 25 -22 47t-21 51q-57 -47 -126.5 -78t-155.5 -31q-102 0 -175 36t-119 96.5t-66.5 141.5t-20.5 173zM508 440 q0 -98 32.5 -155.5t121.5 -57.5q47 0 84.5 18.5t80.5 61.5q4 51 13.5 107.5t19.5 103.5l80 324q-27 4 -48.5 6t-51.5 2q-70 0 -130.5 -35t-105.5 -91t-70.5 -130t-25.5 -154zM528 1368q0 39 14.5 72t38 55.5t53 35.5t60.5 13q57 0 98 -38t41 -101q0 -37 -15 -68.5t-40 -55.5 t-54.5 -36t-58.5 -12q-59 0 -98 37t-39 98zM983 1368q0 39 14 72t38 55.5t53.5 35.5t60.5 13q57 0 98 -38t41 -101q0 -37 -15.5 -68.5t-40 -55.5t-54 -36t-58.5 -12q-59 0 -98 37t-39 98z" />
<glyph unicode="&#xe5;" horiz-adv-x="1204" d="M205 418q0 141 50 266t139 218t210 146.5t262 53.5q18 0 64.5 -2t106 -9.5t125 -23.5t124.5 -45l-133 -557q-29 -117 -16.5 -234.5t47.5 -210.5l-273 -38q-12 25 -22 47t-21 51q-57 -47 -126.5 -78t-155.5 -31q-102 0 -175 36t-119 96.5t-66.5 141.5t-20.5 173zM508 440 q0 -98 32.5 -155.5t121.5 -57.5q47 0 84.5 18.5t80.5 61.5q4 51 13.5 107.5t19.5 103.5l80 324q-27 4 -48.5 6t-51.5 2q-70 0 -130.5 -35t-105.5 -91t-70.5 -130t-25.5 -154zM684 1432q0 57 19.5 101t53 73.5t76.5 45t90 15.5q49 0 93.5 -15.5t78 -45t53 -73.5t19.5 -101 t-19.5 -101.5t-53 -74t-77.5 -45t-94 -15.5q-47 0 -90 15.5t-76.5 45t-53 73.5t-19.5 102zM831 1432q0 -47 29 -72t63 -25q37 0 66 25t29 72t-29 71.5t-66 24.5q-35 0 -63.5 -24.5t-28.5 -71.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1714" d="M184 274q0 115 50.5 195t140.5 130t213 72.5t270 22.5q-14 86 -62 122t-153 36q-66 0 -133.5 -11.5t-126.5 -33.5l-23 240q72 25 158 42t172 17q117 0 217.5 -39t161.5 -139q39 43 88 76.5t102.5 56t109.5 34t110 11.5q68 0 129 -18.5t107 -56.5t74 -95t28 -133 q0 -115 -50.5 -195t-140.5 -130t-213 -72.5t-270 -22.5q14 -86 62 -122t153 -36q66 0 133.5 11.5t126.5 33.5l22 -239q-78 -27 -162.5 -43.5t-170.5 -16.5q-113 0 -211.5 38t-163.5 136q-35 -41 -83 -73.5t-102.5 -55t-111.5 -34t-113 -11.5q-68 0 -128 17.5t-107 54.5 t-75 94.5t-28 136.5zM461 311q-1 -98 110 -98q104 0 179 74.5t100 197.5q-127 -4 -204 -21.5t-118 -42t-54 -54t-13 -56.5zM1151 592q127 4 204 21.5t117.5 42t54 53t13.5 57.5q1 98 -110 98q-104 0 -179 -74.5t-100 -197.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="983" d="M205 430q0 139 45 262t129 215t203.5 145.5t267.5 53.5q92 0 164.5 -17.5t132.5 -46.5l-105 -237q-41 16 -85 29.5t-107 13.5q-154 0 -242 -103.5t-88 -281.5q0 -104 45 -169t166 -65q59 0 114.5 12.5t98.5 30.5l23 -243q-53 -20 -117 -37t-150 -21q-12 -16 -22 -32.5 t-19 -28.5q45 -23 68 -59.5t23 -77.5q0 -59 -24 -102.5t-63.5 -71t-91 -40t-104.5 -12.5q-55 0 -109.5 12.5t-97.5 34.5l74 168q27 -12 60.5 -20t64.5 -8q82 0 82 49q0 23 -14.5 37t-47.5 28l-41 19l82 123q-84 20 -143.5 62t-98 100.5t-56 129t-17.5 148.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1085" d="M205 422q0 131 46 254t130 218t204 153.5t265 58.5q72 0 133 -20.5t107.5 -59.5t72 -95.5t25.5 -127.5q0 -115 -51.5 -195t-142.5 -130t-216 -72.5t-272 -22.5q14 -86 62 -122t153 -36q66 0 133.5 11.5t126.5 33.5l23 -239q-57 -23 -146.5 -41.5t-193.5 -18.5 q-127 0 -214.5 36t-141.5 97.5t-78.5 143.5t-24.5 174zM514 592q127 4 205 21.5t121 42t57 53t14 57.5q1 98 -110 98q-104 0 -183 -74.5t-104 -197.5zM602 1475l189 161l276 -338l-131 -118z" />
<glyph unicode="&#xe9;" horiz-adv-x="1085" d="M205 422q0 131 46 254t130 218t204 153.5t265 58.5q72 0 133 -20.5t107.5 -59.5t72 -95.5t25.5 -127.5q0 -115 -51.5 -195t-142.5 -130t-216 -72.5t-272 -22.5q14 -86 62 -122t153 -36q66 0 133.5 11.5t126.5 33.5l23 -239q-57 -23 -146.5 -41.5t-193.5 -18.5 q-127 0 -214.5 36t-141.5 97.5t-78.5 143.5t-24.5 174zM514 592q127 4 205 21.5t121 42t57 53t14 57.5q1 98 -110 98q-104 0 -183 -74.5t-104 -197.5zM622 1307l328 329l158 -178l-363 -278z" />
<glyph unicode="&#xea;" horiz-adv-x="1085" d="M205 422q0 131 46 254t130 218t204 153.5t265 58.5q72 0 133 -20.5t107.5 -59.5t72 -95.5t25.5 -127.5q0 -115 -51.5 -195t-142.5 -130t-216 -72.5t-272 -22.5q14 -86 62 -122t153 -36q66 0 133.5 11.5t126.5 33.5l23 -239q-57 -23 -146.5 -41.5t-193.5 -18.5 q-127 0 -214.5 36t-141.5 97.5t-78.5 143.5t-24.5 174zM514 592q127 4 205 21.5t121 42t57 53t14 57.5q1 98 -110 98q-104 0 -183 -74.5t-104 -197.5zM561 1309l367 297l252 -318l-123 -108l-170 159l-231 -155z" />
<glyph unicode="&#xeb;" horiz-adv-x="1085" d="M205 422q0 131 46 254t130 218t204 153.5t265 58.5q72 0 133 -20.5t107.5 -59.5t72 -95.5t25.5 -127.5q0 -115 -51.5 -195t-142.5 -130t-216 -72.5t-272 -22.5q14 -86 62 -122t153 -36q66 0 133.5 11.5t126.5 33.5l23 -239q-57 -23 -146.5 -41.5t-193.5 -18.5 q-127 0 -214.5 36t-141.5 97.5t-78.5 143.5t-24.5 174zM514 592q127 4 205 21.5t121 42t57 53t14 57.5q1 98 -110 98q-104 0 -183 -74.5t-104 -197.5zM545 1368q0 39 14.5 72t38 55.5t53 35.5t60.5 13q57 0 98 -38t41 -101q0 -37 -15 -68.5t-40 -55.5t-54.5 -36t-58.5 -12 q-59 0 -98 37t-39 98zM1000 1368q0 39 14 72t38 55.5t53.5 35.5t60.5 13q57 0 98 -38t41 -101q0 -37 -15.5 -68.5t-40 -55.5t-54 -36t-58.5 -12q-59 0 -98 37t-39 98z" />
<glyph unicode="&#xec;" horiz-adv-x="579" d="M143 0l256 1077h306l-259 -1077h-303zM258 1475l189 161l276 -338l-131 -118z" />
<glyph unicode="&#xed;" horiz-adv-x="579" d="M143 0l256 1077h306l-259 -1077h-303zM401 1307l328 329l158 -178l-363 -278z" />
<glyph unicode="&#xee;" horiz-adv-x="579" d="M143 0l256 1077h306l-259 -1077h-303zM292 1309l367 297l252 -318l-123 -108l-170 159l-231 -155z" />
<glyph unicode="&#xef;" horiz-adv-x="579" d="M143 0l256 1077h306l-259 -1077h-303zM248 1368q0 39 14.5 72t38 55.5t53 35.5t60.5 13q57 0 98 -38t41 -101q0 -37 -15 -68.5t-40 -55.5t-54.5 -36t-58.5 -12q-59 0 -98 37t-39 98zM703 1368q0 39 14 72t38 55.5t53.5 35.5t60.5 13q57 0 98 -38t41 -101 q0 -37 -15.5 -68.5t-40 -55.5t-54 -36t-58.5 -12q-59 0 -98 37t-39 98z" />
<glyph unicode="&#xf0;" horiz-adv-x="1241" d="M205 412q0 166 51 278.5t132 181t180.5 99t197.5 30.5q66 0 125 -13t108 -36q-6 61 -17 115.5t-34 106.5l-241 -78l-23 170l172 55q-29 33 -64.5 65.5t-82.5 65.5l202 137q117 -78 201 -186l293 96l20 -174l-223 -72q47 -98 67.5 -207.5t20.5 -232.5q0 -174 -41 -327.5 t-124 -267.5t-207.5 -180.5t-292.5 -66.5q-100 0 -178 35t-132.5 94.5t-82 140.5t-27.5 171zM512 416q0 -74 36 -127t114 -53q74 0 129 41.5t93 109.5t59.5 153t27.5 169q-109 51 -197 51q-66 0 -115 -26.5t-81.5 -74t-49 -110t-16.5 -133.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="1161" d="M143 0l248 1028q35 10 77 23.5t94 24.5t116.5 18.5t146.5 7.5q242 0 332 -139.5t31 -380.5l-139 -582h-306l136 569q12 53 19 103.5t-1 88.5t-38 61.5t-91 23.5q-59 0 -121 -12l-198 -834h-306zM520 1315q47 111 112.5 165t159.5 54q39 0 70 -11.5t57.5 -24.5t51 -24.5 t49.5 -11.5q29 0 52 16.5t58 67.5l137 -84q-47 -111 -112.5 -165t-159.5 -54q-39 0 -69.5 11.5t-57.5 24.5t-51.5 24.5t-48.5 11.5q-29 0 -52.5 -16.5t-58.5 -67.5z" />
<glyph unicode="&#xf2;" horiz-adv-x="1175" d="M205 416q0 123 40 245.5t117.5 221t190.5 161t258 62.5q106 0 187 -33t133.5 -92t79 -140t26.5 -179q0 -123 -39 -246t-114.5 -221.5t-189 -161t-263.5 -62.5q-109 0 -189 33t-132 92.5t-78.5 140t-26.5 179.5zM508 446q0 -104 32.5 -160.5t118.5 -56.5q68 0 119 39 t86 98.5t52.5 130t17.5 134.5q0 104 -33 160.5t-119 56.5q-68 0 -119 -39t-85.5 -98.5t-52 -130t-17.5 -134.5zM559 1475l189 161l276 -338l-131 -118z" />
<glyph unicode="&#xf3;" horiz-adv-x="1175" d="M205 416q0 123 40 245.5t117.5 221t190.5 161t258 62.5q106 0 187 -33t133.5 -92t79 -140t26.5 -179q0 -123 -39 -246t-114.5 -221.5t-189 -161t-263.5 -62.5q-109 0 -189 33t-132 92.5t-78.5 140t-26.5 179.5zM508 446q0 -104 32.5 -160.5t118.5 -56.5q68 0 119 39 t86 98.5t52.5 130t17.5 134.5q0 104 -33 160.5t-119 56.5q-68 0 -119 -39t-85.5 -98.5t-52 -130t-17.5 -134.5zM661 1307l328 329l158 -178l-363 -278z" />
<glyph unicode="&#xf4;" horiz-adv-x="1175" d="M205 416q0 123 40 245.5t117.5 221t190.5 161t258 62.5q106 0 187 -33t133.5 -92t79 -140t26.5 -179q0 -123 -39 -246t-114.5 -221.5t-189 -161t-263.5 -62.5q-109 0 -189 33t-132 92.5t-78.5 140t-26.5 179.5zM508 446q0 -104 32.5 -160.5t118.5 -56.5q68 0 119 39 t86 98.5t52.5 130t17.5 134.5q0 104 -33 160.5t-119 56.5q-68 0 -119 -39t-85.5 -98.5t-52 -130t-17.5 -134.5zM561 1309l367 297l252 -318l-123 -108l-170 159l-231 -155z" />
<glyph unicode="&#xf5;" horiz-adv-x="1175" d="M205 416q0 123 40 245.5t117.5 221t190.5 161t258 62.5q106 0 187 -33t133.5 -92t79 -140t26.5 -179q0 -123 -39 -246t-114.5 -221.5t-189 -161t-263.5 -62.5q-109 0 -189 33t-132 92.5t-78.5 140t-26.5 179.5zM500 1315q47 111 112.5 165t159.5 54q39 0 70 -11.5 t57.5 -24.5t51 -24.5t49.5 -11.5q29 0 52 16.5t58 67.5l137 -84q-47 -111 -112.5 -165t-159.5 -54q-39 0 -69.5 11.5t-57.5 24.5t-51.5 24.5t-48.5 11.5q-29 0 -52.5 -16.5t-58.5 -67.5zM508 446q0 -104 32.5 -160.5t118.5 -56.5q68 0 119 39t86 98.5t52.5 130t17.5 134.5 q0 104 -33 160.5t-119 56.5q-68 0 -119 -39t-85.5 -98.5t-52 -130t-17.5 -134.5z" />
<glyph unicode="&#xf6;" horiz-adv-x="1175" d="M205 416q0 123 40 245.5t117.5 221t190.5 161t258 62.5q106 0 187 -33t133.5 -92t79 -140t26.5 -179q0 -123 -39 -246t-114.5 -221.5t-189 -161t-263.5 -62.5q-109 0 -189 33t-132 92.5t-78.5 140t-26.5 179.5zM493 1368q0 39 14.5 72t38 55.5t53 35.5t60.5 13 q57 0 98 -38t41 -101q0 -37 -15 -68.5t-40 -55.5t-54.5 -36t-58.5 -12q-59 0 -98 37t-39 98zM508 446q0 -104 32.5 -160.5t118.5 -56.5q68 0 119 39t86 98.5t52.5 130t17.5 134.5q0 104 -33 160.5t-119 56.5q-68 0 -119 -39t-85.5 -98.5t-52 -130t-17.5 -134.5zM948 1368 q0 39 14 72t38 55.5t53.5 35.5t60.5 13q57 0 98 -38t41 -101q0 -37 -15.5 -68.5t-40 -55.5t-54 -36t-58.5 -12q-59 0 -98 37t-39 98z" />
<glyph unicode="&#xf7;" d="M184 483l64 256h962l-63 -256h-963zM416 174q0 92 58 144.5t132 52.5q66 0 110 -37t44 -115q0 -92 -58.5 -144t-132.5 -52q-66 0 -109.5 36.5t-43.5 114.5zM614 1001q0 92 58.5 144.5t132.5 52.5q66 0 109.5 -37t43.5 -114q0 -92 -58 -144.5t-132 -52.5q-66 0 -110 37 t-44 114z" />
<glyph unicode="&#xf8;" horiz-adv-x="1175" d="M147 29l117 133q-59 109 -59 254q0 123 40 245.5t117.5 221t190.5 161t258 62.5q70 0 128 -14.5t105 -40.5l95 108l133 -108l-105 -119q35 -53 52.5 -123t17.5 -147q0 -123 -39 -246t-114.5 -221.5t-189 -161t-263.5 -62.5q-78 0 -139.5 17.5t-110.5 46.5l-100 -115z M489 418l381 434q-41 14 -90 14q-80 0 -135 -40t-90 -104.5t-50.5 -144t-15.5 -159.5zM561 238q41 -14 90 -15q80 0 135.5 40t90 104.5t50 144.5t15.5 160z" />
<glyph unicode="&#xf9;" horiz-adv-x="1189" d="M206 303q-3 94 21 193l140 581h305l-137 -577q-10 -45 -17.5 -92.5t-1.5 -87t31.5 -65.5t81.5 -26q47 0 87 17.5t85 60.5q4 51 13 107.5t20 103.5l133 559h305l-146 -612q-29 -117 -16.5 -234.5t47.5 -210.5l-272 -38q-23 45 -43 100q-57 -45 -129 -76t-160 -31 q-121 0 -195.5 44t-111.5 117t-40 167zM520 1475l189 161l276 -338l-131 -118z" />
<glyph unicode="&#xfa;" horiz-adv-x="1189" d="M206 303q-3 94 21 193l140 581h305l-137 -577q-10 -45 -17.5 -92.5t-1.5 -87t31.5 -65.5t81.5 -26q47 0 87 17.5t85 60.5q4 51 13 107.5t20 103.5l133 559h305l-146 -612q-29 -117 -16.5 -234.5t47.5 -210.5l-272 -38q-23 45 -43 100q-57 -45 -129 -76t-160 -31 q-121 0 -195.5 44t-111.5 117t-40 167zM675 1307l328 329l158 -178l-363 -278z" />
<glyph unicode="&#xfb;" horiz-adv-x="1189" d="M206 303q-3 94 21 193l140 581h305l-137 -577q-10 -45 -17.5 -92.5t-1.5 -87t31.5 -65.5t81.5 -26q47 0 87 17.5t85 60.5q4 51 13 107.5t20 103.5l133 559h305l-146 -612q-29 -117 -16.5 -234.5t47.5 -210.5l-272 -38q-23 45 -43 100q-57 -45 -129 -76t-160 -31 q-121 0 -195.5 44t-111.5 117t-40 167zM573 1309l367 297l252 -318l-123 -108l-170 159l-231 -155z" />
<glyph unicode="&#xfc;" horiz-adv-x="1189" d="M206 303q-3 94 21 193l140 581h305l-137 -577q-10 -45 -17.5 -92.5t-1.5 -87t31.5 -65.5t81.5 -26q47 0 87 17.5t85 60.5q4 51 13 107.5t20 103.5l133 559h305l-146 -612q-29 -117 -16.5 -234.5t47.5 -210.5l-272 -38q-23 45 -43 100q-57 -45 -129 -76t-160 -31 q-121 0 -195.5 44t-111.5 117t-40 167zM500 1368q0 39 14.5 72t38 55.5t53 35.5t60.5 13q57 0 98 -38t41 -101q0 -37 -15 -68.5t-40 -55.5t-54.5 -36t-58.5 -12q-59 0 -98 37t-39 98zM955 1368q0 39 14 72t38 55.5t53.5 35.5t60.5 13q57 0 98 -38t41 -101q0 -37 -15.5 -68.5 t-40 -55.5t-54 -36t-58.5 -12q-59 0 -98 37t-39 98z" />
<glyph unicode="&#xfd;" horiz-adv-x="1089" d="M-51 -330l92 240q41 -16 73.5 -25.5t80.5 -9.5q72 0 136 46t107 124q-53 205 -97 459t-60 573h313q2 -80 8 -176t14.5 -193.5t18.5 -189.5t22 -166q98 147 173 327.5t141 397.5h323q-57 -156 -115.5 -291t-122 -256.5t-131 -232.5t-143.5 -219q-55 -80 -114.5 -162 t-132 -147.5t-161.5 -106.5t-206 -41q-72 0 -123 13.5t-96 35.5zM622 1307l328 329l158 -178l-363 -278z" />
<glyph unicode="&#xfe;" horiz-adv-x="1183" d="M53 -379l459 1919l317 49l-118 -491q23 2 47 3t49 1q115 0 197.5 -35t136 -95.5t79 -141t25.5 -173.5q0 -150 -48 -274.5t-136 -213.5t-209 -139t-266 -50q-68 0 -142 12l-88 -371h-303zM504 236q23 -4 43 -5.5t41 -1.5q84 0 149.5 30t111.5 83t69.5 128t23.5 165 q0 88 -39 149.5t-135 61.5q-63 0 -121 -12z" />
<glyph unicode="&#xff;" horiz-adv-x="1089" d="M-51 -330l92 240q41 -16 73.5 -25.5t80.5 -9.5q72 0 136 46t107 124q-53 205 -97 459t-60 573h313q2 -80 8 -176t14.5 -193.5t18.5 -189.5t22 -166q98 147 173 327.5t141 397.5h323q-57 -156 -115.5 -291t-122 -256.5t-131 -232.5t-143.5 -219q-55 -80 -114.5 -162 t-132 -147.5t-161.5 -106.5t-206 -41q-72 0 -123 13.5t-96 35.5zM473 1368q0 39 14.5 72t38 55.5t53 35.5t60.5 13q57 0 98 -38t41 -101q0 -37 -15 -68.5t-40 -55.5t-54.5 -36t-58.5 -12q-59 0 -98 37t-39 98zM928 1368q0 39 14 72t38 55.5t53.5 35.5t60.5 13q57 0 98 -38 t41 -101q0 -37 -15.5 -68.5t-40 -55.5t-54 -36t-58.5 -12q-59 0 -98 37t-39 98z" />
<glyph unicode="&#x152;" horiz-adv-x="2017" d="M252 561q0 209 70.5 372t197.5 274.5t300 169t380 57.5q53 0 117.5 -3.5t120.5 -11.5h784l-65 -268h-584l-66 -279h512l-63 -262h-512l-82 -342h631l-66 -268h-809q-53 -8 -109.5 -12t-113.5 -4q-121 0 -235.5 25.5t-206 90t-146.5 176t-55 285.5zM575 584 q0 -68 19.5 -129.5t60.5 -107.5t104.5 -72.5t151.5 -26.5q66 0 140 12l215 899q-33 4 -63.5 6t-59.5 2q-145 0 -252 -54t-177.5 -138t-104.5 -188.5t-34 -202.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1779" d="M205 410q0 123 40 246.5t117.5 224t191.5 163t261 62.5q104 0 192.5 -44t139.5 -132q82 86 180 131t217 45q72 0 133.5 -20.5t107.5 -59.5t71.5 -95.5t25.5 -127.5q0 -115 -51 -195t-142.5 -130t-216 -72.5t-272.5 -22.5q14 -86 62.5 -122t152.5 -36q66 0 133.5 11.5 t126.5 33.5l23 -239q-57 -23 -146.5 -41.5t-193.5 -18.5q-129 0 -217 48.5t-142 119.5q-61 -72 -156 -120t-212 -48q-106 0 -186 33t-133.5 91.5t-80 138t-26.5 176.5zM508 446q0 -104 32.5 -160.5t118.5 -56.5q68 0 119 39t86 98.5t52.5 130t17.5 134.5q0 104 -33 160.5 t-119 56.5q-68 0 -119 -39t-85.5 -98.5t-52 -130t-17.5 -134.5zM1208 592q127 4 205 21.5t121 42t57.5 53t14.5 57.5q0 98 -111 98q-104 0 -183 -74.5t-104 -197.5z" />
<glyph unicode="&#x178;" horiz-adv-x="1335" d="M348 1419h340q33 -147 72 -288t86 -295q59 76 113.5 146.5t105.5 141t101.5 143t103.5 152.5h352q-86 -121 -166 -229.5t-159.5 -212t-163.5 -205.5t-179 -211l-135 -561h-319l135 561q-86 217 -155.5 424t-131.5 434zM623 1696q0 39 14.5 72t38 55.5t53 35.5t60.5 13 q57 0 98 -38t41 -101q0 -37 -15 -68.5t-40 -55.5t-54.5 -36t-58.5 -12q-59 0 -98 37t-39 98zM1078 1696q0 39 14 72t38 55.5t53.5 35.5t60.5 13q57 0 98 -38t41 -101q0 -37 -15.5 -68.5t-40 -55.5t-54 -36t-58.5 -12q-59 0 -98 37t-39 98z" />
<glyph unicode="&#x2c6;" horiz-adv-x="815" d="M403 1309l367 297l252 -318l-123 -108l-170 159l-231 -155z" />
<glyph unicode="&#x2dc;" horiz-adv-x="763" d="M336 1315q47 111 112.5 165t159.5 54q39 0 70 -11.5t57.5 -24.5t51 -24.5t49.5 -11.5q29 0 52 16.5t58 67.5l137 -84q-47 -111 -112.5 -165t-159.5 -54q-39 0 -69.5 11.5t-57.5 24.5t-51.5 24.5t-48.5 11.5q-29 0 -52.5 -16.5t-58.5 -67.5z" />
<glyph unicode="&#x2000;" horiz-adv-x="982" />
<glyph unicode="&#x2001;" horiz-adv-x="1964" />
<glyph unicode="&#x2002;" horiz-adv-x="982" />
<glyph unicode="&#x2003;" horiz-adv-x="1964" />
<glyph unicode="&#x2004;" horiz-adv-x="654" />
<glyph unicode="&#x2005;" horiz-adv-x="491" />
<glyph unicode="&#x2006;" horiz-adv-x="327" />
<glyph unicode="&#x2007;" horiz-adv-x="327" />
<glyph unicode="&#x2008;" horiz-adv-x="245" />
<glyph unicode="&#x2009;" horiz-adv-x="392" />
<glyph unicode="&#x200a;" horiz-adv-x="109" />
<glyph unicode="&#x2010;" horiz-adv-x="702" d="M150 473l67 277h594l-68 -277h-593z" />
<glyph unicode="&#x2011;" horiz-adv-x="702" d="M150 473l67 277h594l-68 -277h-593z" />
<glyph unicode="&#x2012;" horiz-adv-x="702" d="M150 473l67 277h594l-68 -277h-593z" />
<glyph unicode="&#x2013;" horiz-adv-x="1040" d="M123 485l57 254h1024l-59 -254h-1022z" />
<glyph unicode="&#x2014;" horiz-adv-x="2064" d="M123 485l57 254h2048l-59 -254h-2046z" />
<glyph unicode="&#x2018;" horiz-adv-x="485" d="M266 1014q2 29 7.5 59.5t9.5 44.5q33 137 111.5 261t170.5 210l189 -71q-72 -111 -123 -237t-68 -267h-297z" />
<glyph unicode="&#x2019;" horiz-adv-x="485" d="M246 1071q72 111 120 237t64 263h297q-2 -29 -7 -59.5t-9 -45.5q-33 -137 -112 -261t-171 -210z" />
<glyph unicode="&#x201a;" horiz-adv-x="485" d="M-6 -184q72 111 120 237t64 263h297q-2 -29 -7 -59.5t-9 -45.5q-33 -137 -112 -261t-171 -210z" />
<glyph unicode="&#x201c;" horiz-adv-x="927" d="M266 1014q2 29 7.5 59.5t9.5 44.5q33 137 111.5 261t170.5 210l189 -71q-72 -111 -123 -237t-68 -267h-297zM710 1014q2 29 7.5 59.5t9.5 44.5q33 137 111.5 261t170.5 210l189 -71q-72 -111 -123 -237t-68 -267h-297z" />
<glyph unicode="&#x201d;" horiz-adv-x="927" d="M246 1071q72 111 120 237t64 263h297q-2 -29 -7 -59.5t-9 -45.5q-33 -137 -112 -261t-171 -210zM688 1071q72 111 120 237t64 263h297q-2 -29 -7 -59.5t-9 -45.5q-33 -137 -112 -261t-171 -210z" />
<glyph unicode="&#x201e;" horiz-adv-x="927" d="M-6 -184q72 111 120 237t64 263h297q-2 -29 -7 -59.5t-9 -45.5q-33 -137 -112 -261t-171 -210zM434 -184q72 111 120 237t64 263h297q-2 -29 -7 -59.5t-9 -45.5q-33 -137 -112 -261t-171 -210z" />
<glyph unicode="&#x2022;" horiz-adv-x="794" d="M246 723q0 57 20.5 110.5t59.5 93.5t93 63.5t122 23.5t122 -23.5t93 -63.5t59.5 -93.5t20.5 -110.5q0 -59 -20.5 -111.5t-59.5 -92.5t-93.5 -63.5t-121.5 -23.5q-68 0 -122 23.5t-93 63.5t-59.5 92.5t-20.5 111.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1937" d="M123 139q0 47 17.5 86t46 66.5t65.5 43t76 15.5q72 0 120 -40t48 -126q0 -47 -17.5 -86t-46.5 -66.5t-65.5 -43t-75.5 -15.5q-72 0 -120 40t-48 126zM834 139q0 47 17.5 86t46 66.5t65.5 43t76 15.5q72 0 120 -40t48 -126q0 -47 -17.5 -86t-46.5 -66.5t-65.5 -43 t-75.5 -15.5q-72 0 -120 40t-48 126zM1544 139q0 47 17.5 86t46 66.5t65.5 43t76 15.5q72 0 120 -40t48 -126q0 -47 -17.5 -86t-46.5 -66.5t-65.5 -43t-75.5 -15.5q-72 0 -120 40t-48 126z" />
<glyph unicode="&#x202f;" horiz-adv-x="392" />
<glyph unicode="&#x2039;" horiz-adv-x="702" d="M205 594l448 479l193 -115l-322 -403l148 -371l-203 -94z" />
<glyph unicode="&#x203a;" horiz-adv-x="704" d="M125 205l321 403l-147 371l203 94l264 -504l-449 -479z" />
<glyph unicode="&#x205f;" horiz-adv-x="491" />
<glyph unicode="&#x20ac;" d="M174 446l47 203h131q4 35 11.5 68t15.5 65h-129l49 203h139q45 104 107.5 189.5t148.5 145.5t200 93t261 33q135 0 277 -47l-121 -238q-102 35 -201 35q-143 0 -227 -56.5t-138 -154.5h482l-88 -203h-463q-14 -66 -25 -133h430l-86 -203h-352q10 -127 73.5 -174 t160.5 -47q61 0 131.5 14.5t142.5 36.5l12 -239q-86 -31 -170 -48.5t-170 -17.5q-96 0 -181 26t-150.5 82t-105.5 147t-44 220h-168z" />
<glyph unicode="&#x2122;" horiz-adv-x="1748" d="M246 1208v211h659v-211h-209v-510h-241v510h-209zM985 698q14 254 30.5 427t33.5 294h217q43 -92 81.5 -187t79.5 -188q41 92 83 194.5t75 180.5h219q20 -121 35.5 -294t32.5 -427h-236l-8 383l-121 -307h-161l-121 305q0 -66 -1 -130t-2 -117.5t-2 -90t-1 -43.5h-234z " />
<glyph unicode="&#x25fc;" horiz-adv-x="1075" d="M0 0v1075h1075v-1075h-1075z" />
<hkern u1="K" u2="&#xef;" k="-63" />
<hkern u1="T" u2="&#xff;" k="80" />
<hkern u1="T" u2="&#xef;" k="-111" />
<hkern u1="T" u2="&#xec;" k="-47" />
<hkern u1="V" u2="&#xef;" k="-66" />
<hkern u1="W" u2="&#xef;" k="-96" />
<hkern u1="W" u2="&#xee;" k="-47" />
<hkern u1="W" u2="&#xec;" k="-66" />
<hkern u1="Y" u2="&#xef;" k="-113" />
<hkern u1="Y" u2="&#xec;" k="-49" />
<hkern u1="f" u2="&#xef;" k="-129" />
<hkern u1="f" u2="&#xee;" k="-49" />
<hkern u1="f" u2="&#xec;" k="-145" />
<hkern u1="i" u2="&#xef;" k="-37" />
<hkern u1="j" u2="&#xef;" k="-37" />
<hkern u1="&#xdd;" u2="&#xef;" k="-113" />
<hkern u1="&#xdd;" u2="&#xec;" k="-49" />
<hkern u1="&#xec;" u2="&#xef;" k="-37" />
<hkern u1="&#xed;" u2="&#xef;" k="-37" />
<hkern u1="&#xee;" u2="&#xef;" k="-37" />
<hkern u1="&#xef;" u2="&#xef;" k="-37" />
<hkern u1="&#x178;" u2="&#xef;" k="-113" />
<hkern u1="&#x178;" u2="&#xec;" k="-49" />
<hkern g1="uniFB01" u2="&#xef;" k="-37" />
<hkern g1="uniFB03" u2="&#xef;" k="-37" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="hyphen,uni00AD,endash,emdash" 	k="-31" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="slash" 	k="-51" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="question" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="backslash" 	k="63" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="bracketright" 	k="53" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="x" 	k="-20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="braceright" 	k="25" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quotesinglbase,quotedblbase" 	k="-45" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="guillemotleft,guilsinglleft" 	k="29" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="questiondown" 	k="-27" />
<hkern g1="b" 	g2="hyphen,uni00AD,endash,emdash" 	k="-70" />
<hkern g1="b" 	g2="slash" 	k="27" />
<hkern g1="b" 	g2="question" 	k="31" />
<hkern g1="b" 	g2="backslash" 	k="74" />
<hkern g1="b" 	g2="bracketright" 	k="66" />
<hkern g1="b" 	g2="x" 	k="27" />
<hkern g1="b" 	g2="y,yacute,ydieresis" 	k="37" />
<hkern g1="b" 	g2="braceright" 	k="31" />
<hkern g1="b" 	g2="questiondown" 	k="43" />
<hkern g1="b" 	g2="parenright" 	k="49" />
<hkern g1="b" 	g2="asterisk" 	k="41" />
<hkern g1="b" 	g2="j" 	k="20" />
<hkern g1="b" 	g2="z" 	k="61" />
<hkern g1="b" 	g2="quoteleft,quotedblleft" 	k="35" />
<hkern g1="b" 	g2="quoteright,quotedblright" 	k="55" />
<hkern g1="c,ccedilla" 	g2="comma,period,ellipsis" 	k="-41" />
<hkern g1="c,ccedilla" 	g2="slash" 	k="-47" />
<hkern g1="c,ccedilla" 	g2="question" 	k="20" />
<hkern g1="c,ccedilla" 	g2="backslash" 	k="23" />
<hkern g1="c,ccedilla" 	g2="bracketright" 	k="57" />
<hkern g1="c,ccedilla" 	g2="x" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="braceright" 	k="23" />
<hkern g1="c,ccedilla" 	g2="quotesinglbase,quotedblbase" 	k="-55" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="51" />
<hkern g1="c,ccedilla" 	g2="questiondown" 	k="-27" />
<hkern g1="c,ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="c,ccedilla" 	g2="c,ccedilla" 	k="51" />
<hkern g1="c,ccedilla" 	g2="d" 	k="41" />
<hkern g1="c,ccedilla" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="51" />
<hkern g1="c,ccedilla" 	g2="g" 	k="41" />
<hkern g1="c,ccedilla" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="c,ccedilla" 	g2="q" 	k="41" />
<hkern g1="c,ccedilla" 	g2="eth" 	k="51" />
<hkern g1="d" 	g2="comma,period,ellipsis" 	k="-41" />
<hkern g1="d" 	g2="slash" 	k="-66" />
<hkern g1="d" 	g2="backslash" 	k="-35" />
<hkern g1="d" 	g2="quotesinglbase,quotedblbase" 	k="-61" />
<hkern g1="d" 	g2="guillemotleft,guilsinglleft" 	k="16" />
<hkern g1="d" 	g2="questiondown" 	k="-41" />
<hkern g1="d" 	g2="z" 	k="-20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="hyphen,uni00AD,endash,emdash" 	k="-57" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="question" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="backslash" 	k="59" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="bracketright" 	k="53" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="braceright" 	k="29" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="parenright" 	k="35" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="g" 	g2="hyphen,uni00AD,endash,emdash" 	k="-31" />
<hkern g1="g" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="g" 	g2="slash" 	k="-51" />
<hkern g1="g" 	g2="question" 	k="20" />
<hkern g1="g" 	g2="backslash" 	k="63" />
<hkern g1="g" 	g2="bracketright" 	k="53" />
<hkern g1="g" 	g2="x" 	k="-20" />
<hkern g1="g" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="g" 	g2="braceright" 	k="25" />
<hkern g1="g" 	g2="quotesinglbase,quotedblbase" 	k="-45" />
<hkern g1="g" 	g2="guillemotleft,guilsinglleft" 	k="29" />
<hkern g1="g" 	g2="questiondown" 	k="-27" />
<hkern g1="g" 	g2="j" 	k="-41" />
<hkern g1="h" 	g2="hyphen,uni00AD,endash,emdash" 	k="-57" />
<hkern g1="h" 	g2="question" 	k="25" />
<hkern g1="h" 	g2="backslash" 	k="72" />
<hkern g1="h" 	g2="bracketright" 	k="41" />
<hkern g1="h" 	g2="y,yacute,ydieresis" 	k="23" />
<hkern g1="h" 	g2="braceright" 	k="27" />
<hkern g1="h" 	g2="quotesinglbase,quotedblbase" 	k="-31" />
<hkern g1="h" 	g2="parenright" 	k="41" />
<hkern g1="h" 	g2="asterisk" 	k="39" />
<hkern g1="h" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="h" 	g2="quoteright,quotedblright" 	k="66" />
<hkern g1="i,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="hyphen,uni00AD,endash,emdash" 	k="-47" />
<hkern g1="i,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="backslash" 	k="-41" />
<hkern g1="i,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="braceright" 	k="-23" />
<hkern g1="i,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="quoteleft,quotedblleft" 	k="-61" />
<hkern g1="i,igrave,iacute,icircumflex,idieresis,uniFB01,uniFB03" 	g2="quoteright,quotedblright" 	k="-61" />
<hkern g1="j" 	g2="hyphen,uni00AD,endash,emdash" 	k="-53" />
<hkern g1="j" 	g2="slash" 	k="-61" />
<hkern g1="j" 	g2="backslash" 	k="-41" />
<hkern g1="j" 	g2="bracketright" 	k="-16" />
<hkern g1="j" 	g2="braceright" 	k="-29" />
<hkern g1="j" 	g2="quotesinglbase,quotedblbase" 	k="-31" />
<hkern g1="j" 	g2="j" 	k="-43" />
<hkern g1="j" 	g2="quoteleft,quotedblleft" 	k="-61" />
<hkern g1="j" 	g2="quoteright,quotedblright" 	k="-61" />
<hkern g1="k" 	g2="comma,period,ellipsis" 	k="-41" />
<hkern g1="k" 	g2="slash" 	k="-63" />
<hkern g1="k" 	g2="bracketright" 	k="55" />
<hkern g1="k" 	g2="x" 	k="-51" />
<hkern g1="k" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="k" 	g2="quotesinglbase,quotedblbase" 	k="-41" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="66" />
<hkern g1="k" 	g2="questiondown" 	k="-39" />
<hkern g1="k" 	g2="asterisk" 	k="-41" />
<hkern g1="k" 	g2="quoteleft,quotedblleft" 	k="-82" />
<hkern g1="k" 	g2="quoteright,quotedblright" 	k="-76" />
<hkern g1="k" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="29" />
<hkern g1="k" 	g2="c,ccedilla" 	k="20" />
<hkern g1="k" 	g2="d" 	k="29" />
<hkern g1="k" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="20" />
<hkern g1="k" 	g2="g" 	k="29" />
<hkern g1="k" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="k" 	g2="q" 	k="29" />
<hkern g1="k" 	g2="eth" 	k="20" />
<hkern g1="k" 	g2="ampersand" 	k="25" />
<hkern g1="k" 	g2="v" 	k="-31" />
<hkern g1="k" 	g2="w" 	k="-20" />
<hkern g1="k" 	g2="braceleft" 	k="25" />
<hkern g1="l,uniFB02,uniFB04" 	g2="comma,period,ellipsis" 	k="-41" />
<hkern g1="l,uniFB02,uniFB04" 	g2="slash" 	k="-41" />
<hkern g1="l,uniFB02,uniFB04" 	g2="question" 	k="25" />
<hkern g1="l,uniFB02,uniFB04" 	g2="bracketright" 	k="-20" />
<hkern g1="l,uniFB02,uniFB04" 	g2="x" 	k="-29" />
<hkern g1="l,uniFB02,uniFB04" 	g2="braceright" 	k="18" />
<hkern g1="l,uniFB02,uniFB04" 	g2="quotesinglbase,quotedblbase" 	k="-55" />
<hkern g1="l,uniFB02,uniFB04" 	g2="guillemotleft,guilsinglleft" 	k="57" />
<hkern g1="l,uniFB02,uniFB04" 	g2="asterisk" 	k="39" />
<hkern g1="l,uniFB02,uniFB04" 	g2="j" 	k="25" />
<hkern g1="l,uniFB02,uniFB04" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="27" />
<hkern g1="l,uniFB02,uniFB04" 	g2="d" 	k="27" />
<hkern g1="l,uniFB02,uniFB04" 	g2="g" 	k="27" />
<hkern g1="l,uniFB02,uniFB04" 	g2="q" 	k="27" />
<hkern g1="l,uniFB02,uniFB04" 	g2="v" 	k="23" />
<hkern g1="l,uniFB02,uniFB04" 	g2="w" 	k="27" />
<hkern g1="l,uniFB02,uniFB04" 	g2="braceleft" 	k="23" />
<hkern g1="l,uniFB02,uniFB04" 	g2="l" 	k="20" />
<hkern g1="l,uniFB02,uniFB04" 	g2="t" 	k="18" />
<hkern g1="m,n,ntilde" 	g2="hyphen,uni00AD,endash,emdash" 	k="-57" />
<hkern g1="m,n,ntilde" 	g2="question" 	k="25" />
<hkern g1="m,n,ntilde" 	g2="backslash" 	k="72" />
<hkern g1="m,n,ntilde" 	g2="bracketright" 	k="41" />
<hkern g1="m,n,ntilde" 	g2="y,yacute,ydieresis" 	k="23" />
<hkern g1="m,n,ntilde" 	g2="braceright" 	k="27" />
<hkern g1="m,n,ntilde" 	g2="quotesinglbase,quotedblbase" 	k="-31" />
<hkern g1="m,n,ntilde" 	g2="parenright" 	k="41" />
<hkern g1="m,n,ntilde" 	g2="asterisk" 	k="39" />
<hkern g1="m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="m,n,ntilde" 	g2="quoteright,quotedblright" 	k="66" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="hyphen,uni00AD,endash,emdash" 	k="-72" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="slash" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="question" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="backslash" 	k="68" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="bracketright" 	k="63" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="x" 	k="51" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="y,yacute,ydieresis" 	k="31" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="braceright" 	k="29" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="questiondown" 	k="35" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="parenright" 	k="47" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="asterisk" 	k="45" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="j" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="z" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteleft,quotedblleft" 	k="29" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteright,quotedblright" 	k="74" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="v" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="w" 	k="20" />
<hkern g1="p" 	g2="hyphen,uni00AD,endash,emdash" 	k="-70" />
<hkern g1="p" 	g2="slash" 	k="27" />
<hkern g1="p" 	g2="question" 	k="31" />
<hkern g1="p" 	g2="backslash" 	k="74" />
<hkern g1="p" 	g2="bracketright" 	k="66" />
<hkern g1="p" 	g2="x" 	k="27" />
<hkern g1="p" 	g2="y,yacute,ydieresis" 	k="37" />
<hkern g1="p" 	g2="braceright" 	k="31" />
<hkern g1="p" 	g2="questiondown" 	k="43" />
<hkern g1="p" 	g2="parenright" 	k="49" />
<hkern g1="p" 	g2="asterisk" 	k="41" />
<hkern g1="p" 	g2="j" 	k="20" />
<hkern g1="p" 	g2="z" 	k="61" />
<hkern g1="p" 	g2="quoteleft,quotedblleft" 	k="35" />
<hkern g1="p" 	g2="quoteright,quotedblright" 	k="55" />
<hkern g1="q" 	g2="hyphen,uni00AD,endash,emdash" 	k="-31" />
<hkern g1="q" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="q" 	g2="slash" 	k="-51" />
<hkern g1="q" 	g2="question" 	k="20" />
<hkern g1="q" 	g2="backslash" 	k="63" />
<hkern g1="q" 	g2="bracketright" 	k="53" />
<hkern g1="q" 	g2="x" 	k="-20" />
<hkern g1="q" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="q" 	g2="braceright" 	k="25" />
<hkern g1="q" 	g2="quotesinglbase,quotedblbase" 	k="-45" />
<hkern g1="q" 	g2="guillemotleft,guilsinglleft" 	k="29" />
<hkern g1="q" 	g2="questiondown" 	k="-27" />
<hkern g1="r" 	g2="comma,period,ellipsis" 	k="92" />
<hkern g1="r" 	g2="slash" 	k="59" />
<hkern g1="r" 	g2="question" 	k="20" />
<hkern g1="r" 	g2="bracketright" 	k="90" />
<hkern g1="r" 	g2="x" 	k="-27" />
<hkern g1="r" 	g2="braceright" 	k="47" />
<hkern g1="r" 	g2="quotesinglbase,quotedblbase" 	k="92" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="37" />
<hkern g1="r" 	g2="questiondown" 	k="102" />
<hkern g1="r" 	g2="parenright" 	k="63" />
<hkern g1="r" 	g2="asterisk" 	k="-25" />
<hkern g1="r" 	g2="j" 	k="43" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="33" />
<hkern g1="r" 	g2="c,ccedilla" 	k="41" />
<hkern g1="r" 	g2="d" 	k="33" />
<hkern g1="r" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="41" />
<hkern g1="r" 	g2="g" 	k="33" />
<hkern g1="r" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="r" 	g2="q" 	k="33" />
<hkern g1="r" 	g2="eth" 	k="41" />
<hkern g1="r" 	g2="ampersand" 	k="45" />
<hkern g1="r" 	g2="v" 	k="-39" />
<hkern g1="r" 	g2="w" 	k="-29" />
<hkern g1="r" 	g2="l" 	k="20" />
<hkern g1="r" 	g2="t" 	k="16" />
<hkern g1="r" 	g2="bracketleft" 	k="27" />
<hkern g1="r" 	g2="b" 	k="25" />
<hkern g1="r" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="35" />
<hkern g1="r" 	g2="h" 	k="31" />
<hkern g1="r" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="31" />
<hkern g1="r" 	g2="k" 	k="31" />
<hkern g1="r" 	g2="m,n,ntilde" 	k="33" />
<hkern g1="r" 	g2="p" 	k="33" />
<hkern g1="r" 	g2="r" 	k="33" />
<hkern g1="r" 	g2="bar" 	k="27" />
<hkern g1="r" 	g2="germandbls" 	k="35" />
<hkern g1="s" 	g2="comma,period,ellipsis" 	k="-29" />
<hkern g1="s" 	g2="question" 	k="27" />
<hkern g1="s" 	g2="backslash" 	k="51" />
<hkern g1="s" 	g2="bracketright" 	k="66" />
<hkern g1="s" 	g2="braceright" 	k="29" />
<hkern g1="s" 	g2="quotesinglbase,quotedblbase" 	k="-33" />
<hkern g1="s" 	g2="guillemotleft,guilsinglleft" 	k="49" />
<hkern g1="s" 	g2="parenright" 	k="47" />
<hkern g1="s" 	g2="j" 	k="20" />
<hkern g1="t" 	g2="comma,period,ellipsis" 	k="-47" />
<hkern g1="t" 	g2="slash" 	k="-123" />
<hkern g1="t" 	g2="backslash" 	k="18" />
<hkern g1="t" 	g2="bracketright" 	k="45" />
<hkern g1="t" 	g2="x" 	k="-57" />
<hkern g1="t" 	g2="quotesinglbase,quotedblbase" 	k="-66" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="39" />
<hkern g1="t" 	g2="questiondown" 	k="-45" />
<hkern g1="t" 	g2="z" 	k="-27" />
<hkern g1="t" 	g2="quoteleft,quotedblleft" 	k="-76" />
<hkern g1="t" 	g2="quoteright,quotedblright" 	k="-53" />
<hkern g1="t" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="t" 	g2="c,ccedilla" 	k="31" />
<hkern g1="t" 	g2="d" 	k="41" />
<hkern g1="t" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="31" />
<hkern g1="t" 	g2="g" 	k="41" />
<hkern g1="t" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="t" 	g2="q" 	k="41" />
<hkern g1="t" 	g2="eth" 	k="31" />
<hkern g1="t" 	g2="v" 	k="-29" />
<hkern g1="t" 	g2="w" 	k="-20" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="slash" 	k="-45" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="question" 	k="41" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="backslash" 	k="61" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="bracketright" 	k="63" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="x" 	k="-31" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="y,yacute,ydieresis" 	k="18" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="braceright" 	k="29" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="quotesinglbase,quotedblbase" 	k="-39" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="guillemotleft,guilsinglleft" 	k="37" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="questiondown" 	k="-20" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="asterisk" 	k="16" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="j" 	k="20" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="d" 	k="20" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="g" 	k="20" />
<hkern g1="u,ugrave,uacute,ucircumflex,udieresis" 	g2="q" 	k="20" />
<hkern g1="v" 	g2="hyphen,uni00AD,endash,emdash" 	k="-61" />
<hkern g1="v" 	g2="comma,period,ellipsis" 	k="57" />
<hkern g1="v" 	g2="slash" 	k="35" />
<hkern g1="v" 	g2="question" 	k="51" />
<hkern g1="v" 	g2="bracketright" 	k="68" />
<hkern g1="v" 	g2="braceright" 	k="25" />
<hkern g1="v" 	g2="quotesinglbase,quotedblbase" 	k="55" />
<hkern g1="v" 	g2="questiondown" 	k="76" />
<hkern g1="v" 	g2="parenright" 	k="39" />
<hkern g1="v" 	g2="asterisk" 	k="-47" />
<hkern g1="v" 	g2="j" 	k="20" />
<hkern g1="v" 	g2="quoteleft,quotedblleft" 	k="-57" />
<hkern g1="v" 	g2="quoteright,quotedblright" 	k="-63" />
<hkern g1="v" 	g2="ampersand" 	k="20" />
<hkern g1="v" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="w" 	g2="hyphen,uni00AD,endash,emdash" 	k="-61" />
<hkern g1="w" 	g2="comma,period,ellipsis" 	k="47" />
<hkern g1="w" 	g2="slash" 	k="23" />
<hkern g1="w" 	g2="question" 	k="51" />
<hkern g1="w" 	g2="bracketright" 	k="68" />
<hkern g1="w" 	g2="braceright" 	k="25" />
<hkern g1="w" 	g2="quotesinglbase,quotedblbase" 	k="43" />
<hkern g1="w" 	g2="questiondown" 	k="66" />
<hkern g1="w" 	g2="parenright" 	k="39" />
<hkern g1="w" 	g2="asterisk" 	k="-47" />
<hkern g1="w" 	g2="quoteleft,quotedblleft" 	k="-57" />
<hkern g1="w" 	g2="quoteright,quotedblright" 	k="-61" />
<hkern g1="w" 	g2="c,ccedilla" 	k="-20" />
<hkern g1="w" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="-20" />
<hkern g1="w" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="w" 	g2="eth" 	k="-20" />
<hkern g1="x" 	g2="comma,period,ellipsis" 	k="-29" />
<hkern g1="x" 	g2="slash" 	k="-61" />
<hkern g1="x" 	g2="question" 	k="23" />
<hkern g1="x" 	g2="bracketright" 	k="55" />
<hkern g1="x" 	g2="quotesinglbase,quotedblbase" 	k="-39" />
<hkern g1="x" 	g2="guillemotleft,guilsinglleft" 	k="68" />
<hkern g1="x" 	g2="questiondown" 	k="-39" />
<hkern g1="x" 	g2="quoteleft,quotedblleft" 	k="-25" />
<hkern g1="x" 	g2="quoteright,quotedblright" 	k="-35" />
<hkern g1="x" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="x" 	g2="c,ccedilla" 	k="61" />
<hkern g1="x" 	g2="d" 	k="41" />
<hkern g1="x" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="61" />
<hkern g1="x" 	g2="g" 	k="41" />
<hkern g1="x" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="x" 	g2="q" 	k="41" />
<hkern g1="x" 	g2="eth" 	k="61" />
<hkern g1="x" 	g2="ampersand" 	k="29" />
<hkern g1="x" 	g2="braceleft" 	k="20" />
<hkern g1="x" 	g2="l" 	k="25" />
<hkern g1="x" 	g2="t" 	k="16" />
<hkern g1="x" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="y,yacute,ydieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="-61" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,ellipsis" 	k="51" />
<hkern g1="y,yacute,ydieresis" 	g2="slash" 	k="37" />
<hkern g1="y,yacute,ydieresis" 	g2="bracketright" 	k="72" />
<hkern g1="y,yacute,ydieresis" 	g2="braceright" 	k="41" />
<hkern g1="y,yacute,ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="47" />
<hkern g1="y,yacute,ydieresis" 	g2="questiondown" 	k="61" />
<hkern g1="y,yacute,ydieresis" 	g2="parenright" 	k="53" />
<hkern g1="y,yacute,ydieresis" 	g2="asterisk" 	k="-31" />
<hkern g1="y,yacute,ydieresis" 	g2="quoteleft,quotedblleft" 	k="-47" />
<hkern g1="y,yacute,ydieresis" 	g2="quoteright,quotedblright" 	k="-47" />
<hkern g1="y,yacute,ydieresis" 	g2="c,ccedilla" 	k="-20" />
<hkern g1="y,yacute,ydieresis" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="-20" />
<hkern g1="y,yacute,ydieresis" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="y,yacute,ydieresis" 	g2="eth" 	k="-20" />
<hkern g1="y,yacute,ydieresis" 	g2="ampersand" 	k="18" />
<hkern g1="z" 	g2="hyphen,uni00AD,endash,emdash" 	k="-20" />
<hkern g1="z" 	g2="slash" 	k="-20" />
<hkern g1="z" 	g2="bracketright" 	k="37" />
<hkern g1="z" 	g2="x" 	k="-51" />
<hkern g1="z" 	g2="y,yacute,ydieresis" 	k="-31" />
<hkern g1="z" 	g2="quotesinglbase,quotedblbase" 	k="-59" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="29" />
<hkern g1="z" 	g2="questiondown" 	k="-41" />
<hkern g1="z" 	g2="asterisk" 	k="-41" />
<hkern g1="z" 	g2="quoteleft,quotedblleft" 	k="-51" />
<hkern g1="z" 	g2="quoteright,quotedblright" 	k="-47" />
<hkern g1="z" 	g2="v" 	k="-49" />
<hkern g1="z" 	g2="w" 	k="-39" />
<hkern g1="z" 	g2="parenleft" 	k="-20" />
<hkern g1="eth" 	g2="hyphen,uni00AD,endash,emdash" 	k="-61" />
<hkern g1="eth" 	g2="slash" 	k="23" />
<hkern g1="eth" 	g2="x" 	k="31" />
<hkern g1="eth" 	g2="y,yacute,ydieresis" 	k="37" />
<hkern g1="eth" 	g2="questiondown" 	k="61" />
<hkern g1="eth" 	g2="parenright" 	k="23" />
<hkern g1="eth" 	g2="asterisk" 	k="31" />
<hkern g1="eth" 	g2="j" 	k="27" />
<hkern g1="eth" 	g2="z" 	k="23" />
<hkern g1="eth" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="eth" 	g2="d" 	k="16" />
<hkern g1="eth" 	g2="g" 	k="16" />
<hkern g1="eth" 	g2="q" 	k="16" />
<hkern g1="thorn" 	g2="hyphen,uni00AD,endash,emdash" 	k="-70" />
<hkern g1="thorn" 	g2="slash" 	k="27" />
<hkern g1="thorn" 	g2="question" 	k="31" />
<hkern g1="thorn" 	g2="backslash" 	k="74" />
<hkern g1="thorn" 	g2="bracketright" 	k="66" />
<hkern g1="thorn" 	g2="x" 	k="27" />
<hkern g1="thorn" 	g2="y,yacute,ydieresis" 	k="37" />
<hkern g1="thorn" 	g2="braceright" 	k="31" />
<hkern g1="thorn" 	g2="questiondown" 	k="43" />
<hkern g1="thorn" 	g2="parenright" 	k="49" />
<hkern g1="thorn" 	g2="asterisk" 	k="41" />
<hkern g1="thorn" 	g2="j" 	k="20" />
<hkern g1="thorn" 	g2="z" 	k="61" />
<hkern g1="thorn" 	g2="quoteleft,quotedblleft" 	k="35" />
<hkern g1="thorn" 	g2="quoteright,quotedblright" 	k="55" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="hyphen,uni00AD,endash,emdash" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="slash" 	k="-61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="123" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="123" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="bracketright" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="y,yacute,ydieresis" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="braceright" 	k="27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotesinglbase,quotedblbase" 	k="-41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="questiondown" 	k="-31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk" 	k="164" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="j" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="205" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="164" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="c,ccedilla" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="d" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="g" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="q" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="eth" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="ampersand" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v" 	k="92" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="102" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="braceleft" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="l" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="parenleft" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="at" 	k="51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="B" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,Ccedilla" 	k="92" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="D,Eth" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="F" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="G" 	k="92" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="H" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="J" 	k="-31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="K" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="L" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="N,Ntilde" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="92" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="P" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Q" 	k="92" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="R" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="143" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="143" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="W" 	k="59" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="184" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="AE" 	k="-41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Thorn" 	k="16" />
<hkern g1="B" 	g2="hyphen,uni00AD,endash,emdash" 	k="-31" />
<hkern g1="B" 	g2="backslash" 	k="27" />
<hkern g1="B" 	g2="bracketright" 	k="35" />
<hkern g1="B" 	g2="questiondown" 	k="41" />
<hkern g1="B" 	g2="parenright" 	k="18" />
<hkern g1="B" 	g2="T" 	k="51" />
<hkern g1="B" 	g2="V" 	k="37" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="74" />
<hkern g1="B" 	g2="X" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="hyphen,uni00AD,endash,emdash" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="slash" 	k="-41" />
<hkern g1="C,Ccedilla" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="quotesinglbase,quotedblbase" 	k="-41" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="C,Ccedilla" 	g2="parenright" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="C,Ccedilla" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="C,Ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="c,ccedilla" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="d" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="q" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="eth" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="ampersand" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="v" 	k="82" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="82" />
<hkern g1="C,Ccedilla" 	g2="braceleft" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="l" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="at" 	k="51" />
<hkern g1="C,Ccedilla" 	g2="C,Ccedilla" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="G" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="J" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="Q" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="S" 	k="-41" />
<hkern g1="D,Eth" 	g2="hyphen,uni00AD,endash,emdash" 	k="-41" />
<hkern g1="D,Eth" 	g2="slash" 	k="47" />
<hkern g1="D,Eth" 	g2="question" 	k="51" />
<hkern g1="D,Eth" 	g2="backslash" 	k="37" />
<hkern g1="D,Eth" 	g2="bracketright" 	k="37" />
<hkern g1="D,Eth" 	g2="questiondown" 	k="41" />
<hkern g1="D,Eth" 	g2="parenright" 	k="31" />
<hkern g1="D,Eth" 	g2="J" 	k="39" />
<hkern g1="D,Eth" 	g2="T" 	k="102" />
<hkern g1="D,Eth" 	g2="V" 	k="61" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="133" />
<hkern g1="D,Eth" 	g2="AE" 	k="55" />
<hkern g1="D,Eth" 	g2="X" 	k="72" />
<hkern g1="D,Eth" 	g2="M" 	k="-20" />
<hkern g1="D,Eth" 	g2="Z" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="slash" 	k="-41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="quotesinglbase,quotedblbase" 	k="-72" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guillemotleft,guilsinglleft" 	k="35" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="quoteleft,quotedblleft" 	k="-82" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="quoteright,quotedblright" 	k="-82" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="J" 	k="-20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="V" 	k="-20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="W" 	k="-10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="AE" 	k="-31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="X" 	k="-20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-31" />
<hkern g1="F" 	g2="comma,period,ellipsis" 	k="123" />
<hkern g1="F" 	g2="slash" 	k="41" />
<hkern g1="F" 	g2="backslash" 	k="-31" />
<hkern g1="F" 	g2="braceright" 	k="-41" />
<hkern g1="F" 	g2="quotesinglbase,quotedblbase" 	k="61" />
<hkern g1="F" 	g2="guillemotleft,guilsinglleft" 	k="27" />
<hkern g1="F" 	g2="asterisk" 	k="-29" />
<hkern g1="F" 	g2="z" 	k="41" />
<hkern g1="F" 	g2="quoteleft,quotedblleft" 	k="-82" />
<hkern g1="F" 	g2="quoteright,quotedblright" 	k="-82" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="F" 	g2="c,ccedilla" 	k="41" />
<hkern g1="F" 	g2="d" 	k="41" />
<hkern g1="F" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="41" />
<hkern g1="F" 	g2="g" 	k="41" />
<hkern g1="F" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="F" 	g2="q" 	k="41" />
<hkern g1="F" 	g2="eth" 	k="41" />
<hkern g1="F" 	g2="ampersand" 	k="82" />
<hkern g1="F" 	g2="braceleft" 	k="20" />
<hkern g1="F" 	g2="m,n,ntilde" 	k="82" />
<hkern g1="F" 	g2="p" 	k="82" />
<hkern g1="F" 	g2="r" 	k="82" />
<hkern g1="F" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="F" 	g2="C,Ccedilla" 	k="41" />
<hkern g1="F" 	g2="G" 	k="41" />
<hkern g1="F" 	g2="J" 	k="246" />
<hkern g1="F" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="F" 	g2="Q" 	k="41" />
<hkern g1="F" 	g2="AE" 	k="119" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="70" />
<hkern g1="F" 	g2="Z" 	k="18" />
<hkern g1="F" 	g2="colon,semicolon" 	k="41" />
<hkern g1="F" 	g2="s" 	k="41" />
<hkern g1="G" 	g2="hyphen,uni00AD,endash,emdash" 	k="-41" />
<hkern g1="G" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="G" 	g2="backslash" 	k="20" />
<hkern g1="G" 	g2="bracketright" 	k="20" />
<hkern g1="G" 	g2="quotesinglbase,quotedblbase" 	k="-41" />
<hkern g1="G" 	g2="parenright" 	k="20" />
<hkern g1="G" 	g2="T" 	k="25" />
<hkern g1="G" 	g2="V" 	k="25" />
<hkern g1="G" 	g2="Y,Yacute,Ydieresis" 	k="29" />
<hkern g1="H" 	g2="guillemotleft,guilsinglleft" 	k="23" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="guillemotleft,guilsinglleft" 	k="23" />
<hkern g1="J" 	g2="slash" 	k="35" />
<hkern g1="J" 	g2="guillemotleft,guilsinglleft" 	k="23" />
<hkern g1="J" 	g2="questiondown" 	k="41" />
<hkern g1="J" 	g2="quoteleft,quotedblleft" 	k="-70" />
<hkern g1="J" 	g2="quoteright,quotedblright" 	k="-61" />
<hkern g1="J" 	g2="J" 	k="33" />
<hkern g1="J" 	g2="AE" 	k="47" />
<hkern g1="J" 	g2="X" 	k="16" />
<hkern g1="J" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="45" />
<hkern g1="K" 	g2="hyphen,uni00AD,endash,emdash" 	k="61" />
<hkern g1="K" 	g2="comma,period,ellipsis" 	k="-41" />
<hkern g1="K" 	g2="slash" 	k="-82" />
<hkern g1="K" 	g2="question" 	k="-41" />
<hkern g1="K" 	g2="backslash" 	k="-51" />
<hkern g1="K" 	g2="bracketright" 	k="-41" />
<hkern g1="K" 	g2="x" 	k="-20" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="82" />
<hkern g1="K" 	g2="braceright" 	k="-51" />
<hkern g1="K" 	g2="quotesinglbase,quotedblbase" 	k="-61" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="143" />
<hkern g1="K" 	g2="questiondown" 	k="-37" />
<hkern g1="K" 	g2="parenright" 	k="-20" />
<hkern g1="K" 	g2="asterisk" 	k="20" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="K" 	g2="quoteright,quotedblright" 	k="-29" />
<hkern g1="K" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="82" />
<hkern g1="K" 	g2="c,ccedilla" 	k="82" />
<hkern g1="K" 	g2="d" 	k="82" />
<hkern g1="K" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="82" />
<hkern g1="K" 	g2="g" 	k="82" />
<hkern g1="K" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="K" 	g2="q" 	k="82" />
<hkern g1="K" 	g2="eth" 	k="82" />
<hkern g1="K" 	g2="ampersand" 	k="41" />
<hkern g1="K" 	g2="v" 	k="102" />
<hkern g1="K" 	g2="w" 	k="92" />
<hkern g1="K" 	g2="braceleft" 	k="41" />
<hkern g1="K" 	g2="t" 	k="20" />
<hkern g1="K" 	g2="m,n,ntilde" 	k="20" />
<hkern g1="K" 	g2="p" 	k="20" />
<hkern g1="K" 	g2="r" 	k="20" />
<hkern g1="K" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="K" 	g2="parenleft" 	k="41" />
<hkern g1="K" 	g2="at" 	k="61" />
<hkern g1="K" 	g2="C,Ccedilla" 	k="123" />
<hkern g1="K" 	g2="G" 	k="123" />
<hkern g1="K" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="123" />
<hkern g1="K" 	g2="Q" 	k="123" />
<hkern g1="K" 	g2="T" 	k="-20" />
<hkern g1="K" 	g2="V" 	k="-41" />
<hkern g1="K" 	g2="W" 	k="-20" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="K" 	g2="AE" 	k="-68" />
<hkern g1="K" 	g2="X" 	k="-41" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-41" />
<hkern g1="K" 	g2="Z" 	k="-20" />
<hkern g1="L" 	g2="hyphen,uni00AD,endash,emdash" 	k="41" />
<hkern g1="L" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="L" 	g2="slash" 	k="-61" />
<hkern g1="L" 	g2="question" 	k="61" />
<hkern g1="L" 	g2="backslash" 	k="143" />
<hkern g1="L" 	g2="bracketright" 	k="72" />
<hkern g1="L" 	g2="x" 	k="-20" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="61" />
<hkern g1="L" 	g2="braceright" 	k="31" />
<hkern g1="L" 	g2="quotesinglbase,quotedblbase" 	k="-88" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="86" />
<hkern g1="L" 	g2="asterisk" 	k="246" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="205" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="205" />
<hkern g1="L" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="L" 	g2="c,ccedilla" 	k="41" />
<hkern g1="L" 	g2="d" 	k="41" />
<hkern g1="L" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="41" />
<hkern g1="L" 	g2="g" 	k="41" />
<hkern g1="L" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="L" 	g2="q" 	k="41" />
<hkern g1="L" 	g2="eth" 	k="41" />
<hkern g1="L" 	g2="v" 	k="123" />
<hkern g1="L" 	g2="w" 	k="102" />
<hkern g1="L" 	g2="braceleft" 	k="51" />
<hkern g1="L" 	g2="t" 	k="20" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="L" 	g2="B" 	k="16" />
<hkern g1="L" 	g2="C,Ccedilla" 	k="123" />
<hkern g1="L" 	g2="D,Eth" 	k="16" />
<hkern g1="L" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="16" />
<hkern g1="L" 	g2="F" 	k="16" />
<hkern g1="L" 	g2="G" 	k="123" />
<hkern g1="L" 	g2="H" 	k="16" />
<hkern g1="L" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="16" />
<hkern g1="L" 	g2="J" 	k="-41" />
<hkern g1="L" 	g2="K" 	k="16" />
<hkern g1="L" 	g2="L" 	k="16" />
<hkern g1="L" 	g2="N,Ntilde" 	k="16" />
<hkern g1="L" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="123" />
<hkern g1="L" 	g2="P" 	k="16" />
<hkern g1="L" 	g2="Q" 	k="123" />
<hkern g1="L" 	g2="R" 	k="16" />
<hkern g1="L" 	g2="T" 	k="246" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="41" />
<hkern g1="L" 	g2="V" 	k="246" />
<hkern g1="L" 	g2="W" 	k="123" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="287" />
<hkern g1="L" 	g2="AE" 	k="-41" />
<hkern g1="L" 	g2="Thorn" 	k="16" />
<hkern g1="L" 	g2="X" 	k="-20" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-41" />
<hkern g1="M" 	g2="backslash" 	k="20" />
<hkern g1="M" 	g2="bracketright" 	k="33" />
<hkern g1="M" 	g2="quotesinglbase,quotedblbase" 	k="-41" />
<hkern g1="M" 	g2="guillemotleft,guilsinglleft" 	k="31" />
<hkern g1="M" 	g2="parenright" 	k="27" />
<hkern g1="M" 	g2="T" 	k="41" />
<hkern g1="M" 	g2="V" 	k="41" />
<hkern g1="M" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="N,Ntilde" 	g2="guillemotleft,guilsinglleft" 	k="23" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="hyphen,uni00AD,endash,emdash" 	k="-41" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="47" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="question" 	k="51" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="backslash" 	k="37" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="bracketright" 	k="37" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="questiondown" 	k="41" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="parenright" 	k="31" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="39" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="102" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="61" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="133" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="55" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="72" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="M" 	k="-20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="31" />
<hkern g1="P" 	g2="hyphen,uni00AD,endash,emdash" 	k="-41" />
<hkern g1="P" 	g2="comma,period,ellipsis" 	k="123" />
<hkern g1="P" 	g2="slash" 	k="82" />
<hkern g1="P" 	g2="question" 	k="23" />
<hkern g1="P" 	g2="backslash" 	k="27" />
<hkern g1="P" 	g2="bracketright" 	k="53" />
<hkern g1="P" 	g2="braceright" 	k="27" />
<hkern g1="P" 	g2="quotesinglbase,quotedblbase" 	k="72" />
<hkern g1="P" 	g2="guillemotleft,guilsinglleft" 	k="23" />
<hkern g1="P" 	g2="questiondown" 	k="123" />
<hkern g1="P" 	g2="parenright" 	k="45" />
<hkern g1="P" 	g2="j" 	k="31" />
<hkern g1="P" 	g2="quoteleft,quotedblleft" 	k="-47" />
<hkern g1="P" 	g2="quoteright,quotedblright" 	k="-47" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="P" 	g2="c,ccedilla" 	k="20" />
<hkern g1="P" 	g2="d" 	k="20" />
<hkern g1="P" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="20" />
<hkern g1="P" 	g2="g" 	k="20" />
<hkern g1="P" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="P" 	g2="q" 	k="20" />
<hkern g1="P" 	g2="eth" 	k="20" />
<hkern g1="P" 	g2="ampersand" 	k="33" />
<hkern g1="P" 	g2="v" 	k="-20" />
<hkern g1="P" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="27" />
<hkern g1="P" 	g2="m,n,ntilde" 	k="20" />
<hkern g1="P" 	g2="p" 	k="20" />
<hkern g1="P" 	g2="r" 	k="20" />
<hkern g1="P" 	g2="germandbls" 	k="27" />
<hkern g1="P" 	g2="J" 	k="246" />
<hkern g1="P" 	g2="V" 	k="20" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="57" />
<hkern g1="P" 	g2="AE" 	k="123" />
<hkern g1="P" 	g2="X" 	k="102" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="143" />
<hkern g1="P" 	g2="Z" 	k="123" />
<hkern g1="Q" 	g2="hyphen,uni00AD,endash,emdash" 	k="-41" />
<hkern g1="Q" 	g2="slash" 	k="47" />
<hkern g1="Q" 	g2="question" 	k="51" />
<hkern g1="Q" 	g2="backslash" 	k="37" />
<hkern g1="Q" 	g2="bracketright" 	k="37" />
<hkern g1="Q" 	g2="questiondown" 	k="41" />
<hkern g1="Q" 	g2="parenright" 	k="31" />
<hkern g1="Q" 	g2="J" 	k="39" />
<hkern g1="Q" 	g2="T" 	k="102" />
<hkern g1="Q" 	g2="V" 	k="61" />
<hkern g1="Q" 	g2="Y,Yacute,Ydieresis" 	k="133" />
<hkern g1="Q" 	g2="AE" 	k="55" />
<hkern g1="Q" 	g2="X" 	k="72" />
<hkern g1="Q" 	g2="M" 	k="-20" />
<hkern g1="Q" 	g2="Z" 	k="31" />
<hkern g1="R" 	g2="slash" 	k="-49" />
<hkern g1="R" 	g2="question" 	k="51" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-43" />
<hkern g1="R" 	g2="C,Ccedilla" 	k="16" />
<hkern g1="R" 	g2="G" 	k="16" />
<hkern g1="R" 	g2="J" 	k="-23" />
<hkern g1="R" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="R" 	g2="Q" 	k="16" />
<hkern g1="R" 	g2="T" 	k="27" />
<hkern g1="R" 	g2="V" 	k="43" />
<hkern g1="R" 	g2="X" 	k="-49" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="78" />
<hkern g1="R" 	g2="backslash" 	k="33" />
<hkern g1="R" 	g2="bracketright" 	k="39" />
<hkern g1="R" 	g2="c,ccedilla" 	k="82" />
<hkern g1="R" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="82" />
<hkern g1="R" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="R" 	g2="v" 	k="-20" />
<hkern g1="R" 	g2="x" 	k="-35" />
<hkern g1="R" 	g2="quotesinglbase,quotedblbase" 	k="-51" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="39" />
<hkern g1="R" 	g2="questiondown" 	k="-25" />
<hkern g1="R" 	g2="AE" 	k="-55" />
<hkern g1="R" 	g2="eth" 	k="82" />
<hkern g1="S" 	g2="C,Ccedilla" 	k="20" />
<hkern g1="S" 	g2="G" 	k="20" />
<hkern g1="S" 	g2="J" 	k="-20" />
<hkern g1="S" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="S" 	g2="Q" 	k="20" />
<hkern g1="S" 	g2="V" 	k="61" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="S" 	g2="v" 	k="61" />
<hkern g1="S" 	g2="quotesinglbase,quotedblbase" 	k="-49" />
<hkern g1="S" 	g2="guillemotleft,guilsinglleft" 	k="27" />
<hkern g1="S" 	g2="parenright" 	k="29" />
<hkern g1="S" 	g2="asterisk" 	k="20" />
<hkern g1="S" 	g2="hyphen,uni00AD,endash,emdash" 	k="-57" />
<hkern g1="S" 	g2="comma,period,ellipsis" 	k="-20" />
<hkern g1="S" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="18" />
<hkern g1="S" 	g2="d" 	k="18" />
<hkern g1="S" 	g2="g" 	k="18" />
<hkern g1="S" 	g2="j" 	k="16" />
<hkern g1="S" 	g2="q" 	k="18" />
<hkern g1="S" 	g2="s" 	k="16" />
<hkern g1="S" 	g2="w" 	k="25" />
<hkern g1="S" 	g2="y,yacute,ydieresis" 	k="39" />
<hkern g1="S" 	g2="quoteleft,quotedblleft" 	k="-29" />
<hkern g1="S" 	g2="quoteright,quotedblright" 	k="-29" />
<hkern g1="T" 	g2="slash" 	k="143" />
<hkern g1="T" 	g2="question" 	k="-59" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="156" />
<hkern g1="T" 	g2="C,Ccedilla" 	k="20" />
<hkern g1="T" 	g2="G" 	k="20" />
<hkern g1="T" 	g2="J" 	k="205" />
<hkern g1="T" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="T" 	g2="Q" 	k="20" />
<hkern g1="T" 	g2="T" 	k="-61" />
<hkern g1="T" 	g2="V" 	k="-72" />
<hkern g1="T" 	g2="X" 	k="-39" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-59" />
<hkern g1="T" 	g2="backslash" 	k="-61" />
<hkern g1="T" 	g2="bracketright" 	k="-53" />
<hkern g1="T" 	g2="c,ccedilla" 	k="164" />
<hkern g1="T" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="164" />
<hkern g1="T" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="164" />
<hkern g1="T" 	g2="v" 	k="102" />
<hkern g1="T" 	g2="x" 	k="123" />
<hkern g1="T" 	g2="quotesinglbase,quotedblbase" 	k="92" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="82" />
<hkern g1="T" 	g2="questiondown" 	k="82" />
<hkern g1="T" 	g2="AE" 	k="145" />
<hkern g1="T" 	g2="eth" 	k="164" />
<hkern g1="T" 	g2="parenright" 	k="-29" />
<hkern g1="T" 	g2="asterisk" 	k="-82" />
<hkern g1="T" 	g2="hyphen,uni00AD,endash,emdash" 	k="82" />
<hkern g1="T" 	g2="comma,period,ellipsis" 	k="123" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="143" />
<hkern g1="T" 	g2="d" 	k="143" />
<hkern g1="T" 	g2="g" 	k="143" />
<hkern g1="T" 	g2="q" 	k="143" />
<hkern g1="T" 	g2="s" 	k="82" />
<hkern g1="T" 	g2="w" 	k="102" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="123" />
<hkern g1="T" 	g2="quoteleft,quotedblleft" 	k="-123" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="-123" />
<hkern g1="T" 	g2="ampersand" 	k="41" />
<hkern g1="T" 	g2="parenleft" 	k="18" />
<hkern g1="T" 	g2="colon,semicolon" 	k="61" />
<hkern g1="T" 	g2="at" 	k="61" />
<hkern g1="T" 	g2="M" 	k="31" />
<hkern g1="T" 	g2="S" 	k="-82" />
<hkern g1="T" 	g2="W" 	k="-51" />
<hkern g1="T" 	g2="m,n,ntilde" 	k="143" />
<hkern g1="T" 	g2="p" 	k="143" />
<hkern g1="T" 	g2="r" 	k="143" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="143" />
<hkern g1="T" 	g2="z" 	k="102" />
<hkern g1="T" 	g2="braceleft" 	k="59" />
<hkern g1="T" 	g2="braceright" 	k="-66" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="82" />
<hkern g1="T" 	g2="exclamdown" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="slash" 	k="29" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="39" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="guillemotleft,guilsinglleft" 	k="16" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="questiondown" 	k="49" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="53" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="-41" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="quoteleft,quotedblleft" 	k="-68" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="quoteright,quotedblright" 	k="-68" />
<hkern g1="V" 	g2="slash" 	k="82" />
<hkern g1="V" 	g2="question" 	k="-31" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="102" />
<hkern g1="V" 	g2="C,Ccedilla" 	k="61" />
<hkern g1="V" 	g2="G" 	k="61" />
<hkern g1="V" 	g2="J" 	k="246" />
<hkern g1="V" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="V" 	g2="Q" 	k="61" />
<hkern g1="V" 	g2="T" 	k="-41" />
<hkern g1="V" 	g2="backslash" 	k="-61" />
<hkern g1="V" 	g2="bracketright" 	k="-53" />
<hkern g1="V" 	g2="c,ccedilla" 	k="82" />
<hkern g1="V" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="82" />
<hkern g1="V" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="V" 	g2="x" 	k="20" />
<hkern g1="V" 	g2="quotesinglbase,quotedblbase" 	k="102" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="84" />
<hkern g1="V" 	g2="questiondown" 	k="164" />
<hkern g1="V" 	g2="AE" 	k="160" />
<hkern g1="V" 	g2="eth" 	k="82" />
<hkern g1="V" 	g2="parenright" 	k="-29" />
<hkern g1="V" 	g2="asterisk" 	k="-61" />
<hkern g1="V" 	g2="comma,period,ellipsis" 	k="143" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="82" />
<hkern g1="V" 	g2="d" 	k="82" />
<hkern g1="V" 	g2="g" 	k="82" />
<hkern g1="V" 	g2="q" 	k="82" />
<hkern g1="V" 	g2="s" 	k="61" />
<hkern g1="V" 	g2="quoteleft,quotedblleft" 	k="-82" />
<hkern g1="V" 	g2="quoteright,quotedblright" 	k="-90" />
<hkern g1="V" 	g2="ampersand" 	k="61" />
<hkern g1="V" 	g2="parenleft" 	k="25" />
<hkern g1="V" 	g2="colon,semicolon" 	k="41" />
<hkern g1="V" 	g2="at" 	k="41" />
<hkern g1="V" 	g2="M" 	k="39" />
<hkern g1="V" 	g2="m,n,ntilde" 	k="61" />
<hkern g1="V" 	g2="p" 	k="61" />
<hkern g1="V" 	g2="r" 	k="61" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="43" />
<hkern g1="V" 	g2="z" 	k="61" />
<hkern g1="V" 	g2="braceleft" 	k="49" />
<hkern g1="V" 	g2="braceright" 	k="-66" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="25" />
<hkern g1="V" 	g2="exclamdown" 	k="37" />
<hkern g1="V" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="35" />
<hkern g1="V" 	g2="germandbls" 	k="35" />
<hkern g1="W" 	g2="slash" 	k="45" />
<hkern g1="W" 	g2="question" 	k="-31" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="55" />
<hkern g1="W" 	g2="C,Ccedilla" 	k="-205" />
<hkern g1="W" 	g2="G" 	k="-205" />
<hkern g1="W" 	g2="J" 	k="123" />
<hkern g1="W" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-205" />
<hkern g1="W" 	g2="Q" 	k="-205" />
<hkern g1="W" 	g2="T" 	k="-41" />
<hkern g1="W" 	g2="backslash" 	k="-57" />
<hkern g1="W" 	g2="bracketright" 	k="-47" />
<hkern g1="W" 	g2="c,ccedilla" 	k="41" />
<hkern g1="W" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="41" />
<hkern g1="W" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="W" 	g2="v" 	k="-31" />
<hkern g1="W" 	g2="quotesinglbase,quotedblbase" 	k="45" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="45" />
<hkern g1="W" 	g2="questiondown" 	k="102" />
<hkern g1="W" 	g2="AE" 	k="104" />
<hkern g1="W" 	g2="eth" 	k="41" />
<hkern g1="W" 	g2="asterisk" 	k="-61" />
<hkern g1="W" 	g2="hyphen,uni00AD,endash,emdash" 	k="-41" />
<hkern g1="W" 	g2="comma,period,ellipsis" 	k="82" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="W" 	g2="d" 	k="41" />
<hkern g1="W" 	g2="g" 	k="41" />
<hkern g1="W" 	g2="q" 	k="41" />
<hkern g1="W" 	g2="s" 	k="31" />
<hkern g1="W" 	g2="quoteleft,quotedblleft" 	k="-82" />
<hkern g1="W" 	g2="quoteright,quotedblright" 	k="-82" />
<hkern g1="W" 	g2="S" 	k="-61" />
<hkern g1="W" 	g2="m,n,ntilde" 	k="31" />
<hkern g1="W" 	g2="p" 	k="31" />
<hkern g1="W" 	g2="r" 	k="31" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="29" />
<hkern g1="W" 	g2="z" 	k="27" />
<hkern g1="W" 	g2="braceright" 	k="-82" />
<hkern g1="W" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="33" />
<hkern g1="W" 	g2="germandbls" 	k="33" />
<hkern g1="X" 	g2="slash" 	k="-66" />
<hkern g1="X" 	g2="C,Ccedilla" 	k="72" />
<hkern g1="X" 	g2="G" 	k="72" />
<hkern g1="X" 	g2="J" 	k="-39" />
<hkern g1="X" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="72" />
<hkern g1="X" 	g2="Q" 	k="72" />
<hkern g1="X" 	g2="T" 	k="20" />
<hkern g1="X" 	g2="backslash" 	k="-35" />
<hkern g1="X" 	g2="bracketright" 	k="-27" />
<hkern g1="X" 	g2="c,ccedilla" 	k="61" />
<hkern g1="X" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="61" />
<hkern g1="X" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="X" 	g2="v" 	k="61" />
<hkern g1="X" 	g2="x" 	k="-20" />
<hkern g1="X" 	g2="quotesinglbase,quotedblbase" 	k="-76" />
<hkern g1="X" 	g2="guillemotleft,guilsinglleft" 	k="72" />
<hkern g1="X" 	g2="questiondown" 	k="-61" />
<hkern g1="X" 	g2="AE" 	k="-72" />
<hkern g1="X" 	g2="eth" 	k="61" />
<hkern g1="X" 	g2="hyphen,uni00AD,endash,emdash" 	k="20" />
<hkern g1="X" 	g2="comma,period,ellipsis" 	k="-51" />
<hkern g1="X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="51" />
<hkern g1="X" 	g2="d" 	k="51" />
<hkern g1="X" 	g2="g" 	k="51" />
<hkern g1="X" 	g2="q" 	k="51" />
<hkern g1="X" 	g2="w" 	k="41" />
<hkern g1="X" 	g2="y,yacute,ydieresis" 	k="47" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="-53" />
<hkern g1="X" 	g2="quoteright,quotedblright" 	k="-43" />
<hkern g1="X" 	g2="ampersand" 	k="41" />
<hkern g1="X" 	g2="at" 	k="23" />
<hkern g1="X" 	g2="S" 	k="-41" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="33" />
<hkern g1="X" 	g2="braceleft" 	k="31" />
<hkern g1="X" 	g2="braceright" 	k="-39" />
<hkern g1="X" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="23" />
<hkern g1="X" 	g2="Z" 	k="-35" />
<hkern g1="X" 	g2="t" 	k="31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,Ccedilla" 	k="51" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="G" 	k="51" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="246" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Q" 	k="51" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="backslash" 	k="-68" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="bracketright" 	k="-57" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,ccedilla" 	k="164" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="164" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="164" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quotesinglbase,quotedblbase" 	k="113" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="questiondown" 	k="164" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="162" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="eth" 	k="164" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="parenright" 	k="-35" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="43" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,ellipsis" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="184" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d" 	k="184" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="184" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="q" 	k="184" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="123" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteleft,quotedblleft" 	k="-61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-76" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="ampersand" 	k="123" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="parenleft" 	k="49" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="51" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="at" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="M" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,ntilde" 	k="86" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="p" 	k="86" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="r" 	k="86" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="123" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="braceleft" 	k="63" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="braceright" 	k="-70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="51" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="exclamdown" 	k="61" />
<hkern g1="Z" 	g2="slash" 	k="-35" />
<hkern g1="Z" 	g2="C,Ccedilla" 	k="51" />
<hkern g1="Z" 	g2="G" 	k="51" />
<hkern g1="Z" 	g2="J" 	k="-35" />
<hkern g1="Z" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="Z" 	g2="Q" 	k="51" />
<hkern g1="Z" 	g2="V" 	k="-35" />
<hkern g1="Z" 	g2="X" 	k="-37" />
<hkern g1="Z" 	g2="backslash" 	k="-35" />
<hkern g1="Z" 	g2="bracketright" 	k="-27" />
<hkern g1="Z" 	g2="c,ccedilla" 	k="123" />
<hkern g1="Z" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="123" />
<hkern g1="Z" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="123" />
<hkern g1="Z" 	g2="quotesinglbase,quotedblbase" 	k="-86" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="51" />
<hkern g1="Z" 	g2="AE" 	k="-43" />
<hkern g1="Z" 	g2="eth" 	k="123" />
<hkern g1="Z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="102" />
<hkern g1="Z" 	g2="d" 	k="102" />
<hkern g1="Z" 	g2="g" 	k="102" />
<hkern g1="Z" 	g2="j" 	k="25" />
<hkern g1="Z" 	g2="q" 	k="102" />
<hkern g1="Z" 	g2="quoteleft,quotedblleft" 	k="-82" />
<hkern g1="Z" 	g2="quoteright,quotedblright" 	k="-84" />
<hkern g1="Z" 	g2="W" 	k="-25" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="82" />
<hkern g1="Z" 	g2="braceright" 	k="-41" />
<hkern g1="Thorn" 	g2="slash" 	k="59" />
<hkern g1="Thorn" 	g2="question" 	k="31" />
<hkern g1="Thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="68" />
<hkern g1="Thorn" 	g2="J" 	k="102" />
<hkern g1="Thorn" 	g2="T" 	k="90" />
<hkern g1="Thorn" 	g2="V" 	k="57" />
<hkern g1="Thorn" 	g2="X" 	k="63" />
<hkern g1="Thorn" 	g2="Y,Yacute,Ydieresis" 	k="102" />
<hkern g1="Thorn" 	g2="backslash" 	k="49" />
<hkern g1="Thorn" 	g2="bracketright" 	k="61" />
<hkern g1="Thorn" 	g2="questiondown" 	k="49" />
<hkern g1="Thorn" 	g2="AE" 	k="76" />
<hkern g1="Thorn" 	g2="parenright" 	k="49" />
<hkern g1="Thorn" 	g2="hyphen,uni00AD,endash,emdash" 	k="-61" />
<hkern g1="Thorn" 	g2="quoteright,quotedblright" 	k="18" />
<hkern g1="Thorn" 	g2="M" 	k="18" />
<hkern g1="Thorn" 	g2="braceleft" 	k="-16" />
<hkern g1="Thorn" 	g2="braceright" 	k="33" />
<hkern g1="Thorn" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="16" />
<hkern g1="Thorn" 	g2="Z" 	k="45" />
<hkern g1="parenleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="parenleft" 	g2="C,Ccedilla" 	k="49" />
<hkern g1="parenleft" 	g2="G" 	k="49" />
<hkern g1="parenleft" 	g2="J" 	k="41" />
<hkern g1="parenleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="49" />
<hkern g1="parenleft" 	g2="Q" 	k="49" />
<hkern g1="parenleft" 	g2="T" 	k="-20" />
<hkern g1="parenleft" 	g2="V" 	k="-41" />
<hkern g1="parenleft" 	g2="X" 	k="-20" />
<hkern g1="parenleft" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="parenleft" 	g2="c,ccedilla" 	k="82" />
<hkern g1="parenleft" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="82" />
<hkern g1="parenleft" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="parenleft" 	g2="v" 	k="18" />
<hkern g1="parenleft" 	g2="x" 	k="29" />
<hkern g1="parenleft" 	g2="eth" 	k="82" />
<hkern g1="parenleft" 	g2="parenright" 	k="-123" />
<hkern g1="parenleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="61" />
<hkern g1="parenleft" 	g2="d" 	k="61" />
<hkern g1="parenleft" 	g2="g" 	k="61" />
<hkern g1="parenleft" 	g2="j" 	k="-102" />
<hkern g1="parenleft" 	g2="q" 	k="61" />
<hkern g1="parenleft" 	g2="s" 	k="70" />
<hkern g1="parenleft" 	g2="w" 	k="29" />
<hkern g1="parenleft" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="parenleft" 	g2="W" 	k="-41" />
<hkern g1="parenleft" 	g2="m,n,ntilde" 	k="68" />
<hkern g1="parenleft" 	g2="p" 	k="68" />
<hkern g1="parenleft" 	g2="r" 	k="68" />
<hkern g1="parenleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="parenleft" 	g2="z" 	k="59" />
<hkern g1="parenleft" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="20" />
<hkern g1="parenleft" 	g2="germandbls" 	k="20" />
<hkern g1="parenleft" 	g2="t" 	k="27" />
<hkern g1="parenleft" 	g2="B" 	k="20" />
<hkern g1="parenleft" 	g2="D,Eth" 	k="20" />
<hkern g1="parenleft" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="20" />
<hkern g1="parenleft" 	g2="F" 	k="20" />
<hkern g1="parenleft" 	g2="H" 	k="20" />
<hkern g1="parenleft" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="20" />
<hkern g1="parenleft" 	g2="K" 	k="20" />
<hkern g1="parenleft" 	g2="L" 	k="20" />
<hkern g1="parenleft" 	g2="N,Ntilde" 	k="20" />
<hkern g1="parenleft" 	g2="P" 	k="20" />
<hkern g1="parenleft" 	g2="R" 	k="20" />
<hkern g1="parenleft" 	g2="Thorn" 	k="20" />
<hkern g1="parenright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="parenright" 	g2="J" 	k="20" />
<hkern g1="parenright" 	g2="T" 	k="43" />
<hkern g1="parenright" 	g2="V" 	k="37" />
<hkern g1="parenright" 	g2="X" 	k="35" />
<hkern g1="parenright" 	g2="Y,Yacute,Ydieresis" 	k="72" />
<hkern g1="parenright" 	g2="x" 	k="23" />
<hkern g1="parenright" 	g2="AE" 	k="29" />
<hkern g1="parenright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="parenright" 	g2="d" 	k="16" />
<hkern g1="parenright" 	g2="g" 	k="16" />
<hkern g1="parenright" 	g2="j" 	k="16" />
<hkern g1="parenright" 	g2="q" 	k="16" />
<hkern g1="parenright" 	g2="y,yacute,ydieresis" 	k="29" />
<hkern g1="parenright" 	g2="z" 	k="16" />
<hkern g1="parenright" 	g2="Z" 	k="20" />
<hkern g1="bracketleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="80" />
<hkern g1="bracketleft" 	g2="C,Ccedilla" 	k="45" />
<hkern g1="bracketleft" 	g2="G" 	k="45" />
<hkern g1="bracketleft" 	g2="J" 	k="78" />
<hkern g1="bracketleft" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="45" />
<hkern g1="bracketleft" 	g2="Q" 	k="45" />
<hkern g1="bracketleft" 	g2="T" 	k="-41" />
<hkern g1="bracketleft" 	g2="V" 	k="-55" />
<hkern g1="bracketleft" 	g2="X" 	k="-35" />
<hkern g1="bracketleft" 	g2="Y,Yacute,Ydieresis" 	k="-55" />
<hkern g1="bracketleft" 	g2="bracketright" 	k="-164" />
<hkern g1="bracketleft" 	g2="c,ccedilla" 	k="61" />
<hkern g1="bracketleft" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="61" />
<hkern g1="bracketleft" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="bracketleft" 	g2="AE" 	k="70" />
<hkern g1="bracketleft" 	g2="eth" 	k="61" />
<hkern g1="bracketleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="84" />
<hkern g1="bracketleft" 	g2="d" 	k="84" />
<hkern g1="bracketleft" 	g2="g" 	k="84" />
<hkern g1="bracketleft" 	g2="j" 	k="-164" />
<hkern g1="bracketleft" 	g2="q" 	k="84" />
<hkern g1="bracketleft" 	g2="s" 	k="66" />
<hkern g1="bracketleft" 	g2="y,yacute,ydieresis" 	k="-39" />
<hkern g1="bracketleft" 	g2="M" 	k="29" />
<hkern g1="bracketleft" 	g2="W" 	k="-43" />
<hkern g1="bracketleft" 	g2="m,n,ntilde" 	k="82" />
<hkern g1="bracketleft" 	g2="p" 	k="82" />
<hkern g1="bracketleft" 	g2="r" 	k="82" />
<hkern g1="bracketleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="59" />
<hkern g1="bracketleft" 	g2="z" 	k="59" />
<hkern g1="bracketleft" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="41" />
<hkern g1="bracketleft" 	g2="germandbls" 	k="41" />
<hkern g1="bracketright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="16" />
<hkern g1="bracketright" 	g2="J" 	k="16" />
<hkern g1="bracketright" 	g2="T" 	k="16" />
<hkern g1="bracketright" 	g2="c,ccedilla" 	k="31" />
<hkern g1="bracketright" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="31" />
<hkern g1="bracketright" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="bracketright" 	g2="eth" 	k="31" />
<hkern g1="bracketright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="29" />
<hkern g1="bracketright" 	g2="d" 	k="29" />
<hkern g1="bracketright" 	g2="g" 	k="29" />
<hkern g1="bracketright" 	g2="j" 	k="-25" />
<hkern g1="bracketright" 	g2="q" 	k="29" />
<hkern g1="bracketright" 	g2="s" 	k="20" />
<hkern g1="bracketright" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="bracketright" 	g2="m,n,ntilde" 	k="25" />
<hkern g1="bracketright" 	g2="p" 	k="25" />
<hkern g1="bracketright" 	g2="r" 	k="25" />
<hkern g1="bracketright" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="bracketright" 	g2="z" 	k="31" />
<hkern g1="bracketright" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="20" />
<hkern g1="bracketright" 	g2="germandbls" 	k="20" />
<hkern g1="bracketright" 	g2="t" 	k="20" />
<hkern g1="bracketright" 	g2="h" 	k="25" />
<hkern g1="bracketright" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="25" />
<hkern g1="bracketright" 	g2="k" 	k="25" />
<hkern g1="bracketright" 	g2="l" 	k="20" />
<hkern g1="braceleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="25" />
<hkern g1="braceleft" 	g2="J" 	k="23" />
<hkern g1="braceleft" 	g2="T" 	k="-68" />
<hkern g1="braceleft" 	g2="V" 	k="-82" />
<hkern g1="braceleft" 	g2="X" 	k="-61" />
<hkern g1="braceleft" 	g2="Y,Yacute,Ydieresis" 	k="-82" />
<hkern g1="braceleft" 	g2="c,ccedilla" 	k="41" />
<hkern g1="braceleft" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="41" />
<hkern g1="braceleft" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="braceleft" 	g2="v" 	k="-20" />
<hkern g1="braceleft" 	g2="eth" 	k="41" />
<hkern g1="braceleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="45" />
<hkern g1="braceleft" 	g2="d" 	k="45" />
<hkern g1="braceleft" 	g2="g" 	k="45" />
<hkern g1="braceleft" 	g2="j" 	k="-164" />
<hkern g1="braceleft" 	g2="q" 	k="45" />
<hkern g1="braceleft" 	g2="s" 	k="27" />
<hkern g1="braceleft" 	g2="y,yacute,ydieresis" 	k="-66" />
<hkern g1="braceleft" 	g2="S" 	k="-23" />
<hkern g1="braceleft" 	g2="W" 	k="-72" />
<hkern g1="braceleft" 	g2="m,n,ntilde" 	k="27" />
<hkern g1="braceleft" 	g2="p" 	k="27" />
<hkern g1="braceleft" 	g2="r" 	k="27" />
<hkern g1="braceleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="33" />
<hkern g1="braceleft" 	g2="z" 	k="33" />
<hkern g1="braceleft" 	g2="braceright" 	k="-215" />
<hkern g1="braceleft" 	g2="Z" 	k="-27" />
<hkern g1="braceright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="29" />
<hkern g1="braceright" 	g2="T" 	k="41" />
<hkern g1="braceright" 	g2="V" 	k="39" />
<hkern g1="braceright" 	g2="X" 	k="31" />
<hkern g1="braceright" 	g2="Y,Yacute,Ydieresis" 	k="66" />
<hkern g1="braceright" 	g2="x" 	k="27" />
<hkern g1="braceright" 	g2="AE" 	k="35" />
<hkern g1="asterisk" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="123" />
<hkern g1="asterisk" 	g2="C,Ccedilla" 	k="27" />
<hkern g1="asterisk" 	g2="G" 	k="27" />
<hkern g1="asterisk" 	g2="J" 	k="211" />
<hkern g1="asterisk" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="27" />
<hkern g1="asterisk" 	g2="Q" 	k="27" />
<hkern g1="asterisk" 	g2="T" 	k="29" />
<hkern g1="asterisk" 	g2="V" 	k="18" />
<hkern g1="asterisk" 	g2="X" 	k="90" />
<hkern g1="asterisk" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="asterisk" 	g2="c,ccedilla" 	k="41" />
<hkern g1="asterisk" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="41" />
<hkern g1="asterisk" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="asterisk" 	g2="AE" 	k="184" />
<hkern g1="asterisk" 	g2="eth" 	k="41" />
<hkern g1="asterisk" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="asterisk" 	g2="d" 	k="41" />
<hkern g1="asterisk" 	g2="g" 	k="41" />
<hkern g1="asterisk" 	g2="j" 	k="47" />
<hkern g1="asterisk" 	g2="q" 	k="41" />
<hkern g1="asterisk" 	g2="s" 	k="29" />
<hkern g1="asterisk" 	g2="M" 	k="39" />
<hkern g1="asterisk" 	g2="m,n,ntilde" 	k="33" />
<hkern g1="asterisk" 	g2="p" 	k="33" />
<hkern g1="asterisk" 	g2="r" 	k="33" />
<hkern g1="asterisk" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="18" />
<hkern g1="asterisk" 	g2="z" 	k="41" />
<hkern g1="asterisk" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="35" />
<hkern g1="asterisk" 	g2="germandbls" 	k="35" />
<hkern g1="asterisk" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="16" />
<hkern g1="asterisk" 	g2="Z" 	k="70" />
<hkern g1="asterisk" 	g2="t" 	k="18" />
<hkern g1="asterisk" 	g2="h" 	k="33" />
<hkern g1="asterisk" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="33" />
<hkern g1="asterisk" 	g2="k" 	k="33" />
<hkern g1="asterisk" 	g2="l" 	k="27" />
<hkern g1="asterisk" 	g2="b" 	k="25" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="143" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="184" />
<hkern g1="quoteleft,quotedblleft" 	g2="T" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="V" 	k="-57" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-39" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,ccedilla" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="v" 	k="-41" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="246" />
<hkern g1="quoteleft,quotedblleft" 	g2="eth" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="d" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="g" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="j" 	k="25" />
<hkern g1="quoteleft,quotedblleft" 	g2="q" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="s" 	k="41" />
<hkern g1="quoteleft,quotedblleft" 	g2="w" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="y,yacute,ydieresis" 	k="-23" />
<hkern g1="quoteleft,quotedblleft" 	g2="S" 	k="-47" />
<hkern g1="quoteleft,quotedblleft" 	g2="W" 	k="-57" />
<hkern g1="quoteleft,quotedblleft" 	g2="Z" 	k="-23" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="164" />
<hkern g1="quoteright,quotedblright" 	g2="C,Ccedilla" 	k="18" />
<hkern g1="quoteright,quotedblright" 	g2="G" 	k="18" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="205" />
<hkern g1="quoteright,quotedblright" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="quoteright,quotedblright" 	g2="Q" 	k="18" />
<hkern g1="quoteright,quotedblright" 	g2="V" 	k="-47" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="-29" />
<hkern g1="quoteright,quotedblright" 	g2="c,ccedilla" 	k="82" />
<hkern g1="quoteright,quotedblright" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="82" />
<hkern g1="quoteright,quotedblright" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="quoteright,quotedblright" 	g2="x" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="246" />
<hkern g1="quoteright,quotedblright" 	g2="eth" 	k="82" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="92" />
<hkern g1="quoteright,quotedblright" 	g2="d" 	k="92" />
<hkern g1="quoteright,quotedblright" 	g2="g" 	k="92" />
<hkern g1="quoteright,quotedblright" 	g2="j" 	k="39" />
<hkern g1="quoteright,quotedblright" 	g2="q" 	k="92" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="51" />
<hkern g1="quoteright,quotedblright" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="M" 	k="16" />
<hkern g1="quoteright,quotedblright" 	g2="S" 	k="-37" />
<hkern g1="quoteright,quotedblright" 	g2="W" 	k="-51" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,ntilde" 	k="61" />
<hkern g1="quoteright,quotedblright" 	g2="p" 	k="61" />
<hkern g1="quoteright,quotedblright" 	g2="r" 	k="61" />
<hkern g1="quoteright,quotedblright" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="43" />
<hkern g1="quoteright,quotedblright" 	g2="z" 	k="51" />
<hkern g1="quoteright,quotedblright" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="53" />
<hkern g1="quoteright,quotedblright" 	g2="germandbls" 	k="53" />
<hkern g1="quoteright,quotedblright" 	g2="t" 	k="37" />
<hkern g1="quoteright,quotedblright" 	g2="h" 	k="47" />
<hkern g1="quoteright,quotedblright" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="25" />
<hkern g1="quoteright,quotedblright" 	g2="k" 	k="47" />
<hkern g1="quoteright,quotedblright" 	g2="l" 	k="39" />
<hkern g1="quoteright,quotedblright" 	g2="b" 	k="41" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-78" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="C,Ccedilla" 	k="43" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="G" 	k="43" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="J" 	k="-80" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="43" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Q" 	k="43" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="T" 	k="184" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="V" 	k="164" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="X" 	k="-61" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="225" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="c,ccedilla" 	k="61" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="61" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="v" 	k="61" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="x" 	k="-25" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="AE" 	k="-72" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="eth" 	k="61" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="61" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="d" 	k="61" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="g" 	k="61" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="j" 	k="-102" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="q" 	k="61" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="w" 	k="59" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="M" 	k="-47" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="S" 	k="-63" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="W" 	k="72" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="z" 	k="-29" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="41" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Z" 	k="-68" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="B" 	k="-31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="D,Eth" 	k="-31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="-31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="F" 	k="-31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="H" 	k="-31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="-31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="K" 	k="-31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="L" 	k="-31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="N,Ntilde" 	k="-31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="P" 	k="-31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="R" 	k="-31" />
<hkern g1="quotesinglbase,quotedblbase" 	g2="Thorn" 	k="-31" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="J" 	k="-31" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="121" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="29" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="v" 	k="-25" />
<hkern g1="guillemotright,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="33" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="143" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="80" />
<hkern g1="guillemotright,guilsinglright" 	g2="X" 	k="68" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="205" />
<hkern g1="guillemotright,guilsinglright" 	g2="v" 	k="25" />
<hkern g1="guillemotright,guilsinglright" 	g2="x" 	k="63" />
<hkern g1="guillemotright,guilsinglright" 	g2="AE" 	k="61" />
<hkern g1="guillemotright,guilsinglright" 	g2="j" 	k="39" />
<hkern g1="guillemotright,guilsinglright" 	g2="s" 	k="35" />
<hkern g1="guillemotright,guilsinglright" 	g2="w" 	k="31" />
<hkern g1="guillemotright,guilsinglright" 	g2="y,yacute,ydieresis" 	k="43" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="25" />
<hkern g1="guillemotright,guilsinglright" 	g2="m,n,ntilde" 	k="27" />
<hkern g1="guillemotright,guilsinglright" 	g2="p" 	k="27" />
<hkern g1="guillemotright,guilsinglright" 	g2="r" 	k="27" />
<hkern g1="guillemotright,guilsinglright" 	g2="z" 	k="49" />
<hkern g1="guillemotright,guilsinglright" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="29" />
<hkern g1="guillemotright,guilsinglright" 	g2="germandbls" 	k="29" />
<hkern g1="guillemotright,guilsinglright" 	g2="h" 	k="27" />
<hkern g1="guillemotright,guilsinglright" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="25" />
<hkern g1="guillemotright,guilsinglright" 	g2="k" 	k="27" />
<hkern g1="guillemotright,guilsinglright" 	g2="b" 	k="20" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="T" 	k="123" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="V" 	k="61" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="X" 	k="41" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="205" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="c,ccedilla" 	k="-20" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="-20" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-51" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="d" 	k="-51" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="g" 	k="-51" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="l" 	k="-20" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="b" 	k="-31" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="q" 	k="-51" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="t" 	k="-20" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="-41" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="v" 	k="-20" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="x" 	k="41" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="z" 	k="20" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="eth" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="comma,period,ellipsis" 	g2="v" 	k="61" />
<hkern g1="comma,period,ellipsis" 	g2="x" 	k="-41" />
<hkern g1="comma,period,ellipsis" 	g2="z" 	k="-41" />
<hkern g1="comma,period,ellipsis" 	g2="eth" 	k="41" />
<hkern g1="comma,period,ellipsis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-102" />
<hkern g1="comma,period,ellipsis" 	g2="C,Ccedilla" 	k="123" />
<hkern g1="comma,period,ellipsis" 	g2="G" 	k="123" />
<hkern g1="comma,period,ellipsis" 	g2="J" 	k="-92" />
<hkern g1="comma,period,ellipsis" 	g2="M" 	k="-20" />
<hkern g1="comma,period,ellipsis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="123" />
<hkern g1="comma,period,ellipsis" 	g2="Q" 	k="123" />
<hkern g1="comma,period,ellipsis" 	g2="S" 	k="-82" />
<hkern g1="comma,period,ellipsis" 	g2="T" 	k="123" />
<hkern g1="comma,period,ellipsis" 	g2="V" 	k="123" />
<hkern g1="comma,period,ellipsis" 	g2="W" 	k="20" />
<hkern g1="comma,period,ellipsis" 	g2="X" 	k="-82" />
<hkern g1="comma,period,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="205" />
<hkern g1="comma,period,ellipsis" 	g2="Z" 	k="-82" />
<hkern g1="comma,period,ellipsis" 	g2="c,ccedilla" 	k="41" />
<hkern g1="comma,period,ellipsis" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="41" />
<hkern g1="comma,period,ellipsis" 	g2="j" 	k="-102" />
<hkern g1="comma,period,ellipsis" 	g2="s" 	k="-41" />
<hkern g1="comma,period,ellipsis" 	g2="w" 	k="41" />
<hkern g1="comma,period,ellipsis" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="comma,period,ellipsis" 	g2="AE" 	k="-82" />
<hkern g1="colon,semicolon" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-61" />
<hkern g1="colon,semicolon" 	g2="J" 	k="-61" />
<hkern g1="colon,semicolon" 	g2="M" 	k="-31" />
<hkern g1="colon,semicolon" 	g2="S" 	k="-61" />
<hkern g1="colon,semicolon" 	g2="T" 	k="102" />
<hkern g1="colon,semicolon" 	g2="V" 	k="41" />
<hkern g1="colon,semicolon" 	g2="X" 	k="-41" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="102" />
<hkern g1="colon,semicolon" 	g2="AE" 	k="-61" />
<hkern g1="backslash" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="backslash" 	g2="q" 	k="31" />
<hkern g1="backslash" 	g2="t" 	k="41" />
<hkern g1="backslash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="backslash" 	g2="v" 	k="51" />
<hkern g1="backslash" 	g2="x" 	k="-41" />
<hkern g1="backslash" 	g2="eth" 	k="61" />
<hkern g1="backslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-41" />
<hkern g1="backslash" 	g2="C,Ccedilla" 	k="57" />
<hkern g1="backslash" 	g2="G" 	k="57" />
<hkern g1="backslash" 	g2="J" 	k="-25" />
<hkern g1="backslash" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="57" />
<hkern g1="backslash" 	g2="Q" 	k="57" />
<hkern g1="backslash" 	g2="T" 	k="164" />
<hkern g1="backslash" 	g2="V" 	k="96" />
<hkern g1="backslash" 	g2="W" 	k="51" />
<hkern g1="backslash" 	g2="X" 	k="-47" />
<hkern g1="backslash" 	g2="Y,Yacute,Ydieresis" 	k="143" />
<hkern g1="backslash" 	g2="c,ccedilla" 	k="61" />
<hkern g1="backslash" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="61" />
<hkern g1="backslash" 	g2="j" 	k="-266" />
<hkern g1="backslash" 	g2="w" 	k="57" />
<hkern g1="backslash" 	g2="y,yacute,ydieresis" 	k="-123" />
<hkern g1="backslash" 	g2="AE" 	k="-51" />
<hkern g1="backslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="43" />
<hkern g1="backslash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="backslash" 	g2="b" 	k="20" />
<hkern g1="backslash" 	g2="d" 	k="31" />
<hkern g1="backslash" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="-41" />
<hkern g1="backslash" 	g2="g" 	k="31" />
<hkern g1="backslash" 	g2="h" 	k="16" />
<hkern g1="backslash" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="16" />
<hkern g1="backslash" 	g2="k" 	k="16" />
<hkern g1="backslash" 	g2="l" 	k="41" />
<hkern g1="backslash" 	g2="p" 	k="-20" />
<hkern g1="backslash" 	g2="germandbls" 	k="-41" />
<hkern g1="slash" 	g2="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="164" />
<hkern g1="slash" 	g2="q" 	k="164" />
<hkern g1="slash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="55" />
<hkern g1="slash" 	g2="z" 	k="55" />
<hkern g1="slash" 	g2="eth" 	k="164" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="143" />
<hkern g1="slash" 	g2="C,Ccedilla" 	k="55" />
<hkern g1="slash" 	g2="G" 	k="55" />
<hkern g1="slash" 	g2="J" 	k="143" />
<hkern g1="slash" 	g2="M" 	k="45" />
<hkern g1="slash" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="55" />
<hkern g1="slash" 	g2="Q" 	k="55" />
<hkern g1="slash" 	g2="T" 	k="-43" />
<hkern g1="slash" 	g2="V" 	k="-57" />
<hkern g1="slash" 	g2="W" 	k="-47" />
<hkern g1="slash" 	g2="X" 	k="-37" />
<hkern g1="slash" 	g2="Y,Yacute,Ydieresis" 	k="-57" />
<hkern g1="slash" 	g2="c,ccedilla" 	k="164" />
<hkern g1="slash" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="164" />
<hkern g1="slash" 	g2="s" 	k="74" />
<hkern g1="slash" 	g2="AE" 	k="164" />
<hkern g1="slash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="164" />
<hkern g1="slash" 	g2="d" 	k="164" />
<hkern g1="slash" 	g2="f,uniFB01,uniFB02,uniFB03,uniFB04" 	k="43" />
<hkern g1="slash" 	g2="g" 	k="164" />
<hkern g1="slash" 	g2="p" 	k="123" />
<hkern g1="slash" 	g2="germandbls" 	k="43" />
<hkern g1="slash" 	g2="m,n,ntilde" 	k="123" />
<hkern g1="slash" 	g2="r" 	k="123" />
<hkern g1="question" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="-102" />
<hkern g1="question" 	g2="v" 	k="-102" />
<hkern g1="question" 	g2="x" 	k="-82" />
<hkern g1="question" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="61" />
<hkern g1="question" 	g2="J" 	k="82" />
<hkern g1="question" 	g2="S" 	k="-82" />
<hkern g1="question" 	g2="T" 	k="-61" />
<hkern g1="question" 	g2="w" 	k="-102" />
<hkern g1="question" 	g2="y,yacute,ydieresis" 	k="-102" />
<hkern g1="question" 	g2="AE" 	k="102" />
<hkern g1="ampersand" 	g2="x" 	k="-82" />
<hkern g1="ampersand" 	g2="z" 	k="-51" />
<hkern g1="ampersand" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-102" />
<hkern g1="ampersand" 	g2="J" 	k="-61" />
<hkern g1="ampersand" 	g2="M" 	k="-41" />
<hkern g1="ampersand" 	g2="S" 	k="-41" />
<hkern g1="ampersand" 	g2="T" 	k="102" />
<hkern g1="ampersand" 	g2="V" 	k="82" />
<hkern g1="ampersand" 	g2="X" 	k="-82" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ydieresis" 	k="143" />
<hkern g1="ampersand" 	g2="Z" 	k="-82" />
<hkern g1="ampersand" 	g2="s" 	k="-31" />
<hkern g1="ampersand" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="ampersand" 	g2="AE" 	k="-102" />
<hkern g1="exclamdown" 	g2="T" 	k="119" />
<hkern g1="exclamdown" 	g2="V" 	k="43" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="questiondown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="questiondown" 	g2="C,Ccedilla" 	k="16" />
<hkern g1="questiondown" 	g2="G" 	k="16" />
<hkern g1="questiondown" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="questiondown" 	g2="Q" 	k="16" />
<hkern g1="questiondown" 	g2="S" 	k="16" />
<hkern g1="questiondown" 	g2="T" 	k="123" />
<hkern g1="questiondown" 	g2="V" 	k="78" />
<hkern g1="questiondown" 	g2="W" 	k="27" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="117" />
<hkern g1="questiondown" 	g2="Z" 	k="47" />
<hkern g1="questiondown" 	g2="AE" 	k="80" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="18" />
<hkern g1="at" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="at" 	g2="T" 	k="82" />
<hkern g1="at" 	g2="V" 	k="82" />
<hkern g1="at" 	g2="X" 	k="41" />
<hkern g1="at" 	g2="Y,Yacute,Ydieresis" 	k="102" />
<hkern g1="at" 	g2="AE" 	k="31" />
<hkern g1="bar" 	g2="q" 	k="23" />
<hkern g1="bar" 	g2="z" 	k="23" />
<hkern g1="bar" 	g2="j" 	k="-33" />
<hkern g1="bar" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="23" />
<hkern g1="bar" 	g2="d" 	k="23" />
<hkern g1="bar" 	g2="g" 	k="23" />
<hkern g1="bar" 	g2="p" 	k="16" />
<hkern g1="bar" 	g2="m,n,ntilde" 	k="16" />
<hkern g1="bar" 	g2="r" 	k="16" />
</font>
</defs></svg> 