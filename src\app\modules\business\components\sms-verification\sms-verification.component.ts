import { RegistrationService } from '../../service/registration.service';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { SmsCodeRequest } from './smsCodeRequest.class';
import { SmsCodeResponse } from '../../model/sms-code-response.class';
import { AfterViewInit, Component, OnInit } from '@angular/core';
import * as HttpStatus from 'http-status-codes';
import { ApiErrorsResponse } from '../../../shared/models/api-errors-response';
import { Router } from '@angular/router';
import { LanguageService } from '../../../language/language.service';

@Component({
  selector: 'app-sms-verification',
  templateUrl: './sms-verification.component.html',
  styleUrls: ['./sms-verification.component.scss'],
})
export class SmsVerificationComponent implements OnInit, AfterViewInit {
  smsForm: UntypedFormGroup;
  resendTime: string;
  smsCodeRequest = {} as SmsCodeRequest;
  isFirstTimeSms = true;
  disabledBtn: boolean;

  constructor(
    private registrationService: RegistrationService,
    private languageService: LanguageService,
    private fb: UntypedFormBuilder,
    private router: Router
  ) {}

  ngOnInit() {
    this.smsCodeRequest.firstName = this.registrationService.person.firstName;
    this.smsCodeRequest.phoneNumber = this.registrationService.person.cellPhone;
    this.smsCodeRequest.captcha = '';
    this.smsCodeRequest.lang = this.languageService.currentLang;
    this.createForm();
    this.getSmsCode(this.smsCodeRequest);
  }

  ngAfterViewInit() {
    setTimeout(() => {
      for (const key in this.smsForm.controls) {
        if (this.smsForm.controls[key]) {
          this.smsForm.controls[key].markAsPristine();
        }
      }
    }, 50);
  }

  getSmsCode(smsCodeRequest: SmsCodeRequest): void {
    const success = (res: SmsCodeResponse) => {
      if (res.responseObject !== null) return;
      if (!this.isFirstTimeSms) {
        this.smsForm.get('smsCode').markAsTouched();
        if (res.success) {
          this.smsForm.get('smsCode').setErrors({
            NEW_CODE_HAS_BEEN_SENT: true,
          });
          return;
        }
        this.smsForm.get('smsCode').setErrors({
          SMS_CAN_BE_RESENT_AFTER: { resendTime: res.timeToNextResend },
        });
        this.resendTime = res.timeToNextResend;
      }
      this.isFirstTimeSms = false;
    };

    const fail = (res: any) => {
      this.smsForm.get('smsCode').setErrors({ SOMETHING_WENT_WRONG: true });
    };

    this.registrationService
      .getSmsCode(smsCodeRequest)
      .subscribe(success, fail);
  }

  createForm() {
    this.smsForm = this.fb.group({
      smsCode: ['', [Validators.required]],
    });
  }

  resendSms(event: Event) {
    event.preventDefault();
    this.smsCodeRequest.resend = true;
    this.getSmsCode(this.smsCodeRequest);
  }

  createBusinessAccountAndLoanRequest(event: Event) {
    const success = () => {
      this.disabledBtn = true;
      this.router.navigate(['/loans']);
      window.scrollTo(0, 0);
    };

    const fail = (res: any) => {
      if (res.status === HttpStatus.UNPROCESSABLE_ENTITY) {
        const apiErrors: ApiErrorsResponse = JSON.parse(res.error);
        for (const fieldError of apiErrors.fieldErrors) {
          if (fieldError.field === 'smsCode') {
            const error = {};
            error[fieldError.errorMessage] = true;
            this.smsForm.get('smsCode').setErrors(error);
          }
        }
      }
    };
    event.preventDefault();
    this.registrationService
      .createBusinessAccountAndLoan(
        this.smsForm.get('smsCode').value,
        this.languageService.currentLang
      )
      .subscribe(success, fail);
  }
}
