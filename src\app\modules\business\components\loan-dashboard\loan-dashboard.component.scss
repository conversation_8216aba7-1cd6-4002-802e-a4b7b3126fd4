@import 'styles/vendors/bootstrap/share';
@import 'styles/variables';

.loan-row-section {
  margin: 0 0 30px;
  @include media-breakpoint-up(lg) {
    margin: 0 0 60px;
  }
}

:host ::ng-deep .loan-dashboard-wrap {
  @include media-breakpoint-up(lg) {
    padding: 0 0 30px 100px;
    position: relative;
    margin-left: 100px;
  }

  .rtl & {
    @include media-breakpoint-up(lg) {
      padding: 0 100px 30px 0;
      position: relative;
      margin: 0;
      margin-right: 100px;
    }
  }

  .indicator-line {
    position: absolute;
    left: 29px;
    top: 25px;
    width: 1px;
    height: 100%;
    background: #cbcbcb;

    &:after {
      content: '';
      position: absolute;
      z-index: 2;
      bottom: 0;
      border-radius: 50%;
      left: 50%;
      transform: translateX(-50%);
      height: 18px;
      width: 18px;
      background: #00bec7;
    }

    .rtl & {
      left: auto;
      right: 29px;
    }
  }

  .loan-heading {
    padding: 24px 45px;
    font-size: 20px;
    position: relative;
    @include media-breakpoint-up(lg) {
      font-size: 24px;
      padding: 24px 0;
    }

    h2 {
      margin: 0;
      font-size: inherit;
    }

    .icon-box {
      position: absolute;
      left: 0;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      top: 50%;
      background: #00bec7;
      //overflow: hidden;
      transform: translateY(-50%);
      @include media-breakpoint-up(lg) {
        position: absolute;
        left: -100px;
        width: 59px;
        height: 59px;
      }

      .rtl & {
        left: auto;
        right: 0;
        @include media-breakpoint-up(lg) {
          left: auto;
          right: -100px;
        }
      }

      img {
        display: block;
        width: 100%;
        border-radius: 50%;
      }
    }
  }
}

:host ::ng-deep .loan-row {
  color: #575757;
  margin-bottom: 15px;

  > a {
    display: block;
    // outline: none;
    color: inherit;
    text-decoration: none;

    .no-data {
      display: block;
      color: #267ffa;
    }
  }

  .loan-box {
    display: flex;
    align-items: center;
    border: 1px solid #dcdcdc;
    border-radius: 2px;
    background: #ffffff;
    color: inherit;
    padding: 15px;
    position: relative;
    @include media-breakpoint-up(md) {
      padding: 30px;
    }

    &.has-no-data {
      border: 1px solid #267ffa;
    }

    &:after {
      content: '';
      position: absolute;
      top: 50%;
      right: 15px;
      width: 10px;
      height: 10px;
      border-top: 1px solid #969696;
      border-left: 1px solid #969696;
      transform: translateY(-50%) rotate(135deg);
      @include media-breakpoint-up(md) {
        right: 30px;
      }

      .rtl & {
        right: auto;
        left: 15px;
        transform: translateY(-50%) rotate(-45deg);
        @include media-breakpoint-up(md) {
          right: auto;
          left: 30px;
        }
      }
    }
  }

  h4 {
    font-size: 18px;
    margin: 0 0 10px;
    color: #737373;
    font-weight: bold;

    &:only-child {
      margin: 0;
    }
  }

  .ico-holder {
    width: 30px;
    height: 30px;
    display: block;
    //border: 1px solid #00BEC7;
    border-radius: 50%;
    position: relative;
    @include media-breakpoint-up(md) {
      width: 40px;
      height: 40px;
    }

    img {
      display: block;
      width: 100%;
      border-radius: 50%;
    }

    &.checked {
      &:before {
        font: 8px/1 'icomoon', sans-serif !important;
        content: '\e90b';
        position: absolute;
        top: -3px;
        left: -5px;
        height: 15px;
        color: #fff;
        border-radius: 50%;
        width: 15px;
        background: #267ffa;
        display: flex;
        align-items: center;
        justify-content: center;
        @include media-breakpoint-up(md) {
          height: 20px;
          width: 20px;
          left: -10px;
        }

        .rtl & {
          left: auto;
          right: -5px;
          @include media-breakpoint-up(md) {
            left: auto;
            right: -10px;
          }
        }
      }
    }
  }

  .loan-text {
    padding: 0 12px;
    width: calc(100% - 30px);
    @include media-breakpoint-up(md) {
      width: calc(100% - 40px);
      padding: 0 22px;
    }
  }
}

:host ::ng-deep .sidebar {
  &.loan-dashboard-wrap {
    padding: 0;
    margin-left: 5px;
    .indicator-line {
      left: auto;
      right: 250px;
    }
    .loan-text {
      & h4 {
        font-size: 13px;
      }
      .loan-desc {
        display: none;
      }
      .no-data {
        font-size: 11px;
      }
    }
    .loan-heading {
      h2 {
        font-size: 18px;
      }
      .icon-box {
        width: 35px;
        height: 35px;
        left: auto;
        right: 235px;
      }
    }
    .rtl & {
      padding: 0;
      margin-right: 5px;
      .indicator-line {
        right: -20px;
      }
      .loan-heading .icon-box {
        right: -38px;
      }
      .loan-box {
        &:after {
          right: auto;
          left: 15px;
        }
      }
    }
  }
  .loan-row .loan-box {
    padding: 15px;
  }
  .ico-holder {
    width: 25px;
    height: 25px;
  }
}

@media only screen and (max-width: 700px) {
  :host ::ng-deep .btn-back {
    width: 100%;
  }
  .btn-holder {
    margin-bottom: 20px;
  }
  :host ::ng-deep .sidebar {
    &.loan-dashboard-wrap {
      margin-left: 0;
      .loan-heading .icon-box {
        left: 0;
        right: 0;
      }
      .rtl & {
        margin-right: 0;
        .loan-heading .icon-box {
          right: 0;
        }
      }
    }
  }
}
