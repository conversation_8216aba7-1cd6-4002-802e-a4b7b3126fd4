import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { AgentsDataService } from '~tarya-agents/services/agents-data.service';
import { AgentDetail } from '~tarya-agents/model/agentDetail';
import { AgentTransaction } from '~tarya-agents/model/agent-transaction';
import { TableConfig } from '~tarya-agents/model/tableConfig';

@Component({
  selector: 'app-agent-file',
  templateUrl: './agent-file.component.html',
  styleUrls: ['./agent-file.component.scss'],
})
export class AgentFileComponent implements OnChanges {
  @Input() numberOfLenders: number;

  agentDetail: AgentDetail;
  transactions: AgentTransaction[];
  config: TableConfig;

  constructor(private agentDataService: AgentsDataService) {}

  ngOnChanges(changes: SimpleChanges) {
    if ('numberOfLenders' in changes) {
      this.numberOfLenders = changes.numberOfLenders.currentValue;
    }
    this.agentDataService.getAgentDetail().subscribe((data) => {
      this.agentDetail = data;
      this.agentDetail.numberOfLenders = this.numberOfLenders;
      this.transactions = data.transactions.filter((item) => {
        return item.transactionDate !== null, item.name.split(' ');
      });
    });
    this.config = {
      cols: [
        { field: 'transactionDate', header: 'clients.date', type: 'number' },
        { field: 'name', header: 'clients.name', type: 'string' },
        { field: 'socialId', header: 'clients.id', type: 'string' },
        { field: 'action', header: 'clients.action', type: 'string' },
        { field: 'amount', header: 'clients.amount', type: 'fractional' },
      ],
      filters: [
        {
          field: 'firstName',
          label: 'registrations.first_name',
          type: 'string',
        },
        { field: 'lastName', label: 'registrations.last_name', type: 'string' },
        { field: 'socialId', label: 'registrations.id', type: 'string' },
      ],
    };
  }
}
