<form (ngSubmit)="onSubmit()" [formGroup]="logInForm" class="tarya-mor">
  <div class="text-center">
    <div class="mb-4">
      <app-tar-input-text
        class="mb-4"
        [form]="logInForm"
        [key]="'password'"
        [fieldType]="'password'"
        [label]="'registrations.password' | translate"
      ></app-tar-input-text>
      <control-validation-errors
        [control]="logInForm.controls['password']"
        translateTag="error"
      ></control-validation-errors>
    </div>
    <app-tar-input-text
      [form]="logInForm"
      [key]="'confirmPassword'"
      [fieldType]="'password'"
      [label]="'registrations.repaet_password' | translate"
    ></app-tar-input-text>
    <control-validation-errors
      [control]="logInForm.controls['confirmPassword']"
      translateTag="error"
    ></control-validation-errors>

    <h6 class="rules-title">{{ 'registrations.conditions' | translate }}:</h6>
    <ul class="rules">
      <li>{{ 'registrations.rule_one' | translate }}</li>
      <li>{{ 'registrations.rule_two' | translate }}</li>
      <li>{{ 'registrations.rule_three' | translate }}</li>
    </ul>
  </div>
  <div class="form-group text-left tar-btn-row">
    <button type="submit" class="btn-primary btn" [disabled]="!logInForm.valid">
      {{ 'registrations.continue' | translate }}
    </button>
  </div>
</form>
