import {
  AfterViewInit,
  Component,
  HostListener,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { cloneDeep, isEqual } from 'lodash';
import { SelectItem } from 'primeng/api';
import { Observable, Subject, throwError } from 'rxjs';
import { catchError, takeUntil } from 'rxjs/operators';
import { MessageType } from '~tarya-shared/services/models/popup-message-type.enum';
import { TemplateReferenceService } from '~tarya/modules/login/services/template-reference.service';
import { FormHelpersService } from '~tarya/modules/shared/services/form-helpers.service';
import { Base } from '~tarya/modules/table-generator/shared/base';
import { AccountDetailsService } from '../../services/account-details.service';

@Component({
  selector: 'app-account-details',
  templateUrl: './account-details.component.html',
  styleUrls: ['./account-details.component.scss'],
})
export class AccountDetailsComponent
  extends Base
  implements OnInit, AfterViewInit
{
  @ViewChild('layoutSideImage') layoutSideImage: TemplateRef<HTMLImageElement>;
  form: UntypedFormGroup;
  countries$: Observable<SelectItem[]>;
  cities$: Observable<SelectItem[]>;
  streets$: Observable<SelectItem[]>;
  errors = {
    onlyRequired: {
      required: 'account.form-errors.required',
    },
    mobilePhone: {
      required: 'account.form-errors.required',
      invalidPhone: 'account.form-errors.phone-verification-error',
      phoneVerificationError: 'account.form-errors.number-is-not-verified',
    },
  };
  showMobileConfirmationDialog: boolean;
  verificationPhoneControl: any;
  initialValues: any;
  confirmationDialog = false;
  dashboardRoute: string;
  leaveWithoutSaving = false;
  languages = [
    { label: 'account.language-options.english', value: 'EN' },
    { label: 'account.language-options.hebrew', value: 'HE' },
  ];
  isSuccessfulPhoneVerification: boolean = false;
  constructor(
    private accountDetailsService: AccountDetailsService,
    private translateService: TranslateService,
    private route: ActivatedRoute,
    private formHelpersService: FormHelpersService,
    private templateRef: TemplateReferenceService,
    private router: Router
  ) {
    super();
  }
  @HostListener('window:beforeunload', ['$event'])
  unloadNotification($event: any) {
    if (!isEqual(this.initialValues, this.form.value)) {
      $event.returnValue = true;
    }
  }
  ngOnInit() {
    this.form = this.accountDetailsService.generateForm();

    this.countries$ = this.accountDetailsService.getCountries(
      this.translateService.currentLang
    );
    this.cities$ = this.accountDetailsService.loadCities();

    this.form
      .get(['address', 'city'])
      .valueChanges.pipe(takeUntil(this.destroy$))
      .subscribe((city) => {
        this.streets$ = this.accountDetailsService.getStreets(city);
        this.form.get(['address', 'streetName']).reset();
      });

    this.accountDetailsService.fillAccountDetailsForm(
      this.form,
      this.route.snapshot.data.accountDetails,
      this.route.snapshot.data.lender
    );

    this.accountDetailsService.initialMobilePhone =
      this.route.snapshot.data.accountDetails.mobilePhone;

    this.dashboardRoute = localStorage.getItem('dashboardRoute');

    this.initialValues = cloneDeep(this.form.value);
  }

  ngAfterViewInit() {
    this.templateRef.change$.next(this.layoutSideImage);
  }

  save() {
    if (
      this.isPhoneVerificationNeeded() &&
      !this.isSuccessfulPhoneVerification
    ) {
      this.form.get('mobilePhone').setErrors({ phoneVerificationError: true });
    }

    this.form.markAllAsTouched();

    if (this.form.valid) {
      const data = {
        ...this.form.value,
        verificationCode: this.accountDetailsService.verificationCode,
        dateOfBirth: this.route.snapshot.data.accountDetails.dateOfBirth,
      };
      data.address.description =
        this.route.snapshot.data.accountDetails.address.description;
      data.address.id = this.route.snapshot.data.accountDetails.address.id;

      this.accountDetailsService
        .updateAccountData(data)
        .pipe(
          takeUntil(this.destroy$),
          catchError((err) => {
            this.accountDetailsService.showToast(
              'account.error',
              MessageType.Error
            );
            return throwError(err);
          })
        )
        .subscribe((accountData) => {
          this.accountDetailsService.showToast('account.success');
          this.accountDetailsService.initialMobilePhone =
            accountData.mobilePhone;
          this.initialValues = cloneDeep(this.form.value);
          this.router.navigateByUrl(this.dashboardRoute);
        });
    } else {
      this.formHelpersService.goToFirstInvalidField();
    }
  }

  isPhoneVerificationNeeded(): boolean {
    const field = this.form.get('mobilePhone');

    return (
      field.dirty &&
      (field.valid || field.hasError('phoneVerificationError')) &&
      field.value.length > 0 &&
      field.value !== this.accountDetailsService.initialMobilePhone
    );
  }

  showMobileConfirmation(value: boolean): void {
    this.showMobileConfirmationDialog = value;
  }

  showPhoneVerified(show: boolean): void {
    this.isSuccessfulPhoneVerification = show;
  }

  cancelSave(cancelSave?: boolean): void {
    const currentDetails = cloneDeep(this.form.value);

    if (cancelSave) {
      this.leaveWithoutSaving = true;
      this.confirmationDialog = false;
      this.router.navigateByUrl(this.dashboardRoute);
    } else if (cancelSave === false) {
      this.confirmationDialog = false;
    } else if (!isEqual(this.initialValues, currentDetails)) {
      this.confirmationDialog = true;
    } else {
      this.router.navigateByUrl(this.dashboardRoute);
    }
  }

  disableSave(): boolean {
    const currentDetails = cloneDeep(this.form.value);
    return isEqual(this.initialValues, currentDetails);
  }

  canDeactivate(): Observable<boolean> | boolean {
    const currentDetails = cloneDeep(this.form.value);

    if (
      !this.leaveWithoutSaving &&
      !isEqual(this.initialValues, currentDetails)
    ) {
      this.confirmationDialog = true;
      return false;
    }
    return true;
  }

  getFormControl(formControlName: string): UntypedFormControl {
    return this.form.get(formControlName) as UntypedFormControl;
  }

  getSubFormGroup(formGroupName: string): UntypedFormGroup {
    return this.form.get(formGroupName) as UntypedFormGroup;
  }

  generateAriaLabelsForPrefilledFields(
    translationKey: string,
    formControlName: string
  ): string {
    return `${this.translateService.instant(translationKey)} ${
      this.form.get(formControlName).value
    }`;
  }
}
