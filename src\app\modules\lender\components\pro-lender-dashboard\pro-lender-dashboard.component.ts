import { Component, OnInit, NgZone, ChangeDetectorRef } from '@angular/core';
import { <PERSON><PERSON>ani<PERSON><PERSON>, SafeResourceUrl } from '@angular/platform-browser';
import { TransactionsEnum } from '../../enums/transactions-enum';
import { ActivatedRoute } from '@angular/router';
import { Lender } from '../../models/lender';

import { LenderUserService } from '../../services/lender-user.service';
import { LenderDocumentsService } from '../../services/lender-documents.service';
import { LenderSubuserService } from '~tarya-lender/services/lender-subuser.service';
import { of } from 'rxjs';
import { Transaction } from '../../models/transaction';
import { map, startWith, switchMap, takeUntil, tap } from 'rxjs/operators';
import { UtilityService } from '../../services/utility.service';
import { PeriodsEnum } from '../../enums/periods.enum';
import { TotalInvestedWithdraws } from '~tarya/modules/shared/utils/tarya-utils';
import { Base } from '~tarya/modules/table-generator/shared/base';
import { TranslateService } from '@ngx-translate/core';

@Component({
  templateUrl: './pro-lender-dashboard.component.html',
  styleUrls: ['./pro-lender-dashboard.component.scss'],
})
export class ProLenderDashboardComponent extends Base implements OnInit {
  TR = TransactionsEnum;

  lender: Lender;

  isProtocolUploaded: boolean;
  showProtocolNotification: boolean;

  investmentAgentEnabled: boolean;
  transactions: Transaction[];
  lateLoansDevaluation: number;

  totalInvestedWithdraws: TotalInvestedWithdraws;
  accumulatedGrossReturns: { sum: number; percent: number };
  openReportsPageInfoPopUp = false;

  // Cached YouTube URL and current language tracking
  youtubeUrl: SafeResourceUrl;
  private currentLanguage: string;

  selectedLanguage = this.translateService.onLangChange.pipe(
    map((event) => event.lang),
    startWith(this.translateService.currentLang)
  );

  private generateYoutubeUrl(language: string): SafeResourceUrl {
    return this.ngZone.runOutsideAngular(() => {
      const baseUrl = 'https://www.youtube.com/embed/e083Qk1GZ_M';
      const params = new URLSearchParams({
        si: 'FNfDGmrr0ikVaTvr',
        color: 'red',
        modestbranding: '1',
        rel: '0',
        showinfo: '0',
        hl: language || 'en',
      });
      const url = `${baseUrl}?${params.toString()}`;
      return this.sanitizer.bypassSecurityTrustResourceUrl(url);
    });
  }

  private updateYoutubeUrlIfNeeded(): void {
    const newLanguage = this.translateService.currentLang || 'en';
    if (this.currentLanguage !== newLanguage) {
      this.currentLanguage = newLanguage;
      this.youtubeUrl = this.generateYoutubeUrl(newLanguage);
      this.cdr.markForCheck();
    }
  }
  constructor(
    protected route: ActivatedRoute,
    private subUserservice: LenderSubuserService,
    private lenderService: LenderUserService,
    private documentsService: LenderDocumentsService,
    private utilityService: UtilityService,
    private translateService: TranslateService,
    private sanitizer: DomSanitizer
  ) {
    super();
  }

  ngOnInit() {
    window.scroll(0, 0);
    this.getSublenderData();

    this.transactions = new Lender(
      this.route.snapshot.data.lender
    ).transactions;
    this.getDocuments();
    this.changeInvestmentAgent();
    if (!localStorage.getItem('showReportsInfoPopUp')) {
      this.openReportsPageInfoPopUp = true;
    }
  }

  getSublenderData() {
    this.subUserservice.data
      .pipe(
        switchMap((data) => {
          if (data && data !== 'mainLender') {
            return this.lenderService.observableSubLenderData;
          }
          const lender = this.route.snapshot.data.lender;
          if (lender !== null) {
            this.investmentAgentEnabled = lender.investmentAgentEnabled;
            return of(new Lender(lender));
          }
        }),
        takeUntil(this.destroy$)
      )
      .subscribe((lender) => {
        this.lender = lender;

        this.totalInvestedWithdraws = {
          totalInvest: lender.totalDepositsAmount,
          totalWithdraw: lender.totalWithdrawsAmount,
        };
        this.accumulatedGrossReturns = {
          sum: lender.returns.accumulatedGrossReturn,
          percent: lender.returns.accumulatedGrossReturnPercent,
        };

        this.investmentAgentEnabled = lender.investmentAgentEnabled;
        this.setLateLoansDevaluation();
      });
  }

  getDocuments() {
    this.documentsService
      .isDocumentUploaded()
      .pipe(takeUntil(this.destroy$))
      .subscribe((isProtocolUploaded) => {
        this.showProtocolNotification =
          !isProtocolUploaded && this.lender.isCorporateLender;
      });
  }

  sendFile() {
    setTimeout(() => {
      this.showProtocolNotification = false;
    }, 5000);
  }

  changeInvestmentAgent() {
    this.subUserservice.agentEnabledChange
      .pipe(takeUntil(this.destroy$))
      .subscribe((isEnabled) => (this.investmentAgentEnabled = isEnabled));
  }

  setLateLoansDevaluation() {
    this.lateLoansDevaluation =
      this.utilityService.getReturnsDeductionsByPeriod(
        this.lender,
        PeriodsEnum.SINCE_JOIN
      ).totalDevaluationDueLatePayments;
  }

  closeReportsPageInfoPopUp(): void {
    this.openReportsPageInfoPopUp = false;
    localStorage.setItem('showReportsInfoPopUp', 'alreadyShowed');
  }
}
