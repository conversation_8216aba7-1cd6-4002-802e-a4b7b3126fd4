<div class="white-box-wrap">
  <div class="business-owner-documents">
    <form [formGroup]="parentFilesForm" *ngIf="documentsArray">
        <div class="heading-secondary">
            <h3>מסמכים</h3>
            <h4>{{'loan-request.documents.title' | translate}}</h4>
            <p [innerHtml]="instruction"></p>
        </div>
        <app-documents-box 
            *ngFor="let documentItem of documentsArray" 
            [documentsInfo]="documentItem" 
            [fileStackConfig]="fileStackPickerConfigurations"
            [ownersInfo]="owners"
            [parentFilesForm]="parentFilesForm"
            (checkedFiles)="onCheckedFiles($event)"
            [storeCheckboxs]="storeCheckboxs"
            [filestackWorkflowUUID]="filestackWorkflowUUID">
        </app-documents-box>
        <div class="btn-holder">
          <button type="button" class="btn btn-primary" [disabled]="checkedFiles.length === 0" (click)="submit()">{{ (isSave  ? 'loan-request.documents.btn-save'  : 'loan-request.documents.btn-next') | translate }}</button>
      </div>
    </form>
  </div>
</div>
