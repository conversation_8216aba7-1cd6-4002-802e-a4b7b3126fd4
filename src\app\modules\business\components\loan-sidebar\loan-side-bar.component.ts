import { Component, Input, OnInit } from '@angular/core';
import { LoanRequestService } from '../../service/loan-request.service';
import { LoanRequest } from '../../model/loan-request';
import { ActivatedRoute, Router } from '@angular/router';
import { LOAN_PAGES } from '../../layouts/loan/loan-pages';
import { LoanSideBarData } from './loan-indicators-data.class';
import { DocumentsService } from '../../service/documents.service';
import { LoanRequestStatus } from '../../enums/loan-request-status.enum';
import { Base } from '~tarya/modules/table-generator/shared/base';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'loan-sidebar',
  templateUrl: './loan-side-bar.component.html',
  styleUrls: ['./loan-side-bar.component.scss'],
})
export class LoanSideBarComponent extends Base implements OnInit {
  loanRequest: LoanRequest;
  gaugeVal: any;
  loanCancelled: boolean;
  canSubmitLoan = false;

  @Input() loanSideBarData: LoanSideBarData;

  constructor(
    private loanRequestService: LoanRequestService,
    private route: ActivatedRoute,
    private router: Router,
    private documentsService: DocumentsService
  ) {
    super();
  }

  ngOnInit() {
    this.gaugeVal = 270 + (40 * 180) / 100;
    /*this.gaugeVal = (270 + ((this.loanData.progressValue * 180) / 100));*/
    this.loanCancelled =
      this.loanSideBarData.loanRequestStatus === LoanRequestStatus.CANCELED;

    this.documentsService.currentUploaded
      .pipe(takeUntil(this.destroy$))
      .subscribe((data) => {
        if (!data) {
          this.canSubmitLoan = true;
        }
      });
  }

  submit(event: Event) {
    event.preventDefault();
    this.loanRequestService
      .submitLoanRequest(this.loanSideBarData.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.router.navigate([LOAN_PAGES[6]], { relativeTo: this.route });
        localStorage.clear();
      });
  }
}
