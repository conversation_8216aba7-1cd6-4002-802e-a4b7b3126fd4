import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'detail-info-card',
  templateUrl: './detail-info-card.component.html',
  styleUrls: ['./detail-info-card.component.scss'],
})
export class DetailInfoCardComponent implements OnInit {
  @Input('formLocked')
  formLocked: boolean;

  @Input('lineId')
  id: number;

  @Input('email')
  email: string;

  @Input('name')
  name: string;

  @Output() toggleEditData = new EventEmitter();

  @Output() toggleDeleteData = new EventEmitter();

  constructor() {}

  ngOnInit() {}

  editData() {
    this.toggleEditData.emit(this.id);
  }

  deleteData() {
    this.toggleDeleteData.emit(this.id);
  }
}
