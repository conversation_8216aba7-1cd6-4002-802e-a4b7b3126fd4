import {
  ActivatedRouteSnapshot,
  Resolve,
  RouterStateSnapshot,
} from '@angular/router';
import { Injectable } from '@angular/core';
import { TypeEnum } from '../../model/type-enum';
import { Observable, of } from 'rxjs';
import { EnumService } from '../../service/enum.service';
import { catchError } from 'rxjs/operators';

@Injectable()
export class BusinessCategoriesResolver implements Resolve<TypeEnum[]> {
  businessTypesMock: TypeEnum[] = [
    {
      id: 1,
      en: 'LLC',
      he: 'חברה בע״מ',
    },
    {
      id: 2,
      en: 'Branch of Foreign Company',
      he: 'עוסק מורשה',
    },
    {
      id: 3,
      en: 'Partnership',
      he: 'שותפות',
    },
  ];

  constructor(private enumService: EnumService) {}

  resolve(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<TypeEnum[]> | Promise<TypeEnum[]> | TypeEnum[] {
    return this.enumService
      .getCategoryOptions('businessType')
      .pipe(catchError(() => of(this.businessTypesMock)));
  }
}
